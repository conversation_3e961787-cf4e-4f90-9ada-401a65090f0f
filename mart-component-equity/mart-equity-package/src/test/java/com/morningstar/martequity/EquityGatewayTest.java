package com.morningstar.martequity;

import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.EquityDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.MongodbCollection;
import com.morningstar.martequity.core.service.EquityService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import static org.mockito.Mockito.mockStatic;

public class EquityGatewayTest {

    private EquityService equityService;
    private EquityGateway equityGateway;
    private EquityCalculationLibService equityCalculationLibService;

    @BeforeEach
    public void setup() {
        this.equityService = Mockito.mock(EquityService.class);
        this.equityCalculationLibService = Mockito.mock(EquityCalculationLibService.class);
        this.equityGateway = new EquityGateway(equityService, equityCalculationLibService);
        ExecutorService executorService = Mockito.mock(ExecutorService.class);
        ReflectionTestUtils.setField(equityGateway, "executorService", executorService);
    }

    @Test
    public void testRetrieve_emptyRequest() {
        MartRequest request = MartRequest.builder().build();
        Assertions.assertEquals(Boolean.FALSE, equityGateway.retrieve(request).hasElements().block());
    }

    @Test
    public void testRetrieve_emptyEquityDataPoints() {
        MartRequest request = MartRequest.builder().dps(List.of("N1234")).build();

        Assertions.assertEquals(Boolean.FALSE, equityGateway.retrieve(request).hasElements().block());
    }

    @Test
    public void testRetrieve() {
        MartRequest request = MartRequest.builder().dps(List.of("EQTE1")).build();
        DataPoint dp1 = DataPoint.builder().id("EQTE1").nid("EQTE1").equityDatapoint(EquityDataPoint.builder()
                .mostRecentCollection(MongodbCollection.builder().build())
                .mostRecentField("msField")
                .msLevel(0)
                .build()).build();

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EQTE1")).thenReturn(dp1);
            Mockito.when(equityService.getData(request)).thenReturn(List.of(new CurrentResult("0P0000034T", Map.of("EQTE1", "1"), Map.of())));
            Assertions.assertEquals(1, equityGateway.retrieve(request).collectList().block().size());
        }
    }

    @Test
    public void testRetrieve_mixedEquityAndNonEquityDataPoints() {
        MartRequest request = MartRequest.builder().dps(List.of("EQTE1", "NONCALC1")).build();
        DataPoint equityDp = DataPoint.builder().id("EQTE1").nid("EQTE1")
                .equityDatapoint(EquityDataPoint.builder().build())
                .calculation(Mockito.mock(com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation.class))
                .build();
        DataPoint nonCalcDp = DataPoint.builder().id("NONCALC1").nid("NONCALC1").build();

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EQTE1")).thenReturn(equityDp);
            mockedStatic.when(() -> DataPointRepository.getByNid("NONCALC1")).thenReturn(nonCalcDp);

            Mockito.when(equityService.getData(request)).thenReturn(List.of(new CurrentResult("SEC1", Map.of("EQTE1", "1"), Map.of())));
            MartRequest nonCalcRequest = MartRequest.builder().dps(List.of("NONCALC1")).build();
            Mockito.when(equityService.getData(nonCalcRequest)).thenReturn(List.of(new CurrentResult("SEC1", Map.of("NONCALC1", "2"), Map.of())));

            Assertions.assertTrue(equityGateway.retrieve(request).collectList().block().size() > 0);
        }
    }

    @Test
    public void testRetrieve_isCurrencyTrueAddsRequiredIds() {
        MartRequest request = MartRequest.builder().dps(new java.util.ArrayList<>(List.of("EQTE1"))).currency("USD").build();
        DataPoint dp = Mockito.mock(DataPoint.class);
        com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation calc = Mockito.mock(com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation.class);
        DataPoint endDate = Mockito.mock(DataPoint.class);
        DataPoint cur = Mockito.mock(DataPoint.class);

        Mockito.when(dp.isCalcLib("USD")).thenReturn(true);
        Mockito.when(dp.getCalculation()).thenReturn(calc);
        Mockito.when(calc.getEndDate()).thenReturn(endDate);
        Mockito.when(calc.getCur()).thenReturn(cur);
        Mockito.when(endDate.getId()).thenReturn("ENDDATEID");
        Mockito.when(cur.getId()).thenReturn("CURID");

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EQTE1")).thenReturn(dp);

            Mockito.when(equityService.getData(Mockito.any())).thenReturn(List.of(new CurrentResult("SEC1", Map.of("EQTE1", "1"), Map.of())));
            Mockito.when(equityCalculationLibService.retrieveCalcLib(Mockito.any(), Mockito.any())).thenReturn(reactor.core.publisher.Flux.empty());

            Assertions.assertNotNull(equityGateway.retrieve(request).collectList().block());
        }
    }

    @Test
    public void testSplitBySecurityType_privateModelAndUnknown() {
        CalcRequest calcRequest = Mockito.mock(CalcRequest.class);
        IdMapper privateModelMapper = Mockito.mock(IdMapper.class);
        IdMapper normalMapper = Mockito.mock(IdMapper.class);

        Mockito.when(privateModelMapper.isPrivateModel()).thenReturn(true);
        Mockito.when(privateModelMapper.getInvestmentId()).thenReturn("ID1");
        Mockito.when(normalMapper.isPrivateModel()).thenReturn(false);
        Mockito.when(normalMapper.getInvestmentId()).thenReturn("ID2");
        Mockito.when(normalMapper.getSecurityType()).thenReturn("Equity");

        java.util.List<IdMapper> idMappers = List.of(privateModelMapper, normalMapper);
        java.util.List<String> idList = List.of("ID1", "ID2", "ID3"); // ID3 will be unknown

        Mockito.when(calcRequest.getIdMappers()).thenReturn(new java.util.ArrayList<>(idMappers));
        Mockito.when(calcRequest.getIdList()).thenReturn(new java.util.ArrayList<>(idList));
        Mockito.when(calcRequest.shallowCopy()).thenReturn(calcRequest);

        java.util.List<CalcRequest> split = ReflectionTestUtils.invokeMethod(equityGateway, "splitBySecurityType", calcRequest);
        Assertions.assertFalse(split.isEmpty());
    }

    @Test
    public void testBuildRequestWithoutIds() {
        CalcRequest calcRequest = Mockito.mock(CalcRequest.class);
        Mockito.when(calcRequest.shallowCopy()).thenReturn(calcRequest);
        Mockito.doNothing().when(calcRequest).setIdList(Mockito.anyList());
        Mockito.doNothing().when(calcRequest).setIdMappers(Mockito.anyList());

        CalcRequest result = ReflectionTestUtils.invokeMethod(equityGateway, "buildRequestWithoutIds", calcRequest);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testRetrieve_noDps() {
        MartRequest request = MartRequest.builder().dps(null).build();
        Assertions.assertEquals(Boolean.FALSE, equityGateway.retrieve(request).hasElements().block());
    }

    @Test
    public void testRetrieve_isCurrencyTrue_mergesCalcLibAndNonCalcResults() {
        // 1. Prepare request with both calcLib and non-calc dps
        MartRequest request = MartRequest.builder()
                .ids(List.of("SEC1"))
                .dps(new ArrayList<>(List.of("EQTE1", "NONCALC1")))
                .currency("USD")
                .startDate("2023-01-01")
                .build();

        // 2. Mock DataPoint for "EQTE1" (calcLib, equity)
        DataPoint eqte1Dp = Mockito.mock(DataPoint.class);
        Calculation calc = Mockito.mock(Calculation.class);
        DataPoint endDate = Mockito.mock(DataPoint.class);
        DataPoint cur = Mockito.mock(DataPoint.class);

        Mockito.when(eqte1Dp.isCalcLib("USD")).thenReturn(true);
        Mockito.when(eqte1Dp.getCalculation()).thenReturn(calc);
        Mockito.when(eqte1Dp.getEquityDatapoint()).thenReturn(EquityDataPoint.builder().build());
        Mockito.when(calc.getEndDate()).thenReturn(endDate);
        Mockito.when(calc.getCur()).thenReturn(cur);
        Mockito.when(endDate.getId()).thenReturn("ENDDATEID");
        Mockito.when(cur.getId()).thenReturn("CURID");

        // 3. Mock DataPoint for "NONCALC1" (not calcLib, not equity)
        DataPoint nonCalcDp = Mockito.mock(DataPoint.class);
        Mockito.when(nonCalcDp.isCalcLib("USD")).thenReturn(false);
        Mockito.when(nonCalcDp.getCalculation()).thenReturn(null);
        Mockito.when(nonCalcDp.getEquityDatapoint()).thenReturn(EquityDataPoint.builder().build());

        try (MockedStatic<DataPointRepository> mockedStatic = mockStatic(DataPointRepository.class)) {
            mockedStatic.when(() -> DataPointRepository.getByNid("EQTE1")).thenReturn(eqte1Dp);
            mockedStatic.when(() -> DataPointRepository.getByNid("NONCALC1")).thenReturn(nonCalcDp);
            mockedStatic.when(() -> DataPointRepository.getByNid("ENDDATEID")).thenReturn(Mockito.mock(DataPoint.class));
            mockedStatic.when(() -> DataPointRepository.getByNid("CURID")).thenReturn(Mockito.mock(DataPoint.class));

            // 4. Mock equityService for non-calc request
            Mockito.when(equityService.getData(Mockito.argThat(r -> r != null && r.getDps() != null && r.getDps().size() == 1 && r.getDps().contains("NONCALC1"))))
                    .thenReturn(List.of(new CurrentResult("SEC1", Map.of("NONCALC1", "2"), Map.of())));

            // 5. Mock calculationLibService for calcLib request
            Mockito.when(equityCalculationLibService.retrieveCalcLib(Mockito.any(), Mockito.any()))
                    .thenReturn(reactor.core.publisher.Flux.just(new CurrentResult("SEC1", Map.of("EQTE1", "100"), Map.of())));

            // 6. Run and verify
            List<Result> results = equityGateway.retrieve(request).collectList().block();
            boolean foundCalcLib = results.stream().anyMatch(r -> ((CurrentResult) r).getValues().containsValue("100"));
            boolean foundNonCalc = results.stream().anyMatch(r -> ((CurrentResult) r).getValues().containsValue("2"));
            Assertions.assertTrue(foundCalcLib, "CalcLib result not found in merged results");
            Assertions.assertTrue(foundNonCalc, "NonCalc result not found in merged results");
        }
    }
}
