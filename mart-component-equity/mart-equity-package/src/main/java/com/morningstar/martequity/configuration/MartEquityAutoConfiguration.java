package com.morningstar.martequity.configuration;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.morningstar.dataac.martgateway.core.calculationlib.CalculationLibConversionService;
import com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper;
import com.morningstar.dataac.martgateway.core.common.util.MDCInheritedThreadPoolExecutor;
import com.morningstar.dataac.martgateway.core.datapointloader.config.EnableDataPointLoaders;
import com.morningstar.martequity.EquityCalculationLibService;
import com.morningstar.martequity.EquityGateway;
import com.morningstar.martequity.core.dao.EquityMongoDao;
import com.morningstar.martequity.core.parser.QueryParser;
import com.morningstar.martequity.core.service.EquityService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.concurrent.ExecutorService;

@Lazy
@Configuration
@EnableConfigurationProperties({MartEquityProperties.class})
@EnableDataPointLoaders
public class MartEquityAutoConfiguration {
    private final MartEquityProperties martEquityProperties;

    @Value("${martequity.mongouri:}")
    private String mongoUri;

    @Autowired
    public MartEquityAutoConfiguration(MartEquityProperties martEquityProperties) {
        this.martEquityProperties = martEquityProperties;
    }

    @Bean(name = "equityGateway")
    @ConditionalOnMissingBean(name = "equityGateway")
    public EquityGateway getEquityGateway(EquityService equityService,
                                          EquityCalculationLibService equityCalculationLibService) {
        return new EquityGateway(equityService, equityCalculationLibService);
    }

    @Bean(name = "equityService")
    @ConditionalOnMissingBean(name = "equityService")
    public EquityService getEquityServiceBean(ExecutorService mongoThreadPool, QueryParser equityMongoQueryParser,
                                              EquityMongoDao equityMongoDao) {
        return new EquityService(equityMongoDao, equityMongoQueryParser, mongoThreadPool);
    }

    @Bean(name = "mongoThreadPool", destroyMethod = "shutdown")
    @ConditionalOnMissingBean(name = "mongoThreadPool")
    public ExecutorService getMongoThreadPool() {
        int cores = Runtime.getRuntime().availableProcessors();
        return new MDCInheritedThreadPoolExecutor(cores * 24, "Equity-Mongo-");
    }

    @Bean(name = "equityCalculationLibService")
    public EquityCalculationLibService equityCalculationLibService(
            CalculationLibConversionService equityCalculationLibConversionService) {
        return new EquityCalculationLibService(equityCalculationLibConversionService);
    }

    @Bean(name = "equityMongoQueryParser")
    @ConditionalOnMissingBean(name = "equityMongoQueryParser")
    public QueryParser getEquityMongoQueryParser() {
        return new QueryParser();
    }

    @Bean(name = "equityCalculationLibConversionService")
    @ConditionalOnMissingBean(name = "equityCalculationLibConversionService")
    public CalculationLibConversionService calculationLibConversionService(CurrencyConversionHelper currencyConversionHelper) {
        return new CalculationLibConversionService(currencyConversionHelper);
    }

    @Bean
    @ConditionalOnMissingBean(MongoClient.class)
    public MongoClient getEquityMongoClient(@Value("${mongodb.uri}") String uri) {
        if(StringUtils.isNotEmpty(mongoUri)) {
            uri = mongoUri;
        }
        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(uri))
                .build();

        return MongoClients.create(settings);
    }

    @Bean(name = "equityMongoDao")
    @ConditionalOnMissingBean(EquityMongoDao.class)
    public EquityMongoDao getEquityMongoDao(MongoClient mongoClient, @Value("${mongodb.database}") String database) {
        return new EquityMongoDao(mongoClient, database);
    }
}
