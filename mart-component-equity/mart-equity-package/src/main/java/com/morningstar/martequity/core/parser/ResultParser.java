package com.morningstar.martequity.core.parser;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointType;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.Aggregation;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.Filter;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.MongodbCollection;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martequity.core.entity.DbQueryParameters;
import com.morningstar.martequity.core.entity.ExtractionTask;
import com.morningstar.martequity.core.entity.IdLevel;
import com.morningstar.martequity.util.FilterUtil;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ResultParser {

    private final String DATE_PATTERN = "yyyy-MM-dd";
    private final String DATETIME_PATTERN = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    private final DbQueryParameters dbQueryParameters;
    private final List<Document> documents;
    private final Map<Integer, List<DataPoint>> dataPointsByLevel;
    private final Set<String> requestedDataPoints;
    private final Map<String, List<String>> companyIdsToPerIds;

    private Map<String, TimeSeriesResult> timeSeriesResult;
    private Map<String, CurrentResult> mostRecentResult;
    private Map<String, GroupResult> groupResult;

    private Deque<ExtractionTask> extractionStack;
    @Setter
    private int currentCounter = 0;
    @Getter
    private boolean isComplete = false;
    private final MartRequest martRequest;
    private final FilterUtil filter;

    public ResultParser(
            DbQueryParameters dbParameters,
            List<Document> documents,
            Set<String> requestedDps,
            Map<String, List<String>> companyIdsToPerIds,
            MartRequest martRequest) {
        this.dbQueryParameters = dbParameters;
        this.documents = documents;
        this.dataPointsByLevel = groupDataPointByLevel(dbParameters.getDataPoints());
        this.requestedDataPoints = requestedDps;
        this.extractionStack = new ArrayDeque<>();
        this.timeSeriesResult = new HashMap<>();
        this.mostRecentResult = new HashMap<>();
        this.groupResult = new HashMap<>();
        this.companyIdsToPerIds = companyIdsToPerIds;
        this.martRequest = martRequest;
        this.filter = new FilterUtil();
    }

    private Map<Integer, List<DataPoint>> groupDataPointByLevel(List<DataPoint> dps) {
        if (dbQueryParameters.getType() == DataPointType.timeSeries)
            return dps.stream().collect(Collectors.groupingBy(dp -> dp.getEquityDatapoint().getTsLevel()));
        else
            return dps.stream().collect(Collectors.groupingBy(dp -> dp.getEquityDatapoint().getMsLevel()));
    }

    public void setCurrentCounter(int currentCounter) {
        this.currentCounter = currentCounter;
    }

    public boolean isComplete() {
        return isComplete;
    }

    public void parseNext() {
        if (CollectionUtils.isEmpty(documents) || currentCounter >= documents.size()) {
            isComplete = true;
            return;
        }

      this.extractionStack.push(new ExtractionTask(
              getInvestmentId(documents.get(currentCounter)),
              documents.get(currentCounter),
              StringUtils.EMPTY,
              0,
              new HashMap<>(),
              getTimeField(dbQueryParameters.getType(), documents.get(currentCounter), dbQueryParameters.getTimeField()),
              getInvestmentId(documents.get(currentCounter))
      ));
      parse();
      currentCounter +=1;
    }

    private String getInvestmentId(Document root) {
        return root.get(dbQueryParameters.getLevel().getValue()).toString();
    }

    private String getTimeField(DataPointType type, Document document, String timeFieldPath) {
        if (type == DataPointType.timeSeries && StringUtils.isNotBlank(timeFieldPath)) {
            String[] datePath = timeFieldPath.split("\\.");
            Document currDoc = document;

            for (int i = 0; i < datePath.length - 1; i++) {
                try {
                    if(currDoc.get(datePath[i]) instanceof Document) currDoc = (Document) currDoc.get(datePath[i]);

                } catch (Exception e) {
                    log.error(String.format("Missing date field for timefield %s", timeFieldPath));
                    return StringUtils.EMPTY;
                }
            }
            return getStringValue(currDoc.getOrDefault(datePath[datePath.length - 1], StringUtils.EMPTY), StringUtils.EMPTY);
        }
        return StringUtils.EMPTY;
    }

    private void parse() {
        while (!extractionStack.isEmpty()) {
            ExtractionTask task = extractionStack.pop();
            String investmentId = task.getInvestmentId();
            Document doc = task.getDocument();
            int level = task.getCurrentLevel();
            String path = task.getParentPath();
            List<DataPoint> dps = dataPointsByLevel.getOrDefault(level, Collections.emptyList());
            Map<String, String> primary = new HashMap<>(task.getPrimary());
            String timeField = task.getTimeField();
            StringBuilder dpHash = new StringBuilder(task.getHash());

            for (String primaryField : dbQueryParameters.getCollection()
                    .getPrimaryFieldsByLevel().getOrDefault(level, Collections.emptySet())) {
                extractPrimaryValue(doc, primaryField.replace(path.concat("."), StringUtils.EMPTY), primary, dpHash);
            }

            for (Filter resultFilter : dbQueryParameters.getCollection()
                    .getFiltersByLevel().getOrDefault(level, Collections.emptyList())) {

                List<DataPoint> aggregatedDatapointList = getAggregatedDatapointList(dps);
                if(dbQueryParameters.getCollection().getAggregations() != null && !aggregatedDatapointList.isEmpty()){
                    if(isFilterCheckRequired(dbQueryParameters.getCollection())){
                        addColumnToDocument(aggregatedDatapointList,doc,level);
                    }else{
                        break;
                    }
                }
                if(!filter.checkIfAttributeValueExist(resultFilter.getFilterType(), resultFilter.getAttributes(), martRequest.getEquityMetaData())) continue;
                String filterField = resultFilter.getDbField().replace(path.concat("."),StringUtils.EMPTY);
                if(filterField.contains(".")) continue;
                if (!filter.evaluate(resultFilter, martRequest.getEquityMetaData(), doc, filterField)) {
                    doc = new Document();
                    break;
                }
                if (resultFilter.isTimeField() && timeField.isEmpty())
                    timeField = getStringValue(doc.getOrDefault(filterField, StringUtils.EMPTY), StringUtils.EMPTY);
            }

            if (doc.isEmpty()) continue;

            for (Map.Entry<String, Object> entry : doc.entrySet()) {

                String key = entry.getKey();
                Object value = entry.getValue();
                String entryPath = path.isBlank() ? key : path + "." + key;

                if (value instanceof Document) {
                    extractionStack.add(new ExtractionTask(
                            task.getInvestmentId(),
                            (Document) value,
                            entryPath,
                            level + 1,
                            primary,
                            timeField,
                            dpHash.toString()
                    ));
                } else if (value instanceof List) {
                    for (Document document : (ArrayList<Document>) value) {
                        extractionStack.add(new ExtractionTask(
                                task.getInvestmentId(),
                                document,
                                entryPath,
                                level + 1,
                                primary,
                                timeField,
                                dpHash.toString()
                        ));
                    }
                }

            }

            if (dps == null || dps.isEmpty())
                continue;

        //Extract DataPoints
        for(DataPoint dp: dps.stream()
                .filter(d -> requestedDataPoints.contains(d.getId()))
                .collect(Collectors.toList())
        ) {
           String fieldName = getFieldName(dp, dbQueryParameters.getType(), path);
            if (!dp.isGroup() && !doc.containsKey(fieldName)) continue;
            switch(dbQueryParameters.getType()) {
                case mostRecent:
                    if (dp.isGroup()){
                        addGroupResult(dpHash.toString(), investmentId, dp, doc, path, primary);
                        break;
                    }
                    addMostRecentResult(dpHash.toString(), investmentId, dp.getId(), getStringValue(doc.get(fieldName), dp.getDataType()), primary);
                    break;
                case timeSeries:
                    if (dp.isGroup()){
                        addGroupResult(dpHash.toString(), investmentId, dp, doc, path, primary);
                        break;
                    }
                    addTimeSeriesResult(dpHash.toString(), investmentId, dp.getId(), timeField, getStringValue(doc.get(fieldName), dp.getDataType()), primary);
                    break;
            }
        }
      }
    }

    private void extractPrimaryValue(Document d, String fieldName, Map<String, String> existingPrimary, StringBuilder primaryHash) {
        if (fieldName.contains(".")) return;
        if (!d.containsKey(fieldName)) {
            log.error("Missing primary {} while attempting to parse for collection {}", fieldName, dbQueryParameters.getCollection().getName());
            return;
        }
        existingPrimary.put(fieldName, getStringValue(d.get(fieldName), StringUtils.EMPTY));
        primaryHash.append(getStringValue(d.get(fieldName), StringUtils.EMPTY));
    }

    private String getFieldName(DataPoint dp, DataPointType type, String parentPath) {
        if (dp.isGroup())
            return StringUtils.EMPTY;
        if (type == DataPointType.timeSeries)
            return dp.getEquityDatapoint().getTimeSeriesField().replace(parentPath.concat("."),StringUtils.EMPTY);
        return dp.getEquityDatapoint().getMostRecentField().replace(parentPath.concat("."),StringUtils.EMPTY);
    }

    private String getStringValue(Object value, String dataType) {
        if (value instanceof Date) {
            SimpleDateFormat outputFormat = "datetime".equals(dataType) ? new SimpleDateFormat(DATETIME_PATTERN, Locale.ENGLISH) :
                    new SimpleDateFormat(DATE_PATTERN, Locale.ENGLISH);
            outputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
            return outputFormat.format((Date) value);
        }
         else if (value instanceof Double) {
            DecimalFormat df = new DecimalFormat("0.############################");
            df.setDecimalFormatSymbols(DecimalFormatSymbols.getInstance(Locale.ENGLISH));
            return df.format(value);
        }
        return value.toString();
    }

    private void addMostRecentResult(String dpHash, String investmentId, String dpId, String value, Map<String, String> primary) {
        CurrentResult currentResult = mostRecentResult.computeIfAbsent(dpHash, cr -> new CurrentResult(investmentId, new HashMap<>(), new HashMap<>()));
        currentResult.getPrimaryKeys().putIfAbsent(dpId, primary);
        currentResult.getValues().putIfAbsent(dpId, value);
    }

    private void addTimeSeriesResult(String dpHash, String investmentId, String dpId, String timeField, String value, Map<String, String> primary) {
        TimeSeriesResult timeSeriesResult1 = timeSeriesResult.computeIfAbsent(dpHash, ts -> new TimeSeriesResult(investmentId, new HashMap<>(), new HashMap<>()));
        timeSeriesResult1.getPrimaryKeys().putIfAbsent(dpId, primary);
        List<V> vs = timeSeriesResult1.getValues().computeIfAbsent(dpId, tr -> new ArrayList<>());
        vs.add(new V(timeField, value));
    }

    private void addGroupResult(String dpHash, String investmentId, DataPoint groupDp, Document doc, String path, Map<String, String> primary) {
        List<DataPoint> subDataPoints = groupDp.getSubDataPoints().stream()
                .filter(subDp -> doc.containsKey(getFieldName(subDp, dbQueryParameters.getType(), path)))
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(subDataPoints)) return;

        CurrentResult currentGroupResult = new CurrentResult(investmentId,
                subDataPoints.stream()
                        .filter(subDp -> doc.containsKey(getFieldName(subDp, dbQueryParameters.getType(), path)))
                        .collect(Collectors.toMap(DataPoint::getId, subDp -> getStringValue(doc.get(getFieldName(subDp, dbQueryParameters.getType(), path)), subDp.getDataType()))));

        GroupResult groupResult = this.groupResult.computeIfAbsent(dpHash + groupDp.getId(), gr -> new GroupResult(investmentId, new HashMap<>(), new HashMap<>()));
        groupResult.getPrimaryKeys().putIfAbsent(groupDp.getId(), primary);
        List<CurrentResult> currentResults = groupResult.getValues().computeIfAbsent(groupDp.getId(), gr -> new ArrayList<>());
        currentResults.add(currentGroupResult);
    }

    public List<Result> getAllResults() {
        return Stream.of(getTimeSeriesResults(), getMostRecentResults(), getGroupResults())
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public List<Result> getTimeSeriesResults() {
        return resultsPerInvestmentId(new ArrayList<>(timeSeriesResult.values()), DataPointType.timeSeries);
    }

    public List<Result> getMostRecentResults() {
        return resultsPerInvestmentId(new ArrayList<>(mostRecentResult.values()), DataPointType.mostRecent);
    }

    public List<Result> getGroupResults() {
        return resultsPerInvestmentId(new ArrayList<>(groupResult.values()), DataPointType.subGroup);
    }

    private List<Result> resultsPerInvestmentId(List<Result> results, DataPointType dataType) {
        List<Result> idResults = new ArrayList<>();
        if (dbQueryParameters.getLevel().getValue().equals(IdLevel.PerformanceId.getValue()))
            return results;
        results.forEach(r -> {
            for (String id : companyIdsToPerIds.getOrDefault(r.getId(), Collections.emptyList())) {
                if (dataType == DataPointType.timeSeries)
                    idResults.add(new TimeSeriesResult(id, r.getValues(), r.getPrimaryKeys()));
                else if (dataType == DataPointType.subGroup) {
                    Map<String, List<CurrentResult>> changedValues = convertGroup(r.getValues(), id);
                    idResults.add(new GroupResult(id, changedValues, r.getPrimaryKeys()));
                }
                else
                    idResults.add(new CurrentResult(id, r.getValues(), r.getPrimaryKeys()));
            }
        });
        return idResults;
    }

    private Map<String, List<CurrentResult>> convertGroup(Map<String, List<CurrentResult>> r, String id) {
        return r.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        e -> e.getValue().stream()
                                .map(cr -> new CurrentResult(id, cr.getValues(), cr.getPrimaryKeys()))
                                .collect(Collectors.toList())));
    }

    private List<DataPoint> getAggregatedDatapointList(List<DataPoint> dataPointsOnLevel){
        return dataPointsOnLevel.stream().filter(dp -> StringUtils.isNotEmpty(dp.getAggregationPipelineId())).toList();
    }

    private void addColumnToDocument(List<DataPoint> dps, Document doc, int level){
        for (DataPoint dp: dps){
            // both collection will contain the same aggregation object
            MongodbCollection collection = dp.getEquityDatapoint().isTimeSeries() ? dp.getEquityDatapoint().getTimeSeriesCollection() : dp.getEquityDatapoint().getMostRecentCollection();
            Optional<Aggregation> aggregation = collection.getAggregations().stream().filter(agg -> dp.getAggregationPipelineId().equals(agg.getId()) && "concatenation".equals(agg.getType())).findFirst();
            if(aggregation.isPresent() && "concatenation".equals(aggregation.get().getType())){
                String key = dp.getEquityDatapoint().isTimeSeries() ? dp.getEquityDatapoint().getTimeSeriesField() : dp.getEquityDatapoint().getMostRecentField();
                StringBuilder builder = new StringBuilder();
                String[] placeholders = aggregation.get().getFormat().split(";");

                for (String placeholder: placeholders){
                    String subDocumentKey = placeholder.split("\\.")[level];
                    if(!subDocumentKey.isEmpty()){
                        String subKey = doc.containsKey(subDocumentKey) ? doc.getString(subDocumentKey) : StringUtils.EMPTY;
                        if(!checkForValue(subKey).isEmpty()){
                            builder.append(subKey);
                            builder.append(aggregation.get().getDelimiter());
                        }

                    }
                }
                doc.append(key.split("\\.")[level],builder.toString());
            }
        }
    }

    private String checkForValue(String content){
        // This check only required if field is present and its value is either null or empty
        return content == null || content.isEmpty() ? StringUtils.EMPTY : content;
    }

    private boolean isFilterCheckRequired(MongodbCollection collection){
        return collection.getAggregations().stream()
                .map(agg -> "concatenation".equals(agg.getType()))
                .reduce(false, (a,b) -> a || b );

    }
}
