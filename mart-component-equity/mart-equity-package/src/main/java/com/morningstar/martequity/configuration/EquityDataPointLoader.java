package com.morningstar.martequity.configuration;

import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointLoaderInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.EquityDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.AbstractXmlDataPointLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderDefinition;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.Aggregation;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.Filter;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.FilterTypes;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.MongodbCollection;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.Parameter;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.equity.LatestDataInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.dom4j.Document;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@DataPointLoaderDefinition(info = DataPointLoaderInfo.EQUITY)
@Slf4j
public class EquityDataPointLoader extends AbstractXmlDataPointLoader {
    private static final String FILTERTYPE = "filterType";
    private static final String DBFIELD = "dbField";
    private static final String FALSE = "false";
    private static final String MSFIELD = "msField";
    private static final String TSFIELD = "tsField";
    private static final String ISMANDATORY = "isMandatory";
    private static final String DATATYPE = "dataType";

    public EquityDataPointLoader(DatapointConfigFileService datapointConfigFileService, DocumentLoader documentLoader) {
        super(datapointConfigFileService, documentLoader);
    }

    @Override
    public void processDocument(DataPointLoaderContext context, Document document) {
        Element root = document.getRootElement();
        Map<String, MongodbCollection> collections = parseCollections(root);
        List<DataPoint> datapoints = parseDataPoints(root, collections);

        datapoints.forEach(dp -> {
            DataPoint dataPoint = context.getDataPointById(dp.getNid());
            if(dataPoint != null && StringUtils.equalsIgnoreCase("EQUITY", dataPoint.getSrc())) {
                addCalculationInformation(dataPoint, dp);
                context.getDataPointMap().put(dp.getNid(), dp);
            } else {
                log.warn("Equity DataPoint {} not found in context.  DataPoint may need to be registered in the datapoints_view.xml file", dp.getNid());
            }
        });
    }

    private Map<String, MongodbCollection> parseCollections(Element root) {
        Map<String, MongodbCollection> collectionList = new HashMap<>();
        Optional.ofNullable(root.element("collections"))
                .map(collectionsElement -> collectionsElement.elements("collection"))
                .ifPresent(collections -> collections.forEach(collection -> {
                    MongodbCollection collectionObject = getCollection(collection);
                    if (collectionList.containsKey(collectionObject.getName())) {
                        log.error("Duplicate collection name found in the configuration file. Collection name: {}", collectionObject.getName());
                    } else {
                        collectionList.put(collectionObject.getName(), collectionObject);
                    }
                }));
        return collectionList;
    }
    private MongodbCollection getCollection(Element collection) {
        Map<Integer, Set<String>> primaryByLevel = new HashMap<>();
        Map<Integer, List<Filter>> filtersByLevel = new HashMap<>();
        List<Parameter> parameters = new ArrayList<>();
        List<String> reqAttributes = new ArrayList<>();
        List<Aggregation> aggregations = new ArrayList<>();
        LatestDataInfo latestDataInfo = new LatestDataInfo();
        parsePrimaries(collection, primaryByLevel);
        parseFilters(collection, filtersByLevel, reqAttributes, latestDataInfo);
        parseParameters(collection, parameters, reqAttributes);
        parseAggregations(collection,aggregations);
        String isAggregateOn = collection.attributeValue("isAggregateOn");
        String aggregatedFrequency = collection.attributeValue("aggregatedFrequency");
        return MongodbCollection.builder()
                .name(collection.attributeValue("name"))
                .idLevel(collection.attributeValue("idLevel"))
                .idIndexField(collection.attributeValue("idIndexField"))
                .frequency(checkStringContent(collection.attributeValue("frequency")))
                .isAggregatedOn(checkStringContent(isAggregateOn))
                .aggregatedFrequency(checkStringContent(aggregatedFrequency))
                .primaryFieldsByLevel(primaryByLevel)
                .filtersByLevel(filtersByLevel)
                .parameters(parameters)
                .reqAttributes(reqAttributes.stream().distinct().toList())
                .aggregations(aggregations)
                .applicableForLatestData(latestDataInfo.isApplicableForLatestData())
                .sortingField(latestDataInfo.getSortingField())
                .build();
    }

    private void parsePrimaries(Element collection, Map<Integer, Set<String>> primaryByLevel) {
        Optional.ofNullable(collection.element("primaries"))
                .map(primariesElement -> primariesElement.elements("primary"))
                .ifPresent(primaryElements -> primaryElements.forEach(primaryElement -> {
                    String dbField = primaryElement.attributeValue(DBFIELD);
                    if (dbField == null) return;
                    int level = getDatapointLevel(dbField);
                    primaryByLevel.computeIfAbsent(level, k -> new HashSet<>()).add(dbField);
                }));
    }
    private void parseFilters(Element collection, Map<Integer, List<Filter>> filtersByLevel, List<String> reqAttributes, LatestDataInfo latestDataInfo) {
        Optional.ofNullable(collection.element("filters"))
                .map(filtersElement -> filtersElement.elements("filter"))
                .ifPresent(filterElements -> filterElements.forEach(filterElement -> {
                    List<String> attributeDbFields = getAttributeDbFields(filterElement);
                    if (!attributeDbFields.isEmpty() && "isLatestData".equals(attributeDbFields.get(0))) {
                        latestDataInfo.setApplicableForLatestData(true);
                        latestDataInfo.setSortingField(filterElement.attributeValue(DBFIELD));
                    } else {
                        processFilterElement(filterElement, filtersByLevel, reqAttributes);
                    }
                }));
    }

    private void processFilterElement(Element filterElement, Map<Integer, List<Filter>> filtersByLevel, List<String> reqAttributes) {
        String dbField = filterElement.attributeValue(DBFIELD);
        String filterType = filterElement.attributeValue(FILTERTYPE);
        String timeField = filterElement.attributeValue("isTimeField");
        if (dbField == null || filterType == null) return;

        List<String> attributeDbFields = getAttributeDbFields(filterElement);
        if (attributeDbFields.isEmpty()) return;

        int level = getDatapointLevel(dbField);
        boolean isMandatory = "True".equals(Optional.ofNullable(filterElement.attributeValue(ISMANDATORY)).orElse("True"));
        if (isMandatory) {
            reqAttributes.addAll(getAttributes(attributeDbFields, FilterTypes.valueOf(filterType)));
        }
        filtersByLevel.computeIfAbsent(level, k -> new ArrayList<>()).add(
                Filter.builder()
                        .dbField(dbField)
                        .isTimeField(Boolean.parseBoolean(timeField != null ? timeField : FALSE))
                        .attributes(new ImmutablePair<>(attributeDbFields.get(0), attributeDbFields.size() == 2 ? attributeDbFields.get(1) : null))
                        .filterType(FilterTypes.valueOf(filterType))
                        .isMandatory(isMandatory)
                        .build()
        );
    }
    private List<String> getAttributeDbFields(Element filterElement) {
        List<String> attributeDbFields = new ArrayList<>();
        Optional.ofNullable(filterElement.element("attributes"))
                .map(attributesElement -> attributesElement.elements("attribute"))
                .ifPresent(attributeElements -> attributeElements.forEach(attributeElement -> {
                    String attributeDbField = attributeElement.getText();
                    if (attributeDbField != null)
                        attributeDbFields.add(attributeDbField);
                }));
        return attributeDbFields;
    }
    private void parseParameters(Element collection, List<Parameter> parameters, List<String> reqAttributes) {
        Optional.ofNullable(collection.element("parameters"))
                .map(parametersElement -> parametersElement.elements("parameter"))
                .ifPresent(parameterElements -> parameterElements.forEach(parameterElement -> processParameterElement(parameterElement, parameters, reqAttributes)));
    }
    private void processParameterElement(Element parameterElement, List<Parameter> parameters, List<String> reqAttributes) {
        String dbField = parameterElement.attributeValue(DBFIELD);
        String timeField = parameterElement.attributeValue("isTimeField");
        boolean isTimeField = "True".equals(Optional.ofNullable(timeField).orElse(StringUtils.EMPTY));
        if (dbField == null && !isTimeField) return;

        List<String> attributeDbFields = getAttributeDbField(parameterElement);
        if (attributeDbFields.isEmpty() && !isTimeField) return;

        boolean isMandatory = "True".equals(Optional.ofNullable(parameterElement.attributeValue(ISMANDATORY)).orElse("True"));
        if (isMandatory && !isTimeField) {
            reqAttributes.addAll(getAttributes(attributeDbFields, FilterTypes.valueOf(parameterElement.attributeValue(FILTERTYPE))));
        }
        parameters.add(
                Parameter.builder()
                        .isTimeField(isTimeField)
                        .dbField(dbField)
                        .attributes(new ImmutablePair<>(isTimeField ? StringUtils.EMPTY : attributeDbFields.get(0),
                                attributeDbFields.size() == 2 ? attributeDbFields.get(1) : StringUtils.EMPTY))
                        .filterType(FilterTypes.valueOf(parameterElement.attributeValue(FILTERTYPE)))
                        .isMandatory(isMandatory)
                        .build()
        );
    }
    private List<String> getAttributeDbField(Element parameterElement) {
        List<String> attributeDbFields = new ArrayList<>();
        Optional.ofNullable(parameterElement.element("attributes"))
                .map(attributesElement -> attributesElement.elements("attribute"))
                .ifPresent(attributeElements -> attributeElements.forEach(attributeElement -> {
                    String attributeDbField = attributeElement.getText();
                    if (attributeDbField != null) attributeDbFields.add(attributeDbField);
                }));
        return attributeDbFields;
    }
    private List<DataPoint> parseDataPoints(Element root, Map<String, MongodbCollection> collections) {
        List<DataPoint> dataPoints = new ArrayList<>();
        Element datapointGroups = root.element("datapoint-groups");
        parseDatapointGroup(datapointGroups, collections, dataPoints);
        return dataPoints;
    }
    private void parseDatapointGroup(Element datapointGroup, Map<String, MongodbCollection> collections, List<DataPoint> dataPoints) {
        for (Element datapointNode : datapointGroup.elements("datapoint")) {
            String parseType = datapointNode.attributeValue("parseType");
            String tsCollection = datapointNode.attributeValue("tsCollection");
            String msCollection = datapointNode.attributeValue("msCollection");

            if (msCollection == null && tsCollection == null && parseType == null) {
                log.error("Time series and most recent collection is not defined for the datapoint. Datapoint: {}", datapointNode.attributeValue("id"));
            } else {
                DataPoint datapoint = getDataPoint(collections, datapointNode, tsCollection, msCollection);
                if ("subgroup".equals(parseType)) {
                    parseSubgroup(datapointNode, collections, datapoint);
                }

                if (StringUtils.isEmpty(parseType) || !datapoint.getSubDataPoints().isEmpty()) {
                    dataPoints.add(datapoint);
                }
            }
        }
    }
    private void parseSubgroup(Element datapointNode, Map<String, MongodbCollection> collections, DataPoint datapoint) {
        List<DataPoint> subDataPoints = new ArrayList<>();
        for (Element subDatapointNode : datapointNode.elements("datapoint")) {
            String subdpTSCollection = subDatapointNode.attributeValue("tsCollection");
            String subdpMSCollection = subDatapointNode.attributeValue("msCollection");

            DataPoint subDatapoint = getDataPoint(collections, subDatapointNode, subdpTSCollection, subdpMSCollection);
            subDataPoints.add(subDatapoint);
        }
        datapoint.setMulti(true);
        datapoint.setGroup(true);
        datapoint.setSubDataPoints(subDataPoints);
        if (!subDataPoints.isEmpty()) {
            DataPoint firstSubDataPoint = subDataPoints.get(0);
            datapoint.setIdLevel(firstSubDataPoint.getIdLevel());
            datapoint.getEquityDatapoint().setMostRecentCollection(firstSubDataPoint.getEquityDatapoint().getMostRecentCollection());
            datapoint.getEquityDatapoint().setTimeSeriesCollection(firstSubDataPoint.getEquityDatapoint().getTimeSeriesCollection());
            datapoint.getEquityDatapoint().setMsLevel(firstSubDataPoint.getEquityDatapoint().getMsLevel());
            datapoint.getEquityDatapoint().setTsLevel(firstSubDataPoint.getEquityDatapoint().getTsLevel());
        }
    }


    private DataPoint getDataPoint(
            Map<String, MongodbCollection> collections,
            Element datapointNode,
            String tsCollection,
            String msCollection
    ) {
        DataPoint datapoint = parseDataPoint(
                datapointNode,
                collections.get(tsCollection != null ? tsCollection : msCollection)
        );
        datapoint.setEquityDatapoint(
                parseEquityDatapoint(datapointNode, collections.get(msCollection),collections.get(tsCollection))
        );
        return datapoint;
    }

    private DataPoint parseDataPoint(Element datapoint, MongodbCollection collection) {
        return DataPoint.builder()
                .name(StringUtils.EMPTY)
                .nid(datapoint.attributeValue("id"))
                .id(datapoint.attributeValue("id"))
                .idLevel(collection != null ? collection.getIdLevel() : null)
                .groupName(StringUtils.EMPTY)
                .src("Equity")
                .dataType(datapoint.attributeValue(DATATYPE) == null ? StringUtils.EMPTY : datapoint.attributeValue(DATATYPE))
                .aggregationPipelineId(datapoint.attributeValue("aggregationPipelineId"))
                .build();
    }

    private EquityDataPoint parseEquityDatapoint(
            Element datapointNode,
            MongodbCollection mostRecentCollection,
            MongodbCollection timeSeriesCollection
    ) {
        return EquityDataPoint.builder()
                .mostRecentCollection(mostRecentCollection)
                .timeSeriesCollection(timeSeriesCollection)
                .mostRecentField(datapointNode.attributeValue(MSFIELD))
                .timeSeriesField(datapointNode.attributeValue(TSFIELD))
                .tsLevel(datapointNode.attributeValue(TSFIELD) != null ?
                        getDatapointLevel(datapointNode.attributeValue(TSFIELD)): 0)
                .msLevel(datapointNode.attributeValue(MSFIELD) != null ?
                        getDatapointLevel(datapointNode.attributeValue(MSFIELD)) : 0)
                .build();
    }
    private int getDatapointLevel(String fieldPath) {
        return (int) fieldPath.chars()
                .filter(ch -> ch == '.')
                .count();
    }

    private List<String> getAttributes(List<String> attributeDbFields,FilterTypes filterType){
        return switch (filterType){
            case rangeFilter -> List.of(attributeDbFields.get(0),attributeDbFields.get(1));
            case containsFilter -> List.of(attributeDbFields.get(0));
            default -> List.of();
        };
    }

    private void parseAggregations(Element collection, List<Aggregation> aggregations) {
        Optional.ofNullable(collection.element("aggregations"))
                .map(aggregationElements -> aggregationElements.elements("aggregation"))
                .ifPresent(aggregationElement -> aggregationElement.forEach(aggregationElements -> processAggregation(aggregationElements, aggregations)));
    }
    private void processAggregation(Element aggregationElement, List<Aggregation> aggregations) {
        String id = aggregationElement.attributeValue("id");
        String type = aggregationElement.attributeValue("type");
        Element attribute = aggregationElement.element("attribute");
        String dbField = attribute.attributeValue(DBFIELD);
        String format = attribute.attributeValue("format");
        String delimiter = attribute.attributeValue("delimiter");

        Aggregation aggregation = Aggregation.builder()
                .id(id)
                .type(type)
                .dbField(dbField)
                .format(format)
                .delimiter(delimiter)
                .build();
        aggregations.add(aggregation);
    }

    private String checkStringContent(String content){
        return Optional.ofNullable(content).isPresent() ? content : StringUtils.EMPTY;
    }

    private void addCalculationInformation(DataPoint contextDp, DataPoint equityDp) {
        if (contextDp.getCalculation() != null)
            equityDp.setCalculation(contextDp.getCalculation());
    }

}
