package com.morningstar.martequity;

import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.EquityMetaData;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.MDCInheritedThreadPoolExecutor;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.martequity.core.service.EquityService;
import com.morningstar.martequity.dp.DataPointFilters;
import org.apache.commons.collections4.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class EquityGateway implements Gateway<Result, MartRequest> {

    private final ExecutorService executorService;
    private final Scheduler equityScheduler;
    private final EquityCalculationLibService equityCalculationLibService;
    private final EquityService equityService;

    private static final String UNKNOWN_SECURITY_TYPE = "UNKNOWN";

    public EquityGateway(EquityService equityService, EquityCalculationLibService calculationLibService) {
        this.equityCalculationLibService = calculationLibService;
        int cores = Runtime.getRuntime().availableProcessors();
        executorService = new MDCInheritedThreadPoolExecutor(cores, cores * 25, "Equity-Application-");
        equityScheduler = Schedulers.fromExecutor(executorService);
        this.equityService = equityService;
    }

    @Override
    public Flux<Result> retrieve(MartRequest martRequest) {
        List<String> dps = martRequest.getDps();
        if (isEmptyOrNoEquityDataPoint(dps)) {
            return Flux.empty();
        }

        boolean isCurrency = containsCurrencyDataPoint(dps, martRequest.getCurrency());
        Set<String> nonCalcIds = new HashSet<>();

        if (isCurrency) {
            prepareMartRequestForCurrency(martRequest, dps);
            nonCalcIds = getNonCalcIds(dps);
            addRequiredIds(dps);
        }

        Flux<Result> result = fetchResults(martRequest);
        Flux<Result> nonCalcResult = fetchNonCalcResults(nonCalcIds, martRequest);

        if (isCurrency) {
            Flux<Result> calculationLibResult = fetchCalculationLibResults(martRequest, result);
            return Flux.merge(calculationLibResult, nonCalcResult)
                    .subscribeOn(equityScheduler);
        }
        return result;
    }

    private Integer calculateYear(String dateValue) {
        if (dateValue == null || dateValue.length() < 4) {
            return null;
        }
        try {
            return Integer.parseInt(dateValue.substring(0, 4));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private boolean isEmptyOrNoEquityDataPoint(List<String> dps) {
        return CollectionUtils.isEmpty(dps) || dps.stream()
                .map(DataPointRepository::getByNid)
                .noneMatch(DataPointFilters.isEquityDataPoint());
    }

    private boolean containsCurrencyDataPoint(List<String> dps, String currency) {
        return dps.stream()
                .map(DataPointRepository::getByNid)
                .anyMatch(dp -> dp.isCalcLib(currency));
    }

    private void prepareMartRequestForCurrency(MartRequest martRequest, List<String> dps) {
        if (martRequest.getDatapointIds() == null) {
            martRequest.setDatapointIds(new ArrayList<>(dps));
        }
        if (martRequest.getEquityMetaData() == null && martRequest.getStartDate() != null) {
            EquityMetaData equityMetaData = new EquityMetaData();
            equityMetaData.setStartDate(martRequest.getStartDate());
            equityMetaData.setEndDate(martRequest.getEndDate());
            equityMetaData.setStartYear(calculateYear(martRequest.getStartDate()));
            equityMetaData.setEndYear(calculateYear(martRequest.getEndDate()));
            martRequest.setEquityMetaData(equityMetaData);
        }
    }

    private Set<String> getNonCalcIds(List<String> dps) {
        return dps.stream()
                .filter(id -> DataPointRepository.getByNid(id).getCalculation() == null)
                .collect(Collectors.toSet());
    }

    private void addRequiredIds(List<String> dps) {
        Set<String> requiredIds = dps.stream()
                .map(DataPointRepository::getByNid)
                .filter(dp -> dp.getCalculation() != null)
                .flatMap(dp -> Stream.of(dp.getCalculation().getEndDate().getId(), dp.getCalculation().getCur().getId()))
                .collect(Collectors.toSet());
        dps.forEach(requiredIds::remove);
        dps.addAll(requiredIds);
    }

    private Flux<Result> fetchResults(MartRequest martRequest) {
        return Mono.fromCallable(() -> equityService.getData(martRequest))
                .flatMapMany(Flux::fromIterable)
                .subscribeOn(equityScheduler);
    }

    private Flux<Result> fetchNonCalcResults(Set<String> nonCalcIds, MartRequest martRequest) {
        if (nonCalcIds.isEmpty()) {
            return Flux.empty();
        }
        MartRequest martRequestNonCalc = martRequest.shallowCopy();
        martRequestNonCalc.setDps(new ArrayList<>(nonCalcIds));
        return fetchResults(martRequestNonCalc);
    }

    private Flux<Result> fetchCalculationLibResults(MartRequest martRequest, Flux<Result> result) {
        CalcRequest calcLibRequest = CalcRequest.buildLibRequest(martRequest);
        return splitBySecurityType(calcLibRequest).stream()
                .map(req -> equityCalculationLibService.retrieveCalcLib(req, result))
                .reduce(Flux.empty(), Flux::merge);
    }

    private List<CalcRequest> splitBySecurityType(CalcRequest calcRequest) {
        if (CollectionUtils.isEmpty(calcRequest.getIdMappers())) {
            return Collections.singletonList(calcRequest);
        }
        Map<String, CalcRequest> idsBySecurityType = new HashMap<>();
        Map<String, Optional<IdMapper>> idMap = buildIdMap(calcRequest);
        for (Map.Entry<String, Optional<IdMapper>> idEntry : idMap.entrySet()) {
            Optional<IdMapper> idMapperOpt = idEntry.getValue();
            if (idMapperOpt.isPresent()) {
                IdMapper idMapper = idMapperOpt.get();
                String securityType = extractSecurityType(idMapper);
                CalcRequest calcRequestForSecurityType = idsBySecurityType.computeIfAbsent(securityType, k -> buildRequestWithoutIds(calcRequest));
                calcRequestForSecurityType.getIdList().add(idEntry.getKey());
                calcRequestForSecurityType.getIdMappers().add(idMapper);
                continue;
            }
            CalcRequest calcRequestForSecurityType = idsBySecurityType.computeIfAbsent(UNKNOWN_SECURITY_TYPE, k -> buildRequestWithoutIds(calcRequest));
            calcRequestForSecurityType.getIdList().add(idEntry.getKey());
        }
        return new ArrayList<>(idsBySecurityType.values());
    }

    private String extractSecurityType(IdMapper idMapper) {
        String securityType;
        if (idMapper.isPrivateModel()) {
            securityType = "PrivateModel";
        } else {
            securityType = idMapper.getSecurityType();
        }
        return securityType;
    }

    private Map<String, Optional<IdMapper>> buildIdMap(CalcRequest calcRequest) {
        Map<String, Optional<IdMapper>> idMap = calcRequest.getIdMappers().stream().collect(Collectors.toMap(
                IdMapper::getInvestmentId, Optional::of, (oldMapper, newMapper) -> oldMapper, HashMap::new)
        );
        calcRequest.getIdList().stream().filter(id -> !idMap.containsKey(id))
                .forEach(id -> idMap.put(id, Optional.empty()));
        return idMap;
    }

    private CalcRequest buildRequestWithoutIds(CalcRequest calcRequest) {
        CalcRequest request = calcRequest.shallowCopy();
        request.setIdList(new ArrayList<>());
        request.setIdMappers(new ArrayList<>());
        return request;
    }

}
