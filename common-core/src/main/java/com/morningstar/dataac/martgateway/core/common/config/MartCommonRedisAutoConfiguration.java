package com.morningstar.dataac.martgateway.core.common.config;

import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import com.morningstar.dataac.martgateway.core.common.repository.RedisRepo;
import com.morningstar.dataac.martgateway.core.common.repository.RedisTsRepo;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.RedisClient;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import java.util.concurrent.Semaphore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Lazy
@Slf4j
@ComponentScan(excludeFilters = @ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        value = {
                RedisAutoConfiguration.class,
                RedisReactiveAutoConfiguration.class
        }
))
public class MartCommonRedisAutoConfiguration {
    private final MartCommonProperties martCommonProperties;

    @Autowired
    public MartCommonRedisAutoConfiguration(MartCommonProperties martCommonProperties) {
        this.martCommonProperties = martCommonProperties;
    }

    @Bean(name = "redisWriteSemaphore")
    public Semaphore redisWriteSemaphore(@Value("${martcommon.redis.write.semaphore:2500}")int redisWriteSemaphore) {
        return new Semaphore(redisWriteSemaphore);
    }

    @Bean(name = "storageRedisConnectionFactory")
    public LettuceConnectionFactory redisConnectionFactory(ClusterClientOptions clientOptions) {
        MartCommonProperties.Redis redis = martCommonProperties.getRedis();
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.clusterNode(redis.getStorage().getHost(), redis.getStorage().getPort());
        clusterConfig.setMaxRedirects(3);
        return new LettuceConnectionFactory(clusterConfig, getPoolConfig(3, 100, 15, clientOptions, true));
    }

    @Bean(name = "syncStorageDataTemplate")
    @ConditionalOnMissingBean(name = "syncStorageDataTemplate")
    public RedisTemplate<String, String> syncDataTemplate(@Qualifier("storageRedisConnectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
        return createRedisTemplateWithRetry(lettuceConnectionFactory);
    }

    @Bean(name = "syncRedisStorageClient")
    @ConditionalOnMissingBean(name = "syncRedisStorageClient")
    public RedisRepo redisStorageRepo(@Qualifier("syncStorageDataTemplate") RedisTemplate<String, String> syncStorageDataTemplate) {
        return new RedisRepo(syncStorageDataTemplate);
    }

    @Primary
    @Bean(name = "cacheRedisConnectionFactory", destroyMethod = "destroy")
    public LettuceConnectionFactory syncCacheRedisConnectionFactory(ClusterClientOptions clientOptions) {
        MartCommonProperties.Redis redis = martCommonProperties.getRedis();
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.clusterNode(redis.getCache().getHost(), redis.getCache().getPort());
        clusterConfig.setMaxRedirects(3);
        return new LettuceConnectionFactory(clusterConfig, getPoolConfig(3, 100, 15, clientOptions, false));
    }

    @Primary
    @Bean(name = {"syncCacheDataTemplate"})
    @ConditionalOnMissingBean(name = "syncCacheDataTemplate")
    public RedisTemplate<String, String> syncCacheDataTemplate(@Qualifier("cacheRedisConnectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
        return createRedisTemplateWithRetry(lettuceConnectionFactory);
    }

    @Primary
    @Bean(name = "appCacheRedisTemplate")
    @ConditionalOnMissingBean(name = "appCacheRedisTemplate")
    public ReactiveRedisTemplate<String, String> appCacheRedisTemplate(@Qualifier("cacheRedisConnectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
        return new ReactiveRedisTemplate<>(lettuceConnectionFactory, RedisSerializationContext.string());
    }

    /*
     * Redis Cache Cluster
     * Do not directly use RedisTemplate and RedisAdvancedClusterCommands. Instead, we should always use appCacheRedisClient/syncRedisCacheClient bean.
     * If appCacheRedisClient/syncRedisCacheClient doesn't have all needed methods, please update that RedisReactiveRepo/RedisRepo class.
     */
    @Bean(name = "syncRedisCacheClient")
    @ConditionalOnMissingBean(name = "syncRedisCacheClient")
    public RedisRepo syncRedisCacheClient(@Qualifier("syncCacheDataTemplate") RedisTemplate<String, String> syncCacheDataTemplate) {
        return new RedisRepo(syncCacheDataTemplate);
    }

    @Bean(name = "appCacheRedisClient")
    @ConditionalOnMissingBean(name = "appCacheRedisClient")
    public RedisReactiveRepo redisCacheRepo(@Qualifier("appCacheRedisTemplate") ReactiveRedisTemplate<String, String> appCacheRedisTemplate) {
        return new RedisReactiveRepo(appCacheRedisTemplate);
    }

    @Bean(name = "metaDataRedisConnectionFactory", destroyMethod = "destroy")
    public LettuceConnectionFactory metaDataRedisConnectionFactory(ClusterClientOptions clientOptions) {
        MartCommonProperties.MetaDataCache metaDataCache = martCommonProperties.getRedis().getMetaDataCache();
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.clusterNode(metaDataCache.getHost(), metaDataCache.getPort());
        clusterConfig.setMaxRedirects(3);
        return new LettuceConnectionFactory(clusterConfig, getPoolConfig(3, 100, 15, clientOptions, false));
    }

    @Bean(name = "metaDataRedisTemplate")
    public RedisTemplate<String, String> metaDataRedisTemplate(@Qualifier("metaDataRedisConnectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
        return createRedisTemplateWithRetry(lettuceConnectionFactory);
    }

    @Bean(name = "metaDataRedisClient")
    public RedisRepo metaDataRedisClient(
            @Qualifier("metaDataRedisTemplate") RedisTemplate<String, String> metaDataRedisTemplate) {
        return new RedisRepo(metaDataRedisTemplate);
    }

    @Bean(name = "metaDataReactiveRedisTemplate")
    @ConditionalOnMissingBean(name = "metaDataReactiveRedisTemplate")
    public ReactiveRedisTemplate<String, String> metaDataReactiveRedisTemplate(@Qualifier("metaDataRedisConnectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
        return new ReactiveRedisTemplate<>(lettuceConnectionFactory, RedisSerializationContext.string());
    }

    @Bean(name = "metaDataReactiveRedisClient")
    @ConditionalOnMissingBean(name = "metaDataReactiveRedisClient")
    public RedisReactiveRepo metaDataReactiveRedisClient(@Qualifier("metaDataReactiveRedisTemplate") ReactiveRedisTemplate<String, String> appCacheRedisTemplate) {
        return new RedisReactiveRepo(appCacheRedisTemplate);
    }

    @Bean(name = "timeSeriesRedisConnectionFactory", destroyMethod = "destroy")
    public LettuceConnectionFactory timeSeriesRedisConnectionFactory(ClusterClientOptions clientOptions) {
        MartCommonProperties.TimeSeriesCache timeSeriesCache = martCommonProperties.getRedis().getTimeSeriesCache();
        RedisConfiguration config;

        if(timeSeriesCache.getServerType() == MartCommonProperties.ServerType.Standalone) {
            config = new RedisStandaloneConfiguration(timeSeriesCache.getHost(), timeSeriesCache.getPort());
        } else {
            RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
            clusterConfig.clusterNode(timeSeriesCache.getHost(), timeSeriesCache.getPort());
            clusterConfig.setMaxRedirects(3);
            config = clusterConfig;
        }

        return new LettuceConnectionFactory(config, getPoolConfig(10, 60, 15, clientOptions, false));
    }

    @Bean(name = "timeSeriesRedisTemplate")
    @ConditionalOnMissingBean(name = "timeSeriesRedisTemplate")
    public ReactiveRedisTemplate<byte[], byte[]> timeSeriesRedisTemplate(@Qualifier("timeSeriesRedisConnectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
        return new ReactiveRedisTemplate<>(lettuceConnectionFactory, RedisSerializationContext.byteArray());
    }

    @Bean(name = "redisTsRepo")
    @ConditionalOnMissingBean(name = "redisTsRepo")
    public RedisTsRepo redisTsRepo(@Qualifier("timeSeriesRedisTemplate") ReactiveRedisTemplate<byte[], byte[]> timeSeriesRedisTemplate) {
        return new RedisTsRepo(timeSeriesRedisTemplate);
    }

    @Bean(name = "clientOptions")
    public ClusterClientOptions clientOptions() {
        return ClusterClientOptions.builder()
                .suspendReconnectOnProtocolFailure(true)
                .autoReconnect(true)
                .build();
    }

    @Bean(name = "redisClient")
    @ConditionalOnMissingBean(name = "redisClient")
    public RedisClient redisClient() {
        return RedisClient.create("redis://" + martCommonProperties.getRedis().getCache().getHost() + ":" + martCommonProperties.getRedis().getCache().getPort());
    }

    private RedisTemplate<String, String> createRedisTemplate(LettuceConnectionFactory factory) {
        factory.afterPropertiesSet();
        RedisTemplate<String, String> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setDefaultSerializer(new StringRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    private RedisTemplate<String, String> createRedisTemplateWithRetry(LettuceConnectionFactory lettuceConnectionFactory) {
        int attempts = 0;
        while (attempts++ < 3) {
            try {
                return createRedisTemplate(lettuceConnectionFactory);
            } catch (Exception e) {
                if (attempts == 2) {
                    log.error("error while getting Redis template", e);
                }
            }
        }
        return null;
    }

    private LettucePoolingClientConfiguration getPoolConfig(
            int minPoolSize,
            int maxPoolSize,
            int idlePoolSize,
            ClusterClientOptions clusterClientOptions,
            boolean isReadFromReplica) {

        ClientResources res = DefaultClientResources.builder().ioThreadPoolSize(8).build();

        GenericObjectPoolConfig<?> pool = new GenericObjectPoolConfig<>();
        pool.setMaxTotal(maxPoolSize);
        pool.setMinIdle(minPoolSize);
        pool.setMaxIdle(idlePoolSize);
        pool.setMaxWaitMillis(30000);

        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder =
                LettucePoolingClientConfiguration.builder()
                        .clientOptions(clusterClientOptions)
                        .clientResources(res)
                        .poolConfig(pool);

        if (isReadFromReplica) {
            builder.readFrom(ReadFrom.REPLICA_PREFERRED);
        }

        return builder.build();
    }

    @Bean(name = "tsConnectionFactory", destroyMethod = "destroy")
    public LettuceConnectionFactory tsRedisConnectionFactory(ClusterClientOptions clientOptions) {
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.clusterNode(martCommonProperties.getRedis().getTsData().getHost(), martCommonProperties.getRedis().getTsData().getPort());
        clusterConfig.setMaxRedirects(3);

        return new LettuceConnectionFactory(clusterConfig, getPoolConfig(10, 10, 10, clientOptions, true));
    }

    @Bean(name = "tsTemplate")
    @ConditionalOnMissingBean(name = "tsTemplate")
    public ReactiveRedisTemplate<byte[], byte[]> tsTemplate(@Qualifier("tsConnectionFactory") LettuceConnectionFactory lettuceConnectionFactory) {
        return new ReactiveRedisTemplate<>(lettuceConnectionFactory, RedisSerializationContext.byteArray());
    }

    @Bean(name = "tsRepo")
    @ConditionalOnMissingBean(name = "tsRepo")
    public RedisTsRepo tsRepo(@Qualifier("tsTemplate") ReactiveRedisTemplate<byte[], byte[]> tsTemplate) {
        return new RedisTsRepo(tsTemplate);
    }
}
