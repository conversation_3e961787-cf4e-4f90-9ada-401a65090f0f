package com.morningstar.dataac.martgateway.core.common.util;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.repository.LocalIdMapperCache;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;

public class IdMapUtil {

    private final LocalIdMapperCache localIdMapperCache;

    public IdMapUtil(LocalIdMapperCache localIdMapperCache) {
        this.localIdMapperCache = localIdMapperCache;
    }

    public List<IdMapper> getIdMappers(List<String> idList) {
        return localIdMapperCache.getIdMappersBySecIds(idList);
    }

    public List<IdMapper> buildIdMappers(String idType, List<String> idList) {
        return idList.stream().map(id -> {
            JSONObject jsonObject = new JSONObject()
                    .put("SecId", id)
                    .put("SecurityType", idType)
                    .put(idType, id);
            return new NonEmptyIdMapper(id, jsonObject);
        }).collect(Collectors.toList());
    }

    public static List<String> getByIdLevel(List<IdMapper> idMappers, String idLevel) {
        return idMappers.stream().map(idMapper -> idMapper.getId(idLevel)).filter(StringUtils::isNotBlank).toList();
    }
}
