package com.morningstar.dataac.martgateway.core.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "martcommon")
public class MartCommonProperties {
    private AWS aws;
    private Redis redis;
    private LocalCache localCache;

    @Data
    public static class AWS {
        private String region;
        private S3 s3;
        private Ecs ecs;
    }

    @Data
    public static class S3 {
        private String region;
        private String mode;
        private String defaultEnv;
        private String productIdsRegistration;
        private String serviceNameRegistration;
        private String bucket;
    }

    @Data
    public static class Ecs {
        private Metadata metadata;
    }

    @Data
    public static class Metadata {
        private String baseUrl;
    }

    @Data
    public static class Redis {
        private Storage storage;
        private Cache cache;
        private TimeSeriesCache timeSeriesCache;
        private MetaDataCache metaDataCache;
        private TsData tsData;
    }

    @Data
    public static class Storage {
        private String host;
        private int port;
    }

    @Data
    public static class Cache {
        private String host;
        private int port;
    }

    @Data
    public static class TimeSeriesCache {
        private String host;
        private int port;
        private ServerType serverType = ServerType.Cluster;
    }

    @Data
    public static class MetaDataCache {
        private String host;
        private int port;
    }

    @Data
    public static class TsData {
        private String host;
        private int port;
    }

    @Data
    public static class LocalCache {
        private long count;
        private boolean enableCache;
        private int queueSize;
        private S3File s3File;
    }

    @Data
    public static class S3File {
        private String idMapper;
        private String deltaId;
    }

    public static enum ServerType {
        Standalone,
        Cluster
    }
}