package com.morningstar.dataac.martgateway.core.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import io.vavr.control.Try;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class JsonUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String UNABLE_PARSE_JSON = "Unable to parse Json String";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final Logger LOGGER = LoggerFactory.getLogger(JsonUtils.class);


    static {
        objectMapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule().addDeserializer(LocalDateTime .class, new LocalDateTimeDeserializer(formatter)));
    }

    private static final ObjectWriter writer = objectMapper.writer();

    private JsonUtils() {
    }

    public static ObjectMapper getDefaultObjectMapper() {
        return objectMapper;
    }

    public static String toJsonString(Object value) {
        try {
            return writer.writeValueAsString(value);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }

    public static <T> T fromJsonString(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new IllegalStateException(UNABLE_PARSE_JSON, e);
        }
    }

    public static List<String> parseJsonArray(JSONArray jsonArray) {
        try {
            return objectMapper.readValue(jsonArray.toString(), new TypeReference<List<String>>() {});
        } catch (Exception e) {
           LOGGER.error(UNABLE_PARSE_JSON, e);
           return Collections.emptyList();
        }
    }

    public static <T> T fromJsonString(JsonNode jsonNode, TypeReference<T> valueTypeRef) {
        if (jsonNode == null) {
            return null;
        }
        try {
            return objectMapper.readValue(objectMapper.treeAsTokens(jsonNode), valueTypeRef);
        } catch (Exception e) {
            throw new IllegalStateException(UNABLE_PARSE_JSON, e);
        }
    }
    public static <T> T fromJsonString(String json, TypeReference<T> typeReference) {
        T result = null;

        try {
            if (StringUtils.isNotEmpty(json)) {
                result = objectMapper.readValue(json, typeReference);
            }
        } catch (Exception e) {
            throw new IllegalStateException(UNABLE_PARSE_JSON, e);
        }

        return result;
    }

    public static JsonNode jsonNodeOf(String json) {
        return fromJsonString(json, JsonNode.class);
    }

    public static boolean validateJson(String json) {
        if (StringUtils.isEmpty(json)) {
            return false;
        }
        try {
            objectMapper.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static <T> T fromJsonStream(InputStream jsonStream, Class<T> clazz) {
        return Try.of(() -> objectMapper.readValue(jsonStream, clazz)).get();
    }

    public static JSONObject retrieveStoredContent(String content) {
        return  new JSONObject(retrieveContent(content));
    }

    public static String retrieveContent(String content) {
        try {
            JSONObject storedObject = new JSONObject(content);
            if (storedObject.has("gz") && StringUtils.isNotEmpty(storedObject.getString("gz"))) {
                String decodedObject = GZipUtils
                        .unzipBody(storedObject.getString("gz"));
                return decodedObject;
            } else {
                return content;
            }
        } catch (Exception e) {
            LogEntry.error(e, new LogEntity(
                    LogAttribute.EVENT_DESCRIPTION, "Current data object deserialization failure"));
            return "{}";
        }
    }
}
