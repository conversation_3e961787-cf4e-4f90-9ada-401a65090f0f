package com.morningstar.dataac.martgateway.core.common.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Calendar;
import java.util.Date;

import static java.time.format.DateTimeFormatter.BASIC_ISO_DATE;
import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;
import static java.time.temporal.IsoFields.QUARTER_OF_YEAR;

@Slf4j
public class DateFormatUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateFormatUtil.class);

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    private DateFormatUtil() {

    }

    @Deprecated
    // Use LocalDate instead
    public static String format(Date date) {
        return new SimpleDateFormat(YYYY_MM_DD).format(date);
    }

    public static String format(LocalDate date) {
        return ISO_LOCAL_DATE.format(date);
    }

    public static int daysBetween(Date d1, Date d2) {
        Calendar cd1 = Calendar.getInstance();
        cd1.setTime(d1);
        Calendar cd2 = Calendar.getInstance();
        cd2.setTime(d2);
        return getElapsedDays(cd1, cd2);
    }

    public static Date addDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        return calendar.getTime();
    }

    private static int getElapsedDays(Calendar cd1, Calendar cd2) {
        int elapsed = 0;
        Calendar c1;
        Calendar c2;
        if (cd2.after(cd1)) {
            c2 = (Calendar) cd2.clone();
            c1 = (Calendar) cd1.clone();
        } else {
            c2 = (Calendar) cd1.clone();
            c1 = (Calendar) cd2.clone();
        }

        c1.clear(Calendar.MILLISECOND);
        c1.clear(Calendar.SECOND);
        c1.clear(Calendar.MINUTE);
        c1.clear(Calendar.HOUR_OF_DAY);

        c2.clear(Calendar.MILLISECOND);
        c2.clear(Calendar.SECOND);
        c2.clear(Calendar.MINUTE);
        c2.clear(Calendar.HOUR_OF_DAY);

        while (c1.before(c2)) {
            c1.add(Calendar.DATE, 1);
            elapsed++;
        }

        return elapsed;
    }

    public static String formatTime(int time) {
        Calendar cal = Calendar.getInstance();
        cal.set(1900, 0, 1);
        cal.add(Calendar.DAY_OF_MONTH, time);
        int imonth = cal.get(Calendar.MONTH) + 1;
        int iday = cal.get(Calendar.DAY_OF_MONTH);
        String month = imonth < 10 ? "0" + imonth : Integer.toString(imonth);
        String day = iday < 10 ? "0" + iday : Integer.toString(iday);
        return cal.get(Calendar.YEAR) + "-" + month + "-" + day;
    }

    public static Date parseDate(String date) {
        Date result = null;
        try {
            result = new SimpleDateFormat(YYYY_MM_DD).parse(date);
        } catch (ParseException e) {
            LOGGER.error("date parse error");
        }

        return result;
    }

    public static LocalDate parseLocalDate(String value) {
        if(value == null) return null;
        try {
            return LocalDate.parse(value, ISO_LOCAL_DATE);
        } catch (DateTimeParseException e) {
            log.error("Can't parse date {}", value, e);
            return null;
        }
    }

    public static LocalDate toLocalDateFromInteger(Integer yyyymmdd) {
        return yyyymmdd == null ? null : toLocalDateFromStringYYYYMMDD(String.valueOf(yyyymmdd));
    }

    public static LocalDate toLocalDateFromStringYYYYMMDD(String yyyymmdd) {
        return toLocalDateFromStringYYYYMMDD(yyyymmdd, BASIC_ISO_DATE);
    }

    public static LocalDate toLocalDateFromStringYYYYMMDD(String yyyymmdd, DateTimeFormatter format) {
        LocalDate result = null;
        try {
            result = LocalDate.parse(yyyymmdd, format);
        } catch (DateTimeParseException e) {
            log.error("Date:{} parsed occur error",e.getParsedString(), e);
        }
        return result;
    }

    public static LocalDate getQuarterStartDate(LocalDate date) {
        return YearMonth.of(date.getYear(), 1).with(QUARTER_OF_YEAR, date.get(QUARTER_OF_YEAR)).atDay(1);
    }

    public static LocalDate getQuarterEndDate(LocalDate date) {
        return YearMonth.of(date.getYear(), 3).with(QUARTER_OF_YEAR, date.get(QUARTER_OF_YEAR)).atEndOfMonth();
    }
}
