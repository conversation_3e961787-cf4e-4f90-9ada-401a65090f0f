martcommon:
  aws:
    region: eu-west-1
    s3:
      region: eu-west-1
      bucket: mart-data-prod-eu
      mode: fixed
      default-env: blue
      productIdsRegistration: product_ids_registration.txt
      serviceNameRegistration: service_name_registration.txt
    ecs:
      metadata:
        baseUrl: ${ECS_CONTAINER_METADATA_URI_V4:}
  redis:
    storage:
      host: mart-data-v7-cluster-prod-eu-west-1.dat688b3.eas.morningstar.com
      port: 6379
    cache:
      host: application-cache-v7-cluster-prod-eu-west-1.dpd6b927.eas.morningstar.com
      port: 6379
    time-series-cache:
      host: application-cache-v7-cluster-prod-eu-west-1.dpd6b927.eas.morningstar.com
      port: 6379
      server-type: Cluster
    meta-data-cache:
      host: mart-meta-data-v8-cluster-prod-eu-west-1.dpd6b927.eas.morningstar.com
      port: 6379
    ts-data:
      host: application-cache-v7-cluster-prod-eu-west-1.dpd6b927.eas.morningstar.com
      port: 6379
  localCache:
    count: 2000000
    enableCache: false
    queueSize: 10
    s3File:
      idMapper: activeSecIds
      deltaId: deltaSecIds