package com.morningstar.dataac.martgateway.core.common.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;

import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;

public class JsonUtilsTest {
    private String testString = "{\"id\":\"test\"}";


    @Test
    public void getDefaultObjectMapper() {
        ObjectMapper mapper  = JsonUtils.getDefaultObjectMapper();
        assertEquals(JsonUtils.getDefaultObjectMapper(),mapper);
    }

    @Test
    public void toJsonString() {
        HashMap<String,String> map= new HashMap<>();
        map.put("id","test");
        String result = JsonUtils.toJsonString(map);
        assertEquals(testString,result);
    }

    @Test
    public void fromJsonString() {
        Map map = JsonUtils.fromJsonString(testString, Map.class);
        assertEquals("test",map.get("id"));
        map = JsonUtils.fromJsonString(testString, Map.class);
        assertEquals("test",map.get("id"));
        try {
            map = JsonUtils.fromJsonString("1"+testString+"1", Map.class);
        } catch (IllegalStateException e) {
            assertEquals("Unable to parse Json String",e.getMessage());
        }
    }

    @Test
    public void testFromJsonString() {
        JsonNode jsonNode = JsonUtils.jsonNodeOf(testString);
        Map map =JsonUtils.fromJsonString(jsonNode,new TypeReference<Map<String,String>>(){});
        assertEquals("test",map.get("id"));
        map =JsonUtils.fromJsonString(testString ,new TypeReference<Map<String,String>>(){});
        assertEquals("test",map.get("id"));
    }

    @Test
    public void jsonNodeOf() {
        JsonNode jsonNode = JsonUtils.jsonNodeOf(testString);
        assertEquals("test",jsonNode.get("id").asText());
    }

    @Test
    public void validateJson() {
        assertTrue(JsonUtils.validateJson(testString));
        assertFalse(JsonUtils.validateJson(""));
        assertFalse(JsonUtils.validateJson("{{\"id\":\"test\"}"));
    }

    @Test
    public void emptyJson() {
        String s = JsonUtils.fromJsonString("", String.class);
        assertNull(s);
    }

    @Test
    public void testGetJsonArray() {
        String s = "{\"data\":[\"123\",\"456\"]}";
        JSONObject obj = new JSONObject(s);
        Assert.assertEquals(2, JsonUtils.parseJsonArray(obj.optJSONArray("data")).size());
    }

    @Test
    public void retrieveStoredContentTest(){
        String content = "{\"48041\":\"0C00001YRR\",\"48042\":\"New York\"}";
        JSONObject jsonObject = JsonUtils.retrieveStoredContent(content);
    }

    @Test
    public void retrieveStoredContentExceptionTest(){
        String content = "{\"gz\":\"H4sIAAAAAAAAAKtWSkksSVSyiq5WSlGyMjExMDbQUSpTslIy0DMxVKrVgQsbwoWNlGpjdcDaPIFySpYGBpZAIQAvNfu4SQAAAA==\"}";
        JSONObject result = JsonUtils.retrieveStoredContent(content);
        Assert.assertTrue(result.has("data"));
        content = "{\"gz\":\"\"}";
        result = JsonUtils.retrieveStoredContent(content);
        assertFalse(result.has("data"));
    }
}