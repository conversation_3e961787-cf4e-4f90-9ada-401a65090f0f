package com.morningstar.dataac.martgateway.core.datapointloader.entity.delta;

import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

@Getter
@Builder
public class SearchMapping {

    private String tsType;
    private String dataGroup;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SearchMapping that)) return false;
        return Objects.equals(tsType, that.tsType) && Objects.equals(dataGroup, that.dataGroup);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tsType, dataGroup);
    }
}
