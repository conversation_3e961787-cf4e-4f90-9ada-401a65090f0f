package com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@Builder
public class DataPointAggregate {
    private String id;
    private Map<SrcType, IDataPoint> srcMap;

    public enum SrcType {
        EOD, TS, RDB_CURRENT, RDB_TS, RDB_CURRENT_POST_TAX, RDB_TS_POST_TAX, RDB_CURRENT_LANGUAGE, RDB_EXTENDED_PERFORMANCE,
        FIXED_INCOME_CURRENT, FIXED_INCOME_TS
    }
}
