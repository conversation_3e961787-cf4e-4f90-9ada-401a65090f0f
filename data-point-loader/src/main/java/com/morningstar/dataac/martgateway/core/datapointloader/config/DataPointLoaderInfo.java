package com.morningstar.dataac.martgateway.core.datapointloader.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
/**
 * Central location to define data-point loader properties
 * <p>
 * Please note: order numbers should be unique.
 * For readability, please keep enums in the order defined for the enum
 * </p>
 **/
@AllArgsConstructor
@Getter
public enum DataPointLoaderInfo {

    ALIAS_MAPPING(0, "config/mapping.xml"),
    CODE_MAPPING(1, "config/code_mappings.xml"),
    GROUP_MAPPING(2, null),
    DATA_POINT_VIEW(3, "config/view/"),
    RDB(4, "config/datapoints.xml"),
    XO<PERSON>(5, "config/xoi_datapoints.xml"),
    ATHENA(6, "config/athena_datapoints.xml"),
    HOLDING(7, null),
    DS(8, "config/ds_datapoints.xml"),
    FI(9, "config/fi_datapoints.xml"),
    LAKEHOUSE(10, "config/lh_datapoints.xml"),
    MAPPING(11, null),
    CALC_METADATA(12, "config/calculations.xml"),
    PORTFOLIO_HOLDINGS(13, "config/ph_datapoints.xml"),
    TRANSFER_CALCS(14, null),
    EOD(15, "config/eod/eod_datapoints.xml"),
    RDB_NEW(16, "config/rdb/"),
    FIXED_INCOME_ICE(17, "config/fixed_income_datapoints.xml"),
    EQUITY(18, "config/equity_datapoints.xml,config/equity_datapoints_manual.xml"),
    DATA_POINT_GROUP(19, "config/data-group.xml"),
    DYNAMIC_RDB(20,"config/rdb/dynamicrdb_datapoints.xml"),
    DELTA_DATASET(21, "config/delta/datasets/"),
    DELTA_CONFIGURATION(22, "config/delta/configurations/"),
    LEI_NACE(23, "config/lei_nace_datapoints.xml"),
    TIMESERIES(24, "config/ts/ts_datapoints.xml"),
    // For JUnit
    TEST1(10001, null),
    TEST2(10002, null)
    ;

    private final int order; // Higher values execute later
    private final String fileLocation;
}
