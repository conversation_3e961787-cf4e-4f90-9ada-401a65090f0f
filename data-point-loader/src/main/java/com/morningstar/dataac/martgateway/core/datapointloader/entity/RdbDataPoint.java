package com.morningstar.dataac.martgateway.core.datapointloader.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.DeltaConfiguration;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RdbDataPoint {

    public static final String FREQUENCY_DAILY = "d";
    public static final String FREQUENCY_WEEKLY = "w";
    public static final String FREQUENCY_MONTHLY = "m";
    public static final String FREQUENCY_QUARTERLY = "q";
    public static final String FREQUENCY_ANNUALLY = "y";
    public static final String FREQUENCY_ANNUAL = "a";

    private String id;
    private String nid;
    private String src;
    private String name;
    private String idLevel;
    private String groupName;
    private String dataType;
    private String groupFrequency;
    private String storeProcedure;
    private String spParameter;
    private String column;
    private String parseFunction;
    private String database;
    private String tables;
    private String dateColumn;
    private String expireTime;
    private String rdbCacheFlag;
    private boolean multi;
    private boolean group;
    private boolean compress;
    private String frequency;
    private String columnPrefix;
    private boolean multipleValues;
    private List<DataPoint> subDataPoints;
    private DeltaConfiguration deltaConfiguration;
    private String paramName;

    public boolean isValidTsDatapoint() {
        boolean isValid = true;
        if(!isValidFrequency(frequency)) {
            log.warn("Timeseries data point {} has invalid frequency {}", nid, frequency);
            isValid = false;
        }

        if(!isValidGroupFrequency(groupFrequency)) {
            log.warn("Timeseries data point {} has invalid group frequency {}", nid, groupFrequency);
            isValid = false;
        }

        return isValid;
    }

    public boolean isValidFrequency() {
        return isValidFrequency(frequency);
    }

    public boolean isValidFrequency(String freq) {
        return FREQUENCY_DAILY.equals(freq) || FREQUENCY_MONTHLY.equals(freq) || FREQUENCY_QUARTERLY.equals(freq) || FREQUENCY_ANNUALLY.equals(freq);
    }

    public boolean isValidGroupFrequency(String freq) {
        return isValidFrequency(freq) || FREQUENCY_WEEKLY.equals(freq);
    }

    public String getFrequencyColumn() {
        return columnPrefix != null ? columnPrefix + ";" + id : id;
    }
}
