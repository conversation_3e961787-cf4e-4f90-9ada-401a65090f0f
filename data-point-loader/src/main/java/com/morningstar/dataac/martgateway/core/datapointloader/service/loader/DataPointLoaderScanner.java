package com.morningstar.dataac.martgateway.core.datapointloader.service.loader;

import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DataPointLoaderScanner {

    private final ApplicationContext applicationContext;

    public DataPointLoaderScanner(ApplicationContext context){
        this.applicationContext = context;
    }

    @PostConstruct
    public void loadDataPoints() {
        log.debug("Loading data-points");

        // Get all beans annotated with @DataPointLoader
        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(DataPointLoaderDefinition.class);

        // Filter to ensure they implement DataLoader and sort by order
        List<DataPointLoader> orderedLoaders = beans.values().stream()
                .filter(DataPointLoader.class::isInstance)
                .map(DataPointLoader.class::cast)
                .sorted(Comparator.comparingInt(a -> a.getClass().getAnnotation(DataPointLoaderDefinition.class).info().getOrder()))
                .toList();

        // Call loadData() on each loader in order
        DataPointLoaderContext context = new DataPointLoaderContext();
        for (DataPointLoader loader : orderedLoaders) {
            try {
                loader.loadDataPoints(context);
                log.info("Loaded data-points for loader: {}", loader.getClass().getSimpleName());
            } catch (Exception e) {
                throw new RuntimeException("Failed to load data for loader: " + loader.getClass().getSimpleName(), e);
            }
        }

        updateOperationalMap(context);
        DataPointRepository.setDataPointMap(context.getDataPointMap());
        DataPointRepository.setDataGroupLocationMap(context.getGroupLocationMap());
        DataPointRepository.setRdbGroupColDpsMap(context.getRdbGroupColDpsMap());
        CodeMappings.setCodeMappings(context.getCodeMappings());
        DataPointRepository.setDataPointAggregateMap(context.getDataPointAggregateMap());
    }

    private void updateOperationalMap(DataPointLoaderContext context) {
        Map<String, String> tscacheInternalIdMap = new HashMap<>();
        Map<String, DataPoint> masterHeaderDataPointMap = new HashMap<>();
        context.getDataPointMap().values().forEach(dp -> {
            if (dp.isTsApiDataPoint()) {
                tscacheInternalIdMap.put(dp.getId(), dp.getNid());
            }
            if ("MasterHeader".equals(dp.getGroupName())) {
                masterHeaderDataPointMap.put(dp.getId(), dp);
            }
        });
        DataPointRepository.setTscacheInternalIdMap(tscacheInternalIdMap);
        DataPointRepository.setMasterHeaderDataPointMap(masterHeaderDataPointMap);
    }

}
