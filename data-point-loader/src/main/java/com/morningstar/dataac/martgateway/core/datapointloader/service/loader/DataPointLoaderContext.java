package com.morningstar.dataac.martgateway.core.datapointloader.service.loader;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.DataPointAggregate;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.Dataset;
import com.morningstar.dataac.martgateway.core.datapointloader.util.DataPointUtils;

import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

@Getter
public class DataPointLoaderContext {
    private final Map<String, Alias> aliasMap = new HashMap<>();
    private final Map<String, String> mappingTarget = new HashMap<>();
    private final Map<String, DataPoint> dataPointMap = new HashMap<>();
    private final Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
    private final Map<String, String> groupLocationMap = new HashMap<>();
    private final Map<String, Map<String, String>> codeMappings = new HashMap<>();
    private final Map<String, Dataset> deltaDatasetMap = new HashMap<>();
    private final Map<String, DataPointAggregate> dataPointAggregateMap = new HashMap<>();

    public Alias getAliasById(String id) {
        return aliasMap.get(id);
    }

    public String getMappingTargetById(String id){
        return mappingTarget.get(id);
    }

    public boolean hasMappingTarget(String id){
        return mappingTarget.containsKey(id);
    }

    public DataPoint getOrCreateDataPoint(String id){
        return DataPointUtils.getOrCreateDataPoint.apply(dataPointMap, id);
    }

    @Nullable
    public DataPoint getDataPointById(String id){
        return dataPointMap.get(id);
    }

}
