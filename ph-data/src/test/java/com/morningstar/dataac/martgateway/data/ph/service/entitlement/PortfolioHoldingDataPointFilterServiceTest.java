package com.morningstar.dataac.martgateway.data.ph.service.entitlement;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.MultipleValueDataEntry;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CellMatch;
import com.morningstar.dataac.martgateway.core.entitlement.entity.IdEntitleStatus;
import com.morningstar.dataac.martgateway.core.entitlement.entity.InvestmentApiIdType;
import com.morningstar.dataac.martgateway.core.entitlement.service.IdMapperEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.EntitlementDataPointFilter;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.FilterRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingFilterRequest;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.IsinPrefixFilter;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.StreamHoldingFilterRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.request.StreamHoldingRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingsView;
import com.morningstar.dataac.martgateway.data.ph.entity.InvestmentHoldingData;
import com.morningstar.dataac.martgateway.data.ph.entity.InvestmentHoldingDate;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioDateValue;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDepth;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioHoldingDateResponse;
import com.morningstar.dataac.martgateway.data.ph.testutil.HoldingDataRequestBuilder;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;

public class PortfolioHoldingDataPointFilterServiceTest {

    @Mock
    private PortfolioHoldingPackageEntitlementService packageEntitlementService =
            mock(PortfolioHoldingPackageEntitlementService.class);

    @Test
    public void filterDataPointsPreSuppressionTest() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        Set<String> entitledPackages = Set.of("17", "18", "19");
        List<HoldingDataPoint> holdingDataPoints = Lists.newArrayList();
        holdingDataPoints.add(new HoldingDataPoint("HS762", "HS762_cusip"));
        holdingDataPoints.add(new HoldingDataPoint("HS761", "HS762_isin"));
        HoldingDataRequest request = new HoldingDataRequestBuilder().addHoldingDataPoints(holdingDataPoints).build();

        // Should not filter
        List<HoldingDataPoint> dataPointsToFilterPreSuppression = service.getDataPointsToFilterPreSuppression(
                entitledPackages, request);
        assertEquals(0, dataPointsToFilterPreSuppression.size());

        // Should filter
        entitledPackages = Set.of("18, 19");
        dataPointsToFilterPreSuppression = service.getDataPointsToFilterPreSuppression(
                entitledPackages, request);
        assertEquals(1, dataPointsToFilterPreSuppression.size());
        assertEquals("HS762_cusip", dataPointsToFilterPreSuppression.get(0).getAlias());
    }

    @Test
    public void filterDataPointsPreSuppressionTestEmptyMap() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of(),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        Set<String> entitledPackages = Set.of("17", "18", "19");
        List<HoldingDataPoint> holdingDataPoints = Lists.newArrayList();
        holdingDataPoints.add(new HoldingDataPoint("HS762", "HS762_cusip"));
        holdingDataPoints.add(new HoldingDataPoint("HS761", "HS762_isin"));
        HoldingDataRequest request = new HoldingDataRequestBuilder().addHoldingDataPoints(holdingDataPoints).build();

        // Should not filter
        List<HoldingDataPoint> dataPointsToFilterPreSuppression = service.getDataPointsToFilterPreSuppression(
                entitledPackages, request);
        assertEquals(0, dataPointsToFilterPreSuppression.size());
    }

    @Test
    public void filterInvestmentIdsPreSuppressionSecIdTest() {
        IdMapperEntitlementService idMapperEntitlementService = mock(IdMapperEntitlementService.class);
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                idMapperEntitlementService,
                packageEntitlementService);

        HoldingDataRequest viewRequest = new HoldingDataRequestBuilder()
                .useCase("view")
                .build();
        HoldingDataRequest requestForView = service.filterInvestmentIdsPreSuppression(new CachedEntitlement(), viewRequest,
                Set.of());
        assertEquals(viewRequest, requestForView);

        viewRequest.setUseCase("export");
        requestForView = service.filterInvestmentIdsPreSuppression(new CachedEntitlement(), viewRequest,
                Set.of());
        assertEquals(viewRequest, requestForView);

        CachedEntitlement entitlement = new CachedEntitlement();
        CellMatch c82 = new CellMatch();
        c82.setPackageId("82");
        entitlement.setCellMatchList(List.of(c82));
        IdMapper idMapperEntitled = new NonEmptyIdMapper("entitled", "{}");
        when(idMapperEntitlementService.isIdEntitled(idMapperEntitled, c82, false, false)).thenReturn(new IdEntitleStatus(true, true));
        IdMapper idMapperNotEntitled = new NonEmptyIdMapper("not-entitled", "{}");
        when(idMapperEntitlementService.isIdEntitled(idMapperNotEntitled, c82, false, false)).thenReturn(new IdEntitleStatus(false, false));
        List<IdMapper> idMappers = Lists.newArrayList(idMapperEntitled, idMapperNotEntitled);
        HoldingDataRequest feedRequest = new HoldingDataRequestBuilder()
                .useCase("feed")
                .idType(InvestmentApiIdType.SEC_ID)
                .holdingsView(new HoldingsView("full"))
                .idMappers(idMappers)
                .investments(new HashSet<>() {{
                    add(new Investment("entitled"));
                    add(new Investment("not-entitled"));
                }})
                .masterPortfolioMap(new HashMap<>() {{
                    put("entitledPortfolio", Set.of("entitled"));
                    put("not-entitledPortfolio", Set.of("not-entitled"));
                }})
                .build();
        Set<String> applicablePackages = Set.of("82", "84");

        HoldingDataRequest response = service.filterInvestmentIdsPreSuppression(entitlement, feedRequest,
                applicablePackages);
        assertEquals(1, response.getInvestments().size());
        Set<String> investmentIds = response.getInvestments().stream().map(Investment::getId).collect(Collectors.toSet());
        assertTrue(investmentIds.contains("entitled"));
        assertFalse(investmentIds.contains("not-entitled"));

        List<IdMapper> responseIdMappers = response.getIdMappers();
        assertEquals(1, responseIdMappers.size());
        assertEquals("entitled", responseIdMappers.get(0).getInvestmentId());

        Map<String, Set<String>> responseMasterPortfolioMap = response.getMasterPortfolioMap();
        assertEquals(1, responseMasterPortfolioMap.size());
        assertEquals(1, responseMasterPortfolioMap.get("entitledPortfolio").size());
        assertEquals("entitled", responseMasterPortfolioMap.get("entitledPortfolio").iterator().next());
    }

    @Test
    public void filterInvestmentIdsPreSuppressionMasterPortfolioIdTest() {
        IdMapperEntitlementService idMapperEntitlementService = mock(IdMapperEntitlementService.class);
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                idMapperEntitlementService,
                packageEntitlementService);

        HoldingDataRequest viewRequest = new HoldingDataRequestBuilder()
                .useCase("view")
                .build();
        HoldingDataRequest requestForView = service.filterInvestmentIdsPreSuppression(new CachedEntitlement(), viewRequest,
                Set.of());
        assertEquals(viewRequest, requestForView);

        viewRequest.setUseCase("export");
        requestForView = service.filterInvestmentIdsPreSuppression(new CachedEntitlement(), viewRequest,
                Set.of());
        assertEquals(viewRequest, requestForView);

        CachedEntitlement entitlement = new CachedEntitlement();
        CellMatch c82 = new CellMatch();
        c82.setPackageId("82");
        entitlement.setCellMatchList(List.of(c82));
        IdMapper idMapperEntitled = new NonEmptyIdMapper("entitled", "{}");
        when(idMapperEntitlementService.isIdEntitled(idMapperEntitled, c82, false, false)).thenReturn(new IdEntitleStatus(true, true));
        IdMapper idMapperNotEntitled = new NonEmptyIdMapper("not-entitled", "{}");
        when(idMapperEntitlementService.isIdEntitled(idMapperNotEntitled, c82, false, false)).thenReturn(new IdEntitleStatus(false, false));
        List<IdMapper> idMappers = Lists.newArrayList(idMapperEntitled, idMapperNotEntitled);
        HoldingDataRequest feedRequest = new HoldingDataRequestBuilder()
                .useCase("feed")
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .holdingsView(new HoldingsView("full"))
                .idMappers(idMappers)
                .investments(new HashSet<>() {{
                    add(new Investment("entitledPortfolio"));
                    add(new Investment("not-entitledPortfolio"));
                }})
                .masterPortfolioMap(new HashMap<>() {{
                    put("entitledPortfolio", Set.of("entitled"));
                    put("not-entitledPortfolio", Set.of("not-entitled"));
                }})
                .build();
        Set<String> applicablePackages = Set.of("82", "84");

        HoldingDataRequest response = service.filterInvestmentIdsPreSuppression(entitlement, feedRequest,
                applicablePackages);
        assertEquals(1, response.getInvestments().size());
        Set<String> investmentIds = response.getInvestments().stream().map(Investment::getId).collect(Collectors.toSet());
        assertTrue(investmentIds.contains("entitledPortfolio"));
        assertFalse(investmentIds.contains("not-entitledPortfolio"));

        List<IdMapper> responseIdMappers = response.getIdMappers();
        assertEquals(1, responseIdMappers.size());
        assertEquals("entitled", responseIdMappers.get(0).getInvestmentId());

        Map<String, Set<String>> responseMasterPortfolioMap = response.getMasterPortfolioMap();
        assertEquals(1, responseMasterPortfolioMap.size());
        assertEquals(1, responseMasterPortfolioMap.get("entitledPortfolio").size());
        assertEquals("entitled", responseMasterPortfolioMap.get("entitledPortfolio").iterator().next());
    }

    @Test
    public void filterDatesPostSuppressionSkipTest() {
        IdMapperEntitlementService idMapperEntitlementService = mock(IdMapperEntitlementService.class);
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                idMapperEntitlementService,
                packageEntitlementService);

        PortfolioHoldingDateResponse expectedResponseForViewAndNonBestAvailable = new PortfolioHoldingDateResponse();
        HoldingDataRequest viewRequest = new HoldingDataRequestBuilder()
                .useCase("view")
                .build();
        PortfolioHoldingDateResponse responseForView = service.filterDatesPostSuppression(
                new PortfolioHoldingDateResponse(), Set.of(), viewRequest, new CachedEntitlement());
        assertEquals(expectedResponseForViewAndNonBestAvailable, responseForView);

        viewRequest.setUseCase("export");
        responseForView = service.filterDatesPostSuppression(
                new PortfolioHoldingDateResponse(), Set.of(), viewRequest, new CachedEntitlement());
        assertEquals(expectedResponseForViewAndNonBestAvailable, responseForView);

        HoldingDataRequest feedFullRequest = new HoldingDataRequestBuilder()
                .useCase("feed")
                .holdingsView(new HoldingsView("full"))
                .build();
        PortfolioHoldingDateResponse responseForNonBestAvailable = service.filterDatesPostSuppression(
                new PortfolioHoldingDateResponse(), Set.of(), feedFullRequest, new CachedEntitlement());
        assertEquals(responseForNonBestAvailable, responseForView);
    }
    @Test
    public void filterDatesPostSuppressionTest() {
        IdMapperEntitlementService idMapperEntitlementService = mock(IdMapperEntitlementService.class);
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                idMapperEntitlementService,
                new PortfolioHoldingPackageEntitlementService());
        CachedEntitlement entitlement = new CachedEntitlement();
        CellMatch c82 = new CellMatch();
        c82.setPackageId("82");
        entitlement.setCellMatchList(List.of(c82));
        IdMapper idMapperEntitled = new NonEmptyIdMapper("entitled", "{}");
        when(idMapperEntitlementService.isIdEntitled(idMapperEntitled, c82, false, false)).thenReturn(new IdEntitleStatus(true, true));
        List<IdMapper> idMappers = Lists.newArrayList(idMapperEntitled);

        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .useCase("feed")
                .holdingsView(new HoldingsView("bestAvailable"))
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .idMappers(idMappers)
                .masterPortfolioMap(Map.of("id", Set.of("entitled")))
                .build();

        PortfolioHoldingDateResponse dateResponse = new PortfolioHoldingDateResponse();
        InvestmentHoldingDate investmentHoldingDate = new InvestmentHoldingDate();
        investmentHoldingDate.setId("id");
        PortfolioDateValue holdingView5 = new PortfolioDateValue("2020-01-01", "5");
        PortfolioDateValue holdingView15 = new PortfolioDateValue("2021-01-01", "15");
        PortfolioDateValue holdingView30 = new PortfolioDateValue("2022-01-01", "30");
        investmentHoldingDate.setPortfolioDateValues(Lists.newArrayList(holdingView5, holdingView15, holdingView30));
        dateResponse.setInvestmentHoldingDates(Lists.newArrayList(investmentHoldingDate));

        // Only entitled ot 81. HoldingsView 15 and 30 should be removed
        PortfolioHoldingDateResponse portfolioHoldingDateResponse = service.filterDatesPostSuppression(
                dateResponse, Set.of("81"), request, entitlement);

        List<InvestmentHoldingDate> investmentHoldingDates = portfolioHoldingDateResponse.getInvestmentHoldingDates();
        assertEquals(1, investmentHoldingDates.size());

        InvestmentHoldingDate holdingDate = investmentHoldingDates.get(0);
        assertEquals("id", holdingDate.getId());
        assertEquals(1, holdingDate.getPortfolioDateValues().size());
        assertEquals("2020-01-01", holdingDate.getPortfolioDateValues().get(0).getPortfolioDate());
        assertEquals("5", holdingDate.getPortfolioDateValues().get(0).getHoldingsView());

        when(idMapperEntitlementService.isIdEntitled(idMapperEntitled, c82, false, false)).thenReturn(new IdEntitleStatus(false, false));
        PortfolioHoldingDateResponse dateResponse2 = new PortfolioHoldingDateResponse();
        InvestmentHoldingDate investmentHoldingDate2 = new InvestmentHoldingDate();
        investmentHoldingDate2.setId("id");
        PortfolioDateValue holdingView5_2 = new PortfolioDateValue("2020-01-01", "5");
        PortfolioDateValue holdingView15_2 = new PortfolioDateValue("2021-01-01", "15");
        PortfolioDateValue holdingView30_2 = new PortfolioDateValue("2022-01-01", "30");
        investmentHoldingDate2.setPortfolioDateValues(Lists.newArrayList(holdingView5_2, holdingView15_2, holdingView30_2));
        dateResponse2.setInvestmentHoldingDates(Lists.newArrayList(investmentHoldingDate2));
        dateResponse2.setInvestmentHoldingDates(Lists.newArrayList(investmentHoldingDate2));
        PortfolioHoldingDateResponse portfolioHoldingDateResponse2 = service.filterDatesPostSuppression(
                dateResponse2, Set.of("81"), request, entitlement);

        List<InvestmentHoldingDate> investmentHoldingDates2 = portfolioHoldingDateResponse2.getInvestmentHoldingDates();
        assertEquals(0, investmentHoldingDates2.size());
    }

    @Test
    public void filterDatesPostSuppressionUniverseFilterTest() {
        IdMapperEntitlementService idMapperEntitlementService = mock(IdMapperEntitlementService.class);
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                idMapperEntitlementService,
                new PortfolioHoldingPackageEntitlementService());
        CachedEntitlement entitlement = new CachedEntitlement();
        CellMatch c82 = new CellMatch();
        c82.setPackageId("82");
        entitlement.setCellMatchList(List.of(c82));
        IdMapper idMapperEntitled = new NonEmptyIdMapper("entitled", "{}");
        when(idMapperEntitlementService.isIdEntitled(idMapperEntitled, c82, false, false)).thenReturn(new IdEntitleStatus(false, false));
        List<IdMapper> idMappers = Lists.newArrayList(idMapperEntitled);

        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .useCase("feed")
                .holdingsView(new HoldingsView("bestAvailable"))
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .idMappers(idMappers)
                .masterPortfolioMap(Map.of("id", Set.of("entitled")))
                .build();

        PortfolioHoldingDateResponse dateResponse = new PortfolioHoldingDateResponse();
        InvestmentHoldingDate investmentHoldingDate = new InvestmentHoldingDate();
        investmentHoldingDate.setId("id");
        PortfolioDateValue holdingView5 = new PortfolioDateValue("2020-01-01", "5");
        investmentHoldingDate.setPortfolioDateValues(Lists.newArrayList(holdingView5));
        dateResponse.setInvestmentHoldingDates(Lists.newArrayList(investmentHoldingDate));

        // Only entitled ot 81. HoldingsView 15 and 30 should be removed
        PortfolioHoldingDateResponse portfolioHoldingDateResponse = service.filterDatesPostSuppression(
                dateResponse, Set.of("82"), request, entitlement);

        List<InvestmentHoldingDate> investmentHoldingDates = portfolioHoldingDateResponse.getInvestmentHoldingDates();
        assertEquals(0, investmentHoldingDates.size());
    }

    @Test
    public void filterDatesPostSuppression_AnyEntitledCellKeepsPackageTest() {
        // Arrange
        IdMapperEntitlementService idMapperEntitlementService = mock(IdMapperEntitlementService.class);
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                idMapperEntitlementService,
                new PortfolioHoldingPackageEntitlementService()
        );

        // c82a => UNentitled
        // c82b => Entitled
        CellMatch c82a = new CellMatch();
        c82a.setPackageId("82");
        CellMatch c82b = new CellMatch();
        c82b.setPackageId("82");

        CachedEntitlement entitlement = new CachedEntitlement();
        entitlement.setCellMatchList(List.of(c82a, c82b));
        IdMapper idMapperEntitled = new NonEmptyIdMapper("someId", "{}");

        // Mock calls so that c82a => false, c82b => true
        when(idMapperEntitlementService.isIdEntitled(idMapperEntitled, c82a, false, false))
                .thenReturn(new IdEntitleStatus(false, false));
        when(idMapperEntitlementService.isIdEntitled(idMapperEntitled, c82b, false, false))
                .thenReturn(new IdEntitleStatus(true, true));

        // Build a HoldingDataRequest with "feed" useCase + "bestAvailable" holdingsView
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .useCase("feed")
                .holdingsView(new HoldingsView("bestAvailable"))
                .idMappers(Lists.newArrayList(idMapperEntitled))
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .masterPortfolioMap(Map.of("holdingDateId", Set.of("someId")))
                .build();

        // Create a single date with a single holdingsView => "10" (arbitrary)
        PortfolioHoldingDateResponse dateResponse = new PortfolioHoldingDateResponse();
        InvestmentHoldingDate holdingDate = new InvestmentHoldingDate();
        holdingDate.setId("holdingDateId");
        PortfolioDateValue dateValue = new PortfolioDateValue("2022-01-01", "10");
        holdingDate.setPortfolioDateValues(Lists.newArrayList(dateValue));
        dateResponse.setInvestmentHoldingDates(Lists.newArrayList(holdingDate));

        PortfolioHoldingDateResponse filteredResponse = service.filterDatesPostSuppression(
                dateResponse, Set.of("82"), request, entitlement);

        // Assert
        // Because one of the cell matches (c82b) was entitled, we should NOT remove the date
        List<InvestmentHoldingDate> filteredDates = filteredResponse.getInvestmentHoldingDates();
        assertEquals(1, filteredDates.size());
        assertEquals(1, filteredDates.get(0).getPortfolioDateValues().size());
        assertEquals("10", filteredDates.get(0).getPortfolioDateValues().get(0).getHoldingsView());
        // We confirm nothing got removed
        assertTrue(filteredDates.get(0).getPortfolioDateValues().contains(dateValue));
    }

    @Test
    public void filterDatesPostSuppression_NoCellMatchRemovesPackageTest() {
        IdMapperEntitlementService idMapperEntitlementService = mock(IdMapperEntitlementService.class);
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17")),
                idMapperEntitlementService,
                new PortfolioHoldingPackageEntitlementService()
        );

        // We have 1 package => "81" that does NOT appear in any CellMatch
        // So, cellMatchList is empty => no matches
        CachedEntitlement entitlement = new CachedEntitlement();
        entitlement.setCellMatchList(Collections.emptyList());
        IdMapper idMapper = new NonEmptyIdMapper("someIdMapper", "{}");

        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .useCase("feed")
                .holdingsView(new HoldingsView("bestAvailable"))
                .idMappers(Lists.newArrayList(idMapper))
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .masterPortfolioMap(Map.of("id", Set.of("someIdMapper")))
                .build();

        PortfolioHoldingDateResponse dateResponse = new PortfolioHoldingDateResponse();
        InvestmentHoldingDate holdingDate = new InvestmentHoldingDate();
        holdingDate.setId("id");
        PortfolioDateValue dateValue = new PortfolioDateValue("2024-01-01", "5");
        holdingDate.setPortfolioDateValues(Lists.newArrayList(dateValue));
        dateResponse.setInvestmentHoldingDates(Lists.newArrayList(holdingDate));

        // "81" doesn't match any cell,
        // no cell match => removes package
        Set<String> entitledPackages = Set.of("81");

        PortfolioHoldingDateResponse filteredResponse = service.filterDatesPostSuppression(
                dateResponse, entitledPackages, request, entitlement);

        // Because there's no cell match at all, the code should remove that package
        List<InvestmentHoldingDate> filteredDates = filteredResponse.getInvestmentHoldingDates();
        assertEquals(0, filteredDates.size());
    }

    @Test
    public void testApplyDataPointFilter_EmptyFilters() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        InvestmentHoldingData holdings = new InvestmentHoldingData();
        Map<String, String> aliasToDpId = new HashMap<>();
        aliasToDpId.put("alias1", "dp1");
        CachedEntitlement entitlement = new CachedEntitlement();

        // Should return early without any processing
        service.applyDataPointFilter(holdings, aliasToDpId, "useCase", entitlement);
        assertTrue(service.getFilters().isEmpty());
    }

    @Test
    public void testApplyDataPointFilter_WithFilters() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        // Create a mock filter
        EntitlementDataPointFilter mockFilter = mock(EntitlementDataPointFilter.class);
        service.addFilter("dp1", mockFilter);

        InvestmentHoldingData holdings = new InvestmentHoldingData();
        Map<String, String> aliasToDpId = new HashMap<>();
        aliasToDpId.put("alias1", "dp1");
        aliasToDpId.put("alias2", "dp2"); // This one shouldn't trigger filter
        CachedEntitlement entitlement = new CachedEntitlement();

        service.applyDataPointFilter(holdings, aliasToDpId, "useCase", entitlement);

        // Verify filter was applied only for dp1
        verify(mockFilter, times(1)).apply(any(FilterRequest.class));
    }

    @Test
    public void testApplyDataPointFilter_MultipleFiltersPerDataPoint() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        // Create multiple mock filters for same datapoint
        EntitlementDataPointFilter mockFilter1 = mock(EntitlementDataPointFilter.class);
        EntitlementDataPointFilter mockFilter2 = mock(EntitlementDataPointFilter.class);
        service.addFilter("dp1", mockFilter1);
        service.addFilter("dp1", mockFilter2);

        InvestmentHoldingData holdings = new InvestmentHoldingData();
        Map<String, String> aliasToDpId = new HashMap<>();
        aliasToDpId.put("alias1", "dp1");
        CachedEntitlement entitlement = new CachedEntitlement();

        service.applyDataPointFilter(holdings, aliasToDpId, "useCase", entitlement);

        // Verify both filters were applied
        verify(mockFilter1, times(1)).apply(any(FilterRequest.class));
        verify(mockFilter2, times(1)).apply(any(FilterRequest.class));
    }

    @Test
    public void testApplyDataPointFilter_VerifyFilterRequestContent() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        // Create a mock filter that captures the FilterRequest
        ArgumentCaptor<FilterRequest> filterRequestCaptor = ArgumentCaptor.forClass(FilterRequest.class);
        EntitlementDataPointFilter mockFilter = mock(EntitlementDataPointFilter.class);
        service.addFilter("dp1", mockFilter);

        InvestmentHoldingData holdings = new InvestmentHoldingData();
        Map<String, String> aliasToDpId = new HashMap<>();
        aliasToDpId.put("alias1", "dp1");
        CachedEntitlement entitlement = new CachedEntitlement();
        String useCase = "testUseCase";

        service.applyDataPointFilter(holdings, aliasToDpId, useCase, entitlement);

        // Verify the content of FilterRequest
        verify(mockFilter).apply(filterRequestCaptor.capture());
        HoldingFilterRequest capturedRequest = (HoldingFilterRequest) filterRequestCaptor.getValue();
        assertEquals("dp1", capturedRequest.getDatapointId());
        assertEquals("alias1", capturedRequest.getAlias());
        assertEquals(useCase, capturedRequest.getUseCase());
        assertEquals(entitlement, capturedRequest.getEntitlement());
        assertEquals(holdings, capturedRequest.getHoldings());
    }

    @Test
    public void testGetFiltersMap() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        Map<String, List<EntitlementDataPointFilter>> filtersMap = service.getFilters();

        // Verify it returns the internal filters map
        assertNotNull(filtersMap);
        assertTrue(filtersMap.isEmpty());

        // Verify modifications to returned map affect the service
        EntitlementDataPointFilter mockFilter = mock(EntitlementDataPointFilter.class);
        service.addFilter("dp1", mockFilter);

        assertEquals(1, service.getFilters().size());
        assertTrue(service.getFilters().containsKey("dp1"));
    }

    @Test
    public void cannotModify() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);
        service.addFilter("OS05J", new IsinPrefixFilter(Set.of("17"), Set.of("AW")));
        assertThrows(UnsupportedOperationException.class, () -> service.getFilters().clear());
        assertThrows(UnsupportedOperationException.class, () -> service.getFilters().remove("OS05J"));
        assertThrows(UnsupportedOperationException.class, () -> service.getFilters().put("ABC", null));
    }

    @Test
    public void applyStreamingPostDataRetrievalFilter() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);

        Map<String, List<String>> requestedDpIdToAliasMap = new HashMap<>();
        requestedDpIdToAliasMap.put("dp1", List.of("dp1", "alias_for_dp1"));
        requestedDpIdToAliasMap.put("dp2", List.of("dp2"));

        Map<String, Object> holdingDetails = new HashMap<>();
        holdingDetails.put("dp1", "value1");
        holdingDetails.put("dp2", "value2");
        holdingDetails.put("alias_for_dp1", "value1");

        assertEquals(3, holdingDetails.size());

        StreamHoldingRequest streamHoldingRequest1 = new StreamHoldingRequest(
                null,
                "id",
                "date",
                "full",
                requestedDpIdToAliasMap,
                null,
                new HashMap<>(),
                Map.of(),
                Map.of(),
                false,
                Map.of());

        // no registered filters
        service.applyPostDataRetrievalFilterOnBatch(streamHoldingRequest1, List.of(holdingDetails));
        assertEquals(3, holdingDetails.size());

        // no registered filters for requested datapoints
        EntitlementDataPointFilter mockFilter = mock(EntitlementDataPointFilter.class);
        service.addFilter("dp3", mockFilter);
        StreamHoldingRequest streamHoldingRequest2 = new StreamHoldingRequest(
                null,
                "id",
                "date",
                "full",
                requestedDpIdToAliasMap,
                null,
                new HashMap<>(),
                Map.of(),
                Map.of(),
                false,
                Map.of());
        service.applyPostDataRetrievalFilterOnBatch(streamHoldingRequest2, List.of(holdingDetails));
        assertEquals(3, holdingDetails.size());

        // registered filters for requested datapoints
        ArgumentCaptor<FilterRequest> filterRequestCaptor = ArgumentCaptor.forClass(FilterRequest.class);
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .useCase("view")
                .build();
        service.addFilter("dp1", mockFilter);
        StreamHoldingRequest streamHoldingRequest3 = new StreamHoldingRequest(
                request,
                "id",
                "date",
                "full",
                requestedDpIdToAliasMap,
                null,
                new HashMap<>(),
                Map.of(),
                Map.of(),
                false,
                Map.of());
        service.applyPostDataRetrievalFilterOnBatch(streamHoldingRequest3, List.of(holdingDetails));
        doNothing().when(mockFilter).apply(any(StreamHoldingFilterRequest.class));
        verify(mockFilter, times(1)).apply(any(StreamHoldingFilterRequest.class));

        verify(mockFilter).apply(filterRequestCaptor.capture());
        StreamHoldingFilterRequest capturedRequest = (StreamHoldingFilterRequest) filterRequestCaptor.getValue();
        assertEquals("dp1", capturedRequest.getDatapointId());
        assertEquals("dp1", capturedRequest.getAlias());
    }

    @Test
    public void testGetCodeValueMappings() {
        PortfolioHoldingDataPointFilterService service = new PortfolioHoldingDataPointFilterService(
                Collections.emptyMap(),
                new IdMapperEntitlementService(),
                packageEntitlementService);
        CodeMappings.setCodeMappings(Map.of("codedp1", Map.of("code1", "value1"), "codedp2", Map.of("code2", "value2")));

        Map<String, Object> detail1 = new HashMap<>();
        detail1.put("dp1", "dpvalue1");
        Map<String, Object> detail2 = new HashMap<>();
        detail2.put("dp1", "dpvalue1");
        detail2.put("codedp1", "code1");
        detail2.put("codedp2", new V("2025-01-01", "code2"));
        Map<String, Object> detail3 = new HashMap<>();
        detail3.put("codedp1", "code0");

        List<Map<String, Object>> holdingDetails1 = List.of(detail1, detail2, detail3);
        service.getCodeValueMappings(holdingDetails1, true);

        Assert.assertEquals("dpvalue1", holdingDetails1.get(0).get("dp1"));
        Assert.assertEquals(new MultipleValueDataEntry("code1","value1"), holdingDetails1.get(1).get("codedp1"));
        Assert.assertEquals(new MultipleValueDataEntry("code2","value2"), ((Map) holdingDetails1.get(1).get("codedp2")).get("v"));
        Assert.assertEquals("2025-01-01", ((Map) holdingDetails1.get(1).get("codedp2")).get("i"));
        Assert.assertNull(holdingDetails1.get(2).get("codedp1"));

        List<Map<String, Object>> holdingDetails2 = List.of(detail1);
        service.getCodeValueMappings(holdingDetails2, false);
        Assert.assertEquals("dpvalue1", holdingDetails2.get(0).get("dp1"));
    }
}
