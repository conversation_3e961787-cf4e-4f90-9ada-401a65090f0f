package com.morningstar.dataac.martgateway.data.ph.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.morningstar.dataac.martgateway.core.common.repository.S3Client;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.InvestmentApiIdType;
import com.morningstar.dataac.martgateway.data.ph.repository.PortfolioToSecIdsDao;
import com.morningstar.dataac.martgateway.data.ph.entity.InvestmentHoldingDate;
import com.morningstar.dataac.martgateway.data.ph.service.delta.PortfolioHoldingDeltaService;
import com.morningstar.dataac.martgateway.data.ph.testutil.HoldingDataRequestBuilder;
import com.morningstar.dataac.martgateway.data.ph.exception.PortfolioHoldingException;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioHoldingDateResponse;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingsView;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDateType;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDepth;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.SuppressionTypeEnum;
import com.morningstar.dataac.martgateway.data.ph.service.holdingdate.HoldingDateHandlerFactory;
import com.morningstar.dataac.martgateway.data.ph.repository.suppression.SuppressionApiClient;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.SuppressionApiResponse;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.UnsuppressedPortfolioData;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@RunWith(MockitoJUnitRunner.class)
public class HoldingDateServiceTest {

    @Mock
    private SuppressionApiClient suppressionApiClient;
    @Mock
    private PortfolioHoldingDeltaService portfolioHoldingDeltaService;
    private HoldingDateService holdingDateService;

    @Before
    public void setup() {
        S3Client mockS3Client = mock(S3Client.class);
        String testBucket = "test-bucket";
        String testPrefix = "test-prefix/";
        holdingDateService = new HoldingDateService(
                suppressionApiClient,
                new HoldingDateResponseMapper(new HoldingDateHandlerFactory(mockS3Client, testBucket, testPrefix)),
                new HoldingDateIdConversionService(mock(IdMapUtil.class), mock(PortfolioToSecIdsDao.class)),
                portfolioHoldingDeltaService
        );
    }

    @Test
    public void getMostRecentHoldingDates() {
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("a", "b", "c"))
                .suppressionTypeEnum(SuppressionTypeEnum.DEFAULT)
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();

        SuppressionApiResponse suppressionApiResponse = setupResponse();
        when(suppressionApiClient.getSuppressionData(any(), any())).thenReturn(Mono.just(suppressionApiResponse));
        Mono<PortfolioHoldingDateResponse> holdingDates = holdingDateService.getHoldingDates(request, new CachedEntitlement(), true);

        StepVerifier.create(holdingDates)
                .assertNext(holdingDateResponse -> {
                    assertEquals(3, holdingDateResponse.getInvestmentHoldingDates().size());
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void validSuppressionClientId() {
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("a", "b", "c"))
                .suppressionTypeEnum(SuppressionTypeEnum.CLIENT_SPECIFIC)
                .suppressionClientId("clientId")
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();

        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        cachedEntitlement.setPortfolioSuppressionClientId("clientId");
        SuppressionApiResponse suppressionApiResponse = setupResponse();
        when(suppressionApiClient.getSuppressionData(any(), any())).thenReturn(Mono.just(suppressionApiResponse));

        Mono<PortfolioHoldingDateResponse> holdingDates = holdingDateService.getHoldingDates(request, cachedEntitlement, true);

        StepVerifier.create(holdingDates)
                .assertNext(holdingDateResponse ->
                    assertEquals(3, holdingDateResponse.getInvestmentHoldingDates().size()))
                .expectComplete()
                .verify();
    }

    @Test
    public void invalidSuppressionClientId() {
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("a", "b", "c"))
                .suppressionTypeEnum(SuppressionTypeEnum.CLIENT_SPECIFIC)
                .suppressionClientId("clientId")
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();

        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        cachedEntitlement.setPortfolioSuppressionClientId("wrongClientId");

        PortfolioHoldingException exception = assertThrows(PortfolioHoldingException.class, () ->
                holdingDateService.getHoldingDates(request, cachedEntitlement, true));
        assertEquals("Suppression client id does not match token", exception.getMessage());
        assertEquals("401", exception.getCode());
    }

    @Test
    public void getAsyncHoldingDates() {
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("a", "b", "c"))
                .suppressionTypeEnum(SuppressionTypeEnum.DEFAULT)
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();
        when(suppressionApiClient.getSuppressionData(any(), any())).thenReturn(Mono.just(setupResponse()));
        PortfolioHoldingDateResponse asyncHoldingDates = holdingDateService.getAsyncHoldingDates(request, Map.of());
        assertEquals(3, asyncHoldingDates.getInvestmentHoldingDates().size());
    }

    @Test
    public void hasInvestmentHoldingDatesFromAsync() {
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("a", "b", "c"))
                .suppressionTypeEnum(SuppressionTypeEnum.DEFAULT)
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();
        InvestmentHoldingDate investmentHoldingDate = new InvestmentHoldingDate();
        investmentHoldingDate.setId("testId");
        request.setInvestmentHoldingDates(List.of(investmentHoldingDate));

        Mono<PortfolioHoldingDateResponse> holdingDates = holdingDateService.getHoldingDates(request, new CachedEntitlement(), true);
        StepVerifier.create(holdingDates)
                .assertNext(holdingDateResponse -> {
                    assertEquals(1, holdingDateResponse.getInvestmentHoldingDates().size());
                })
                .expectComplete()
                .verify();
        verify(suppressionApiClient, times(0)).getSuppressionData(any(), any());
    }

    @Test
    public void getDatesByDelta() {
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("a", "b", "c"))
                .suppressionTypeEnum(SuppressionTypeEnum.DEFAULT)
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();
        request.setDeltaStartTime(Instant.now().minus(Duration.ofDays(5)));

        SuppressionApiResponse suppressionApiResponse = setupResponse();
        when(suppressionApiClient.getSuppressionData(any(), any())).thenReturn(Mono.just(suppressionApiResponse));
        when(portfolioHoldingDeltaService.filterSuppressionForDelta(any(), any())).thenReturn(Mono.just(suppressionApiResponse));
        Mono<PortfolioHoldingDateResponse> holdingDates = holdingDateService.getHoldingDates(request, new CachedEntitlement(), true);

        StepVerifier.create(holdingDates)
                .assertNext(holdingDateResponse -> {
                    assertEquals(3, holdingDateResponse.getInvestmentHoldingDates().size());
                })
                .expectComplete()
                .verify();
        verify(portfolioHoldingDeltaService, times(1)).filterSuppressionForDelta(any(), any());
    }
    @Test
    public void getDatesByDeltaAsync() {
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("a", "b", "c"))
                .suppressionTypeEnum(SuppressionTypeEnum.DEFAULT)
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();
        request.setDeltaStartTime(Instant.now().minus(Duration.ofDays(5)));

        SuppressionApiResponse response = setupResponse();
        when(suppressionApiClient.getSuppressionData(any(), any())).thenReturn(Mono.just(response));
        when(portfolioHoldingDeltaService.filterSuppressionForDelta(any(), any())).thenReturn((Mono.just(response)));
        PortfolioHoldingDateResponse asyncHoldingDates = holdingDateService.getAsyncHoldingDates(request, Map.of());
        assertEquals(3, asyncHoldingDates.getInvestmentHoldingDates().size());
        verify(portfolioHoldingDeltaService, times(1)).filterSuppressionForDelta(any(), any());
    }

    private static SuppressionApiResponse setupResponse() {
        SuppressionApiResponse suppressionApiResponse = new SuppressionApiResponse();
        UnsuppressedPortfolioData a = new UnsuppressedPortfolioData();
        a.setMasterPortfolioId("a");
        a.setTopNCount(0);
        a.setUnsuppressedFullHoldingPortfolioDates(Lists.newArrayList("2020-01-01"));

        UnsuppressedPortfolioData b = new UnsuppressedPortfolioData();
        b.setMasterPortfolioId("b");
        b.setTopNCount(1);
        b.setUnsuppressedFullHoldingPortfolioDates(Lists.newArrayList("2021-01-01"));

        UnsuppressedPortfolioData c = new UnsuppressedPortfolioData();
        c.setMasterPortfolioId("c");
        c.setTopNCount(1);
        c.setUnsuppressedFullHoldingPortfolioDates(Lists.newArrayList("2022-01-01"));
        suppressionApiResponse.setUnsuppressedPortfolioList(List.of(a, b, c));
        return suppressionApiResponse;
    }
}