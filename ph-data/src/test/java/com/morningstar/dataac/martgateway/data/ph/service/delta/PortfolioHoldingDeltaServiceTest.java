package com.morningstar.dataac.martgateway.data.ph.service.delta;

import com.google.common.collect.Lists;
import com.morningstar.dataac.martgateway.core.entitlement.entity.InvestmentApiIdType;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingsView;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDateType;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDepth;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.SuppressionTypeEnum;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.SuppressionApiResponse;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.UnsuppressedPortfolioData;
import com.morningstar.dataac.martgateway.data.ph.testutil.HoldingDataRequestBuilder;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.get.MultiGetItemResponse;
import org.elasticsearch.action.get.MultiGetRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@ExtendWith(MockitoExtension.class)
public class PortfolioHoldingDeltaServiceTest {

    @Mock
    private ReactiveElasticsearchClient esChangeLogClient;

    private PortfolioHoldingDeltaService portfolioHoldingDeltaService;

    @BeforeEach
    public void setup(){
        this.portfolioHoldingDeltaService = new PortfolioHoldingDeltaService(esChangeLogClient);
    }

    @Test
    public void filterSuppressionForDelta(){
        HoldingDataRequest request = new HoldingDataRequestBuilder()
                .idType(InvestmentApiIdType.MASTER_PORTFOLIO_ID)
                .investmentValues(Set.of("1", "2", "3"))
                .suppressionTypeEnum(SuppressionTypeEnum.DEFAULT)
                .portfolioDepth(PortfolioDepth.BASE_LEVEL)
                .holdingsView(new HoldingsView("full"))
                .frequency("d")
                .portfolioDateType(PortfolioDateType.MOST_RECENT)
                .build();
        request.setDeltaStartTime(Instant.ofEpochMilli(1700000000000L));
        SuppressionApiResponse suppressionApiResponse = setupResponse();

        Flux<MultiGetItemResponse> mockFlux = buildEsResponse();
        Mockito.doReturn(mockFlux)
                .when(esChangeLogClient)
                .multiGet(ArgumentMatchers.any(MultiGetRequest.class));

        StepVerifier.create(portfolioHoldingDeltaService.filterSuppressionForDelta(request, suppressionApiResponse))
                .assertNext(deltaSuppressionResponse -> {
                    Assertions.assertEquals(2, deltaSuppressionResponse.getUnsuppressedPortfolioList().size());
                    Assertions.assertTrue(deltaSuppressionResponse.getUnsuppressedPortfolioList().stream().anyMatch(x -> "1".equals(x.getMasterPortfolioId()) && x.getUnsuppressedFullHoldingPortfolioDates().size() == 1 && "2020-01-01".equals(x.getUnsuppressedFullHoldingPortfolioDates().get(0))));
                    Assertions.assertTrue(deltaSuppressionResponse.getUnsuppressedPortfolioList().stream().anyMatch(x -> "3".equals(x.getMasterPortfolioId()) && x.getUnsuppressedFullHoldingPortfolioDates().size() == 1 && "2022-01-01".equals(x.getUnsuppressedFullHoldingPortfolioDates().get(0))));
                })
                .verifyComplete();

    }

    private Flux<MultiGetItemResponse> buildEsResponse() {
        return Flux.just(
                buildMultiGetItemResponse("managed_portfolio_holding_base_1_20200101", true, "{\"instrumentId\":1,\"asOfDate\":\"20200101\",\"dataset\":\"managed_portfolio_holding_base\",\"updateOn\":1754068117851,\"producerTimeStamp\":1754068105282}"),
                buildMultiGetItemResponse(null, false, null),
                buildMultiGetItemResponse("managed_portfolio_holding_base_3_20220101", true, "{\"instrumentId\":3,\"asOfDate\":\"20220101\",\"dataset\":\"managed_portfolio_holding_base\",\"updateOn\":1754068117851,\"producerTimeStamp\":1754068105282}")
        );
    }

    private MultiGetItemResponse buildMultiGetItemResponse(String id, boolean exists, String jsonDoc) {
        MultiGetItemResponse multiGetItemResponse = Mockito.mock(MultiGetItemResponse.class);
        GetResponse getResponse = Mockito.mock(GetResponse.class);
        Mockito.when(getResponse.isExists()).thenReturn(exists);
        if (StringUtils.isNotEmpty(jsonDoc)) {
            Mockito.when(getResponse.getSourceAsString()).thenReturn(jsonDoc);
        }
        if (StringUtils.isNotEmpty(id)) {
            Mockito.when(getResponse.getId()).thenReturn(id);
        }
        Mockito.when(multiGetItemResponse.getResponse()).thenReturn(getResponse);
        return multiGetItemResponse;
    }

    private SuppressionApiResponse setupResponse() {
        SuppressionApiResponse suppressionApiResponse = new SuppressionApiResponse();
        UnsuppressedPortfolioData a = new UnsuppressedPortfolioData();
        a.setMasterPortfolioId("1");
        a.setTopNCount(0);
        a.setUnsuppressedFullHoldingPortfolioDates(Lists.newArrayList("2020-01-01"));

        UnsuppressedPortfolioData b = new UnsuppressedPortfolioData();
        b.setMasterPortfolioId("2");
        b.setTopNCount(1);
        b.setUnsuppressedFullHoldingPortfolioDates(Lists.newArrayList("2021-01-01"));

        UnsuppressedPortfolioData c = new UnsuppressedPortfolioData();
        c.setMasterPortfolioId("3");
        c.setTopNCount(1);
        c.setUnsuppressedFullHoldingPortfolioDates(Lists.newArrayList("2022-01-01"));
        suppressionApiResponse.setUnsuppressedPortfolioList(List.of(a, b, c));
        return suppressionApiResponse;
    }

}
