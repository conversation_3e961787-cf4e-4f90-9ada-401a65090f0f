package com.morningstar.dataac.martgateway.data.ph.service.parsers.deriveddata;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.RawHoldingData;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDepth;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DerivedDataHandlerTest {

    private final DerivedDataHandler derivedDataHandler = new DerivedDataHandler();

    @Mock
    private RawHoldingData rawHoldingData;

    @Mock
    private DataPoint dataPoint;

    @Test
    public void testHasNoMatchingDeriveType() {
        DataPoint dataPoint = DataPoint.builder().deriveType("unknown").build();
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertNull(result);
    }

    @Test
    public void testComputeLookThroughShorted() {
        DataPoint dataPoint = DataPoint.builder()
                .deriveType("computeShorted[lookThrough;multiplier=100]")
                .contentType("double")
                .build();

        when(rawHoldingData.getPortfolioDepth()).thenReturn(PortfolioDepth.LOOK_THROUGH);

        doReturn("marketValuePath").when(rawHoldingData).getMarketValuePath();
        doReturn("dpPath").when(rawHoldingData).getDataPointPath(dataPoint);

        Object result;

        // MV negative,  W positive -> R negative
        doReturn(-100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(-5D, result);

        // MV negative,  W negative -> R negative
        doReturn(-100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(-0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(-5D, result);

        // MV positive,  W positive -> R positive
        doReturn(100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(5D, result);

        // MV positive,  W negative -> R positive
        doReturn(100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(-0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(5D, result);
    }

    @Test
    public void testComputeBaseLevelShorted() {
        DataPoint dataPoint = DataPoint.builder()
                .deriveType("computeShorted[lookThrough;multiplier=100]")
                .contentType("double")
                .build();

        when(rawHoldingData.getPortfolioDepth()).thenReturn(PortfolioDepth.BASE_LEVEL);

        doReturn("marketValuePath").when(rawHoldingData).getMarketValuePath();
        doReturn("dpPath").when(rawHoldingData).getDataPointPath(dataPoint);

        Object result;

        // MV negative,  W positive -> R negative
        doReturn(-100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(-0.05D, result);

        // MV negative,  W negative -> R negative
        doReturn(-100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(-0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(-0.05D, result);

        // MV positive,  W positive -> R positive
        doReturn(100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(0.05D, result);

        // MV positive,  W negative -> R positive
        doReturn(100D).when(rawHoldingData).getValueForType("marketValuePath", "double");
        doReturn(-0.05D).when(rawHoldingData).getValueForType("dpPath", "double");

        result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);
        assertEquals(0.05D, result);
    }

    @Test
    public void testProcessDeriveType_Mapping_NullMappingSrc() {
        // Setup
        when(dataPoint.getDeriveType()).thenReturn("codeValueMapping");
        when(dataPoint.getMappingSrc()).thenReturn(null);

        // Execute
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);

        // Verify
        assertNull(result);
    }

    @Test
    public void testProcessDeriveType_Mapping_EmptyMappingSrcPath() {
        // Setup
        when(dataPoint.getDeriveType()).thenReturn("codeValueMapping");

        DataPoint mappingSrc = mock(DataPoint.class);
        when(dataPoint.getMappingSrc()).thenReturn(mappingSrc);

        when(rawHoldingData.getDataPointPath(mappingSrc)).thenReturn("");

        // Execute
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);

        // Verify
        assertNull(result);
    }

    @Test
    public void testProcessDeriveType_Mapping_EmptyValueToMap() {
        // Setup
        when(dataPoint.getDeriveType()).thenReturn("codeValueMapping");

        DataPoint mappingSrc = mock(DataPoint.class);
        when(dataPoint.getMappingSrc()).thenReturn(mappingSrc);

        when(rawHoldingData.getDataPointPath(mappingSrc)).thenReturn("mappingSrcPath");
        when(rawHoldingData.getValueForType("mappingSrcPath", "string")).thenReturn(null);

        // Execute
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);

        // Verify
        assertNull(result);
    }

    @Test
    public void testProcessDeriveType_BaseLevelOnlyMapping() {
        // Setup - same as mapping test since they use the same method
        when(dataPoint.getDeriveType()).thenReturn("duplicateDataPointMapping");

        DataPoint mappingSrc = mock(DataPoint.class);
        when(dataPoint.getMappingSrc()).thenReturn(mappingSrc);

        when(rawHoldingData.getDataPointPath(mappingSrc)).thenReturn("mappingSrcPath");
        when(rawHoldingData.getValueForType("mappingSrcPath", "string")).thenReturn("keyToMap");
        // Execute
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);

        // Verify
        assertEquals("keyToMap", result);
    }

    @Test
    public void testProcessDeriveType_LookThroughStorageId() {
        // Setup
        when(dataPoint.getDeriveType()).thenReturn("lookThroughStorageId");
        when(rawHoldingData.getDataPointPath(dataPoint)).thenReturn("dataPointPath");
        when(rawHoldingData.getValueForType("dataPointPath", "int")).thenReturn(123);

        // Execute
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);

        // Verify
        assertEquals(123, result);
    }

    @Test
    public void testProcessDeriveType_ShowIfParentIsPresent() {
        // Setup
        when(dataPoint.getDeriveType()).thenReturn("showIfParentIsPresent[parentPath]");
        when(rawHoldingData.get("parentPath")).thenReturn("someValue");
        when(rawHoldingData.getDataPointPath(dataPoint)).thenReturn("dataPointPath");
        when(dataPoint.getContentType()).thenReturn("string");
        when(rawHoldingData.getValueForType("dataPointPath", "string")).thenReturn("childValue");

        // Execute
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);

        // Verify
        assertEquals("childValue", result);
    }

    @Test
    public void testProcessDeriveType_ShowIfParentIsPresent_ParentNotPresent() {
        // Setup
        when(dataPoint.getDeriveType()).thenReturn("showIfParentIsPresent[parentPath]");
        when(rawHoldingData.get("parentPath")).thenReturn(null);

        // Execute
        Object result = derivedDataHandler.processDeriveType(rawHoldingData, dataPoint);

        // Verify
        assertNull(result);
    }



}