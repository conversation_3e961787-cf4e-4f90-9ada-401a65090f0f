package com.morningstar.dataac.martgateway.data.ph.util;

import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.IceDataPointType;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants;
import com.morningstar.dataac.martgateway.core.common.entity.EnrichmentType;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.IdLevelType;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.MandatoryDataPointId;
import com.morningstar.dataac.martgateway.data.ph.service.PhDataPointInfo;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataPoint;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.LEINACE;

public class PhDataPointUtil {

    private PhDataPointUtil() {}

    // Always return mandatory datapoints
    public static Map<String, List<String>> mapDpIdsToAliases(List<HoldingDataPoint> holdingsDataPoints) {
        LinkedHashMap<String, List<String>> mandatoryDpIdsToAliases = new LinkedHashMap<>();
        LinkedHashMap<String, List<String>> orderedDpIdsToAliases = new LinkedHashMap<>();

        if (CollectionUtils.isNotEmpty(holdingsDataPoints)) {
            for (HoldingDataPoint holdingDataPoint : holdingsDataPoints) {
                if (HoldingMapperUtil.isMandatory(holdingDataPoint.getDataPointId())) {
                    addToMap(holdingDataPoint, mandatoryDpIdsToAliases);
                } else {
                    addToMap(holdingDataPoint, orderedDpIdsToAliases);
                }
            }
        }

        // Add missing mandatory datapoints and reorder according to spec
        LinkedHashMap<String, List<String>> orderedMandatoryDpIdsToAliases = new LinkedHashMap<>();
        for (MandatoryDataPointId mandatoryDataPointId : MandatoryDataPointId.values()) {
            if (!mandatoryDpIdsToAliases.containsKey(mandatoryDataPointId.getId())) {
                orderedMandatoryDpIdsToAliases.put(mandatoryDataPointId.getId(), List.of(mandatoryDataPointId.getId()));
            } else {
                orderedMandatoryDpIdsToAliases.put(mandatoryDataPointId.getId(), mandatoryDpIdsToAliases.get(mandatoryDataPointId.getId()));
            }
        }
        // Mandatory datapoints must be first
        orderedMandatoryDpIdsToAliases.putAll(orderedDpIdsToAliases);
        return orderedMandatoryDpIdsToAliases;
    }

    private static void addToMap(HoldingDataPoint holdingDataPoint, LinkedHashMap<String, List<String>> aliasMap) {
        List<String> aliases = aliasMap.computeIfAbsent(holdingDataPoint.getDataPointId(), k -> new ArrayList<>());
        aliases.add(StringUtils.defaultIfEmpty(holdingDataPoint.getAlias(), holdingDataPoint.getDataPointId()));
    }

    // Get datapoints that are not requested but are a mapping source for a requested datapoint
    public static Map<String, String> getMappingSrcToRequestedDpId(Set<String> requestedDpIds) {
        Map<String, String> mappingSrcToRequestedDpId = new HashMap<>();
        if (CollectionUtils.isEmpty(requestedDpIds)) {
            return mappingSrcToRequestedDpId;
        }
        for (String requestedDpId : requestedDpIds) {
            if (PhDataPointInfo.hasMappingDp(requestedDpId)) {
                String mappingDpId = PhDataPointInfo.getMappingDpId(requestedDpId);
                if (!requestedDpIds.contains(mappingDpId)) {
                    mappingSrcToRequestedDpId.put(mappingDpId, requestedDpId);
                }
            }
        }
        return mappingSrcToRequestedDpId;
    }

    // Extract ice data points, grouping them by their type (most recent vs time series)
    public static Map<IceDataPointType, List<DataPoint>> extractIceDataPoints(List<HoldingDataPoint> requestedDataPoints, EnrichmentType enrichmentType) {
        if (CollectionUtils.isEmpty(requestedDataPoints)) {
            return Map.of();
        }

        Map<IceDataPointType, List<DataPoint>> iceDataPoints = new EnumMap<>(IceDataPointType.class);

        // LATEST_AVAILABLE -> All ICE datapoints are considered Most Recent
        if (enrichmentType == EnrichmentType.LATEST_AVAILABLE) {
            iceDataPoints.put(IceDataPointType.MOST_RECENT, requestedDataPoints.stream()
                    .map(dp -> DataPointRepository.getByNid(dp.getDataPointId()))
                    .filter(dp -> StringUtils.contains(dp.getSrc(), DataPointConstants.ICEFI))
                    .collect(Collectors.toList()));
            return iceDataPoints;
        }

        for (HoldingDataPoint holdingDataPoint : requestedDataPoints) {
            DataPoint dataPoint = DataPointRepository.getByNid(holdingDataPoint.getDataPointId());
            if (dataPoint == null || !StringUtils.contains(dataPoint.getSrc(), DataPointConstants.ICEFI)) {
                continue;
            }
            IceDataPointType type = (dataPoint.getTsFixedIncomeDataPoint() == null)
                    ? IceDataPointType.MOST_RECENT
                    : IceDataPointType.TIME_SERIES;
            iceDataPoints
                    .computeIfAbsent(type, k -> new ArrayList<>())
                    .add(dataPoint);
        }

        return iceDataPoints;
    }

    public static boolean needCodeMapping(String dataPointId) {
        return CodeMappings.hasCodeMapping(dataPointId);
    }

    public static Map<IdLevelType, List<DataPoint>> extractIdLevelTypeToReqDpMapping(List<HoldingDataPoint> dpList) {
        if (CollectionUtils.isEmpty(dpList)) {
            return Map.of();
        }
        Map<IdLevelType, List<DataPoint>> idLevelToDataPointMap = new EnumMap<>(IdLevelType.class);

        for(HoldingDataPoint holdingDataPoint : dpList) {
            DataPoint dp = DataPointRepository.getByNid(holdingDataPoint.getDataPointId());
            if(LEINACE.equals(dp.getSrc())) {
                IdLevelType idLevelType = IdLevelType.getByIdLevel(dp.getIdLevel());
                if (idLevelType != null) {
                    idLevelToDataPointMap.computeIfAbsent(idLevelType, k -> new ArrayList<>()).add(dp);
                }
            }
        }
        return idLevelToDataPointMap;
    }
}
