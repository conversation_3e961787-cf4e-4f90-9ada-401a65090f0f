package com.morningstar.dataac.martgateway.data.ph.service.delta;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.delta.PortfolioDelta;
import com.morningstar.dataac.martgateway.data.ph.entity.enums.PortfolioDepth;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.SuppressionApiResponse;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.UnsuppressedPortfolioData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.get.MultiGetItemResponse;
import org.elasticsearch.action.get.MultiGetRequest;
import org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.google.common.base.Stopwatch;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_MESSAGE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXECUTE_TIME;

@Slf4j
public class PortfolioHoldingDeltaService {

    private static final String PORTFOLIO_DELTA_INDEX = "portfoliodelta";

    private static final DateTimeFormatter ES_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter PH_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final ReactiveElasticsearchClient esChangeLogClient;

    public PortfolioHoldingDeltaService(ReactiveElasticsearchClient esChangeLogClient) {
        this.esChangeLogClient = esChangeLogClient;
    }

    public Mono<SuppressionApiResponse> filterSuppressionForDelta(HoldingDataRequest request, SuppressionApiResponse suppressionApiResponse) {
        if (CollectionUtils.isEmpty(suppressionApiResponse.getUnsuppressedPortfolioList())) {
            return Mono.just(suppressionApiResponse);
        }

        Map<String, List<String>> datesByMasterPortfolioIds = extractDatesByMasterPortfolioId(suppressionApiResponse);
        if (MapUtils.isEmpty(datesByMasterPortfolioIds)) {
            return Mono.just(suppressionApiResponse);
        }

        List<String> esIdList = buildEsIdList(request, datesByMasterPortfolioIds);
        Instant deltaStartTime = request.getDeltaStartTime();
        Instant now = Instant.now();

        return Mono.defer(() -> {
            Stopwatch stopWatch = Stopwatch.createStarted();
            return Flux.fromIterable(esIdList)
                    .buffer(1000)
                    .flatMap(this::getPortfolioDeltas)
                    .filter(delta -> filterDelta(delta, deltaStartTime.toEpochMilli(), now.toEpochMilli()))
                    .reduce(new HashMap<String, Set<String>>(), (datesById, delta) -> {
                        String masterPortfolioId = delta.getInstrumentId();
                        String date = formatToPhDate(delta.getAsOfDate());
                        datesById.computeIfAbsent(masterPortfolioId, k -> new HashSet<>()).add(date);
                        return datesById;
                    })
                    .map(deltaDatesByMasterPortfolioId -> filterSuppressionByDelta(suppressionApiResponse, deltaDatesByMasterPortfolioId))
                    .doOnSuccess(result -> LogEntry.info(new LogEntity(EVENT_DESCRIPTION, "portfolio holding delta"),
                            new LogEntity(EXECUTE_TIME, stopWatch.elapsed().toMillis())))
                    .doOnError(e -> LogEntry.error(e, new LogEntity(EVENT_DESCRIPTION, "portfolio holding delta")));
        });
    }

    private SuppressionApiResponse filterSuppressionByDelta(SuppressionApiResponse response, HashMap<String, Set<String>> deltaDatesByMasterPortfolioId) {
        List<UnsuppressedPortfolioData> deltaUnsuppressedPortfolioData = response.getUnsuppressedPortfolioList().stream()
                .filter(unsuppressedPortfolioData -> deltaDatesByMasterPortfolioId.containsKey(unsuppressedPortfolioData.getMasterPortfolioId()))
                .map(unsuppressedPortfolioData -> buildDeltaUnsuppressedPortfolioData(deltaDatesByMasterPortfolioId, unsuppressedPortfolioData))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
        return new SuppressionApiResponse(deltaUnsuppressedPortfolioData);
    }

    private boolean filterDelta(PortfolioDelta delta, long start, long now) {
        return delta.getUpdateOn() >= start && delta.getUpdateOn() <= now;
    }

    private Map<String, List<String>> extractDatesByMasterPortfolioId(SuppressionApiResponse response) {
        Map<String, List<String>> datesByMasterPortfolioIds = new HashMap<>();
        for (UnsuppressedPortfolioData unsuppressedPortfolioData: response.getUnsuppressedPortfolioList()) {
            String masterPortfolioId = unsuppressedPortfolioData.getMasterPortfolioId();
            List<String> dates = extractUnsuppressedDates(unsuppressedPortfolioData);
            if (masterPortfolioId != null && CollectionUtils.isNotEmpty(dates)) {
                datesByMasterPortfolioIds.put(masterPortfolioId, dates);
            }
        }
        return datesByMasterPortfolioIds;
    }

    private List<String> extractUnsuppressedDates(UnsuppressedPortfolioData unsuppressedPortfolioData) {
        return Stream.of(unsuppressedPortfolioData.getUnsuppressedTopHoldingPortfolioDates(), unsuppressedPortfolioData.getUnsuppressedFullHoldingPortfolioDates())
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .distinct()
                .toList();
    }

    private Optional<UnsuppressedPortfolioData> buildDeltaUnsuppressedPortfolioData(Map<String, Set<String>> deltaDatesByMasterPortfolioId, UnsuppressedPortfolioData unsuppressedPortfolioData) {
        String masterPortfolioId = unsuppressedPortfolioData.getMasterPortfolioId();
        Set<String> deltaDates = deltaDatesByMasterPortfolioId.get(masterPortfolioId);
        List<String> unsuppressedFullHoldingPortfolioDates = filterDatesByDelta(unsuppressedPortfolioData.getUnsuppressedFullHoldingPortfolioDates(), deltaDates);
        List<String> unsuppressedTopHoldingPortfolioDates = filterDatesByDelta(unsuppressedPortfolioData.getUnsuppressedTopHoldingPortfolioDates(), deltaDates);
        if (CollectionUtils.isEmpty(unsuppressedFullHoldingPortfolioDates) && CollectionUtils.isEmpty(unsuppressedTopHoldingPortfolioDates)) {
            return Optional.empty();
        }
        return Optional.of(new UnsuppressedPortfolioData(masterPortfolioId, unsuppressedFullHoldingPortfolioDates, unsuppressedTopHoldingPortfolioDates, unsuppressedPortfolioData.getTopNCount()));
    }

    private List<String> filterDatesByDelta(List<String> dates, Set<String> deltaDates) {
        if (CollectionUtils.isEmpty(dates)) {
            return Collections.emptyList();
        }
        return dates.stream()
                .filter(deltaDates::contains)
                .collect(Collectors.toCollection(ArrayList::new));
    }

    private List<String> buildEsIdList(HoldingDataRequest request, Map<String, List<String>> datesByMasterPortfolioIds) {
        return selectIdPrefix(request)
                .map(prefix -> buildEsIdList(prefix, datesByMasterPortfolioIds))
                .orElse(Collections.emptyList());
    }

    private List<String> buildEsIdList(String prefix, Map<String, List<String>> datesByMasterPortfolioIds) {
        List<String> idList = new ArrayList<>();
        for (Map.Entry<String,List<String>> entry: datesByMasterPortfolioIds.entrySet()) {
            String masterPortfolioId = entry.getKey();
            List<String> dates = entry.getValue();
            dates.stream().map(date -> buildEsId(prefix, masterPortfolioId, date)).forEach(idList::add);
        }
        return idList;
    }

    private String buildEsId(String prefix, String masterPortfolioId, String date) {
        return prefix + "_" + masterPortfolioId + "_" + formatToEsDate(date);
    }

    private Optional<String> selectIdPrefix(HoldingDataRequest request) {
        PortfolioDepth portfolioDepth = request.getPortfolioSetting().getPortfolioDepth();
        String prefix = null;
        if (portfolioDepth == PortfolioDepth.BASE_LEVEL) {
            prefix = "managed_portfolio_holding_base";
        }
        if (portfolioDepth == PortfolioDepth.LOOK_THROUGH) {
            prefix = "private_aggregated_look_through";
        }
        return Optional.ofNullable(prefix);
    }

    private String formatToEsDate(String date) {
        return ES_DATE_FORMATTER.format(LocalDate.parse(date, PH_DATE_FORMATTER));
    }

    private String formatToPhDate(LocalDate date) {
        return PH_DATE_FORMATTER.format(date);
    }

    private Flux<PortfolioDelta> getPortfolioDeltas(List<String> ids) {
        MultiGetRequest request = buildMultiGetRequest(ids);
        return esChangeLogClient.multiGet(request)
                .filter(x -> x.getResponse().isExists())
                .flatMap(this::transformToDelta);
    }

    private MultiGetRequest buildMultiGetRequest(List<String> ids) {
        MultiGetRequest request = new MultiGetRequest();
        for (String id : ids) {
            MultiGetRequest.Item item = new MultiGetRequest.Item(PORTFOLIO_DELTA_INDEX, id);
            request.add(item);
        }
        return request;
    }

    private Mono<PortfolioDelta> transformToDelta(MultiGetItemResponse multiGetItemResponse) {
        GetResponse getResponse = multiGetItemResponse.getResponse();
        String source = getResponse.getSourceAsString();
        PortfolioDelta delta = null;
        try {
            delta = JsonUtils.fromJsonString(source, PortfolioDelta.class);
        } catch (IllegalStateException e) {
            LogEntry.error(e, new LogEntity(EVENT_DESCRIPTION, "Unable to parse ph delta ES document"),
                    new LogEntity(EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(EXCEPTION_MESSAGE, e.getMessage()));
        }
        if (delta == null) {
            return Mono.empty();
        }
        delta.setId(getResponse.getId());
        return Mono.just(delta);
    }
}
