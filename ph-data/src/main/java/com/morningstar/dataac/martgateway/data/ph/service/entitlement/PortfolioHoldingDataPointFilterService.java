package com.morningstar.dataac.martgateway.data.ph.service.entitlement;

import com.google.common.base.Stopwatch;
import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.MultipleValueDataEntry;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CellMatch;
import com.morningstar.dataac.martgateway.core.entitlement.service.IdMapperEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.AbstractFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.EntitlementDataPointFilter;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.FilterRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingFilterRequest;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.StreamHoldingFilterRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.entitlement.entity.InvestmentApiIdType;
import com.morningstar.dataac.martgateway.data.ph.entity.request.StreamHoldingRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingsView;
import com.morningstar.dataac.martgateway.data.ph.entity.InvestmentHoldingData;
import com.morningstar.dataac.martgateway.data.ph.entity.InvestmentHoldingDate;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioDateValue;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioHoldingDateResponse;
import com.morningstar.dataac.martgateway.data.ph.util.PhDataPointUtil;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.core.common.util.LogUtil;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class PortfolioHoldingDataPointFilterService extends AbstractFilterService {

    private final Map<String, Set<String>> preRetrievalDpToRequiredCpq;
    private final IdMapperEntitlementService idMapperEntitlementService;
    private final PortfolioHoldingPackageEntitlementService packageEntitlementService;
    private static final String FEED = "feed";

    public PortfolioHoldingDataPointFilterService(Map<String, Set<String>> preRetrievalDpToRequiredCpq,
            IdMapperEntitlementService idMapperEntitlementService,
            PortfolioHoldingPackageEntitlementService portfolioHoldingPackageEntitlementService) {
        this.preRetrievalDpToRequiredCpq = preRetrievalDpToRequiredCpq;
        this.idMapperEntitlementService = idMapperEntitlementService;
        this.packageEntitlementService = portfolioHoldingPackageEntitlementService;
    }

    public List<HoldingDataPoint> getDataPointsToFilterPreSuppression(Set<String> entitledPackages, HoldingDataRequest request) {
        return request.getPortfolioSetting().getHoldingsDataPoints()
                .stream()
                .filter(dp -> preRetrievalDpToRequiredCpq.containsKey(dp.getDataPointId()))
                .filter(dp -> Collections.disjoint(entitledPackages, preRetrievalDpToRequiredCpq.get(dp.getDataPointId())))
                .toList();
    }

    public HoldingDataRequest filterInvestmentIdsPreSuppression(
            CachedEntitlement entitlement,
            HoldingDataRequest request,
            Set<String> applicablePreSuppressionPackages)
    {
        String useCase = request.getUseCase();
        HoldingsView holdingsView = request.getPortfolioSetting().getHoldingsView();
        // no filtering for useCase = view or holdingsView = bestAvailable
        if (FEED.equalsIgnoreCase(useCase) && !holdingsView.isBestAvailable()) {
            List<CellMatch> applicableCellMatches = getApplicableCellMatches(entitlement, applicablePreSuppressionPackages);
            Set<String> secIdsToKeep = getSecIdsToKeep(request.getIdMappers(), applicableCellMatches);
            removeInvestmentIdsFromRequest(request, secIdsToKeep);
            removeSecIdsFromIdMappers(request, secIdsToKeep);
            removeEntriesFromMasterPortfolioMap(request, secIdsToKeep);
        }
        return request;
    }

    public PortfolioHoldingDateResponse filterDatesPostSuppression(
            PortfolioHoldingDateResponse holdingDateResponse,
            Set<String> entitledPackages,
            HoldingDataRequest request,
            CachedEntitlement entitlement)
    {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String useCase = request.getUseCase();

        if (!FEED.equalsIgnoreCase(useCase) || !request.getPortfolioSetting().getHoldingsView().isBestAvailable()) {
            return holdingDateResponse;
        }

        Set<String> portfolioIdsToRemove = new HashSet<>();
        Map<HoldingsView, Set<String>> holdingViewToApplicablePackages = new HashMap<>();
        Map<String, List<PortfolioDateValue>> portfolioIdsToDatesMap = mapPortfolioIdsToDates(holdingDateResponse);
        // loop for each portfolioId
        for (Entry<String, List<PortfolioDateValue>> datesForPortfolioId : portfolioIdsToDatesMap.entrySet()) {
            processPortfolioId(
                    datesForPortfolioId, portfolioIdsToRemove, entitledPackages,
                    request, entitlement, holdingViewToApplicablePackages);
        }

        if (!portfolioIdsToRemove.isEmpty()) {
            holdingDateResponse.getInvestmentHoldingDates()
                    .removeIf(investmentHoldingDate -> portfolioIdsToRemove.contains(investmentHoldingDate.getId()));
            log.info("Removing unentitled portfolio ids {}", portfolioIdsToRemove);
        }

        LogUtil.logPortfolioHoldingPerformance("filterDatesPostSuppression", stopwatch.elapsed(TimeUnit.MILLISECONDS), request.getRequestId());
        return holdingDateResponse;
    }

    private void processPortfolioId(
            Entry<String, List<PortfolioDateValue>> datesForPortfolioId,
            Set<String> portfolioIdsToRemove,
            Set<String> entitledPackages,
            HoldingDataRequest request,
            CachedEntitlement entitlement,
            Map<HoldingsView, Set<String>> holdingViewToApplicablePackages) {

        // get entitled holdingsViews for portfolio id
        Set<String> entitledHoldingsViewsForPortfolioId = getEntitledHoldingsViewsForPortfolioId(
                datesForPortfolioId.getValue(), entitledPackages, request, holdingViewToApplicablePackages);

        // if entitledHoldingsViewsForPortfolioId is empty, portfolio id is not entitled. Flag and move on
        if (entitledHoldingsViewsForPortfolioId.isEmpty()) {
            portfolioIdsToRemove.add(datesForPortfolioId.getKey());
            return;
        }

        // check entitled holdings views against idMappers for portfolioId
        List<IdMapper> idMappersForPortfolioId = getIdMappersForPortfolioId(request, datesForPortfolioId);
        Set<String> entitledHoldingViewsForIdMappers = filterEntitledHoldingViewsUsingIdMappers(
                idMappersForPortfolioId, entitledHoldingsViewsForPortfolioId,
                entitlement, holdingViewToApplicablePackages);

        if (entitledHoldingViewsForIdMappers.isEmpty()) {
            portfolioIdsToRemove.add(datesForPortfolioId.getKey());
            return;
        }

        List<PortfolioDateValue> removedPdvList = datesForPortfolioId.getValue().stream()
                .filter(pdv -> !entitledHoldingViewsForIdMappers.contains(pdv.getHoldingsView()))
                .toList();

        datesForPortfolioId.getValue().removeAll(removedPdvList);
        if (!removedPdvList.isEmpty()) {
            log.info("Portfolio [{}]: removed date values => {}", datesForPortfolioId.getKey(), removedPdvList);
        }
    }

    private Set<String> getEntitledHoldingsViewsForPortfolioId(
            List<PortfolioDateValue> portfolioDateValues,
            Set<String> entitledPackages,
            HoldingDataRequest request,
            Map<HoldingsView, Set<String>> holdingViewToApplicablePackages) {
        Set<String> distinctHoldingsViewsForPortfolioId = portfolioDateValues
                .stream().map(PortfolioDateValue::getHoldingsView).collect(Collectors.toSet());

        Set<String> entitledHoldingsViewsForPortfolioId = new HashSet<>();
        // get entitled holdingsViews for portfolio id
        for (String holdingsView : distinctHoldingsViewsForPortfolioId) {
            HoldingsView wrappedHoldingsView = new HoldingsView(holdingsView);
            Set<String> applicablePackagesForCurrentHoldingsViews = holdingViewToApplicablePackages.computeIfAbsent(wrappedHoldingsView,
                    hv -> packageEntitlementService.getApplicablePackages(request, hv));
            boolean hasRequiredPackage = packageEntitlementService.isEntitled(entitledPackages, applicablePackagesForCurrentHoldingsViews);
            if (hasRequiredPackage) {
                entitledHoldingsViewsForPortfolioId.add(holdingsView);
            }
        }
        return entitledHoldingsViewsForPortfolioId;
    }

    private Set<String> filterEntitledHoldingViewsUsingIdMappers(
            List<IdMapper> idMappersForPortfolioId,
            Set<String> entitledHoldingsViewsForPortfolioId,
            CachedEntitlement entitlement,
            Map<HoldingsView, Set<String>> holdingViewToApplicablePackages) {
        Set<String> entitledHoldingViewsForIdMappers = new HashSet<>();
        for (String entitledHoldingView : entitledHoldingsViewsForPortfolioId) {
            Set<String> applicablePackagesForCurrentEntitledHoldingView = holdingViewToApplicablePackages.get(new HoldingsView(entitledHoldingView));
            List<CellMatch> applicableCellMatchesForEntitledHoldingsView = getApplicableCellMatches(entitlement, applicablePackagesForCurrentEntitledHoldingView);
            Set<String> secIdsToKeep = getSecIdsToKeep(idMappersForPortfolioId, applicableCellMatchesForEntitledHoldingsView);
            if (!secIdsToKeep.isEmpty()) {
                entitledHoldingViewsForIdMappers.add(entitledHoldingView);
            }
        }
        return entitledHoldingViewsForIdMappers;
    }

    public void applyDataPointFilter(InvestmentHoldingData holdings, Map<String, String> aliasToDpId, String useCase, CachedEntitlement entitlement) {
        if (getFilters().isEmpty()) {
            return;
        }
        for (Map.Entry<String, String> entry : aliasToDpId.entrySet()) {
            String datapointId = entry.getValue();
            if (getFilters().containsKey(datapointId)) {
                FilterRequest filterRequest = new HoldingFilterRequest(datapointId, entry.getKey(), useCase, entitlement, holdings);
                List<EntitlementDataPointFilter> entitlementDataPointFilters = getFilters().get(datapointId);
                for (EntitlementDataPointFilter filter : entitlementDataPointFilters) {
                    filter.apply(filterRequest);
                }
            }
        }
    }

    public void applyPostDataRetrievalFilterOnBatch(StreamHoldingRequest streamRequest, List<Map<String, Object>> batchedHoldingDetails) {
        batchedHoldingDetails.forEach(holdingDetails -> applyStreamingPostDataRetrievalFilter(streamRequest, holdingDetails));
    }


    public void getCodeValueMappings(List<Map<String, Object>> cachedOrderedHoldingDetails, boolean hasCodeMappingDps) {
        if(hasCodeMappingDps) {
            cachedOrderedHoldingDetails.forEach(detail -> {
                detail.entrySet().forEach(entry -> {
                    if (PhDataPointUtil.needCodeMapping(entry.getKey())) {
                        if (entry.getValue() instanceof String) {
                            entry.setValue(getCodeValueMappings(entry.getKey(), entry.getValue()));
                        }
                        if (entry.getValue() instanceof V) {
                            V originalValue = (V) entry.getValue();
                            entry.setValue(buildTimeSeriesCodeValueMapping(entry.getKey(), originalValue));
                        }
                    }
                });
            });
        }
    }

    private MultipleValueDataEntry getCodeValueMappings(String key, Object code) {
        String codeString = (String) code;
        if(StringUtils.isEmpty(codeString)) {
            return null;
        }
        String value = CodeMappings.getCodeMapping(key, codeString).orElseGet(() -> null);

        return StringUtils.isNotEmpty(value) ? new MultipleValueDataEntry(codeString, value) : null;
    }

    private Map<String, Object> buildTimeSeriesCodeValueMapping(String dpId, V timeSeriesValue) {
        Map<String, Object> result = new HashMap<>();
        result.put("i", timeSeriesValue.getI());
        result.put("v", getCodeValueMappings(dpId, timeSeriesValue.getV()));
        return result;
    }

    private void applyStreamingPostDataRetrievalFilter(StreamHoldingRequest streamRequest, Map<String, Object> holdingDetails) {
        Map<String, List<EntitlementDataPointFilter>> filters = getFilters();
        if (MapUtils.isEmpty(filters)) {
            return;
        }
        Set<String> requestedDpsWithFilters = new HashSet<>();
        for (String dpWithFilter : filters.keySet()) {
            if (streamRequest.getAliasMap().containsKey(dpWithFilter)) {
                requestedDpsWithFilters.add(dpWithFilter);
            }
        }
        if (CollectionUtils.isEmpty(requestedDpsWithFilters)) {
            return;
        }
        for (String datapointId: requestedDpsWithFilters) {
            if (filters.containsKey(datapointId)) {
                StreamHoldingFilterRequest filterRequest = new StreamHoldingFilterRequest(
                        datapointId,
                        streamRequest.getHoldingDataRequest().getUseCase(),
                        streamRequest.getCachedEntitlement(),
                        datapointId,
                        holdingDetails
                );
                List<EntitlementDataPointFilter> entitlementDataPointFilters = filters.get(datapointId);
                for (EntitlementDataPointFilter filter : entitlementDataPointFilters) {
                    filter.apply(filterRequest);
                }
            }
        }
    }

    private Set<String> getSecIdsToKeep(List<IdMapper> idMappers, List<CellMatch> applicableCellMatches) {
        Set<String> secIdsToKeep = new HashSet<>();
        for (IdMapper idMapper : idMappers) {
            for (CellMatch cellMatch : applicableCellMatches) {
                if (idMapperEntitlementService.isIdEntitled(idMapper, cellMatch, false, false).isEntitled()) {
                    secIdsToKeep.add(idMapper.getInvestmentId());
                    break;
                }
            }
        }
        Set<String> secIdsToRemove = idMappers.stream()
                .map(IdMapper::getInvestmentId)
                .filter(id -> !secIdsToKeep.contains(id))
                .collect(Collectors.toSet());
        log.info("SecIds removed for entitlement check: {}", secIdsToRemove);
        return secIdsToKeep;
    }

    private static List<IdMapper> getIdMappersForPortfolioId(HoldingDataRequest request, Entry<String, List<PortfolioDateValue>> datesForPortfolioId) {
        Set<String> secIdsForPortfolioId = request.getMasterPortfolioMap().get(datesForPortfolioId.getKey());
        return request.getIdMappers()
                .stream()
                .filter(idMapper -> secIdsForPortfolioId.contains(idMapper.getInvestmentId()))
                .toList();
    }

    private static Map<String, List<PortfolioDateValue>> mapPortfolioIdsToDates(PortfolioHoldingDateResponse holdingDateResponse) {
        return holdingDateResponse.getInvestmentHoldingDates()
                .stream()
                .collect(Collectors.toMap(InvestmentHoldingDate::getId, InvestmentHoldingDate::getPortfolioDateValues));
    }

    private static List<CellMatch> getApplicableCellMatches(CachedEntitlement entitlement, Set<String> applicablePackages) {
        return entitlement.getCellMatchList().stream()
                .filter(cellMatch -> applicablePackages.contains(cellMatch.getPackageId()))
                .toList();
    }

    private static void removeEntriesFromMasterPortfolioMap(HoldingDataRequest request, Set<String> secIdsToKeep) {
        request.getMasterPortfolioMap().entrySet().removeIf(entry -> Collections.disjoint(secIdsToKeep, entry.getValue()));
    }

    private static void removeSecIdsFromIdMappers(HoldingDataRequest request, Set<String> secIdsToKeep) {
        request.getIdMappers().removeIf(idMapper -> !secIdsToKeep.contains(idMapper.getInvestmentId()));
    }

    private static void removeInvestmentIdsFromRequest(HoldingDataRequest request, Set<String> secIdsToKeep) {
        if (request.getIdType() == InvestmentApiIdType.SEC_ID) {
            request.getInvestments().removeIf(investment -> !secIdsToKeep.contains(investment.getId()));
        } else {
            Map<String, Set<String>> masterPortfolioMap = request.getMasterPortfolioMap();
            Set<String> masterPortfolioIdsToFilter = masterPortfolioMap.entrySet().stream()
                    .filter(entry -> Collections.disjoint(entry.getValue(), secIdsToKeep))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
            boolean hasRemovedItems = request.getInvestments()
                    .removeIf(masterPortfolio -> masterPortfolioIdsToFilter.contains(masterPortfolio.getId()));
            if (hasRemovedItems) {
                log.info("Removed investments [{}] due to lacking entitlements.", masterPortfolioIdsToFilter);
            }
        }
    }
}
