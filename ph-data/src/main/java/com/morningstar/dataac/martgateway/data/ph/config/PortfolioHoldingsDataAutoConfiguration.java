package com.morningstar.dataac.martgateway.data.ph.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.morningstar.dataac.martgateway.core.common.config.YamlPropertySourceFactory;
import com.morningstar.dataac.martgateway.core.common.repository.RedisRepo;
import com.morningstar.dataac.martgateway.core.common.repository.S3Client;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointAutoConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.config.EnableDataPointLoaders;
import com.morningstar.dataac.martgateway.core.entitlement.config.EntitlementAutoConfiguration;
import com.morningstar.dataac.martgateway.core.entitlement.config.filterloader.EnableEntitlementFilterLoaders;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementCacheService;
import com.morningstar.dataac.martgateway.core.entitlement.service.IdMapperEntitlementService;
import com.morningstar.dataac.martgateway.core.uim.config.UimAutoConfiguration;
import com.morningstar.dataac.martgateway.core.uim.service.UimTokenService;
import com.morningstar.dataac.martgateway.data.ph.repository.issuerid.IssuerMappingStore;
import com.morningstar.dataac.martgateway.data.ph.repository.PortfolioToSecIdsDao;
import com.morningstar.dataac.martgateway.data.ph.repository.holdingdata.BaseLevelDao;
import com.morningstar.dataac.martgateway.data.ph.repository.holdingdata.LookThroughDao;
import com.morningstar.dataac.martgateway.data.ph.repository.holdingdata.PortfolioHoldingDaoFactory;
import com.morningstar.dataac.martgateway.data.ph.repository.s3.DefaultS3StreamProvider;
import com.morningstar.dataac.martgateway.data.ph.repository.s3.S3StreamProvider;
import com.morningstar.dataac.martgateway.data.ph.repository.suppression.SuppressionApiClient;
import com.morningstar.dataac.martgateway.data.ph.service.HoldingDateIdConversionService;
import com.morningstar.dataac.martgateway.data.ph.service.HoldingDateResponseMapper;
import com.morningstar.dataac.martgateway.data.ph.service.HoldingDateService;
import com.morningstar.dataac.martgateway.data.ph.service.PortfolioHoldingsDataService;
import com.morningstar.dataac.martgateway.data.ph.service.delta.PortfolioHoldingDeltaService;
import com.morningstar.dataac.martgateway.data.ph.service.enrichment.HoldingDetailsEnrichmentProcessor;
import com.morningstar.dataac.martgateway.data.ph.service.enrichment.HoldingDetailsEnrichmentService;
import com.morningstar.dataac.martgateway.data.ph.service.entitlement.FixedIncomeDataPointFilterService;
import com.morningstar.dataac.martgateway.data.ph.service.entitlement.PortfolioHoldingDataPointFilterService;
import com.morningstar.dataac.martgateway.data.ph.service.entitlement.PortfolioHoldingPackageEntitlementService;
import com.morningstar.dataac.martgateway.data.ph.service.entitlement.PortfolioHoldingRequestPreFilterService;
import com.morningstar.dataac.martgateway.data.ph.service.holdingdate.HoldingDateHandlerFactory;
import com.morningstar.dataac.martgateway.data.ph.service.parsers.baselevel.DatumStreamerFactory;
import com.morningstar.dataac.martgateway.data.ph.service.parsers.lookthrough.LookThroughStreamHandlerFactory;
import io.netty.channel.ChannelOption;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient;
import org.springframework.data.elasticsearch.client.reactive.ReactiveRestClients;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.tcp.TcpClient;

@Configuration
@EnableDataPointLoaders
@EnableEntitlementFilterLoaders
@PropertySource(ignoreResourceNotFound = false, value = {
        "classpath:portfolioholdings.yaml",
        "classpath:portfolioholdings-${spring.profiles.active}.yaml"
}, factory = YamlPropertySourceFactory.class)
@EnableConfigurationProperties(PortfolioHoldingsProperties.class)
@AutoConfigureAfter({EntitlementAutoConfiguration.class, DataPointAutoConfiguration.class, UimAutoConfiguration.class})
public class PortfolioHoldingsDataAutoConfiguration {
    private static final int MAX_WAIT = 5;
    private final PortfolioHoldingsProperties portfolioHoldingsProperties;

    public PortfolioHoldingsDataAutoConfiguration(PortfolioHoldingsProperties portfolioHoldingsProperties){
        this.portfolioHoldingsProperties = portfolioHoldingsProperties;
    }

    @Bean(name = "suppressionApiClient")
    @ConditionalOnMissingBean(name = "suppressionApiClient")
    public SuppressionApiClient suppressionApiClient(UimTokenService uimTokenService) {
        int timeout = portfolioHoldingsProperties.getSuppressionApi().getTimeout();
        String url = portfolioHoldingsProperties.getSuppressionApi().getBaseUrl();

        return new SuppressionApiClient(
                WebClient.builder()
                        .clientConnector(new ReactorClientHttpConnector(HttpClient.from(TcpClient.create()
                                .wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG)
                                .option(ChannelOption.SO_KEEPALIVE, Boolean.TRUE)
                                .option(ChannelOption.TCP_NODELAY, Boolean.TRUE)
                                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, MAX_WAIT * 1000)//Connect Timeout
                                .doOnConnected(
                                        c -> c.addHandlerLast(new ReadTimeoutHandler(timeout, TimeUnit.SECONDS))
                                                .addHandlerLast(new WriteTimeoutHandler(MAX_WAIT, TimeUnit.SECONDS))))))
                        .exchangeStrategies(ExchangeStrategies.builder()
                                .codecs(configure -> configure.defaultCodecs().maxInMemorySize(-1))
                                .build())
                        .baseUrl(url)
                        .build(),
                uimTokenService,
                portfolioHoldingsProperties.getSuppressionApi().getMaxPortfolioIdsPerRequest()
        );
    }

    @Bean(name = "holdingDateService")
    @ConditionalOnMissingBean(name = "holdingDateService")
    public HoldingDateService holdingDateService(
            SuppressionApiClient suppressionApiClient,
            HoldingDateResponseMapper holdingDateResponseMapper,
            HoldingDateIdConversionService holdingDateIdConversionService,
            PortfolioHoldingDeltaService portfolioHoldingDeltaService)
    {
        return new HoldingDateService(suppressionApiClient, holdingDateResponseMapper, holdingDateIdConversionService, portfolioHoldingDeltaService);
    }

    @Bean(name = "holdingDateIdConversionService")
    @ConditionalOnMissingBean(name = "holdingDateIdConversionService")
    public HoldingDateIdConversionService holdingDateIdConversionService(IdMapUtil idMapUtil, PortfolioToSecIdsDao portfolioToSecIdsDao)
    {
        return new HoldingDateIdConversionService(idMapUtil, portfolioToSecIdsDao);
    }

    @Bean(name = "portfolioToSecIdsDao")
    @ConditionalOnMissingBean(name = "portfolioToSecIdsDao")
    public PortfolioToSecIdsDao portfolioToSecIdsDao(@Qualifier("metaDataRedisClient") RedisRepo metaDataRedisClient)
    {
        return new PortfolioToSecIdsDao(metaDataRedisClient);
    }

    @Bean(name = "holdingDateResponseMapper")
    @ConditionalOnMissingBean(name = "holdingDateResponseMapper")
    public HoldingDateResponseMapper holdingDateResponseMapper(HoldingDateHandlerFactory holdingDateHandlerFactory) {
        return new HoldingDateResponseMapper(holdingDateHandlerFactory);
    }

    @Bean(name = "holdingDateHandlerFactory")
    @ConditionalOnMissingBean(name = "holdingDateHandlerFactory")
    public HoldingDateHandlerFactory holdingDateHandlerFactory(@Qualifier("s3ClientGlobal") S3Client s3Client) {
        return new HoldingDateHandlerFactory(
                s3Client,
                portfolioHoldingsProperties.getAws().getS3().getLookThroughBucket(),
                portfolioHoldingsProperties.getAws().getS3().getLookThroughPrefix()
        );
    }

    @Bean(name = "portfolioHoldingDataPointFilterService")
    @ConditionalOnMissingBean(name = "portfolioHoldingDataPointFilterService")
    public PortfolioHoldingDataPointFilterService portfolioHoldingDataPointFilterService(
            PortfolioHoldingPackageEntitlementService portfolioHoldingPackageEntitlementService
    ) {
        return new PortfolioHoldingDataPointFilterService(
                Map.of("HS762", Set.of("17"), "OS05K", Set.of("17")),
                new IdMapperEntitlementService(),
                portfolioHoldingPackageEntitlementService);
    }
    
    @Bean(name = "requestPreFilterService")
    @ConditionalOnMissingBean(name = "requestPreFilterService")
    public PortfolioHoldingRequestPreFilterService requestPreFilterService(
            PortfolioHoldingPackageEntitlementService entitlementService,
            PortfolioHoldingDataPointFilterService dataPointFilterService,
            HoldingDateIdConversionService holdingDateIdConversionService,
            FixedIncomeDataPointFilterService fixedIncomeDataPointFilterService
    ) {
        return new PortfolioHoldingRequestPreFilterService(entitlementService,
                dataPointFilterService,
                holdingDateIdConversionService,
                fixedIncomeDataPointFilterService);
    }

    @Bean(name = "fixedIncomeDataPointFilterService")
    @ConditionalOnMissingBean(name = "fixedIncomeDataPointFilterService")
    public FixedIncomeDataPointFilterService fixedIncomeDataPointFilterService(
            EntitlementCacheService entitlementCacheService,
            @Value("${martgateway.fixed-income.packages}") String csvPackages) {
        return new FixedIncomeDataPointFilterService(entitlementCacheService, csvPackages);
    }

    @Bean(name = "portfolioHoldingPackageEntitlementService")
    @ConditionalOnMissingBean(name = "portfolioHoldingPackageEntitlementService")
    public PortfolioHoldingPackageEntitlementService portfolioHoldingPackageEntitlementService() {
        return new PortfolioHoldingPackageEntitlementService();
    }

    @Bean(name = "portfolioHoldingDataService")
    @ConditionalOnMissingBean(name = "portfolioHoldingDataService")
    public PortfolioHoldingsDataService portfolioHoldingDataService(PortfolioHoldingDaoFactory portfolioHoldingDaoFactory) {
        return new PortfolioHoldingsDataService(portfolioHoldingDaoFactory);
    }

    @Bean(name = "portfolioHoldingDaoFactory")
    @ConditionalOnMissingBean(name = "portfolioHoldingDaoFactory")
    public PortfolioHoldingDaoFactory portfolioHoldingDaoFactory(
            BaseLevelDao baseLevelDao,
            LookThroughDao lookThroughDao
    ) {
        return new PortfolioHoldingDaoFactory(baseLevelDao, lookThroughDao);
    }

    @Bean(name = "baseLevelDao")
    @ConditionalOnMissingBean(name = "baseLevelDao")
    public BaseLevelDao baseLevelDao(
            DatumStreamerFactory datumStreamerFactory,
            @Qualifier("defaultS3StreamProvider") S3StreamProvider streamProvider) {
        return new BaseLevelDao(
                streamProvider,
                datumStreamerFactory,
                portfolioHoldingsProperties.getAws().getS3().getBaseLevelBucket()
        );
    }

    @Bean(name = "defaultS3StreamProvider")
    public S3StreamProvider s3StreamProvider(@Qualifier("s3Helper") S3Client s3Client) {
        return new DefaultS3StreamProvider(s3Client);
    }

    @Bean(name = "datumStreamerFactory")
    @ConditionalOnMissingBean(name = "datumStreamerFactory")
    public DatumStreamerFactory dataStreamerFactory(
            HoldingDetailsEnrichmentProcessor holdingDetailsEnrichmentProcessor,
            PortfolioHoldingDataPointFilterService portfolioHoldingDataPointFilterService
    ) {
        return new DatumStreamerFactory(holdingDetailsEnrichmentProcessor, portfolioHoldingDataPointFilterService);
    }

    @Bean(name = "lookThroughDao")
    @ConditionalOnMissingBean(name = "lookThroughDao")
    public LookThroughDao lookThroughDao(@Qualifier("defaultS3StreamProvider") S3StreamProvider streamProvider, LookThroughStreamHandlerFactory lookThroughStreamHandlerFactory) {
        return new LookThroughDao(streamProvider,
                lookThroughStreamHandlerFactory,
                portfolioHoldingsProperties.getAws().getS3().getLookThroughBucket(),
                portfolioHoldingsProperties.getAws().getS3().getLookThroughPrefix());
    }

    @Bean(name = "lookThroughStreamHandlerFactory")
    @ConditionalOnMissingBean(LookThroughStreamHandlerFactory.class)
    public LookThroughStreamHandlerFactory lookThroughStreamHandlerFactory(
            HoldingDetailsEnrichmentProcessor holdingDetailsEnrichmentProcessor,
            PortfolioHoldingDataPointFilterService portfolioHoldingDataPointFilterService) {
        return new LookThroughStreamHandlerFactory(holdingDetailsEnrichmentProcessor, portfolioHoldingDataPointFilterService);
    }

    @Bean(name = "holdingDetailsEnrichmentProcessor")
    @ConditionalOnMissingBean(HoldingDetailsEnrichmentProcessor.class)
    public HoldingDetailsEnrichmentProcessor holdingDetailsEnrichmentProcessor(
            List<HoldingDetailsEnrichmentService> enrichers) {
        return new HoldingDetailsEnrichmentProcessor(enrichers);
    }

    @Bean(name = "issuerMappingStore")
    @ConditionalOnMissingBean(IssuerMappingStore.class)
    public IssuerMappingStore issuerMappingStore(
            @Qualifier("s3Helper") S3Client s3Client,
            @Value("${martcommon.aws.s3.bucket}") String bucket,
            @Value("${martgateway.phdata.issuer-mapping.eager-init:false}") boolean eagerInit) {
        IssuerMappingStore issuerMappingStore = new IssuerMappingStore(s3Client, bucket);
        if (eagerInit) {
            issuerMappingStore.init();
        }
        return issuerMappingStore;
    }


    @Primary
    @Bean(name = "amazonS3Global")
    @ConditionalOnMissingBean(name = "amazonS3Global")
    public AmazonS3 getS3ClientGlobal() {
        return AmazonS3ClientBuilder.standard()
                .withForceGlobalBucketAccessEnabled(true)
                .withClientConfiguration(new ClientConfiguration().withMaxConnections(500)
                        .withConnectionTimeout(60 * 1000 * 5)
                        .withMaxErrorRetry(7)).build();
    }

    @Primary
    @Bean(name = "s3ClientGlobal")
    @ConditionalOnMissingBean(name = "s3ClientGlobal")
    public S3Client s3ClientGlobal(@Qualifier("amazonS3Global") AmazonS3 amazonS3) {
        return new S3Client("", amazonS3);
    }

    @Bean(name = "esChangeLogClient")
    @ConditionalOnMissingBean(name = "esChangeLogClient")
    public ReactiveElasticsearchClient reactiveElasticsearchClient(@Value("${martgateway.delta.esChangeLogUrl}") String esChangeLogUrl) {
        org.springframework.data.elasticsearch.client.ClientConfiguration clientConfiguration = org.springframework.data.elasticsearch.client.ClientConfiguration.builder()
                .connectedTo(esChangeLogUrl)
                .build();
        return ReactiveRestClients.create(clientConfiguration);
    }

    @Bean(name = "portfolioHoldingDeltaService")
    @ConditionalOnMissingBean(name = "portfolioHoldingDeltaService")
    public PortfolioHoldingDeltaService portfolioHoldingDeltaService(@Qualifier("esChangeLogClient") ReactiveElasticsearchClient esChangeLogClient) {
        return new PortfolioHoldingDeltaService(esChangeLogClient);
    }
}
