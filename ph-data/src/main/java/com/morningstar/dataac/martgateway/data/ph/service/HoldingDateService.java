package com.morningstar.dataac.martgateway.data.ph.service;

import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.data.ph.entity.HoldingDataRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.PortfolioHoldingDateResponse;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.SuppressionApiRequest;
import com.morningstar.dataac.martgateway.data.ph.entity.suppression.SuppressionApiResponse;
import com.morningstar.dataac.martgateway.data.ph.exception.PortfolioHoldingException;
import com.morningstar.dataac.martgateway.data.ph.repository.suppression.SuppressionApiClient;
import com.morningstar.dataac.martgateway.data.ph.service.delta.PortfolioHoldingDeltaService;
import com.morningstar.dataac.martgateway.data.ph.util.SuppressionClientIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Set;

@Slf4j
public class HoldingDateService {

    private final SuppressionApiClient suppressionApiClient;
    private final HoldingDateResponseMapper holdingDateResponseMapper;
    private final HoldingDateIdConversionService holdingDateIdConversionService;
    private final PortfolioHoldingDeltaService portfolioHoldingDeltaService;

    public HoldingDateService(
            SuppressionApiClient suppressionApiClient,
            HoldingDateResponseMapper holdingDateResponseMapper,
            HoldingDateIdConversionService holdingDateIdConversionService,
            PortfolioHoldingDeltaService portfolioHoldingDeltaService) {
        this.suppressionApiClient = suppressionApiClient;
        this.holdingDateResponseMapper = holdingDateResponseMapper;
        this.holdingDateIdConversionService = holdingDateIdConversionService;
        this.portfolioHoldingDeltaService = portfolioHoldingDeltaService;
    }

    /**
     * @param request request from /holding-date API or /holding-data service
     * @param mapBackToSecIds if idType = SecId, determines if response should be mapped back to SecIds from MasterPortfolioIds. If the call comes from /holding-data service, we don't want to map back because we need to fetch data using the MasterPortfolioIds
     * @return PortfolioHoldingDateResponse
     */
    public Mono<PortfolioHoldingDateResponse> getHoldingDates(
            HoldingDataRequest request,
            CachedEntitlement cachedEntitlement,
            boolean mapBackToSecIds) {
        if (CollectionUtils.isNotEmpty(request.getInvestmentHoldingDates())) {
            PortfolioHoldingDateResponse responseFromAsync = new PortfolioHoldingDateResponse();
            responseFromAsync.setIdMappers(request.getIdMappers());
            responseFromAsync.setInvestmentHoldingDates(request.getInvestmentHoldingDates());
            return Mono.just(responseFromAsync);
        }
        if (SuppressionClientIdUtil.isSuppressionClientIdValid(request, cachedEntitlement)) {
            Map<String, Set<String>> masterPortfolioMap = getFinalMasterPortfolioMap(request);
            SuppressionApiRequest suppressionApiRequest = SuppressionApiRequest.from(request.getPortfolioSetting(), masterPortfolioMap.keySet());
            Mono<SuppressionApiResponse> suppressionApiResponseMono = suppressionApiClient.getSuppressionData(suppressionApiRequest, request.getRequestId());
            if (request.getDeltaStartTime() != null) {
                suppressionApiResponseMono = suppressionApiResponseMono.flatMap(suppressionApiResponse -> portfolioHoldingDeltaService.filterSuppressionForDelta(request, suppressionApiResponse));
            }
            return suppressionApiResponseMono.map(suppressionApiResponse -> holdingDateResponseMapper.from(suppressionApiResponse, request, masterPortfolioMap, mapBackToSecIds))
                    .onErrorMap(e -> new PortfolioHoldingException(Status.INTERNAL_ERROR.getCode(), "Suppression Api Error: " + e.getMessage(), e));
        } else {
            throw new PortfolioHoldingException("401", "Suppression client id does not match token");
        }
    }

    public PortfolioHoldingDateResponse getAsyncHoldingDates(HoldingDataRequest request,  Map<String, Set<String>> masterPortfolioMapForBatch) {
        SuppressionApiRequest suppressionApiRequest = SuppressionApiRequest
                .from(request.getPortfolioSetting(), masterPortfolioMapForBatch.keySet());
        Mono<SuppressionApiResponse> suppressionApiResponseMono = suppressionApiClient.getSuppressionData(suppressionApiRequest, request.getRequestId());
        if (request.getDeltaStartTime() != null) {
            suppressionApiResponseMono = suppressionApiResponseMono.flatMap(suppressionApiResponse -> portfolioHoldingDeltaService.filterSuppressionForDelta(request, suppressionApiResponse));
        }
        return suppressionApiResponseMono
                .map(suppressionApiResponse -> holdingDateResponseMapper.from(suppressionApiResponse, request, masterPortfolioMapForBatch, false))
                .onErrorMap(e -> new PortfolioHoldingException(Status.INTERNAL_ERROR.getCode(), "Suppression Api Error: " + e.getMessage(), e))
                .block();
    }

    // masterPortfolioMap can be provided by PortfolioHoldingDataGateway
    private Map<String, Set<String>> getFinalMasterPortfolioMap(HoldingDataRequest request) {
        if (MapUtils.isEmpty(request.getMasterPortfolioMap())) {
            return holdingDateIdConversionService.getMasterPortfolioIdsMap(request);
        } else {
            return request.getMasterPortfolioMap();
        }
    }
}
