package com.morningstar.dataac.martgateway.data.ph.service.parsers.deriveddata;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.data.ph.entity.RawHoldingData;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public class DerivedDataHandler {

    public Object processDeriveType(RawHoldingData rawHoldingData, DataPoint dataPoint) {
        // LT only has computeShorted
        return switch (dataPoint.getDeriveType().split("\\[")[0]) {
            case "computeShorted" -> processComputeShorted(rawHoldingData, dataPoint);
            case "duplicateDataPointMapping", "codeValueMapping" ->  processMapping(rawHoldingData, dataPoint);
            case "showIfParentIsPresent" -> showIfParentIsPresent(rawHoldingData, dataPoint);
            case "lookThroughStorageId" -> rawHoldingData.getValueForType(rawHoldingData.getDataPointPath(dataPoint), "int");
            default -> null;
        };
    }

    //deriveType = duplicateDataPointMapping -> Copy the value from an existing data point configuration
    //deriveType = codeValueMapping -> Copy the code value and later convert the code to code-value response in PortfolioHoldingDataPointFilterService.getCodeValueMappings(...)
    private Object processMapping(RawHoldingData rawHoldingData, DataPoint dataPoint) {
        DataPoint mappingSrc = dataPoint.getMappingSrc();
        if (mappingSrc == null) {
            return null;
        }
        String mappingSrcPath = rawHoldingData.getDataPointPath(mappingSrc);
        if (StringUtils.isEmpty(mappingSrcPath)) {
            return null;
        }
        String contentType = StringUtils.isEmpty(dataPoint.getContentType()) ? "string" : dataPoint.getContentType();
        return rawHoldingData.getValueForType(mappingSrcPath, contentType);
    }

    private Object processComputeShorted(RawHoldingData rawHoldingData, DataPoint dataPoint) {
        Object rawMarketValue = rawHoldingData.getValueForType(rawHoldingData.getMarketValuePath(), "double");
        Object value = rawHoldingData.getValueForType(rawHoldingData.getDataPointPath(dataPoint), dataPoint.getContentType());
        if (rawMarketValue == null) {
            return value;
        }
        if (value == null) {
            return null;
        }
        double marketValue = (double) rawMarketValue;
        double valueDouble = (double) value;
        // Ensure valueDouble has the same sign as marketValue
        double result = Math.abs(valueDouble) * (marketValue >= 0 ? 1 : -1);
        String deriveType = dataPoint.getDeriveType();

        if (deriveType.contains(rawHoldingData.getPortfolioDepth().getValue()) && deriveType.contains("multiplier=")) {
            result *= Double.parseDouble(StringUtils.substringBetween(deriveType, "multiplier=", "]"));
            result = BigDecimal.valueOf(result).setScale(5, RoundingMode.HALF_UP).doubleValue();
        }
        return result;
    }

    // showIfParentIsPresent has the parent's path in the [square brackets]
    private Object showIfParentIsPresent(RawHoldingData rawHoldingData, DataPoint dataPoint) {
        String deriveType = dataPoint.getDeriveType();
        String parent = StringUtils.substringBetween(deriveType, "[", "]");
        if (rawHoldingData.get(parent) != null) {
            return rawHoldingData.getValueForType(rawHoldingData.getDataPointPath(dataPoint), dataPoint.getContentType());
        }
        return null;
    }

}
