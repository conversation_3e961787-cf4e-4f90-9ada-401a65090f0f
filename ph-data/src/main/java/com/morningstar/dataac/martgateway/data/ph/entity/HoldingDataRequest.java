package com.morningstar.dataac.martgateway.data.ph.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.UseCaseRequest;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.InvestmentApiIdType;
import com.morningstar.dataac.martgateway.data.ph.service.AsyncFixedIncomeDataCache;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder(toBuilder = true)
public class HoldingDataRequest implements UseCaseRequest {

    private String userId;
    private String configId;
    private String useCase;
    private Instant deltaStartTime;
    private InvestmentApiIdType idType;
    private Set<Investment> investments;
    private PortfolioSetting portfolioSetting;
    @JsonIgnore
    private List<IdMapper> idMappers;
    @JsonIgnore
    private Map<String, Set<String>> masterPortfolioMap;
    @JsonIgnore
    private String requestId;
    @JsonIgnore
    private CachedEntitlement cachedEntitlement;
    @JsonIgnore
    private List<InvestmentHoldingDate> investmentHoldingDates;
    @JsonIgnore
    private boolean isAsync;
    @JsonIgnore
    private AsyncFixedIncomeDataCache asyncFixedIncomeDataCache;
    private Integer blBatchSize;
    private Integer ltBatchSize;
    @Builder.Default
    private boolean readCache = true;
    private String issuerCache; // redis or rdb


    public static HoldingDataRequest copyWithNewHoldingDates(HoldingDataRequest original, List<InvestmentHoldingDate> investmentHoldingDates) {
        return original.toBuilder()
                .investmentHoldingDates(investmentHoldingDates)
                .build();
    }
}
