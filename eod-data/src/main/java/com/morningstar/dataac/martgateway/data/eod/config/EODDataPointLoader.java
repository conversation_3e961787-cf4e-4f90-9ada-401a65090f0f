package com.morningstar.dataac.martgateway.data.eod.config;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.data.eod.filter.GatewayFilterRegistry;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointLoaderInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.AbstractXmlDataPointLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderDefinition;

import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.Node;

import java.util.Arrays;
import java.util.List;

import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.ALIAS;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.COLUMN;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATABASE_SCHEMA;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATAPOINT_GROUP;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATA_TYPE;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.FILTER;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.GROUP;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.ID;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.IDLEVEL;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.IS_REQUIRED;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.PRIMARYSRC;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.PARSE_FUNCTION;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.STORE_PROCEDURE;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.TABLES;

@DataPointLoaderDefinition(info = DataPointLoaderInfo.EOD)
@Slf4j
public class EODDataPointLoader extends AbstractXmlDataPointLoader {

    private final GatewayFilterRegistry gatewayFilterRegistry;

    public EODDataPointLoader(DatapointConfigFileService datapointConfigFileService, DocumentLoader documentLoader, GatewayFilterRegistry gatewayFilterRegistry) {
        super(datapointConfigFileService, documentLoader);
        this.gatewayFilterRegistry = gatewayFilterRegistry;
    }

    @Override
    public void processDocument(DataPointLoaderContext context, Document document) {

        List<Node> nodes = document.getRootElement().selectNodes(DATAPOINT_GROUP);

        // Recreate specific datapoint object for specific configuration from the view
        nodes.stream()
                .map(Element.class::cast)
                .forEach(group -> {
                    String groupName = group.attributeValue(GROUP);
                    String idLevel = group.attributeValue(IDLEVEL);
                    String databaseSchema = group.attributeValue(DATABASE_SCHEMA);
                    String tables = group.attributeValue(TABLES);
                    String storeProcedure = group.attributeValue(STORE_PROCEDURE);
                    String dataType = group.attributeValue(DATA_TYPE);

                    List<Element> dataPointElements = group.elements();
                    dataPointElements.forEach(dpElement -> {
                        String id = dpElement.attributeValue(ID);

                        DataPoint dpView = context.getDataPointById(id);
                        if (dpView != null) {
                            String nid = dpView.getNid();
                            String column = dpElement.attributeValue(COLUMN);
                            String alias = dpElement.attributeValue(ALIAS);
                            String isRequired = dpElement.attributeValue(IS_REQUIRED);
                            String primarySrc = dpElement.attributeValue(PRIMARYSRC);
                            String filter = dpElement.attributeValue(FILTER);
                            List<String> filters = (filter != null) ? Arrays.asList(filter.split(",")) : null;
                            String parseFunction = dpElement.attributeValue(PARSE_FUNCTION);

                            EODDataPoint eodDataPoint = EODDataPoint.builder()
                                    .id(id)
                                    .nid(nid)
                                    .groupName(groupName)
                                    .dataType(dataType)
                                    .idLevel(idLevel)
                                    .databaseSchema(databaseSchema)
                                    .tables(tables)
                                    .storeProcedure(storeProcedure)
                                    .column(column)
                                    .alias(alias)
                                    .isRequired(Boolean.parseBoolean(isRequired))
                                    .filter(filters)
                                    .primarySrc(primarySrc)
                                    .parseFunction(parseFunction)
                                    .src(dpView.getSrc())
                                    .build();
                            // if a datapoint defines filter, register the filter
                            gatewayFilterRegistry.register(eodDataPoint);
                            // add required datapoint to the group map
                            if (eodDataPoint.isRequired()) {
                                DataPointRepository.addGroupRequiredDataPoint(eodDataPoint);
                            }

                            context.getDataPointMap().put(id, eodDataPoint);
                        }
                    });
                });

    }

}
