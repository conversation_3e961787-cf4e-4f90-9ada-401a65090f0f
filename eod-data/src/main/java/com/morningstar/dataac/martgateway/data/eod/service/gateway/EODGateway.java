package com.morningstar.dataac.martgateway.data.eod.service.gateway;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.dataac.martgateway.data.eod.filter.EntitlementEODErrorHandler;
import com.morningstar.dataac.martgateway.data.eod.filter.EntitlementEODHandler;
import com.morningstar.dataac.martgateway.data.eod.service.EODDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODMDB;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODPG;

/**
 * A gateway to retrieve data from relation database according to the configuration in
 * datapoint mapping
 */
@Slf4j
@Lazy
public class EODGateway implements Gateway<Result, MartRequest> {

    private final EODDataService eodDataService;
    private final EntitlementEODHandler entitlementEODHandler;
    private final EntitlementEODErrorHandler entitlementEODErrorHandler;

    public EODGateway(EODDataService eodDataService, EntitlementEODHandler entitlementEODHandler, EntitlementEODErrorHandler entitlementEODErrorHandler) {
        this.eodDataService = eodDataService;
        this.entitlementEODHandler = entitlementEODHandler;
        this.entitlementEODErrorHandler = entitlementEODErrorHandler;
    }

    @Override
    public Flux<Result> retrieve(MartRequest martRequest) {
        log.debug("EODGateway data retrieval");
        if (CollectionUtils.isEmpty(martRequest.getIds()) || CollectionUtils.isEmpty(martRequest.getDps())
    || StringUtils.hasText(martRequest.getStartDate())) {
            return Flux.empty();
        }

        // group the data points by group name
        Map<String, List<EODDataPoint>> groupDataPoints = martRequest.getDps().stream()
                .filter(dpId -> this.isEODDataPoint(DataPointRepository.getByNid(dpId)))
                .map(dpId -> (EODDataPoint)DataPointRepository.getByNid(dpId))
                .collect(Collectors.groupingBy(DataPoint::getGroupName));

        if (groupDataPoints.isEmpty()) {
            return Flux.empty();
        }

        // apply eod entitlement and set entitled investment id in martRequest
        entitlementEODHandler.apply(martRequest);

        // each group represent one atomic query with return result in flux
        Set<String> requestDataPointIdSet = new HashSet<>(martRequest.getDps());
        Flux<Result> resultFlux =  Flux.merge(groupDataPoints.values().stream()
                .map(dataPoints -> filterNotRequestedDataPoint(eodDataService.getData(martRequest,
                        withRequiredDataPoint(dataPoints)), requestDataPointIdSet)).toList())
                .subscribeOn(SchedulerConfiguration.getScheduler());

        List<String> dpIds = groupDataPoints.values().stream().flatMap(Collection::stream).map(DataPoint::getId).toList();
        return entitlementEODErrorHandler.apply(martRequest, resultFlux, dpIds);
    }

    private List<EODDataPoint> withRequiredDataPoint(List<EODDataPoint> dataPoints) {
        //get group required data point
        String groupName = dataPoints.get(0).getGroupName();

        List<DataPoint> requiredDataPoints = DataPointRepository.getGroupRequiredDataPoint(groupName);
        if (CollectionUtils.isEmpty(requiredDataPoints)) {
            return dataPoints;
        }

        List<EODDataPoint> responseDataPoints = new ArrayList<>(dataPoints);
        for (DataPoint requireDataPoint : requiredDataPoints) {
            if (requireDataPoint instanceof EODDataPoint && !dataPoints.contains(requireDataPoint)) {
                responseDataPoints.add((EODDataPoint) requireDataPoint);
            }
        }

        return responseDataPoints;
    }

    protected Flux<Result> filterNotRequestedDataPoint(Flux<Result> resultFlux, Set<String> requestDataPointIds) {
        return resultFlux.mapNotNull(result -> filterNotRequestedDataPoint(result, requestDataPointIds));
    }

    private Result filterNotRequestedDataPoint(Result result, Set<String> requestDataPointIds) {
        if (result instanceof CurrentResult currentResult) {
            final Map<String, String> requestValues = currentResult.getValues();

            Map<String, String> filteredValues = requestValues.keySet().stream()
                    .filter(dpId -> isRequiredDatapoint(requestValues.get(dpId), dpId, requestDataPointIds))
                    .collect(Collectors.toMap(this::getMapKey, requestValues::get));
            currentResult.setValues(filteredValues);

            return currentResult;
        } else if (result instanceof ErrorResult errorResult) {
            return requestDataPointIds.contains(errorResult.getDatapointId()) ? errorResult : null;
        }
        return result;
    }

    private String getMapKey(String dpId) {
        EODDataPoint eodDataPoint = (EODDataPoint) DataPointRepository.getByNid(dpId);
        String primarySrc = eodDataPoint.getPrimarySrc();
        return StringUtils.hasText(primarySrc) ? primarySrc : dpId;
    }

    private boolean isRequiredDatapoint(String value, String dpId, Set<String> requestDataPointIds) {
        if (!StringUtils.hasText(value)) { //only datapoint which has value
            return false;
        }
        EODDataPoint eodDataPoint = (EODDataPoint) DataPointRepository.getByNid(dpId);
        String primarySrc = eodDataPoint.getPrimarySrc();
        return requestDataPointIds.contains(dpId) ||
                (StringUtils.hasText(primarySrc) && requestDataPointIds.contains(primarySrc));
    }

    private boolean isEODDataPoint(DataPoint dataPoint) {
        if (!(dataPoint instanceof EODDataPoint)) {
            return false;
        }
        String src = dataPoint.getSrc();
        return src != null && (src.equals(EODPG) || src.equals(EODMDB));
    }
}
