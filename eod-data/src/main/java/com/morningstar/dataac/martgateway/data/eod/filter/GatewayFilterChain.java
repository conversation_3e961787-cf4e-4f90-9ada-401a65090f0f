package com.morningstar.dataac.martgateway.data.eod.filter;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * This gateway result filter chain will apply all register datapoint filters in a chain
 * to the defined pointcut methods
 */
@Slf4j
@Aspect
public class GatewayFilterChain {

    private final GatewayFilterRegistry gatewayFilterRegistry;

    public GatewayFilterChain(GatewayFilterRegistry gatewayFilterRegistry) {
        this.gatewayFilterRegistry = gatewayFilterRegistry;
    }

    @Around(value = "@annotation(com.morningstar.dataac.martgateway.data.eod.filter.ResultFilter) && args(martRequest, dataPoints)",
            argNames = "joinPoint, martRequest, dataPoints")
    public Flux<Result> doAfterFilters(ProceedingJoinPoint joinPoint,
                                       MartRequest martRequest,
                                       List<? extends DataPoint> dataPoints) throws Throwable {

        Object result = joinPoint.proceed();
        if (result instanceof Flux) {
            Flux<Result> resultFlux = (Flux<Result>) result;
            log.debug("Apply flux result filters");
            for (DataPoint dataPoint : dataPoints) {
                String id = dataPoint.getNid();
                if (gatewayFilterRegistry.hasResultFilter(id)) {
                    for (AbstractFluxResultFilter filter : gatewayFilterRegistry.getResultFilters(id)) {
                        resultFlux = filter.apply(martRequest, resultFlux, dataPoints);
                    }
                }
            }

            return resultFlux;
        } else {
            return Flux.empty();
        }
    }
}
