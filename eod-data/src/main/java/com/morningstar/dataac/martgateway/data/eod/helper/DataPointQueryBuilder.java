package com.morningstar.dataac.martgateway.data.eod.helper;

import java.util.List;

public final class DataPointQueryBuilder extends SimpleQueryBuilder {

    private DataPointQueryBuilder() {
        super();
    }

    /**
     * Build sql query string with the formated store procedure in data point configuration
     *
     * @param storeProcedure sql query string
     * @param columns list of column
     * @param schema database schema
     * @param table table or view in the database
     * @param investmentIds list of investment ids
     * @return query string
     */
    public static String buildWithInvestmentIds(String storeProcedure, List<String> columns,
                                                String schema, String table, List<String> investmentIds) {
        return String.format(storeProcedure, buildColumnList(columns), schema, table, buildIdsJoin(investmentIds));
    }

    /**
     * Build query string with the formated store procedure in data point configuration
     *
     * @param storeProcedure query string
     * @param investmentIds list of investment ids
     * @return query string
     */
    public static String buildWithInvestmentIds(String storeProcedure, List<String> investmentIds) {
        return String.format(storeProcedure, buildIdsJoin(investmentIds));
    }
}
