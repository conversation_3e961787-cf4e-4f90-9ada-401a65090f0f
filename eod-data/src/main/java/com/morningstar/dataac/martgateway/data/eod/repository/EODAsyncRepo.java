package com.morningstar.dataac.martgateway.data.eod.repository;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.Map;

@Slf4j
public class EODAsyncRepo {

    private final Scheduler scheduler;

    private final EODRepo eodRepo;

    public EODAsyncRepo(Scheduler scheduler, EODRepo eodRepo) {
        this.scheduler = scheduler;
        this.eodRepo = eodRepo;
    }

    public Flux<Map<String, Object>> executeSQL(@NonNull String sql) {
        log.debug("Executing SQL to query data with jdbc connection: {}", sql);

        return Mono.fromCallable(() -> {
            try {
                return eodRepo.executeSQL(sql);
            } catch (DataAccessException e) {
                log.error("failed to execute SQL [{}] due to {}", sql, e.getMessage());
                throw e;
            }
        }).subscribeOn(scheduler).flatMapMany(Flux::fromIterable);
    }
}
