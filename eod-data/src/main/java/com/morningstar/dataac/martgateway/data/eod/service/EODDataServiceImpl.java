package com.morningstar.dataac.martgateway.data.eod.service;

import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.data.eod.repository.EODMongodbAsyncRepo;
import com.morningstar.dataac.martgateway.data.eod.filter.ResultFilter;
import com.morningstar.dataac.martgateway.data.eod.helper.DataPointQueryBuilder;
import com.morningstar.dataac.martgateway.data.eod.helper.SimpleQueryBuilder;
import com.morningstar.dataac.martgateway.data.eod.repository.EODAsyncRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODMDB;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODPG;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.UNIQUEID;
import static com.morningstar.dataac.martgateway.data.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;

@Slf4j
@Lazy
public class EODDataServiceImpl implements EODDataService {

    private final EODAsyncRepo eodAsyncRepo;
    private final EODMongodbAsyncRepo eodMongodbAsyncRepo;

    public EODDataServiceImpl(EODAsyncRepo eodAsyncRepo, EODMongodbAsyncRepo eodMongodbAsyncRepo) {
        this.eodAsyncRepo = eodAsyncRepo;
        this.eodMongodbAsyncRepo = eodMongodbAsyncRepo;
    }

    @Override
    @ResultFilter
    public Flux<Result> getData(MartRequest martRequest, List<EODDataPoint> dataPoints) {
        if (CollectionUtils.isEmpty(dataPoints)) {
            return Flux.empty();
        }

        Map<String, String> columeDpMap = new HashMap<>();

        EODDataPoint dataPoint = dataPoints.get(0);
        String schema = dataPoint.getDatabaseSchema();
        String table = dataPoint.getTables();
        String storeProcedure = dataPoint.getStoreProcedure();
        String src = dataPoint.getSrc();

        List<String> columns = dataPoints.stream().map(dp -> {
            columeDpMap.put(getColumnName(dp), dp.getId());
            return src.equals(EODPG) ? SimpleQueryBuilder.buildColumnName(dp.getColumn(), dp.getAlias())
                    : SimpleQueryBuilder.buildProjection(dp.getColumn(), dp.getAlias());
        }).toList();

        Collection<Investment> investments = (Collection<Investment>)martRequest.getAttribute(ENTITLED_INVESTMENTS);
        if (investments.isEmpty()) {
            return Flux.empty();
        }
        List<String> performanceIds = investments.stream().map(Investment::getPerformanceId).toList();

        //fetch data from multiple datasource
        log.debug("fetch data for performanceIds:{}, src:{}", performanceIds, src);

        return switch (src) {
            case EODMDB -> fetchMongoData(schema, table, performanceIds, columeDpMap, storeProcedure, columns);
            case EODPG -> fetchPostgresData(schema, table, performanceIds, columeDpMap, storeProcedure, columns);
            default -> Flux.empty();
        };
    }

    /**
     * Fetch data from mongo DB
     */
    private Flux<Result> fetchMongoData(String database, String collectionName, List<String> performanceIds,
                                   Map<String, String> columeDpMap, String storeProcedure, List<String> projectionFields) {
        String query = DataPointQueryBuilder.buildWithInvestmentIds(storeProcedure, performanceIds);
        Flux<Document> rawResults = eodMongodbAsyncRepo.findAll(query, database, collectionName, projectionFields);
        return rawResults.map(rawResult ->  transformResult(rawResult, columeDpMap));
    }

    /**
     * Fetch data from Postgres
     */
    private Flux<Result> fetchPostgresData(String schema, String table, List<String> performanceIds,
                                   Map<String, String> columeDpMap, String storeProcedure, List<String> columns) {
        String sqlStatement = DataPointQueryBuilder.buildWithInvestmentIds(storeProcedure,
                columns, schema, table, performanceIds);
        Flux<Map<String, Object>> rawResults = eodAsyncRepo.executeSQL(sqlStatement);
        return rawResults.map(rawResult ->  transformResult(rawResult, columeDpMap));
    }

    /**
     * transform the raw result set to current result set by the column and datapoint map
     */
    private Result transformResult(Map<String, Object> rawResult, Map<String, String> columeDpMap) {
        final Map<String, String> stringValuesMap = new HashMap<>();
        rawResult.forEach((col, value) -> stringValuesMap.put(columeDpMap.get(col),
                value != null ? parseString(value) : null));
        return new CurrentResult((String)rawResult.get(UNIQUEID), stringValuesMap);
    }

    private String parseString(Object value) {
        String strValue = String.valueOf(value);
        return StringUtils.hasText(strValue) ? strValue : null;
    }

    private String getColumnName(EODDataPoint dataPoint) {
        return dataPoint.getAlias() == null || dataPoint.getAlias().isBlank() ?
                dataPoint.getColumn() : dataPoint.getAlias();
    }
}
