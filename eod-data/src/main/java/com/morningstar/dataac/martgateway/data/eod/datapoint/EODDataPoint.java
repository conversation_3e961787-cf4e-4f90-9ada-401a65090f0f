package com.morningstar.dataac.martgateway.data.eod.datapoint;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public final class EODDataPoint extends DataPoint {

    private String tables;
    private String dataType;
    private String databaseSchema;
    private String alias;
    private boolean isRequired;
    private String primarySrc;
}
