package com.morningstar.dataac.martgateway.data.eod.filter;

import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.entitlement.eod.data.entity.Status;
import com.morningstar.entitlement.eod.data.entity.entitlementrequest.EntitlementRequest;
import com.morningstar.entitlement.eod.data.entity.entitlementresponse.EntitlementResponse;
import com.morningstar.entitlement.eod.data.interfaces.EntitlementEODDataService;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.morningstar.dataac.martgateway.data.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;

@Slf4j
public class EntitlementEODHandler {

    private final EntitlementEODDataService<EntitlementResponse, EntitlementRequest> entitlementEODDataService;

    public EntitlementEODHandler(EntitlementEODDataService<EntitlementResponse, EntitlementRequest> entitlementEODDataService) {
        this.entitlementEODDataService = entitlementEODDataService;
    }

    public void apply(MartRequest martRequest) {
        log.debug("Apply entitlementEODHandler");

        String userId = martRequest.getUserId();
        String configId = martRequest.getConfigId();
        if (StringUtils.isBlank(userId) && StringUtils.isBlank(configId)) {
            return;
        }

        List<String> performanceIds = martRequest.getIds();
        if (CollectionUtils.isEmpty(performanceIds)) {
            martRequest.setAttribute(ENTITLED_INVESTMENTS, Collections.emptyList());
            return;
        }

        // fetch eod entitlement
        EntitlementRequest entitlementRequest = EntitlementRequest.builder()
                .userId(UUID.fromString(userId))
                .configId(configId)
                .useCase(martRequest.getUseCase())
                .performanceIds(performanceIds)
                .build();

        EntitlementResponse response =  entitlementEODDataService.retrieveUserInvestments(entitlementRequest);

        if (response != null && response.getStatus() != null && response.getStatus().equals(Status.OK)) {
            List<String> validPerformanceIds = response.getInvestments().stream()
                    .map(Investment::getPerformanceId)
                    .toList();

            // set the investments for embargoTimeFilter use
            martRequest.setAttribute(ENTITLED_INVESTMENTS, response.getInvestments());
            log.debug("Entitlement EOD filtered mart request performanceIds = {}", validPerformanceIds);
        } else {
            log.warn("Failed to fetch eod entitlement, no entitled investment");
            martRequest.setAttribute(ENTITLED_INVESTMENTS, Collections.emptyList());
        }
    }
}
