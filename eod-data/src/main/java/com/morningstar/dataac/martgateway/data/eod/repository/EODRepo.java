package com.morningstar.dataac.martgateway.data.eod.repository;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Mapper
@Component
public interface EODRepo {

    @Select("${sp}")
    List<Map<String, Object>> executeSQL(@Param("sp") String sp);
}
