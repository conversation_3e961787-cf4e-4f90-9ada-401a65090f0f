package com.morningstar.dataac.martgateway.data.eod.filter;

import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.DateConst;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.data.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;

@Slf4j
@NoArgsConstructor
public class EmbargoTimeFilter extends AbstractFluxResultFilter {

    private static final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern(DateConst.DEFAULT_DATE_PATTERN, Locale.ENGLISH);

    @Override
    public Flux<Result> apply(MartRequest martRequest, Flux<Result> resultFlux, List<? extends DataPoint> dataPoints) {
        log.debug("Apply embargoTimeFilter");
        Collection<Investment> investments = (Collection<Investment>)martRequest.getAttribute(ENTITLED_INVESTMENTS);
        if (CollectionUtils.isEmpty(investments)) {
            return resultFlux;
        }

        Instant now = Instant.now();
        ZonedDateTime currentDateTime = ZonedDateTime.ofInstant(now, ZoneOffset.UTC);
        // fill isToday map from the embargo time per investment
        final Map<String, Boolean> isPassedEmbargoTimeMap = investments.stream()
                .collect(Collectors.toMap(
                        Investment::getPerformanceId,
                        investment -> isPassedEmbargoTime(now, currentDateTime, investment)
                ));

        return resultFlux.groupBy(Result::getId)
                .flatMap(Flux::collectList)
                .mapNotNull(list -> pickOneRow(list, isPassedEmbargoTimeMap, currentDateTime));
    }

    /*
     * There are two rows for each performance id.
     * Pick one of the rows according to the embargo
     * time from the isPassedEmbargoTimeMap map.
     */
    private Result pickOneRow(List<Result> list, Map<String, Boolean> isPassedEmbargoTimeMap,
                              ZonedDateTime currentDateTime) {
        int first = 0, second = 1;
        Result firstResult = list.get(first);
        // If there is only one result, return it directly.
        if (list.size() == 1) {
            return firstResult;
        }
        Result secondResult = list.get(second);
        // Extract details from the results.
        String performanceId = firstResult.getId();
        // return null if performance id has not eod entitlement
        if (!isPassedEmbargoTimeMap.containsKey(performanceId)) {
            return null;
        }

        boolean isPassedEmbargoTime = isPassedEmbargoTimeMap.get(performanceId);
        // publish date is utc date
        LocalDate later = getPublishDate(firstResult);
        LocalDate earlier = getPublishDate(secondResult);
        // Swap indices if the later date is actually earlier.
        if (later.isBefore(earlier)) {
            first = 1;
            second = 0;
            later = earlier;
        }
        log.debug("later: {}, earlier {}", later, earlier);
        // Determine which result to return.
        return (isPassedEmbargoTime || !currentDateTime.toLocalDate().isEqual(later)) ? list.get(first) : list.get(second);
    }

    private boolean isPassedEmbargoTime(Instant now, ZonedDateTime currentDateTime, Investment investment) {
        LocalTime releaseTime = investment.getReleaseTime();
        String performanceId = investment.getPerformanceId();
        log.debug("investment id [{}] release time [{}]", performanceId, releaseTime);
        //convert release time to utc date time
        ZonedDateTime releaseDateTime = ZonedDateTime.ofInstant(now, ZoneOffset.UTC).with(releaseTime);
        //compare current utc date time to release utc date time
        return currentDateTime.isAfter(releaseDateTime);
    }

    /**
     * get the publish_date of eod record from the datapoint.
     *
     * @param result single record
     * @return local date of publish date
     */
    private LocalDate getPublishDate(Result result) {
        String datetimeStr = (String)result.getValues().get(dataPoint.getId());
        return LocalDate.parse(datetimeStr, formatter);
    }
}
