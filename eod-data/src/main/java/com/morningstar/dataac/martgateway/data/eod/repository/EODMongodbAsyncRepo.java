package com.morningstar.dataac.martgateway.data.eod.repository;

import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.List;

/**
 * EOD async repository for reference data which are stored in mongodb.
 * This repository uses a normal(blocked) mongo client to fetch data.
 * Then it wrapped by a reactive process with specific scheduler.
 */
@Slf4j
public class EODMongodbAsyncRepo {

    private final Scheduler scheduler;
    private final EODMongodbRepo eodMongodbRepo;

    public EODMongodbAsyncRepo(Scheduler scheduler, EODMongodbRepo eodMongodbRepo) {
        this.scheduler = scheduler;
        this.eodMongodbRepo = eodMongodbRepo;
    }

    public Flux<Document> findAll(String query, String database, String collectionName, List<String> projectionFields) {
        log.debug("mongodb find all docs by query: {} from collection: {}", query, collectionName);

        return Mono.fromCallable(() -> {
            try {
                return eodMongodbRepo.findAll(query, database, collectionName, projectionFields);
            } catch (Exception e) {
                log.error("failed to fetch data from mongodb due to: {}", e.getMessage());
                throw new MartException("failed to fetch data from mongodb", e);
            }
        }).subscribeOn(scheduler).flatMapMany(Flux::fromIterable);
    }
}
