package com.morningstar.dataac.martgateway.data.eod.service;

import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import reactor.core.publisher.Flux;

import java.util.List;

public interface EODDataService {

    Flux<Result> getData(MartRequest martRequest, List<EODDataPoint> dataPoints);
}
