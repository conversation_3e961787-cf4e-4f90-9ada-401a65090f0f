package com.morningstar.dataac.martgateway.data.eod.repository;

import com.mongodb.client.MongoClient;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
public class EODMongodbRepo {

    private final MongoClient mongoClient;

    public EODMongodbRepo(MongoClient mongoClient) {
        this.mongoClient = mongoClient;
    }

    public List<Document> findAll(String query, String database, String collectionName, List<String> projectionFields) {
        if (!StringUtils.hasText(query) || !StringUtils.hasText(database) || !StringUtils.hasText(collectionName)
                || projectionFields == null || projectionFields.isEmpty()) {
            return Collections.emptyList();
        }

        log.debug("fetch data from mongodb, database:{} collection:{} query:{} projection:{}", database, collectionName, query, projectionFields);

        return mongoClient
                .getDatabase(database)
                .getCollection(collectionName)
                .find(buildFilter(query))
                .projection(buildProjections(projectionFields))
                .into(new ArrayList<>());
    }

    private Bson buildFilter(String query) {
        return Document.parse(query);
    }

    private Bson buildProjections(List<String> fields) {
        return Document.parse(String.format("{%s, %s}", String.join(",", fields), "_id:0")); //not show mongo default doc id
    }
}
