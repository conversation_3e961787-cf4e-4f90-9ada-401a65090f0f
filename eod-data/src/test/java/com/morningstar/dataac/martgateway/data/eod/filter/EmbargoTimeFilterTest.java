package com.morningstar.dataac.martgateway.data.eod.filter;

import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.DateConst;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.data.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EmbargoTimeFilterTest {

    private final static String DUMMY_ID = "dummyId";

    private List<DataPoint> dataPoints;

    private MartRequest request;

    private Flux<Result> resultFlux;

    private EmbargoTimeFilter embargoTimeFilter;

    @BeforeAll
    void setUp() {
        dataPoints = List.of(DataPoint.builder().id(DUMMY_ID).build());
        embargoTimeFilter = new EmbargoTimeFilter();
        embargoTimeFilter.setDataPoint(DataPoint.builder().id(DUMMY_ID).build());
        Map<String, Object> attributes = new HashMap<>();
        List<Investment> investmentList = new ArrayList<>();
        investmentList.add(Investment.builder()
                .performanceId(DUMMY_ID).releaseTime(LocalTime.now())
                .build());
        attributes.put(ENTITLED_INVESTMENTS, investmentList);

        request = MartRequest.builder()
                .attributes(attributes)
                .build();

        LocalDate now = LocalDate.now();

        // create two result for today and yesterday
        resultFlux = Flux.just(new CurrentResult(DUMMY_ID, Map.of(DUMMY_ID, DateUtil.formatToGeneralDate(now))),
                new CurrentResult(DUMMY_ID, Map.of(DUMMY_ID, DateUtil.formatToGeneralDate(now.minusDays(1)))));
    }

    @Test
    @DisplayName("filter should be applied")
    @Order(1)
    public void shouldApplyFilter() {
        int today = LocalDateTime.now().getDayOfYear();
        resultFlux = embargoTimeFilter.apply(request, resultFlux, dataPoints);
        StepVerifier.create(resultFlux)
                .assertNext(result -> {
                    String date = (String)result.getValues().get(DUMMY_ID);
                    LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(DateConst.DEFAULT_DATE_PATTERN));
                    assertEquals(today, localDate.getDayOfYear());
                })
                .verifyComplete();
    }

    @Test
    @DisplayName("filter should not be applied when given empty entitled investments")
    @Order(2)
    void givenEmptyInvestments_shouldSkipFilter() {
        request.setAttribute(ENTITLED_INVESTMENTS, null);

        resultFlux = embargoTimeFilter.apply(request, Flux.just(new ErrorResult(DUMMY_ID, "")), dataPoints);

        StepVerifier.create(resultFlux)
                .assertNext(result -> {
                    assertInstanceOf(ErrorResult.class, result);
                })
                .verifyComplete();
    }
}
