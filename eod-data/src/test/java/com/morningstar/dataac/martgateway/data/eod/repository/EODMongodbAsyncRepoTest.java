package com.morningstar.dataac.martgateway.data.eod.repository;

import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;
import reactor.test.StepVerifier;

import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class EODMongodbAsyncRepoTest {

    @Mock
    private EODMongodbRepo repo;

    private EODMongodbAsyncRepo asyncRepo;

    @BeforeEach
    void setUp() {
        asyncRepo = new EODMongodbAsyncRepo(Schedulers.boundedElastic(), repo);
    }

    @Test
    @DisplayName("should get flux of documents")
    void shouldGetFluxOfDocuments() {
        String query = "{}";
        String dbname = "dbname";
        String collection = "collection";
        List<String> projections = List.of("field1:1", "field2:1");
        Document doc = new Document();

        when(repo.findAll(query, dbname, collection, projections)).thenReturn(List.of(doc));

        Flux<Document> fluxResult = asyncRepo.findAll(query, dbname, collection, projections);

        StepVerifier.create(fluxResult).expectNext(doc).verifyComplete();
    }
}
