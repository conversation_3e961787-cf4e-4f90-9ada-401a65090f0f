package com.morningstar.dataac.martgateway.data.eod.filter;

import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.*;

import static com.morningstar.dataac.martgateway.data.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EntitlementEODErrorHandlerTest {

    private final static String DUMMY_ID_1 = "dummyId1";
    private final static String DUMMY_ID_2 = "dummyId2";
    private final static String DUMMY_DP_ID_1 = "DpId1";

    private List<String> dataPointIds;

    private MartRequest martRequest;

    private Flux<Result> resultFlux;

    private EntitlementEODErrorHandler entitlementEODErrorHandler;

    @BeforeAll
    void setUp() {

        dataPointIds = List.of(DUMMY_DP_ID_1);
        entitlementEODErrorHandler = new EntitlementEODErrorHandler();

        Map<String, Object> attributes = new HashMap<>();
        attributes.put(ENTITLED_INVESTMENTS, List.of(Investment.builder().performanceId(DUMMY_ID_1).build()));

        martRequest = MartRequest.builder()
                .ids(List.of(DUMMY_ID_1, DUMMY_ID_2))
                .dps(List.of(DUMMY_DP_ID_1))
                .attributes(attributes)
                .build();

        resultFlux = Flux.just(new CurrentResult(DUMMY_ID_1, Collections.emptyMap()));
    }

    @Test
    @DisplayName("should get one unentitled error and one current result")
    @Order(1)
    void shouldGetOneUnentitledError() {
        Flux<Result> filteredResultFlux = entitlementEODErrorHandler.apply(martRequest, resultFlux, dataPointIds);

        StepVerifier.create(filteredResultFlux)
                .assertNext(result -> assertInstanceOf(CurrentResult.class, result))
                .assertNext(result -> assertInstanceOf(ErrorResult.class, result))
                .verifyComplete();
    }

    @Test
    @DisplayName("should get all unentitled error")
    @Order(2)
    void shouldGetAllUnentitledError() {
        martRequest.setAttribute(ENTITLED_INVESTMENTS, null);
        Flux<Result> filteredResultFlux = entitlementEODErrorHandler.apply(martRequest, Flux.empty(), dataPointIds);

        StepVerifier.create(filteredResultFlux)
                .assertNext(result -> assertInstanceOf(ErrorResult.class, result))
                .assertNext(result -> assertInstanceOf(ErrorResult.class, result))
                .verifyComplete();
    }

    @Test
    @DisplayName("should get all unentitled error")
    @Order(3)
    void shouldGetAllEntitledError() {
        martRequest.setAttribute(ENTITLED_INVESTMENTS,
                List.of(DUMMY_ID_1,DUMMY_ID_2));
        resultFlux = Flux.just(new CurrentResult(DUMMY_ID_1, Collections.emptyMap()),
                new CurrentResult(DUMMY_ID_2, Collections.emptyMap()));
        Flux<Result> filteredResultFlux = entitlementEODErrorHandler.apply(martRequest, resultFlux, dataPointIds);

        StepVerifier.create(filteredResultFlux)
                .assertNext(result -> assertInstanceOf(CurrentResult.class, result))
                .assertNext(result -> assertInstanceOf(CurrentResult.class, result))
                .verifyComplete();
    }
}
