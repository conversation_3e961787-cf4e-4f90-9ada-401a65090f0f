package com.morningstar.dataac.martgateway.data.eod.filter;

import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class GatewayFilterRegistryTest {

    private final static String DUMMY_ID = "dummyId";
    private final static String DUMMY_GROUP = "group";
    private final static String DUMMY_FILTER = "filter";

    private EODDataPoint dataPoint;

    @Mock
    private AbstractFluxResultFilter resultFilter;
    @Mock
    private ApplicationContext applicationContext;

    @InjectMocks
    private GatewayFilterRegistry gatewayFilterRegistry;

    @BeforeAll
    void setUp() {
        dataPoint = EODDataPoint.builder()
                .nid(DUMMY_ID)
                .groupName(DUMMY_GROUP)
                .filter(List.of(DUMMY_FILTER))
                .build();

        when(applicationContext.getBean(DUMMY_FILTER, AbstractFluxResultFilter.class)).thenReturn(resultFilter);
    }

    @Test
    @Order(1)
    @DisplayName("should register request filter and result filter")
    void shouldRegisterFilter() {

        gatewayFilterRegistry.register(dataPoint);

        assertTrue(gatewayFilterRegistry.hasResultFilter(DUMMY_ID));
    }

    @Test
    @Order(2)
    @DisplayName("should get registered filters")
    void shouldGetRegisteredFilter() {
        assertNotNull(gatewayFilterRegistry.getResultFilters(DUMMY_ID));
    }
}
