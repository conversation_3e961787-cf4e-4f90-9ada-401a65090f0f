package com.morningstar.dataac.martgateway.data.eod.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EODDataSourceConfigurationTest {

    @Mock
    private DataSource eodDataSource;

    private EODDataSourceConfiguration eodDataSourceConfiguration;

    @BeforeAll
    void setUp() {
        eodDataSourceConfiguration = new EODDataSourceConfiguration();
    }

    @Test
    @DisplayName("eod sql session factory bean is created")
    @Order(1)
    void testEodSqlSessionFactory() throws Exception {
        SqlSessionFactory sqlSessionFactory = eodDataSourceConfiguration
                .eodSqlSessionFactory(eodDataSource);
        assertNotNull(sqlSessionFactory);
    }
}
