package com.morningstar.dataac.martgateway.data.eod.helper;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class DataPointQueryBuilderTest {

    String storeProcedure;
    List<String> columns;
    String schema;
    String table;
    List<String> performanceIds;

    @BeforeAll
    void setUp() {
        storeProcedure = "SELECT %s FROM %s.%s WHERE perf_id in (%s)";
        columns = List.of("column");
        schema = "dummy_schema";
        table = "dummy_table";
        performanceIds = List.of("ABC123");
    }

    @Test
    @DisplayName("build select query string with performance id")
    void shouldBuildASelectQueryWithPerformanceIds() {
        String query = DataPointQueryBuilder.buildWithInvestmentIds(storeProcedure, columns, schema, table, performanceIds);

        assertNotNull(query);
        assertEquals(query, String.format("SELECT %s FROM %s.%s WHERE perf_id in ('%s')",
                columns.get(0), schema, table, performanceIds.get(0)));
    }

    @Test
    @DisplayName("build select query string without performance id")
    void shouldBuildASelectQueryWithoutPerformanceIds() {
        String query = DataPointQueryBuilder.buildWithInvestmentIds(storeProcedure, columns, schema, table, Collections.emptyList());

        assertNotNull(query);
        assertEquals(query, String.format("SELECT %s FROM %s.%s WHERE perf_id in ('')",
                columns.get(0), schema, table));
    }
}
