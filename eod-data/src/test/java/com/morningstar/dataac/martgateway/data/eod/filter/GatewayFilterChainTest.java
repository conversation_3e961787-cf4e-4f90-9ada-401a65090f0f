package com.morningstar.dataac.martgateway.data.eod.filter;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class GatewayFilterChainTest {

    private final static String DUMMY_ID = "dummyId";
    private final static String DUMMY_GROUP = "group";

    private MartRequest martRequest;

    private List<? extends DataPoint> dataPoints;

    @Mock
    private AbstractFluxResultFilter resultFilter;
    @Mock
    private GatewayFilterRegistry gatewayFilterRegistry;
    @Mock
    private ProceedingJoinPoint joinPoint;
    @InjectMocks
    private GatewayFilterChain gatewayFilterChain;

    @BeforeAll
    void setUp() {
        dataPoints = List.of(EODDataPoint.builder()
                .nid(DUMMY_ID).groupName(DUMMY_GROUP)
                .build());
        martRequest = MartRequest.builder().build();
    }

    @Test
    @DisplayName("should run after filter")
    public void shouldDoAfterFilter() throws Throwable {
        Flux<Result> resultFlux = Flux.just(new CurrentResult(DUMMY_ID, Map.of("test", "test")));
        when(resultFilter.apply(martRequest, resultFlux, dataPoints)).thenReturn(resultFlux);
        when(joinPoint.proceed()).thenReturn(resultFlux);

        resultFlux = gatewayFilterChain.doAfterFilters(joinPoint, martRequest, dataPoints);
        StepVerifier.create(resultFlux)
                .assertNext(result -> assertEquals(DUMMY_ID, result.getId()))
                .verifyComplete();
    }
}
