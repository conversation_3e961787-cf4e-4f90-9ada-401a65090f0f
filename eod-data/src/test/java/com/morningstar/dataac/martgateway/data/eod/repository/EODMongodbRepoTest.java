package com.morningstar.dataac.martgateway.data.eod.repository;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EODMongodbRepoTest {

    @Mock
    private MongoClient mongoClient;
    @Mock
    private MongoDatabase mongoDatabase;
    @Mock
    private MongoCollection<Document> mongoCollection;
    @Mock
    private FindIterable<Document> findIterable;
    @InjectMocks
    private EODMongodbRepo repo;

    private String query;
    private String dbname;
    private String collection;
    private List<String> projections;

    @BeforeEach
    void setUp() {
        query = "{performanceId: {$in: ['F000000TST']}}";
        dbname = "dbname";
        collection = "collection";
        projections = List.of("field1:1", "field2:0");
    }

    @Test
    @DisplayName("Should get list of document with valid parameters")
    @Order(1)
    void shouldGetListOfDoc() {
        when(mongoClient.getDatabase(anyString())).thenReturn(mongoDatabase);
        when(mongoDatabase.getCollection(anyString())).thenReturn(mongoCollection);
        when(mongoCollection.find(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.projection(any(Bson.class))).thenReturn(findIterable);
        when(findIterable.into(any(List.class))).thenReturn(List.of(new Document()));

        List<Document> results = repo.findAll(query, dbname, collection, projections);

        assertEquals(1, results.size());
    }

    @Test
    @DisplayName("Should get empty list with empty parameters")
    @Order(2)
    void shouldGetEmptyList() {
        List<Document> results = repo.findAll(null, dbname, collection, projections);
        assertEquals(0, results.size());
        results = repo.findAll(query, null, collection, projections);
        assertEquals(0, results.size());
        results = repo.findAll(query, dbname, null, projections);
        assertEquals(0, results.size());
        results = repo.findAll(query, dbname, collection, null);
        assertEquals(0, results.size());
    }
}
