package com.morningstar.dataac.martgateway.data.eod.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class EODLoggingAspectTest {
    @Mock
    private ProceedingJoinPoint pjp;

    @Mock
    Logger logger;

    @InjectMocks
    private EODLoggingAspect eodLoggingAspect;

    @Test
    @DisplayName("should run log aspect pointcut for sql performance")
    void shouldRunSQLPerformanceLoggerPointcut() throws Throwable {
        when(pjp.proceed()).thenReturn(Collections.emptyList());
        Object result = eodLoggingAspect.postgresPerformanceLogger(pjp);
        assertNotNull(result);
        assertInstanceOf(List.class, result);
    }

    @Test
    @DisplayName("should run log aspect pointcut for eod entitlement lib performance")
    void shouldRunEODEntitlePerformanceLoggerPointcut() throws Throwable {
        when(pjp.proceed()).thenReturn(Collections.emptyList());
        Object result = eodLoggingAspect.eodEntitlePerformanceLogger(pjp);
        assertNotNull(result);
        assertInstanceOf(List.class, result);
    }
}
