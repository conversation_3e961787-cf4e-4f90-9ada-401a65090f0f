package com.morningstar.dataac.martgateway.data.eod.repository;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class EODAsyncRepoTest {

    @Mock
    private EODRepo eodRepo;

    private EODAsyncRepo eodAsyncRepo;

    @BeforeAll
    void setup() {
        eodAsyncRepo = new EODAsyncRepo(Schedulers.boundedElastic(), eodRepo);
    }

    @Test
    @DisplayName("execute sql query and get result")
    void givenValidSQLString_shouldGetResult() {
        String sql = "SELECT id FROM test";
        Map<String, Object> testMap = Map.of("id", "ABC123");

        when(eodRepo.executeSQL(sql)).thenReturn(List.of(testMap));

        Flux<Map<String, Object>> rawResults = eodAsyncRepo.executeSQL(sql);
        StepVerifier.create(rawResults).expectNext(testMap).verifyComplete();
    }

    @Test
    @DisplayName("execute null sql query string and get exception")
    void givenNullSQLString_shouldGetException() {
        String sql = null;
        assertThrows(NullPointerException.class, () -> eodAsyncRepo.executeSQL(sql));
    }
}
