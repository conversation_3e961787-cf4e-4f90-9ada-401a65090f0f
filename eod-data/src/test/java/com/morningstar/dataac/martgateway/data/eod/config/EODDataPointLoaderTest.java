package com.morningstar.dataac.martgateway.data.eod.config;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.data.eod.filter.GatewayFilterRegistry;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EODDataPointLoaderTest {
    private EODDataPointLoader eodDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @Mock
    private GatewayFilterRegistry gatewayFilterRegistry;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);

        eodDataPointLoader = new EODDataPointLoader(datapointConfigFileService, documentLoader, gatewayFilterRegistry);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/eod/eod_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/eod/eod_datapoints.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }

    @Test
    public void testTransferCalcToMappingSrc() {
         eodDataPointLoader.loadDataPoints(context);

        { // Test:
            DataPoint dataPoint = context.getDataPointById("EO001");
            assertThat(dataPoint, notNullValue());
            assertInstanceOf(EODDataPoint.class, dataPoint);

            EODDataPoint edoDataPoint = (EODDataPoint) dataPoint;

            assertThat(edoDataPoint.getGroupName(), equalTo("MorningstarEODMarketData"));
            assertThat(edoDataPoint.getDataType(), equalTo("CurrentData"));
            assertThat(edoDataPoint.getIdLevel(), equalTo("PerformanceId"));
            assertThat(edoDataPoint.getDatabaseSchema(), equalTo("eod"));
            assertThat(edoDataPoint.getTables(), equalTo("view_price_data"));
            assertThat(edoDataPoint.getStoreProcedure(), equalTo("SELECT %s FROM %s.%s WHERE perf_id IN (%s)"));
            assertThat(edoDataPoint.getColumn(), equalTo("open"));
            assertThat(edoDataPoint.getAlias(), nullValue());
            assertThat(edoDataPoint.isRequired(), equalTo(false));
            assertThat(edoDataPoint.getFilter(), nullValue());
            assertThat(edoDataPoint.getParseFunction(), equalTo("DECIMAL"));
            assertThat(edoDataPoint.getSrc(), equalTo("EODPG"));

        }

        { // Test:
            DataPoint dataPoint = context.getDataPointById("EO_UNIQUE_ID_01");
            assertThat(dataPoint, notNullValue());
            assertInstanceOf(EODDataPoint.class, dataPoint);

            EODDataPoint edoDataPoint = (EODDataPoint) dataPoint;

            assertThat(edoDataPoint.getGroupName(), equalTo("MorningstarEODMarketData"));
            assertThat(edoDataPoint.getDataType(), equalTo("CurrentData"));
            assertThat(edoDataPoint.getIdLevel(), equalTo("PerformanceId"));
            assertThat(edoDataPoint.getDatabaseSchema(), equalTo("eod"));
            assertThat(edoDataPoint.getTables(), equalTo("view_price_data"));
            assertThat(edoDataPoint.getStoreProcedure(), equalTo("SELECT %s FROM %s.%s WHERE perf_id IN (%s)"));
            assertThat(edoDataPoint.getColumn(), equalTo("perf_id"));
            assertThat(edoDataPoint.getAlias(), equalTo("uniqueid"));
            assertThat(edoDataPoint.isRequired(), equalTo(true));
            assertEquals("OS06Y", edoDataPoint.getPrimarySrc());
            assertThat(edoDataPoint.getParseFunction(), nullValue());
            assertThat(edoDataPoint.getSrc(), equalTo("EODPG"));

        }


    }
}
