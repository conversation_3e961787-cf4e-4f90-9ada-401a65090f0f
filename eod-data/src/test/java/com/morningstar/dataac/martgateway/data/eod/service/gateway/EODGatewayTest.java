package com.morningstar.dataac.martgateway.data.eod.service.gateway;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.data.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.data.eod.filter.EntitlementEODErrorHandler;
import com.morningstar.dataac.martgateway.data.eod.filter.EntitlementEODHandler;
import com.morningstar.dataac.martgateway.data.eod.service.EODDataService;
import java.util.HashMap;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODMDB;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODPG;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EODGatewayTest {

    private static final String validId1 = "validId1";
    private static final String validId2 = "validId2";
    private static final String DUMMY_VALUE = "dummy";
    private static final String DUMMY_GROUP = "dummyGroup";
    private static MartRequest request;

    @Mock
    private EODDataService eodDataService;
    @Mock
    private EntitlementEODHandler entitlementEODHandler;
    @Mock
    private EntitlementEODErrorHandler entitlementEODErrorHandler;

    private EODGateway eodGateway;

    @BeforeEach
    void setUp() {
        eodGateway = new EODGateway(eodDataService, entitlementEODHandler, entitlementEODErrorHandler);

        DataPointRepository.setDataPointMap(new HashMap<>());

        request = MartRequest.builder()
                .ids(List.of("inv1"))
                .dps(List.of(validId1, "invalidId"))
                .build();
        DataPointRepository.getDataPointMap().put(validId1, EODDataPoint.builder()
                .nid(validId1).groupName(DUMMY_GROUP).src(EODPG).build());
        DataPointRepository.getDataPointMap().put(validId2, EODDataPoint.builder()
                .nid(validId2).groupName(DUMMY_GROUP).src(EODPG).build());
    }

    @AfterAll
    public static void tearDown() {
        DataPointRepository.setDataPointMap(new HashMap<>());
    }

    @Test
    @DisplayName("retrieve flux result without required datapoint")
    @Order(1)
    void givenRequest_shouldRetrieveFluxResultWithoutRequiredDataPoints() {
        Result currentResult = new CurrentResult("inv1", Map.of(validId1, "value1", validId2, "value2"));
        Result errorResult = new ErrorResult("inv2", "invalidId", "403");
        Flux<Result> _results = Flux.just(currentResult, errorResult);

        when(eodDataService.getData(any(MartRequest.class), anyList())).thenReturn(_results);
        when(entitlementEODErrorHandler.apply(any(MartRequest.class), any(Flux.class), anyList())).thenReturn(_results);

        Flux<Result> resultFlux = eodGateway.retrieve(request);
        StepVerifier.create(resultFlux)
                .expectNext(currentResult)
                .expectNext(errorResult)
                .verifyComplete();

        resultFlux = eodGateway.filterNotRequestedDataPoint(_results, new HashSet<>(request.getDps()));
        StepVerifier.create(resultFlux)
                .assertNext(result -> assertEquals(1, result.getValues().size()))
                .assertNext(result -> assertEquals("inv2", result.getId()))
                .verifyComplete();
    }

    @Test
    @DisplayName("retrieve flux result with required datapoint")
    @Order(2)
    void givenRequest_shouldRetrieveFluxResultWithRequiredDataPoints() {
        DataPointRepository.addGroupRequiredDataPoint(EODDataPoint.builder()
                .nid(validId2).groupName(DUMMY_GROUP).src(EODPG).build());

        Map<String, String> rowMap = Map.of(validId1, DUMMY_VALUE);

        Result testResult = new CurrentResult(validId1, rowMap);

        when(eodDataService.getData(any(MartRequest.class), anyList())).thenReturn(Flux.just(testResult));
        when(entitlementEODErrorHandler.apply(any(MartRequest.class), any(Flux.class), anyList())).thenReturn(Flux.just(testResult));

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectNext(testResult).verifyComplete();
    }

    @Test
    @DisplayName("retrieve flux result with required datapoint which dp value is null")
    @Order(2)
    void givenRequest_shouldRetrieveFluxResultWithRequiredDataPointsWhileDPValueIsNull() {
        //given
        DataPointRepository.addGroupRequiredDataPoint(EODDataPoint.builder()
                .nid(validId1).groupName(DUMMY_GROUP).src(EODPG).build());
        DataPointRepository.addGroupRequiredDataPoint(EODDataPoint.builder()
                .nid(validId2).groupName(DUMMY_GROUP).src(EODMDB).build());

        Map<String, String> rowMap = new HashMap<>();
        rowMap.put(validId1, null);

        Flux<Result> resultFlux = Flux.just(new CurrentResult(validId1, rowMap));

        //when
        Flux<Result> filteredResultFlux = eodGateway.filterNotRequestedDataPoint(resultFlux, Set.of(validId1, validId2));

        //then
        StepVerifier.create(filteredResultFlux)
                .assertNext(result -> assertNull(result.getValues().get(validId1)))
                .verifyComplete();
    }

    @Test
    @DisplayName("return empty flux result with empty group mapping")
    @Order(3)
    void givenEmptyGroupDataPoints_shouldRetrieveEmptyFluxResult() {
        DataPointRepository.setDataPointMap(Collections.emptyMap());

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectNextCount(0).verifyComplete();
    }

    @Test
    @DisplayName("return empty flux result with non eod datapoint")
    @Order(3)
    void givenNonEODGroupDataPoints_shouldRetrieveEmptyFluxResult() {
        DataPointRepository.setDataPointMap(Map.of(validId1,  DataPoint.builder()
                .nid(validId1).groupName("dummyGroup").src(EODPG).build()));

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectNextCount(0).verifyComplete();
    }

    @Test
    @DisplayName("return empty flux result with null eod src DP")
    @Order(3)
    void givenNullEODSrcDataPoint_shouldRetrieveEmptyFluxResult() {
        DataPointRepository.setDataPointMap(Map.of(validId1,  EODDataPoint.builder()
                .nid(validId1).groupName(DUMMY_GROUP).src(null).build()));

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectNextCount(0).verifyComplete();
    }

    @Test
    @DisplayName("return empty flux result with non eod src DP")
    @Order(3)
    void givenNonEODSrcDataPoint_shouldRetrieveEmptyFluxResult() {
        DataPointRepository.setDataPointMap(Map.of(validId1,  EODDataPoint.builder()
                .nid(validId1).groupName(DUMMY_GROUP).src("ABC").build()));

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectNextCount(0).verifyComplete();
    }

    @Test
    @DisplayName("should retrieve requested datapoint in error result only")
    @Order(4)
    void givenRequest_shouldRetrieveSupportedErrorResult() {
        DataPointRepository.setDataPointMap(Map.of(validId1,  EODDataPoint.builder()
                .nid(validId1).groupName(DUMMY_GROUP).src(EODPG).build()));

        Result errorResult1 = new ErrorResult(validId1, "403");
        Result errorResult2 = new ErrorResult(validId2, "403");

        when(eodDataService.getData(any(MartRequest.class), anyList())).thenReturn(Flux.just(errorResult1, errorResult2));
        when(entitlementEODErrorHandler.apply(any(MartRequest.class), any(Flux.class), anyList())).thenReturn(Flux.just(errorResult1, errorResult2));

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux)
                .assertNext(result -> assertEquals(validId1,  ((ErrorResult) result).getDatapointId()))
                .assertNext(result -> assertEquals(validId2,  ((ErrorResult) result).getDatapointId()))
                .verifyComplete();
    }

    @Test
    @DisplayName("should not retrieve not requested datapoint in error result")
    @Order(5)
    void givenRequest_shouldNotRetrieveUnSupportedErrorResult() {
        DataPointRepository.setDataPointMap(Map.of(validId1,  EODDataPoint.builder()
                .nid(validId1).groupName(DUMMY_GROUP).src(EODPG).build()));

        Result errorResult = new ErrorResult(validId2, "403");

        when(eodDataService.getData(any(MartRequest.class), anyList())).thenReturn(Flux.just(errorResult));
        when(entitlementEODErrorHandler.apply(any(MartRequest.class), any(Flux.class), anyList())).thenReturn(Flux.empty());

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectComplete().verify();
    }

    @Test
    @DisplayName("return empty flux result with empty ids in request")
    @Order(6)
    void givenEmptyIds_shouldReturnEmptyFluxResult() {
        request.setIds(Collections.emptyList());

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectNextCount(0).verifyComplete();
    }

    @Test
    @DisplayName("return empty flux result with empty datapoint list in request")
    @Order(7)
    void givenEmptyDatapointList_shouldReturnEmptyFluxResult() {
        request.setDps(Collections.emptyList());

        Flux<Result> resultFlux = eodGateway.retrieve(request);

        StepVerifier.create(resultFlux).expectNextCount(0).verifyComplete();
    }
}
