package com.morningstar.dataac.martgateway.data.timeseries.service;

import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.dataac.martgateway.core.common.repository.RedisTsRepo;
import com.morningstar.dataac.martgateway.core.common.util.Lz4Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TsCacheHelper {
    private final RedisTsRepo redisTsRepo;

    private static final String REDIS_KEY_PREFIX = "ts:";
    private static final int YEARS_PER_KEY = 10;
    private static final int YEARS_PER_FIELD = 2;

    public TsCacheHelper(RedisTsRepo redisTsRepo) {
        this.redisTsRepo = redisTsRepo;
    }

    public Flux<TsDataProtoBuf.TSDataDouble> getData(List<String> ids, List<String> dps, String groupPrefix, LocalDate start, LocalDate end) {
        Set<Integer> requiredYears = new HashSet<>();
        for (int year = start.getYear(); year <= end.getYear(); year++) {
            requiredYears.add(year);
        }

        // Calculate Redis keys and fields (same for all investment ids)
        Map<Integer, List<String>> keyToFields = calculateKeyToFields(requiredYears, dps);

        return Flux.fromIterable(ids)
                .flatMap(id -> {
                    return Flux.fromIterable(keyToFields.entrySet())
                            .flatMap(entry -> {
                                Integer keyYear = entry.getKey();
                                List<String> fields = entry.getValue();

                                // Use the same pattern as calculateKeyToFields for consistency
                                String redisKey = REDIS_KEY_PREFIX + groupPrefix + ":" + id + ":" + keyYear;

                                // Convert to bytes
                                byte[] keyBytes = redisKey.getBytes();
                                List<byte[]> fieldBytes = fields.stream()
                                        .map(String::getBytes)
                                        .collect(Collectors.toList());

                                return redisTsRepo.getHashValue(keyBytes, fieldBytes)
                                        .flatMap(compressedData -> {
                                            try {
                                                byte[] decompressed = Lz4Util.decompress(compressedData);
                                                TsDataProtoBuf.TSDataDouble tsData = TsDataProtoBuf.TSDataDouble.parseFrom(decompressed);

                                                // Filter data points within the date range and remove excess dates
                                                TsDataProtoBuf.TSDataDouble filteredData = filterDateRange(tsData, start, end);

                                                return Flux.just(filteredData);
                                            } catch (Exception e) {
                                                log.error("Error decompressing and parsing TSDataDouble", e);
                                                return Flux.empty();
                                            }
                                        });
                            });
                });
    }

    private Map<Integer, List<String>> calculateKeyToFields(Set<Integer> requiredYears, List<String> dps) {
        Map<Integer, List<String>> keyToFields = new HashMap<>();

        // Group years by 10-year periods (Redis keys)
        // For example: 1803-1825 -> keys: 1800, 1810, 1820
        Map<Integer, List<Integer>> keyYearToYears = requiredYears.stream()
                .collect(Collectors.groupingBy(year -> (year / YEARS_PER_KEY) * YEARS_PER_KEY));

        for (Map.Entry<Integer, List<Integer>> entry : keyYearToYears.entrySet()) {
            Integer keyYear = entry.getKey();
            Set<String> fields = getStrings(dps, entry);

            keyToFields.put(keyYear, new ArrayList<>(fields));
        }

        return keyToFields;
    }

    private static Set<String> getStrings(List<String> dps, Map.Entry<Integer, List<Integer>> entry) {
        List<Integer> yearsInKey = entry.getValue();

        // Calculate fields for this key (2-year periods)
        Set<String> fields = new HashSet<>();

        // First collect unique fieldStartYears to avoid duplicate calculations
        Set<Integer> fieldStartYears = new HashSet<>();
        for (Integer year : yearsInKey) {
            // Calculate the 2-year field this year belongs to
            // For example: 1803 -> field year 1802, 1825 -> field year 1824
            int fieldStartYear = (year / YEARS_PER_FIELD) * YEARS_PER_FIELD;
            fieldStartYears.add(fieldStartYear);
        }

        // Now generate fields only for unique fieldStartYears
        for (Integer fieldStartYear : fieldStartYears) {
            for (String dp : dps) {
                fields.add(dp + ":" + fieldStartYear);
            }
        }
        return fields;
    }

    private TsDataProtoBuf.TSDataDouble filterDateRange(TsDataProtoBuf.TSDataDouble tsData, LocalDate start, LocalDate end) {
        List<Long> originalDates = tsData.getDatesList();

        if (CollectionUtils.isEmpty(originalDates)) {
            return tsData;
        }

        // Convert LocalDate to epoch days for comparison with proto dates
        long startEpochDay = start.toEpochDay();
        long endEpochDay = end.toEpochDay();

        // Check boundary conditions - if all dates are within range, return as is
        long firstDate = originalDates.get(0);
        long lastDate = originalDates.get(originalDates.size() - 1);

        if (firstDate >= startEpochDay && lastDate <= endEpochDay) {
            // All dates are within range, no filtering needed
            return tsData;
        }

        // Need to filter - prepare data
        List<Double> originalValues = tsData.getValuesList();
        List<Integer> originalCopyOverIndices = tsData.getCopyOverDateIndicesList();

        // Optimize: use size() and get(index) instead of creating intermediate lists
        int valuesSize = originalValues.size();
        int copyOverSize = originalCopyOverIndices.size();

        // Convert copyOverIndices to Set for O(1) lookup
        Set<Integer> copyOverSet = new HashSet<>(originalCopyOverIndices);

        // Use arrays for better performance
        int originalSize = originalDates.size();
        long[] filteredDatesArray = new long[originalSize]; // max possible size
        double[] filteredValuesArray = new double[originalSize];
        int[] filteredCopyOverArray = new int[copyOverSize]; // max possible size

        int dateIndex = 0;
        int copyOverIndex = 0;

        for (int i = 0; i < originalSize; i++) {
            long date = originalDates.get(i);

            // Check if date is within range [start, end]
            if (date >= startEpochDay && date <= endEpochDay) {
                filteredDatesArray[dateIndex] = date;
                if (i < valuesSize) {
                    filteredValuesArray[dateIndex] = originalValues.get(i);
                }

                // Check if current original index is a copyover index
                if (copyOverSet.contains(i)) {
                    filteredCopyOverArray[copyOverIndex++] = dateIndex;
                }

                dateIndex++;
            }
        }

        TsDataProtoBuf.TSDataDouble.Builder builder = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId(tsData.getInvestmentId())
                .setDpId(tsData.getDpId());

        for (int i = 0; i < dateIndex; i++) {
            builder.addDates(filteredDatesArray[i]);
            builder.addValues(filteredValuesArray[i]);
        }

        for (int i = 0; i < copyOverIndex; i++) {
            builder.addCopyOverDateIndices(filteredCopyOverArray[i]);
        }

        return builder.build();
    }

}
