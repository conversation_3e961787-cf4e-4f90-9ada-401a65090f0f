package com.morningstar.dataac.martgateway.data.timeseries.config;

import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointLoaderInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.DataPointAggregate;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.IDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.AbstractXmlDataPointLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderDefinition;
import com.morningstar.dataac.martgateway.data.timeseries.datapoint.TsDataPoint;
import com.morningstar.dataac.martgateway.data.timeseries.datapoint.TsDataPointGroup;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;

import java.util.HashMap;
import java.util.Map;

import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATAPOINT;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATAPOINT_GROUP;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.GROUP;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.ID;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.IDLEVEL;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.PARSE_FUNCTION;

@DataPointLoaderDefinition(info = DataPointLoaderInfo.TIMESERIES)
@Slf4j
public class TsDataPointLoader extends AbstractXmlDataPointLoader {

    public TsDataPointLoader(DatapointConfigFileService datapointConfigFileService, DocumentLoader documentLoader) {
        super(datapointConfigFileService, documentLoader);
    }

    @Override
    public void processDocument(DataPointLoaderContext context, Document document) {
        Map<String, Map<String, String>> fixedIncomeGroupColDpsMap = new HashMap<>();

        for (Element dataPointGroupElement : document.getRootElement().elements(DATAPOINT_GROUP)) {
            String groupName = dataPointGroupElement.attributeValue(GROUP);
            String idLevel = dataPointGroupElement.attributeValue(IDLEVEL);
            TsDataPointGroup tsDataPointGroup = TsDataPointGroup.builder().groupName(groupName).idLevel(idLevel).build();

            for (Element dataPointElement : dataPointGroupElement.elements(DATAPOINT)) {
                String nid = dataPointElement.attributeValue(ID);
                String parseFunction = dataPointElement.attributeValue(PARSE_FUNCTION);
                TsDataPoint tsDataPoint = TsDataPoint.builder().nid(nid).parseFunction(parseFunction).tsDataPointGroup(tsDataPointGroup).build();

                // Check if DataPointAggregate exists for this nid
                DataPointAggregate existingAggregate = context.getDataPointAggregateMap().get(nid);
                if (existingAggregate != null) {
                    // DataPointAggregate exists, add TS srcType to existing srcMap
                    existingAggregate.getSrcMap().put(DataPointAggregate.SrcType.TS, tsDataPoint);
                } else {
                    // DataPointAggregate doesn't exist, create new one
                    Map<DataPointAggregate.SrcType, IDataPoint> srcMap = new HashMap<>();
                    srcMap.put(DataPointAggregate.SrcType.TS, tsDataPoint);

                    DataPointAggregate newAggregate = DataPointAggregate.builder()
                            .id(nid)
                            .srcMap(srcMap)
                            .build();

                    context.getDataPointAggregateMap().put(nid, newAggregate);
                }
            }
        }
    }

}
