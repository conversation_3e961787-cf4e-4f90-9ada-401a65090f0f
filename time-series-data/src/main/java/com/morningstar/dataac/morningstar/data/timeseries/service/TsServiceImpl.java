package com.morningstar.dataac.morningstar.data.timeseries.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.FixedIncomeDataPoint;
import com.morningstar.dataac.morningstar.data.rds.repository.RdsAsyncRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.ID;
import static com.morningstar.dataac.morningstar.data.rds.util.RdsUtils.parseDbColumnValue;

@Slf4j
public class TsServiceImpl implements TsService {
    private final RdsAsyncRepo rdsAsyncRepo;
    private final TsCacheHelper fixedIncomeCurrentCacheHelper;
    private static final long CACHE_EXPIRE_HOURS = 4L;
    private Semaphore semaphore;

    public TsServiceImpl(RdsAsyncRepo rdsAsyncRepo, TsCacheHelper fixedIncomeCurrentCacheHelper, Semaphore semaphore) {
        this.rdsAsyncRepo = rdsAsyncRepo;
        this.fixedIncomeCurrentCacheHelper = fixedIncomeCurrentCacheHelper;
        this.semaphore = semaphore;
    }

    @Override
    public Flux<Result> getData(MartRequest martRequest, List<String> idList, List<FixedIncomeDataPoint> dataPoints, Map<String, String> multiSrcMap) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start getData for requestId={}, id_count={}, datapoint_count={}, uuid={}", martRequest.getRequestId(), idList.size(), dataPoints.size(), uuid);
        boolean isMostRecent = StringUtils.isEmpty(martRequest.getStartDate());
        return isMostRecent ?
                fetchMostRecentData(dataPoints, idList, martRequest.isReadCache(), multiSrcMap, martRequest.getRequestId(), uuid) :
                fetchTimeSeriesData(dataPoints, idList, multiSrcMap, martRequest, uuid);
    }

    private String buildIds(Collection<String> idList) {
        return "'" + String.join("','", idList) + "'";
    }

    private String buildDate(String date) {
        return "'" + date + "'";
    }

    private Flux<Result> fetchMostRecentData(List<FixedIncomeDataPoint> dataPoints, List<String> ids, boolean readCache, Map<String, String> multiSrcMap, String requestId, String uuid) {
        AtomicLong startTime = new AtomicLong();
        log.info("Start fetchMostRecentData for requestId={}, uuid={}", requestId, uuid);
        String currentGroupPrefix = dataPoints.get(0).getIdLevel() + "/" + dataPoints.get(0).getGroupName();
        List<String> dpSet = new ArrayList<>(List.of(ID));
        dataPoints.forEach(dp -> dpSet.add(dp.getNid()));
        Flux<Map<String, Object>> cacheData = readCache ? fixedIncomeCurrentCacheHelper.getData(ids, dpSet, currentGroupPrefix)
                .doOnSubscribe(s -> startTime.set(System.currentTimeMillis()))
                .doFinally(signalType -> {
                    long timeSpent = System.currentTimeMillis() - startTime.get();
                    log.info("fixed income data retrieval from cache for requestId={}, group={}, ids={}, uuid={}, time_spent={} ms", requestId, currentGroupPrefix, ids.size(), uuid, timeSpent);
                })
                : Flux.empty();
        Flux<Map<String, Object>> combinedResults = cacheData
                .collectList()
                .flatMapMany(cd -> combineCurrentDateWithCache(cd, ids, dataPoints.get(0), readCache, requestId, uuid));
        Set<String> dpIdSet = dataPoints.stream().map(FixedIncomeDataPoint::getNid).collect(Collectors.toSet());
        return transformCurrentResults(combinedResults, dpIdSet, multiSrcMap)
                .doOnComplete(() -> log.info("End fetchMostRecentData for requestId={}, uuid={}", requestId, uuid));
    }

    private Flux<Map<String, Object>> combineCurrentDateWithCache(List<Map<String, Object>> cacheData, List<String> idList, FixedIncomeDataPoint groupDpInfo, boolean readCache, String requestId, String uuid) {
        String sp = groupDpInfo.getStoreProcedure();
        String groupName = groupDpInfo.getGroupName();
        String currentGroupPrefix = groupDpInfo.getIdLevel() + "/" + groupDpInfo.getGroupName();
        long expireHours = getExpireTime(groupDpInfo.getExpireTime());
        Set<String> idsToCache = new HashSet<>(idList);
        cacheData.forEach(cd -> idsToCache.remove(cd.getOrDefault(ID, "")));
        Mono<List<Map<String, Object>>> rawResults = Mono.empty();
        if (!CollectionUtils.isEmpty(idsToCache)) {
            String sql = String.format(sp, buildIds(idsToCache));
            AtomicLong startTime = new AtomicLong();
            rawResults = transformCurrentDataFromDb(rdsAsyncRepo.executeSQL(sql, groupName, System.currentTimeMillis()), groupName).collectList();
            cacheData(readCache, requestId, uuid, rawResults, idsToCache, currentGroupPrefix, expireHours, startTime);
        }
        return Flux.concat(rawResults.flatMapMany(Flux::fromIterable), Flux.fromIterable(cacheData))
                .doOnComplete(() -> log.info("fixed income fetch current data for requestId={}, cache enabled={}, group={}, ids={}", requestId, readCache, currentGroupPrefix, idList.size()));
    }

    private void cacheData(boolean readCache, String requestId, String uuid, Mono<List<Map<String, Object>>> rawResults, Set<String> idsToCache, String currentGroupPrefix, long expireHours, AtomicLong startTime) {
        if (semaphore.tryAcquire()) {
            int idsToCacheSize = idsToCache.size();
            rawResults.flatMapMany(results ->
                    fixedIncomeCurrentCacheHelper.cacheData(results, idsToCache, currentGroupPrefix, expireHours, readCache, uuid)
                            .doOnSubscribe(s -> startTime.set(System.currentTimeMillis()))
                            .doFinally(signalType -> {
                                long timeSpent = System.currentTimeMillis() - startTime.get();
                                log.info("fixed income store data in cache for requestId={}, cache enabled={}, group={}, ids={}, time_spent={} ms", requestId, readCache, currentGroupPrefix, idsToCacheSize, timeSpent);
                            })
            ).doFinally(i -> semaphore.release()).subscribe();
        } else {
            log.debug("semaphore is not available when caching fixed income current data.");
        }
    }

    private Flux<Result> fetchTimeSeriesData(
            List<FixedIncomeDataPoint> dataPoints,
            List<String> idList,
            Map<String, String> multiSrcMap,
            MartRequest martRequest,
            String uuid) {
        log.info("Start fetchTimeSeriesData for requestId={}, uuid={}", martRequest.getRequestId(), uuid);
        long startTime = System.currentTimeMillis();
        String sp = martRequest.isFromPortfolioHoldings() ? dataPoints.get(0).getHoldingsStoreProcedure() : dataPoints.get(0).getStoreProcedure();
        String dateField = dataPoints.get(0).getDateColumn();
        String groupName = dataPoints.get(0).getGroupName();
        String sql = String.format(sp, buildIds(idList), buildDate(martRequest.getStartDate()), buildDate(martRequest.getEndDate()));
        Flux<Map<String, Object>> rawResults = rdsAsyncRepo.executeTimeSeriesSQL(sql, dateField, groupName, System.currentTimeMillis());
        Set<String> dpSet = dataPoints.stream().map(FixedIncomeDataPoint::getNid).collect(Collectors.toSet());
        return transformTimeSeriesResults(rawResults, dpSet, multiSrcMap)
                .doOnComplete(() -> log.info("fixed income fetch time series data for requestId={}, uuid={}, group={}, ids={}, time={}ms", martRequest.getRequestId(), uuid, groupName, idList.size(), System.currentTimeMillis() - startTime));
    }

    private Flux<Result> transformTimeSeriesResults(Flux<Map<String, Object>> rawResults, Set<String> dpSet, Map<String, String> multiSrcMap) {
        return rawResults.map(rawResult ->  {
            final Map<String, List<V>> timeSeriesValuesMap = new HashMap<>();
            rawResult.forEach((key, value) -> {
                if(dpSet.contains(key)) {
                    timeSeriesValuesMap.put(multiSrcMap.getOrDefault(key,key),
                            value != null ? (List<V>)value : null);
                }
            });
            return new TimeSeriesResult((String)rawResult.get(ID), timeSeriesValuesMap);
        });
    }

    private Flux<Result> transformCurrentResults(Flux<Map<String, Object>> rawResults, Set<String> dpIdSet, Map<String, String> multiSrcMap) {
        //transform the raw result set to current result set by the column and datapoint map
        return rawResults.map(rawResult ->  {
            final Map<String, String> stringValuesMap = new HashMap<>();
            rawResult.forEach((key, value) -> {
                if(dpIdSet.contains(key)) {
                    stringValuesMap.put(multiSrcMap.getOrDefault(key, key), parseDbColumnValue(value));
                }
            });
            return new CurrentResult((String)rawResult.get(ID), stringValuesMap);
        });
    }

    private Flux<Map<String, Object>> transformCurrentDataFromDb(Flux<Map<String, Object>> rawResults, String groupName) {
        return rawResults.map(rawResult ->  {
            final Map<String, Object> dpValuesMap = new HashMap<>();
            rawResult.forEach((key, value) -> {
                String dpId = ID.equalsIgnoreCase(key) ? ID : DataPointRepository.getRdsDpsMapByGroupName(groupName).get(key);
                if(dpId != null) {
                    dpValuesMap.put(dpId, value);
                }
            });
            return dpValuesMap;
        });
    }

    private long getExpireTime(String expireTime) {
        try {
            return NumberUtils.toLong(expireTime);
        } catch (Exception e) {
            return CACHE_EXPIRE_HOURS;
        }
    }
}
