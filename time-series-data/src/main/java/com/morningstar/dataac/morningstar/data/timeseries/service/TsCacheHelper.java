package com.morningstar.dataac.morningstar.data.timeseries.service;

import com.google.common.collect.Lists;
import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.ID;
import static com.morningstar.dataac.morningstar.data.rds.util.RdsUtils.parseDbColumnValue;

@Slf4j
public class TsCacheHelper {
    private final RedisReactiveRepo appCacheRedisClient;

    private static final String REDIS_PREFIX = "fixed-income:DP_%s:%s";

    public TsCacheHelper(RedisReactiveRepo appCacheRedisClient) {
        this.appCacheRedisClient = appCacheRedisClient;
    }

    public Flux<Map<String, Object>> getData(List<String> idList, List<String> dpSet, String groupPrefix) {
        List<String> cacheKeys = idList.stream().map(id -> String.format(REDIS_PREFIX, groupPrefix, id)).collect(Collectors.toList());
        return Flux.fromIterable(Lists.partition(cacheKeys, 200)).flatMap(keys -> appCacheRedisClient.multiGetHash(keys, dpSet));
    }

    public Flux<Object> cacheData(List<Map<String, Object>> dataList, Set<String> idList, String groupPrefix, long expireHours, boolean readCache, String uuid) {
        long start = System.currentTimeMillis();
        return readCache ? Flux.fromIterable(dataList)
                .flatMap(map -> {
                    // Convert the current cache data for each map
                    idList.remove(map.get(ID));
                    Pair<String, Map<String, String>> cacheData = convertCurrentCacheData(map, groupPrefix);
                    return Mono.just(cacheData);
                }).concatWith(addIdsWithNoData(idList, groupPrefix))
                .collectMap(Pair::getLeft, Pair::getRight)  // Collect into a Map<String, Map<String, Object>>
                .flatMapMany(dataMap -> appCacheRedisClient.multiSetHash(dataMap, expireHours)
                        .doOnComplete(() -> log.info("Done writing to cache for id_count={}, time_spent={}ms, uuid={}", idList.size(), System.currentTimeMillis() - start, uuid))
                ) :  Flux.empty();
    }

    private Flux<Pair<String, Map<String, String>>> addIdsWithNoData(Set<String> idList, String groupPrefix) {
        return Flux.fromIterable(idList).flatMap(id -> {
            Pair<String, Map<String, String>> cacheData = convertCurrentCacheData(Map.of(ID, id), groupPrefix);
            return Mono.just(cacheData);
        });
    }

    private Pair<String, Map<String, String>> convertCurrentCacheData(Map<String, Object> data, String groupPrefix) {
        Map<String, String> result = data.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, value -> parseDbColumnValue(value.getValue())));
        return Pair.of(String.format(REDIS_PREFIX, groupPrefix, result.get(ID)), result);
    }
}
