package com.morningstar.dataac.morningstar.data.timeseries.config;

import com.morningstar.dataac.martgateway.core.common.config.YamlPropertySourceFactory;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Data
@Configuration
@ConfigurationProperties(prefix = "martgateway.fixed-income")
@PropertySource(ignoreResourceNotFound = false, value = "classpath:fixed-income-${spring.profiles.active}.yaml", factory = YamlPropertySourceFactory.class)
public class TsProperties {
    private int maximumPoolSize;
    private int minimumIdle;
    private String driverClassName;
    private String jdbcUrl;
    private String username;
    private String password;
    private String poolName;
    private String packages;
}
