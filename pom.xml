<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.morningstar</groupId>
    <artifactId>mart-component-gateway</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>mart-component-gateway</name>

    <properties>
        <mart-feed-components.version>1.0-SNAPSHOT</mart-feed-components.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <log4j-2.version>2.15.0</log4j-2.version>
        <lombok.version>1.18.26</lombok.version>
        <aws.version>1.11.724</aws.version>
        <spring.boot.version>2.7.18</spring.boot.version>

        <surefire.version>2.19</surefire.version>
        <junit-jupiter.version>5.10.5</junit-jupiter.version>
        <jacoco.version>0.8.8</jacoco.version>

        <sonar.version>3.9.1.2184</sonar.version>
        <sonar.host.url>https://mssonar.morningstar.com</sonar.host.url>
        <sonar.projectKey>DATAAC-PID0462-Mart-Component-Gateway</sonar.projectKey>
        <sonar.projectName>DATAAC-PID0462-Mart-Component-Gateway</sonar.projectName>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
        <sonar.coverage.exclusions>
            **/entity/**/*,**/event/**/*,**/*Configuration.java,**/*Properties.java
        </sonar.coverage.exclusions>
        <!--suppress UnresolvedMavenProperty -->
        <sonar.coverage.jacoco.xmlReportPaths>
            ${maven.multiModuleProjectDirectory}/report-aggregate/target/site/jacoco-aggregate/jacoco.xml
        </sonar.coverage.jacoco.xmlReportPaths>
        <harnessArgLine />
    </properties>

    <modules>
        <module>common-core</module>
        <module>mart-component-cloud</module>
        <module>data-point-loader</module>
        <module>data-point-loader-test</module>
        <module>entitlement-core</module>
        <module>mart-component-equity</module>
        <module>gateway-core</module>
        <module>mart-gateway</module>
        <module>report-aggregate</module>
        <module>fixed-income-ice-data</module>
        <module>ph-data</module>
        <module>ph-service</module>
        <module>uim-core</module>
        <module>redisson-lock</module>
        <module>async-core</module>
        <module>rdb-data</module>
        <module>rds-data</module>
        <module>custom-data</module>
        <module>lei-nace-data</module>
        <module>eod-data</module>
        <module>calculation-lib-core</module>
        <module>time-series-data</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>5.8.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>5.8.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>4.5.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>2.13.5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>6.1.6.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.2.3</version>
            </dependency>
            <!-- Dom4j & XPath -->
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.4</version>
            </dependency>
            <dependency>
                <groupId>jaxen</groupId>
                <artifactId>jaxen</artifactId>
                <version>1.1.6</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>1.12.120</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.7</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.dynamic-sql</groupId>
                <artifactId>mybatis-dynamic-sql</artifactId>
                <version>1.4.0</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.28</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.springframework/spring-context -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.mybatis.dynamic-sql/mybatis-dynamic-sql -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/junit/junit -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.2</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>**/*IT.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire.version}</version>
                <configuration>
                    <useSystemClassLoader>false</useSystemClassLoader>
                    <argLine>${harnessArgLine} @{argLine} </argLine>
                    <excludes>
                        <exclude>**/*IT.java</exclude>
                    </excludes>
                    <systemPropertyVariables>
                        <listener>org.sonar.java.jacoco.JUnitListener</listener>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>2.3</version>
                    <configuration>
                        <createDependencyReducedPom>false</createDependencyReducedPom>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${surefire.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>maven-dev-group</id>
            <url>https://artifacts.morningstar.com/repository/maven-dev-group/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>maven-prod-group</id>
            <url>https://artifacts.morningstar.com/repository/maven-prod-group/</url>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>maven-prod-hosted</id>
            <url>https://artifacts.morningstar.com/repository/maven-prod-hosted/</url>
        </repository>
        <snapshotRepository>
            <id>maven-dev-snapshot-hosted</id>
            <url>https://artifacts.morningstar.com/repository/maven-dev-snapshot-hosted/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
