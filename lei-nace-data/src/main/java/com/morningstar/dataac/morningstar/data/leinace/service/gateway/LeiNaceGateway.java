package com.morningstar.dataac.morningstar.data.leinace.service.gateway;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.dataac.morningstar.data.leinace.service.LeiNaceDataService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.LEINACE;

public class LeiNaceGateway implements Gateway<Result, MartRequest> {

    private final LeiNaceDataService leiNaceDataService;

    public LeiNaceGateway(LeiNaceDataService leiNaceDataService) {
        this.leiNaceDataService = leiNaceDataService;
    }
    @Override
    public Flux<Result> retrieve(MartRequest martRequest) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), martRequest.getRequestId());
        if (CollectionUtils.isEmpty(martRequest.getIds()) || CollectionUtils.isEmpty(martRequest.getDps())) {
            return Flux.empty();
        }
        Map<String, List<DataPoint>> groupDataPoints = martRequest.getDps().stream()
                .map(dpId -> DataPointRepository.getByNid(dpId))
                .filter(Objects::nonNull)
                .filter(dp -> isLeiNaceDatapoint(dp))
                .collect(Collectors.groupingBy(DataPoint::getGroupName));

        return Flux.fromIterable(groupDataPoints.values())
                .flatMap(dataPoints -> leiNaceDataService.getData(martRequest, martRequest.getIds(), dataPoints)
                        .subscribeOn(SchedulerConfiguration.getScheduler()));

    }

    private boolean isLeiNaceDatapoint(DataPoint dp) {
        return StringUtils.contains(dp.getSrc(), LEINACE);
    }
}
