package com.morningstar.dataac.martgateway.core.calculationlib.util;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import reactor.core.publisher.Flux;

@NoArgsConstructor
public class CalculationUtil {

    /**
     * merge the same type of result (current, group, timeseries, etc.) in the given flux results for
     * different investment id. The merge applies to the same type in same investment id.
     * For example:
     * INV000001(CurrentResult1, CurrentResult2, TimeserviesResult1, TimeseriesResult2, GroupResult1, GroupResult2, ...)
     * merge to INV000001(CurrentResult, TimeserviesResult, GroupResult) by join the all the values in same type
     *
     * @param results mixed different type of results in the flux
     * @return Flux<Result>
     */
    public static Flux<Result> mergeResultsWithSameIds(Flux<Result> results) {
        return results
                .filter(CalculationUtil::validateResult)
                .groupBy(Result::getId)
                .flatMap(gf1 -> gf1.groupBy(Result::getClass)
                                .flatMap(gf2 -> gf2.collectList()
                                        .flatMapMany(Flux::fromIterable)
                                        .reduce(CalculationUtil::mergeResults)));
    }

    private static Result mergeResults(Result origin, Result append) {
        origin.getValues().putAll(append.getValues());
        return origin;
    }

    private static boolean validateResult(Result result) {
        return !MapUtils.isEmpty(result.getValues());
    }
}
