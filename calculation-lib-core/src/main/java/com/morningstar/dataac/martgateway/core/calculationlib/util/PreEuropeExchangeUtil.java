package com.morningstar.dataac.martgateway.core.calculationlib.util;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

public class PreEuropeExchangeUtil {

    private static final Map<String, Double> EXCHANGE_RATE = ImmutableMap.<String, Double>builder()
            .put("BEF", 40.3399)// Belgian franc BEF
            .put("DEM", 1.95583)// Deutsche Mark
            .put("ESP", 166.386)// Spanish peseta
            .put("FRF", 6.55957)// French franc
            .put("IEP", 0.787564)// Irish pound
            .put("ITL", 1936.27)// Italian lira
            .put("LUF", 40.3399)// Luxembourg franc
            .put("NLG", 2.20371)// Dutch guilder
            .put("ATS", 13.7603)// Austrian schilling
            .put("PTE", 200.482)// Portuguese escudo
            .put("FIM", 5.94573)// Finnish markka
            .put("XEU", 1.0).build(); // European Currency Unit

    public static boolean isPreEurope(String currency) {
        return EXCHANGE_RATE.containsKey(currency);
    }

    public static double getRate(String key) {
        return EXCHANGE_RATE.get(key);
    }
}
