package com.morningstar.dataac.martgateway.core.calculationlib;

import static com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper.CURCHANGE;
import static com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper.PERFORMANCEDATE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper;
import com.morningstar.dataac.martgateway.core.calculationlib.entity.ConvertCurrency;
import com.morningstar.dataac.martgateway.core.calculationlib.entity.CalcMetaData;
import com.morningstar.dataac.martgateway.core.calculationlib.util.CurrencyIdUtil;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.StringUtils;

public class CalculationLibConversionService {

	private final CurrencyConversionHelper currencyConversionHelper;

	private static final String CCY_CU = "CU$$$$$";
	private static final String USD = "USD";
	private static final String GBP = "GBP";
	private static final String GBX = "GBX";

	public CalculationLibConversionService(CurrencyConversionHelper currencyConversionHelper){
		this.currencyConversionHelper = currencyConversionHelper;
	}

	public CurrentResult convertCurrent(CurrentResult result,
			Set<DataPoint> dpSet,
			String targetCurrency,
			CalcMetaData calcMetaData) {

		String secId = result.getId();
		Map<String, String> valuesMap = result.getValues();
		handlePenceTraded(dpSet, valuesMap);
		if (StringUtils.hasText(targetCurrency)) {
			handleCurrencyConversion(dpSet, targetCurrency, secId, valuesMap, calcMetaData);
		}
		return result;
	}

	public GroupResult convertGroup(GroupResult groupResult, Set<DataPoint> dpSet, String targetCurrency, CalcMetaData calcMetaData) {

		Map<String, List<CurrentResult>> groupValuesMap = groupResult.getValues();
		if (StringUtils.hasText(targetCurrency) && groupValuesMap != null && !groupValuesMap.isEmpty()) {
			String secId = groupResult.getId();
			for (Entry<String, List<CurrentResult>> entry : groupValuesMap.entrySet()) {
				for (CurrentResult result : entry.getValue()){
					Map<String, String> valuesMap = result.getValues();
					handlePenceTraded(dpSet, valuesMap);
					handleCurrencyConversion(dpSet, targetCurrency, secId, valuesMap, calcMetaData);
				}
			}
		}
		return groupResult;
	}

	private void handlePenceTraded(Set<DataPoint> dpSet, Map<String, String> dpMap) {
		for (Entry<String, String> entry : dpMap.entrySet()) {
			String nid = entry.getKey();
			Optional<DataPoint> dataPointOpt = dpSet.stream()
					.filter(dataPoint ->
							dataPoint.getNid().equals(nid) &&
									dataPoint.getCalculation().getPenceTraded() != null &&
									dataPoint.getCalculation().getCcy() != null)
					.findFirst();
			if (dataPointOpt.isPresent()) {
				DataPoint dataPoint = dataPointOpt.get();
				DataPoint penceTradeDp = dataPoint.getCalculation().getPenceTraded();
				DataPoint ccyDp = dataPoint.getCalculation().getCcy();
				if (dpMap.containsKey(penceTradeDp.getNid()) && dpMap.containsKey(ccyDp.getNid())) {
					String penceTraded = dpMap.get(penceTradeDp.getNid());
					String ccy = (dpMap.get(ccyDp.getNid())).trim();
					if ("1".equals(penceTraded) && GBP.equals(ccy)) {
						String convertedValue = convertForPenceTraded(entry);
						entry.setValue(convertedValue);
					}
				}
			}
		}
	}

	private String convertForPenceTraded(Entry<String, String> entry) {
		String convertedValue = entry.getValue();
		if (NumberUtils.isParsable(convertedValue)) {
			convertedValue = Double.toString(Double.parseDouble(convertedValue) * 100);
		} else if (GBP.equals(convertedValue)) {
			convertedValue = GBX;
		}
		return convertedValue;
	}

	private void handleCurrencyConversion(Set<DataPoint> dpSet, String targetCurrency, String secId,
										  Map<String, String> valuesMap, CalcMetaData calcMetaData) {
		String inceptionDate = calcMetaData.getInceptionMap().get(secId + PERFORMANCEDATE);
		Map<String, String> secIdCurrencyMap = calcMetaData.getSecIdCurrencyMap();
		Map<String, DataPoint> dpMap = dpSet.stream().collect(Collectors.toMap(DataPoint::getNid, dp -> dp));
		valuesMap.entrySet().forEach(entry -> {
			String nid = entry.getKey();
			String compoundKey = secId + nid;
			if (secIdCurrencyMap.containsKey(compoundKey) && dpMap.containsKey(nid)) {
				DataPoint dataPoint = dpMap.get(nid);
				String hisCurrency = secIdCurrencyMap.get(compoundKey);
				String endDateId = calcMetaData.getEndDateDpMap().get(dataPoint.getNid()).getNid();
				if (hisCurrency.startsWith(CCY_CU)) {
					hisCurrency = hisCurrency.substring(7);
				}
				if (CurrencyIdUtil.isExchangeId(secId)) {
					hisCurrency = USD;
				}
				if (StringUtils.hasText(targetCurrency) && !hisCurrency.equalsIgnoreCase(targetCurrency)) {
					ConvertCurrency convertCurrency = new ConvertCurrency(inceptionDate, targetCurrency, endDateId, hisCurrency);
					String convertedValue = convertToCurrency(secId, valuesMap, entry, dataPoint, convertCurrency);
					entry.setValue(convertedValue);
				}
			}
		});
	}

	private String convertToCurrency(String secId, Map<String, String> dpMap, Entry<String, String> entry,
									 DataPoint dataPoint, ConvertCurrency convertCurrency) {
		String convertedValue = CurrencyConversionHelper.NOT_A_NUMBER;
		try {
			convertedValue = currencyConversionHelper.convertCDExchangeRate(secId, dataPoint, entry.getValue(),
					dpMap.get(convertCurrency.getEndDateId()), convertCurrency.getHisCurrency(), convertCurrency.getTargetCurrency(), convertCurrency.getInceptionDate());
		} catch (NumberFormatException e) {
			LogEntry.warn(new LogEntity(EVENT_TYPE, "currency conversion"),
					new LogEntity(EVENT_DESCRIPTION, "Original value is not numeric"));
		}
		return convertedValue;
	}

	public TimeSeriesResult convertTS(TimeSeriesResult result,
							   Set<DataPoint> tsDpSet,
							   String targetCurrency,
							   String preCurrency, CalcMetaData calcMetaData) {
		if (tsDpSet.isEmpty()) {
			return result;
		}
		String secId = result.getId();
		List<CurrentResult> changeList = calcMetaData.getCurrencyChangeMap().get(secId + CURCHANGE);
		int size = changeList != null ? changeList.size() + 1 : 1;
		String[] historyCurrency = new String[size];
		String[] historyDate = new String[size];
		historyDate[0] = "1900-01-01";

		fillHistoryDate(historyDate, historyCurrency, changeList);

		Map<String, List<V>> dpMap = result.getValues();
		Map<String, String> secIdCurrencyMap = calcMetaData.getSecIdCurrencyMap();
		dpMap.entrySet().forEach(stringListEntry -> {
			String nid = stringListEntry.getKey();
			Optional<DataPoint> dpOptional = tsDpSet.stream()
					.filter(dataPoint -> dataPoint.getNid().equals(nid) &&
							secIdCurrencyMap.containsKey(secId + nid)).findFirst();
			if (dpOptional.isPresent()) {
				String baseCurrency = secIdCurrencyMap.get(secId + nid);
				DataPoint dataPoint = dpOptional.get();
				List<V> endDateValues = null;
				if (dataPoint.getCalculation().getEndDate() != null) {
					String endDateId = calcMetaData.getEndDateDpMap().get(dataPoint.getNid()).getNid();
                    if (dataPoint.getEquityDatapoint() == null) {
                        endDateValues = dpMap.get(endDateId);
                    }
                }
				List<V> dpValues = stringListEntry.getValue();
				List<V> convertedValues = Collections.emptyList();
				try {
					convertedValues = currencyConversionHelper.handleConversion(targetCurrency, preCurrency, baseCurrency, secId, dpValues, endDateValues, historyCurrency, historyDate, dataPoint);
				} catch (NumberFormatException e) {
					LogEntry.warn(new LogEntity(EVENT_TYPE, "currency conversion"),
							new LogEntity(EVENT_DESCRIPTION, "Original value is not numeric"));
				}
				stringListEntry.setValue(convertedValues);
			}
		});
		return result;
	}

	private void fillHistoryDate(String[] historyDate, String[] historyCurrency, List<CurrentResult> changeList) {
		if (changeList == null || changeList.isEmpty()) {
			return;
		}

		for (int i = 0; i < changeList.size(); i++) {
			List<V> changeDetail = changeList.get(i).transform();
			if (changeDetail == null) {
				continue;
			}

			for (V v : changeDetail) {
				String vI = v.getI();
				String vV = v.getV();
				if (vI.equals(CurrencyConversionHelper.CURID) && i < historyCurrency.length) {
					historyCurrency[i] = vV.replace(CCY_CU, "");
				} else if (vI.equals(CurrencyConversionHelper.ENDDATE) && (i + 1 < historyDate.length)) {
					Date date = DateFormatUtil.parseDate(vV);
					Date nextDay = DateFormatUtil.addDays(date, 1);
					historyDate[i + 1] = DateFormatUtil.format(nextDay);
				}
			}
		}
	}
}
