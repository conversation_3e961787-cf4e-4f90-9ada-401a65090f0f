package com.morningstar.dataac.martgateway.core.calculationlib.conversion;

import com.morningstar.calculationlibrary.CalcLibraryDefine;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.calculationlib.util.PreEuropeExchangeUtil;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import com.morningstar.oneteam.calculation.common.DateUtil;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper.EMPTY_RESULT;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;

public class CurrencyConversion {

    private static final DecimalFormat format = new DecimalFormat("##0.00000");

    private ConversionCenter conversionCenter;

    public CurrencyConversion(ConversionCenter conversionCenter) {
        this.conversionCenter = conversionCenter;
    }

    public String convertCurrencySeries(V data, String hisCurr, String hisDate, String target, String precurrency) {
        List<V> dataList = Arrays.asList(data);
        String[] hisCurrs = {hisCurr};
        String[] hisDates = {hisDate};
        convertCurrencySeries(dataList, hisCurrs, hisDates, target, precurrency);
        return dataList.get(0).getV();
    }

    public void convertCurrencySeries(List<V> dataList, String[] hisCurrs, String[] hisDates, String target, String precurrency) {
        CalcLibraryDefine.DoubleTS[] priceSerice = convert(dataList);
        ArrayList<CalcLibraryDefine.DoubleTS> resultSeries = new ArrayList<>();
        if ("EUR".equals(target) && PreEuropeExchangeUtil.isPreEurope(precurrency)) {
            target = precurrency + target;
        }
        int[] dates = convertStrToInt(hisDates);
        CalcLibraryDefine.ErrorCode errorCode = conversionCenter.convertCurrency(priceSerice, hisCurrs, dates, target, resultSeries);
        if (!errorCode.equals(CalcLibraryDefine.ErrorCode.NoError)) {
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setV(EMPTY_RESULT);
            }
        } else {
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setV(formatValue(resultSeries.get(i).getValue()));
            }
        }
    }

    private CalcLibraryDefine.DoubleTS[] convert(List<V> list) {
        int size = list.size();
        CalcLibraryDefine.DoubleTS[] datas = new CalcLibraryDefine.DoubleTS[list.size()];
        for (int i = 0; i < size; i++) {
            String value = list.get(i).getV();
            CalcLibraryDefine.DoubleTS data = new CalcLibraryDefine.DoubleTS();
            data.setDate(DateUtil.convertDate(DateUtil.convertDate(list.get(i).getI())));
            data.setVaule(Double.parseDouble(value));
            datas[i] = data;
        }
        return datas;
    }

    private int[] convertStrToInt(String[] hisDates) {
        int[] ret = new int[hisDates.length];
        for (int i = 0; i < hisDates.length; i++) {
            ret[i] = DateUtil.convertDate(DateFormatUtil.parseDate(hisDates[i]));
        }
        return ret;
    }

    public void convertReturn(List<V> dataList, String[] hisCurrs, String[] hisDates, String target, String precurrency, String freq) {
        CalcLibraryDefine.DoubleTS[] priceSerice = convert(dataList);
        ArrayList<CalcLibraryDefine.DoubleTS> resultSeries = new ArrayList<>();
        if ("EUR".equals(target) && PreEuropeExchangeUtil.isPreEurope(precurrency)) {
            target = precurrency + target;
        }
        int[] dates = convertStrToInt(hisDates);
        CalcLibraryDefine.ErrorCode errorCode = conversionCenter.convertReturn(priceSerice, freq, hisCurrs, dates, target, resultSeries);
        if (!errorCode.equals(CalcLibraryDefine.ErrorCode.NoError)) {
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setV(EMPTY_RESULT);
            }
        } else {
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setV(formatValue(resultSeries.get(i).getValue()));
            }
        }
    }

    public String convertReturn(double value, Date start, Date end, String hisCurr, String hisDate, String target, String precurrency) {
        String[] hisDates = {hisDate};
        String[] hisCurrs = {hisCurr};
        ArrayList<Double> resultSeries = new ArrayList<>();
        if ("EUR".equals(target) && PreEuropeExchangeUtil.isPreEurope(precurrency)) {
            target = precurrency + target;
        }

        int[] dates = convertStrToInt(hisDates);
        CalcLibraryDefine.ErrorCode errorCode = conversionCenter.convertReturn(start, end, value, hisCurrs, dates, target, resultSeries);
        if (!errorCode.equals(CalcLibraryDefine.ErrorCode.NoError)) {
            LogEntry.error(new LogEntity(EVENT_DESCRIPTION, "currency conversion failure"),
                    new LogEntity("error_code", errorCode.getValue()));
        }
        return !resultSeries.isEmpty() ? formatValue(resultSeries.get(0)) : EMPTY_RESULT;
    }

    public String convertExchangeRates(V value, String target, String base, String precurrency) {
        List<V> dataList = new ArrayList<>();
        dataList.add(value);
        convertExchangeRates(dataList, target, base, precurrency);
        return dataList.get(0).getV();
    }

    public void convertExchangeRates(List<V> dataList, String target, String base, String precurrency) {
        List<Double> resultList = new ArrayList<>();
        if ("EUR".equals(target) && PreEuropeExchangeUtil.isPreEurope(precurrency)) {
            target = precurrency + target;
        }
        Date date = null;
        double[] rates = new double[dataList.size()];
        for (int i = 0; i < dataList.size(); i++) {
            rates[i] = Double.parseDouble(dataList.get(i).getV());
            if (date == null) {
                date = DateUtil.convertDate(dataList.get(i).getI());
            }
        }
        CalcLibraryDefine.ErrorCode errorCode = conversionCenter.convertExchangeRates(rates, base, target, date, resultList);
        if (!errorCode.equals(CalcLibraryDefine.ErrorCode.NoError)) {
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setV(EMPTY_RESULT);
            }
        } else {
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setV(formatValue(resultList.get(i)));
            }
        }
    }

    private String formatValue(double value) {
        if (Double.isNaN(value)) {
            return EMPTY_RESULT;
        } else if (Double.isInfinite(value)) {
            return String.valueOf(value);
        }
        return format.format(value);
    }
}
