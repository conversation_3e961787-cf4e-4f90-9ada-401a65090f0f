package com.morningstar.dataac.martgateway.core.calculationlib.util;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;

public class CurrencyIdUtil {
    private static List<String> idList;
    private static Map<String, String> currencyIdMap;
    private static List<String> calcIdList;
    private static final String FILE = "CurrConv.xml";

    static {
        idList = new ArrayList<>();
        currencyIdMap = new HashMap<>();
        calcIdList = new ArrayList<>();
        try {
            InputStream is = CurrencyIdUtil.class.getClassLoader().getResourceAsStream(FILE);
            Document doc = new SAXReader().read(is);
            List currencyList = doc.selectNodes("/root/currency/curr");
            for (Object node : currencyList) {
                Element element = (Element) node;
                String cid = element.attributeValue("cid");
                idList.add("CU$$$$$" + cid);
                currencyIdMap.put("CU$$$$$" + cid, cid);
                currencyIdMap.put(element.attributeValue("pid"), null);
            }
            currencyList = doc.selectNodes("/root/calcs/calc");
            for (Object node : currencyList) {
                Element element = (Element) node;
                String id = element.attributeValue("id");
                calcIdList.add(id);
            }

        } catch (DocumentException e) {
            LogEntry.error(e, new LogEntity(EVENT_TYPE, "initialize"),
                    new LogEntity(EVENT_DESCRIPTION, "load currency id document failure"));
        }
    }

    public static String getCurrencyId(String performanceId) {
        return currencyIdMap.get(performanceId);
    }

    public static int getIndexByI(String key) {
        return (int)(LocalDate.parse(key).toEpochDay() - LocalDate.parse("1900-01-01").toEpochDay());
    }

    public static List<String> getIdList() {
        return idList;
    }

    public static boolean isExchangeId(String secId) {
        return secId.startsWith("CU$$$$$") || currencyIdMap.containsKey(secId);
    }
}
