package com.morningstar.dataac.martgateway.core.calculationlib.conversion;


import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Base64;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_MESSAGE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;


public class ExchangeRateLoader {
    private RedisTemplate<String, String> syncStorageDataTemplate;

    public ExchangeRateLoader(RedisTemplate<String, String> syncStorageDataTemplate) {
        this.syncStorageDataTemplate = syncStorageDataTemplate;
    }

    public double[] getExchangeRates(String currencyId, String startDate, String endDate) {
        int length = (int) (LocalDate.parse(endDate).toEpochDay() - LocalDate.parse(startDate).toEpochDay() + 1);
        double[] resultArray = new double[length];
        Arrays.fill(resultArray, Double.NaN);
        String exchangeRateKey = "DP_CurrencyId/ExchangeRate/" + currencyId;
        String dataValue = syncStorageDataTemplate.opsForValue().get(exchangeRateKey);
        if (StringUtils.isEmpty(dataValue)) {
            LogEntry.info(new LogEntity(LogAttribute.EVENT_DESCRIPTION, MessageFormat.format("Exchange Rate is empty for {0}", exchangeRateKey)),
                    new LogEntity(EXCEPTION_TYPE, "ExchangeRateLoader"));
            return resultArray;
        }
        JSONObject extractedObject = JsonUtils.retrieveStoredContent(dataValue);
        String roe = extractedObject.optString("roe");

        if (StringUtils.isNotEmpty(roe)) {
            try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(Base64.getDecoder().decode(roe));
                    DataInputStream dataInputStream = new DataInputStream(byteArrayInputStream)) {
                int startDateValue = dataInputStream.readInt();
                int counter = 0;
                while (dataInputStream.available() > 0) {
                    double exchangeRate = dataInputStream.readDouble();
                    resultArray[counter + startDateValue] = exchangeRate;
                    counter++;
                }
            } catch (Exception e) {
                LogEntry.error(e, new LogEntity(LogAttribute.EVENT_DESCRIPTION,
                                "Error occurs while dispatching exchange rate document"),
                        new LogEntity(EXCEPTION_TYPE, e.getClass()),
                        new LogEntity(EXCEPTION_MESSAGE, e.getMessage()));
            }
        }

        return resultArray;
    }

}
