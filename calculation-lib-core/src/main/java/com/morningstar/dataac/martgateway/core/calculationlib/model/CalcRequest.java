package com.morningstar.dataac.martgateway.core.calculationlib.model;

import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
public class CalcRequest {

    private List<String> idList;
    private List<String> calcDps;
    private List<GridviewDataPoint> customCalcDps;
    private String currency;
    private String preCurrency;
    private String startDate;
    private String endDate;
    @Builder.Default
    private String adjustment = "";
    @Builder.Default
    private String annualized = "true";
    @Builder.Default
    private String readCache = "";
    private List<IdMapper> idMappers;
    private String userId;

    private String postTax;
    private String frequency;
    private String benchmark;
    private String riskFree;
    private String sourceId;
    private String compoundingMethod;
    private String windowType;
    private String windowSize;
    private String stepSize;
    private String annualDays;
    private String skipHoliday;
    private String requireContinueData;
    private String extendedPerformance;
    private String requestId;
    private Boolean isApplyIsraelsenModification;

    public static CalcRequest buildApiRequest(MartRequest martRequest) {
        List<String> dpList = martRequest.getDps().stream()
                .map(DataPointRepository::getByNid)
                .filter(dp -> dp.isCalcApi(martRequest.getCurrency()))
                .map(DataPoint::getNid)
                .collect(Collectors.toList());
        List<String> idList = martRequest.getIds()
                .stream()
                .filter(id -> !StringUtils.isEmpty(id) && !"null".equalsIgnoreCase(id))
                .collect(Collectors.toList());

        return CalcRequest.builder()
                .customCalcDps(martRequest.getCustomCalcDataPoints())
                .readCache((martRequest.getReadCache() != null) ? martRequest.getReadCache() : "")
                .calcDps(dpList)
                .idList(idList)
                .idMappers(martRequest.getIdMappers())
                .userId(martRequest.getUserId())
                .postTax(martRequest.getPostTax())
                .requestId(martRequest.getRequestId())
                .currency(martRequest.getCurrency())
                .build();
    }

    public static CalcRequest buildLibRequest(MartRequest martRequest) {
        List<String> dpList = martRequest.getDps().stream()
                .map(DataPointRepository::getByNid)
                .filter(dp -> dp.isCalcLib(martRequest.getCurrency()) || dp.isCustomCalc())
                .map(DataPoint::getNid)
                .collect(Collectors.toList());
        List<String> idList = martRequest.getIds()
                .stream()
                .filter(id -> !StringUtils.isEmpty(id) && !"null".equalsIgnoreCase(id))
                .collect(Collectors.toList());

        return CalcRequest.builder()
                .customCalcDps(martRequest.getCustomCalcDataPoints())
                .readCache(martRequest.getReadCache())
                .calcDps(dpList)
                .idList(idList)
                .idMappers(martRequest.getIdMappers())
                .userId(martRequest.getUserId())
                .adjustment(martRequest.getAdjustment())
                .startDate(martRequest.getStartDate())
                .endDate(martRequest.getEndDate())
                .currency(martRequest.getCurrency())
                .preCurrency(martRequest.getPreCurrency())
                .build();
    }

    public boolean isTimeseriesRequest() {
        return (CollectionUtils.isNotEmpty(customCalcDps) && StringUtils.hasLength(customCalcDps.get(0).getStartDate()) && StringUtils.hasLength(customCalcDps.get(0).getEndDate())) ||
                (StringUtils.hasLength(startDate) && StringUtils.hasLength(endDate));
    }

    public CalcRequest shallowCopy() {
        return  CalcRequest.builder()
                .idList(this.getIdList())
                .calcDps(this.getCalcDps())
                .customCalcDps(this.customCalcDps)
                .currency(this.getCurrency())
                .preCurrency(this.getPreCurrency())
                .startDate(this.getStartDate())
                .endDate(this.getEndDate())
                .adjustment(this.getAdjustment())
                .annualized(this.getAnnualized())
                .readCache(this.getAnnualized())
                .idMappers(this.getIdMappers())
                .userId(this.getUserId())
                .postTax(this.getPostTax())
                .frequency(this.getFrequency())
                .benchmark(this.getBenchmark())
                .riskFree(this.getRiskFree())
                .sourceId(this.getSourceId())
                .compoundingMethod(this.getCompoundingMethod())
                .windowType(this.getWindowType())
                .windowSize(this.getWindowSize())
                .stepSize(this.getStepSize())
                .annualDays(this.getAnnualDays())
                .skipHoliday(this.getSkipHoliday())
                .requireContinueData(this.getRequireContinueData())
                .build();
    }
}
