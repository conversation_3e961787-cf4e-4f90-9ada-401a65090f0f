package com.morningstar.dataac.martgateway.core.calculationlib.conversion;

import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.calculationlib.util.CurrencyIdUtil;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import java.math.BigDecimal;
import org.springframework.util.StringUtils;

import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public final class CurrencyConversionHelper {
    public static final String CURCHANGE = "81280";
    public static final String CURID = "81281";
    public static final String ENDDATE = "81282";
    public static final String BASECUR = "3010";
    public static final String PERFORMANCEDATE = "5764";
    public static final String NOT_A_NUMBER = "NaN";
    public static final String EMPTY_RESULT = "";

    private static final String HIS_DATE = "1900-01-01";
    private static final String DP_100000 = "100000";
    private static final String DP_100001 = "100001";
    private static final String DP_100002 = "100002";
    private static final String DP_100003 = "100003";

    private static final String FREQ_0 = "0";
    private static final String FREQ_1 = "1";
    private static final String FREQ_2 = "2";
    private static final String FREQ_3 = "3";
    private static final String FREQ_4 = "4";

    private final CurrencyConversion currencyConversion;

    public CurrencyConversionHelper(CurrencyConversion currencyConversion) {
        this.currencyConversion = currencyConversion;
    }

    public String convertCDExchangeRate(String secId, DataPoint dataPoint, String value, String endDateStr,
            String hisCurrency, String target, String inceptionDate) {


        String calcId = dataPoint.getCalculation().getDp().getNid();

        if (!StringUtils.hasText(endDateStr)) {
            return EMPTY_RESULT;
        }

        return switch (calcId) {
            case DP_100003 ->
                    convertForDP100003(secId, dataPoint, value, endDateStr, hisCurrency, target, inceptionDate);
            case DP_100001, DP_100002 -> convertForDP100001AND100002(secId, value, endDateStr, hisCurrency, target);
            default -> EMPTY_RESULT;
        };
    }

    private String convertForDP100003(String secId, DataPoint dataPoint, String value, String endDateStr,
                                      String hisCurrency, String target, String inceptionDate) {
        String freq = dataPoint.getCalculation().getFreq();
        BigDecimal trailing = new BigDecimal(dataPoint.getCalculation().getTrailingPeriod());
        Date endDate = DateFormatUtil.parseDate(endDateStr);
        endDate = getEndDate(endDate, freq, dataPoint.getCalculation().getOffset());
        Calendar calendar;
        Double year;
        if (trailing.intValue() == -1) {
            if (!StringUtils.hasText(inceptionDate)) {
                return EMPTY_RESULT;
            }
            calendar = Calendar.getInstance();
            calendar.setTime(DateFormatUtil.parseDate(inceptionDate));
            year = getAnnualizeYear(endDate, inceptionDate);
        } else if (trailing.intValue() <= 0) {
            calendar = trailStartDate(endDate, trailing);
            year = getAnnualizeYear(trailing, freq);
        } else {
            calendar = trailStartDateWithFrequency(endDate, trailing, freq, secId);
            year = getAnnualizeYear(trailing, freq);
        }

        if("0".equals(dataPoint.getCalculation().getAnn()) || "false".equals(dataPoint.getCalculation().getAnn())){
            return currencyConversion.convertReturn(Double.parseDouble(value),
                    calendar.getTime(), endDate, hisCurrency, HIS_DATE, target, null);
        } else {
            value = annualize(value, year, false);
            value = currencyConversion.convertReturn(Double.parseDouble(value),
                    calendar.getTime(), endDate, hisCurrency, HIS_DATE, target, null);
            return annualize(value, year, true);
        }
    }

    private String convertForDP100001AND100002(String secId, String value, String endDateStr, String hisCurrency, String target) {
        if (CurrencyIdUtil.isExchangeId(secId)) {
            if (!StringUtils.hasText(target) || target.equalsIgnoreCase("USD")) {
                return value;
            }
            return currencyConversion.convertExchangeRates(new V(endDateStr, value), target, hisCurrency, "");
        } else {
            return currencyConversion.convertCurrencySeries(new V(endDateStr, value), hisCurrency, HIS_DATE, target, null);
        }
    }

    public List<V> handleConversion(String targetCurrency, String preCurrency, String baseCurrency, String secId, List<V> dpValues, List<V> endDateValues, String[] historyCurrency, String[] historyDate, DataPoint dataPoint) {

        if (CurrencyIdUtil.isExchangeId(secId)) {
            baseCurrency = "USD";
        }
        historyCurrency[historyCurrency.length - 1] = baseCurrency;
        if (StringUtils.hasText(targetCurrency) && !baseCurrency.equalsIgnoreCase(targetCurrency)) {
            String calcId = dataPoint.getCalculation().getDp().getNid();
            String freq = dataPoint.getCalculation().getFreq();
            if (DP_100003.equals(calcId)) {
                currencyConversion.convertReturn(dpValues, historyCurrency, historyDate, targetCurrency, preCurrency, freq);
            } else {
                convertReturn(secId, calcId, targetCurrency, preCurrency , dpValues, historyCurrency, historyDate, endDateValues);
            }
        }

        return dpValues;
    }

    private void convertReturn(String secId, String calcId, String targetCurrency,
                               String preCurrency, List<V> dpValues, String[] historyCurrency, String[] historyDate, List<V> endDateValues) {
        if (endDateValues != null) {
            for (int i = 0; i < dpValues.size(); i++) {
                V v = dpValues.get(i);
                v.setI(DateFormatUtil.formatTime(Double.valueOf(endDateValues.get(i).getV()).intValue()));
            }
        }
        //todo: We haven't covered this case for equity
        if (CurrencyIdUtil.isExchangeId(secId)) {
            currencyConversion.convertExchangeRates(dpValues, targetCurrency, preCurrency, historyCurrency[historyCurrency.length - 1]);
        } else {
            String[] realHistoryCurrency;
            String[] realHistoryDate;
            if (DP_100000.equals(calcId)) {
                realHistoryCurrency = new String[]{historyCurrency[historyCurrency.length - 1]};
                realHistoryDate = new String[]{historyDate[0]};
            } else {
                realHistoryCurrency = historyCurrency;
                realHistoryDate = historyDate;
            }
            currencyConversion.convertCurrencySeries(dpValues, realHistoryCurrency, realHistoryDate, targetCurrency, preCurrency);
        }
    }

    private Calendar trailStartDate(Date endDate, BigDecimal trailingPeriod) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endDate);
        if (trailingPeriod.intValue() == 0) {
            calendar.set(Calendar.DAY_OF_YEAR, 1);
        }
        if (trailingPeriod.intValue() == -2) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
        }
        if (trailingPeriod.intValue() == -3) {
            int month = calendar.get(Calendar.MONTH);
            calendar.set(Calendar.MONTH, month - month % 3);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
        }
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar;
    }

    private Calendar trailStartDateWithFrequency(Date endDate, BigDecimal trailingPeriod, String frequency, String secId) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endDate);

        //frequency 0 is the default value when it is null
        switch (!StringUtils.hasText(frequency) ? FREQ_0 : frequency) {
            case FREQ_1 -> {
                calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
                calendar.add(Calendar.WEEK_OF_YEAR, -trailingPeriod.intValue());
            }
            case FREQ_2 -> {
                calendar.add(Calendar.MONTH, -trailingPeriod.intValue());
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            }
            case FREQ_3 -> calendar.add(Calendar.MONTH, -(trailingPeriod.multiply(new BigDecimal(3)).intValue()));
            case FREQ_4 -> calendar.add(Calendar.YEAR, -trailingPeriod.intValue());
            default -> { //set FQ_0 as default case
                if (!CurrencyIdUtil.isExchangeId(secId) && trailingPeriod.intValue() % 30 == 0) {
                    int month = trailingPeriod.intValue() / 30;
                    calendar.add(Calendar.MONTH, -month);
                } else {
                    calendar.add(Calendar.DAY_OF_YEAR, -trailingPeriod.intValue());
                }
            }
        }

        return calendar;
    }

    private Double getAnnualizeYear(BigDecimal trailingPeriod, String frequency) {
        if (trailingPeriod.intValue() > 0) {
            return switch (StringUtils.hasText(frequency) ? frequency : "") {
                case FREQ_0 -> trailingPeriod.divide(new BigDecimal(360),0, RoundingMode.DOWN).doubleValue();
                case FREQ_2 -> trailingPeriod.divide(new BigDecimal(12),0, RoundingMode.DOWN).doubleValue();
                case FREQ_3 -> trailingPeriod.multiply(new BigDecimal(3)).divide(new BigDecimal(12),0,RoundingMode.DOWN).doubleValue();
                case FREQ_4 -> trailingPeriod.doubleValue();
                default -> 0d;
            };
        }

        return 0d;
    }

    private Double getAnnualizeYear(Date endDate, String inceptionDate) {
        if (!StringUtils.hasText(inceptionDate)) {
            return 0d;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateFormatUtil.parseDate(inceptionDate));
        return (DateFormatUtil.daysBetween(endDate, calendar.getTime()) / 365.25);
    }

    private String annualize(String value, Double year, boolean isAnnualize) {
        if (!StringUtils.hasText(value) || year < 2) {
            return value;
        }
        double valueDouble = Double.parseDouble(value);
        if (0.0 == valueDouble) {
            return value;
        }
        if (isAnnualize) {
            year = 1.0 / year;
        }
        valueDouble = valueDouble / 100 + 1;
        valueDouble = (Math.pow(valueDouble, year) - 1) * 100;
        return formatValue(valueDouble);
    }

    private Date getEndDate(Date endDate, String freq, String offset) {
        if (!StringUtils.hasText(offset) || "0".equals(offset)) {
            return endDate;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endDate);
        int trailing = Integer.parseInt(offset);

        //FREQ_0 is the default branch
        switch (!StringUtils.hasText(freq) ? FREQ_0 : freq) {
            case FREQ_1 -> {
                calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
                calendar.add(Calendar.WEEK_OF_YEAR, -trailing);
            }
            case FREQ_2 -> calendar.add(Calendar.MONTH, -trailing);
            case FREQ_3 -> calendar.add(Calendar.MONTH, -(trailing * 3));
            case FREQ_4 -> calendar.add(Calendar.YEAR, -trailing);
            default -> calendar.add(Calendar.DAY_OF_YEAR, -trailing);
        }

        return calendar.getTime();
    }

    private String formatValue(double value) {
        if (Double.isInfinite(value) || Double.isNaN(value)) {
            return String.valueOf(value);
        }
        return String.format("%.5f", value);
    }

}
