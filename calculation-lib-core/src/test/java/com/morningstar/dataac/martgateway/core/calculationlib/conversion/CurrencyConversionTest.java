package com.morningstar.dataac.martgateway.core.calculationlib.conversion;

import com.morningstar.calculationlibrary.CalcLibraryDefine;
import com.morningstar.dataac.martgateway.core.calculationlib.util.CurrencyIdUtil;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
public class CurrencyConversionTest {

    private V data;
    private final String hisCurr = "2";
    private final String hisDate = "2020-10-22";
    private final String target = "1";
    private final String precurrency = "ESP";
    private final List<V> dataList = new ArrayList<>();
    private final String[] hisCurrs = {"2","1"};
    private final String[] hisDates =  {"2020-10-07","2020-11-08"};
    private final String freq = "66";
    private final double value = 1.300;
    private Date start = new Date();
    private Date end = new Date();

    private int index = (int) (LocalDate.parse("1999-01-01").toEpochDay() - LocalDate.parse("1900-01-01").toEpochDay());
    @Mock
    private ExchangeRateLoader exchangeRateLoader;
    @Mock
    private RedisTemplate<String, String> redisTemplate;
    @Mock
    private ValueOperations mockvalueOperations;
    private ExchangeRateLoader getExchangeRateLoader;
    private CurrencyConversion currencyConversion;
    @Mock
    private ConversionCenter conversionCenter;
    private ConversionCenter conversionCenterTest;

    private String host = "8080";
    private String region = "east-1";
    private String baseUrl = "test";
    private String uri = "test2";

    private static MockedStatic<CurrencyIdUtil> currencyIdUtilMockedStatic;


    @Before
    public void setUp(){
        data = new V("2020-10-23","6.982");
        dataList.add(data);
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, 5);
        end = ca.getTime();

        currencyIdUtilMockedStatic = Mockito.mockStatic(CurrencyIdUtil.class, Mockito.RETURNS_DEEP_STUBS);

        Map<String, Double> preEurMap = new HashMap<>();
        preEurMap.put("234",1.5453);
        Map<String, Double> preEurMap2 = new HashMap<>();
        preEurMap2.put("EUR",1.5453);
        getExchangeRateLoader = new ExchangeRateLoader(redisTemplate);
        currencyConversion = new CurrencyConversion(conversionCenter);
    }
    @After
    public void afterEachTest(){
        currencyIdUtilMockedStatic.close();
    }

    @Test
    public void shouldConvertCurrencySeriesNaNTest() {
        when(conversionCenter.convertCurrency(any(),any(),any(),any(),any())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidOutput);
        String result = currencyConversion.convertCurrencySeries(data,hisCurr,hisDate,target,precurrency);
        assertEquals("",result);
    }

    @Test
    public void shouldConvertCurrencyEmptyTest() {
        when(conversionCenter.convertReturn(any(),any(),any(),any(),any(),any())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidOutput);
        currencyConversion.convertReturn(dataList,hisCurrs,hisDates,target,precurrency,freq);
        assertEquals("",dataList.get(0).getV());
    }

    @Test
    public void shouldConvertReturnTest() {
        String hisCurr = "2020-10-30";
        String precurrency = "BEF";
        String target = "EUR";
        String hisDate = "2020-08-20";
        when(conversionCenter.convertReturn(any(),any(),anyDouble(),any(),any(),anyString(),anyList())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidParameter);
        String result = currencyConversion.convertReturn(value,start,end,hisCurr,hisDate,target,precurrency);
        assertEquals("",result);
    }
    @Test
    public void shouldConvertExchangeRatesTest() {
        String precurrency = "BEF";
        String target = "EUR";
        V v = new V("44071","44071");
        String base = "1";
        when(conversionCenter.convertExchangeRates(any(),anyString(),anyString(),any(),anyList())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidOutput);
        String result = currencyConversion.convertExchangeRates(v,target,base,precurrency);
        assertEquals("",result);
    }

    @Test
    public void shouldGetExchangeRatesQ1Test() {
        String currencyId = "CU$$$$$EUR";
        String startDate = "2020-10-22";
        String endDate = "2020-11-22";
        ValueOperations<String,String> vo1 = mock(ValueOperations.class);
        when(redisTemplate.opsForValue()).thenReturn(vo1);
        double[] result = getExchangeRateLoader.getExchangeRates(currencyId,startDate,endDate);
        assertEquals(32, result.length);
    }
    @Test
    public void shouldGetExchangeRatesQ2Test() {
        String currencyId = "CU$$$$$EUR";
        String startDate = "2020-10-22";
        String endDate = "2020-11-22";
        String jsonStr = "{\"startDate\":\"1\",\"exchangeRateList\":\"1,2,3\"}";
        when(redisTemplate.opsForValue()).thenReturn(mockvalueOperations);
        doReturn(jsonStr).when(mockvalueOperations).get(anyString());
        double[] result = getExchangeRateLoader.getExchangeRates(currencyId,startDate,endDate);
        assertNotNull(result[1]);
    }
    //ConversionCenter
    @Test
    public void setExchangeRate() {
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        String currency = "CU$$$$$ERU";
        double[] exchangeRate = {0.98,0.765,1.0966};
        Date startDate = new Date();
        conversionCenterTest.setExchangeRate(currency,exchangeRate,startDate);
    }

    @Test
    public void convertCurrency() {
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        CalcLibraryDefine.DoubleTS[] priceSeries = new CalcLibraryDefine.DoubleTS[]{};
        String[] historyCurrency = {"CU$$$$$ERU","CU$$$$$USA"};
        int[] historyCurrencyDate = {1,2};
        String target = "CU$$$$$ERU";
        List<CalcLibraryDefine.DoubleTS> convertedValue = new ArrayList<>();
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        CalcLibraryDefine.ErrorCode code = conversionCenterTest.convertCurrency(priceSeries,historyCurrency,historyCurrencyDate,target,convertedValue);
    }

    @Test
    public void convertReturn() {
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        CalcLibraryDefine.DoubleTS[] priceSeries = new CalcLibraryDefine.DoubleTS[]{};
        String freq = "12";
        String[] historyCurrency = {"CU$$$$$ERU","CU$$$$$ETF"};
        int[] historyCurrencyDate = {1,2};
        String target = "CU$$$$$ERU";
        List<CalcLibraryDefine.DoubleTS> convertedValue = new ArrayList<>();
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        conversionCenterTest.convertReturn(priceSeries,freq,historyCurrency,historyCurrencyDate,target,convertedValue);

    }


    @Test
    public void testConvertReturn() {
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        Date startDate = new Date();
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, 5);
        Date endDate = ca.getTime();
        double value = 0.98;
        String[] historyCurrency = {"ERU","AUS"};
        int[] historyCurrencyDate = {1492,7853};
        String target = "CU$$$$$ERU";
        List<Double> convertedValue = new ArrayList<>();
        convertedValue.add(value);
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        conversionCenterTest.convertReturn(startDate,endDate,value,historyCurrency,historyCurrencyDate,target,convertedValue);
    }

    @Test
    public void convertExchangeRates() {
        double[] rates = {0.98,1.076};
        String base = "CU$$$$$ERU";
        String target = "CU$$$$$ERU";
        Date date = new Date();
        double value = 0.98;
        List<Double> convertedValue  = new ArrayList<>();
        convertedValue.add(value);
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        conversionCenterTest.convertExchangeRates(rates,base,target,date,convertedValue);
    }
}
