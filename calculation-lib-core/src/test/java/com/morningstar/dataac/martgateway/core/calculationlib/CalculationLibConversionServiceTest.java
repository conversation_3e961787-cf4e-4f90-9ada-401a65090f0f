package com.morningstar.dataac.martgateway.core.calculationlib;

import static com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper.PERFORMANCEDATE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.morningstar.dataac.martgateway.core.calculationlib.entity.CalcMetaData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CalculationLibConversionServiceTest {

	@Mock
	private CurrentResult currentResult;
	@Mock
	private TimeSeriesResult timeSeriesResult;
	@Mock
	private CurrencyConversionHelper currencyConversionHelper;

	private CalculationLibConversionService calculationLibConversionService;

	@BeforeEach
	void setUp(){
		calculationLibConversionService = new CalculationLibConversionService(currencyConversionHelper);
	}

	@Test
	@DisplayName("should convert currency in current result")
	void shouldConvertCurrencySeriesTwoTest(){
		MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic = mockStatic(DataPointRepository.class, Mockito.RETURNS_DEEP_STUBS);
		Map<String, String> mapping = new HashMap<>();
		mapping.put("100002", "US Dollar");
		List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build(),
				DataPoint.builder().nid("25200").name("Currency").src("TSAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
		DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
		Calculation cal = Calculation.builder()
				.dp(dataPoint).trailingPeriod("-1").offset("2").freq("2").cur(dataPoint).endDate(dataPoint)
				.build();
		Calculation cal2 = Calculation.builder()
				.dp(dataPoint).trailingPeriod("-1").offset("2").freq("2").cur(dataPoint).endDate(dataPoint)
				.build();
		DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(false).subDataPoints(dps).build();
		DataPoint dataPoint3 = DataPoint.builder().id("81281").nid("100002").calculation(cal2).src("TSAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();

		when(DataPointRepository.getByNid("25200")).thenReturn(dataPoint2);
		when(DataPointRepository.getByNid("81280")).thenReturn(dataPoint3);
		when(DataPointRepository.getByNid("81280")).thenReturn(dataPoint2);
		when(DataPointRepository.getByNid("5764")).thenReturn(dataPoint3);
		when(DataPointRepository.getByNid("3010")).thenReturn(dataPoint3);

		Set<DataPoint> dpSet = new HashSet<>();
		dpSet.add(dataPoint2);
		String targetCurrency = "CU$$$$$USA";
		Map<String, String> currencyMap = new HashMap<>();
		currencyMap.put("F00000QAW7100002", "US Dollar");
		Map<String, String> inceptionMap = new HashMap<>();
		inceptionMap.put("inceptionMap", "testIn");

		when(currentResult.getValues()).thenReturn(mapping);
		when(currentResult.getId()).thenReturn("F00000QAW7");

        Map<String, DataPoint> endDateDpMap = new HashMap<>();
        endDateDpMap.put(dataPoint2.getNid(), dataPoint2.getCalculation().getEndDate());
        endDateDpMap.put(dataPoint3.getNid(), dataPoint3.getCalculation().getEndDate());
        CalcMetaData calcMetaData = CalcMetaData.builder()
                .baseCurrencyMap(Collections.emptyMap())
                .endDateDpMap(endDateDpMap)
				.secIdCurrencyMap(currencyMap)
				.inceptionMap(inceptionMap)
                .build();

		calculationLibConversionService.convertCurrent(currentResult, dpSet, targetCurrency, calcMetaData);

		assertNotNull(currentResult);
		dataPointRepositoryMockedStatic.close();
	}

	@Test
	@DisplayName("should convert currency in time series result")
	void shouldConvertTSTest(){
		Map<String, String> mapping = new HashMap<>();
		mapping.put("100002", "US Dollar");
		List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build(),
				DataPoint.builder().nid("25200").name("Currency").src("TSAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
		DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
		List<String> requestCalList = Arrays.asList("3", "5");
		Calculation cal = Calculation.builder()
				.dp(dataPoint).trailingPeriod("-1").offset("2").freq("2").cur(dataPoint).endDate(dataPoint)
				.build();
		Calculation cal2 = Calculation.builder()
				.dp(dataPoint).trailingPeriod("-1").offset("2").freq("2").cur(dataPoint).endDate(dataPoint)
				.build();
		DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(false).subDataPoints(dps).build();
		DataPoint dataPoint3 = DataPoint.builder().id("81281").nid("100002").calculation(cal2).src("TSAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
		List<String> requestCalcDps = Arrays.asList("25200", "81280");
		Set<DataPoint> dpSet = new HashSet<>();
		dpSet.add(dataPoint2);
		Map<String, String> currencyMap = new HashMap<>();
		currencyMap.put("F00000QAW7100002", "US Dollar");
		List<CurrentResult> currentResults = new ArrayList<>();
		String id = "CU$$$$$USA";
		Map<String, String> valuesMap = new HashMap<>();

		mapping.put("100002", "US Dollar");
		CurrentResult currentResult = new CurrentResult(id,valuesMap, Instant.now());

		currentResults.add(currentResult);
		Map<String, List<CurrentResult>> currencyChangeMap = new HashMap<>();
		currencyChangeMap.put("F00000QAW781280", currentResults);
		List<V> dpValues = List.of(new V("81281", "44701"));
		Map<String, List<V>> dpMap = new HashMap<>();
		dpMap.put("100002",dpValues);

		when(timeSeriesResult.getValues()).thenReturn(dpMap);
		when(timeSeriesResult.getId()).thenReturn("F00000QAW7");

        Map<String, DataPoint> endDateDpMap = new HashMap<>();
        endDateDpMap.put(dataPoint2.getNid(), dataPoint2.getCalculation().getEndDate());
        endDateDpMap.put(dataPoint3.getNid(), dataPoint3.getCalculation().getEndDate());

        CalcMetaData calcMetaData = CalcMetaData.builder()
                .baseCurrencyMap(Collections.emptyMap())
                .endDateDpMap(endDateDpMap)
				.secIdCurrencyMap(currencyMap)
				.currencyChangeMap(currencyChangeMap)
                .build();

		calculationLibConversionService.convertTS(timeSeriesResult, dpSet, "CU$$$$$USA", "CU$$$$$USA", calcMetaData);

		assertNotNull(timeSeriesResult);
	}

	@Test
	@DisplayName("should convert currency in group result")
	void shouldConvertCurrencyInGroupResult() {
		//given
		String secId = "TEST00000A";
		String dpId = "AB001";
		DataPoint dataPoint = DataPoint.builder()
				.id(dpId)
				.nid(dpId)
				.calculation(
						Calculation.builder()
								.penceTraded(DataPoint.builder().nid(dpId).build())
								.ccy(DataPoint.builder().nid(dpId).build())
								.endDate(DataPoint.builder().nid(dpId)
										.build())
								.build()
				).build();
		Map<String, String> entry = new HashMap<>();
		entry.put(dataPoint.getId(), "123.0000");
		GroupResult groupResult = new GroupResult(secId,
				Map.of(secId, List.of(new CurrentResult(secId, entry))));
		Set<DataPoint> dpSet = Set.of(dataPoint);
		String targetCurrency = "EUR";
		Map<String, String> secIdCurrencyMap = Map.of("TEST00000AAB001", "USD");
		Map<String, String> inceptionMap = Map.of(secId + PERFORMANCEDATE, "2000-01-01");
		Map<String, DataPoint> endDateDpMap = Map.of(dataPoint.getNid(), dataPoint.getCalculation().getEndDate());
		CalcMetaData calcMetaData = CalcMetaData.builder()
				.baseCurrencyMap(Collections.emptyMap())
				.endDateDpMap(endDateDpMap)
				.secIdCurrencyMap(secIdCurrencyMap)
				.inceptionMap(inceptionMap)
				.build();
		when(currencyConversionHelper.convertCDExchangeRate(anyString(), any(), anyString(), anyString(), anyString(), anyString(), anyString()))
				.thenReturn("456.000");

		//when
		calculationLibConversionService.convertGroup(groupResult, dpSet, targetCurrency, calcMetaData);

		//then
		assertEquals("456.000", groupResult.getValues().get(secId).get(0).getValues().get(dpId));
	}
}
