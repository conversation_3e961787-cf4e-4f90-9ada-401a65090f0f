package com.morningstar.dataac.martgateway.core.calculationlib.conversion;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CurrencyConversionHelperTest {

    @Mock
    private CurrencyConversion currencyConversion;

    private CurrencyConversionHelper currencyConversionHelper;

    private static final String secId = "F000010TIH";
    private static final String hisCurrency = "CU$$$$$USD";
    private static final String endDateStr = "2020-10-30";
    private static String target;
    private static String inceptionDate;

    @BeforeEach
    void setUp(){
        inceptionDate = "2020-08-20";
        target = "1";
        currencyConversionHelper = new CurrencyConversionHelper(currencyConversion);
    }

    @Test
    void shouldConvertCDExchangeRateNaNTest() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("81280").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "1";
        Calculation cal = Calculation.builder()
                .dp(dataPoint)
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("81280").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String result = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint2,value,endDateStr,hisCurrency,target,inceptionDate);
        assertEquals("",result);
    }
    @Test
    void shouldConvertCDExchangeRateTest() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100003").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2").ann("0")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100003").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        when(currencyConversion.convertReturn(anyDouble(),any(),any(),anyString(),anyString(),anyString(),any())).thenReturn("check");
        String result = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint2,value,endDateStr,hisCurrency,target,inceptionDate);
        assertEquals("check",result);
    }
    @Test
    void shouldConvertCDExchangeRate2Test() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        when(currencyConversion.convertCurrencySeries(any(),anyString(),anyString(),anyString(),any())).thenReturn("check");
        String result = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint2,value,endDateStr,hisCurrency,target,inceptionDate);
        assertEquals("check",result);
    }

    @Test
    void shouldConvertCDExchangeRate3Test() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100003").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("0").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100003").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        when(currencyConversion.convertReturn(anyDouble(),any(),any(),anyString(),anyString(),anyString(),any())).thenReturn("check");
        String result = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint2,value,endDateStr,hisCurrency,target,inceptionDate);
        assertEquals("check",result);
    }
    @Test
    void shouldConvertCDExchangeRate4Test() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100003").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("-1").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100003").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal_two = Calculation.builder()
                .dp(dataPoint).trailingPeriod("-1").offset("2").freq("2")
                .build();
        DataPoint dataPoint__two = DataPoint.builder().id("81280").nid("100003").calculation(cal_two).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();

        Calculation cal_2 = Calculation.builder()
                .dp(dataPoint).trailingPeriod("-2").offset("2").freq("2")
                .build();
        DataPoint dataPoint_2 = DataPoint.builder().id("81280").nid("100003").calculation(cal_2).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal_3 = Calculation.builder()
                .dp(dataPoint).trailingPeriod("-3").offset("2").freq("2")
                .build();
        DataPoint dataPoint_3 = DataPoint.builder().id("81280").nid("100003").calculation(cal_3).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        when(currencyConversion.convertReturn(anyDouble(),any(),any(),anyString(),anyString(),anyString(),any())).thenReturn("2");
        String result = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint2,value,endDateStr,hisCurrency,target,inceptionDate);
        String result_2 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_2,value,endDateStr,hisCurrency,target,inceptionDate);
        String result_3 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_3,value,endDateStr,hisCurrency,target,inceptionDate);
        inceptionDate = "2010-08-20";
        String result_4 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint__two,value,endDateStr,hisCurrency,target,inceptionDate);
        assertEquals("2",result);
        assertEquals("2",result_2);
        assertEquals("2",result_3);
        assertEquals("0.19765",result_4);
    }

    @Test
    void shouldConvertCDExchangeRate5Test() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100003").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("30").offset("2").freq("0")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100003").calculation(cal).src("CDAPI")
                .name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal_3 = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("0")
                .build();
        DataPoint dataPoint_3 = DataPoint.builder().id("81280").nid("100003").calculation(cal_3).src("CDAPI")
                .name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal_4 = Calculation.builder()
                .dp(dataPoint).trailingPeriod("30").offset("2").freq("1")
                .build();
        DataPoint dataPoint_4 = DataPoint.builder().id("81280").nid("100003").calculation(cal_4).src("CDAPI")
                .name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal_5 = Calculation.builder()
                .dp(dataPoint).trailingPeriod("30").offset("2").freq("3")
                .build();
        DataPoint dataPoint_5 = DataPoint.builder().id("81280").nid("100003").calculation(cal_5).src("CDAPI")
                .name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal_6 = Calculation.builder()
                .dp(dataPoint).trailingPeriod("30").offset("2").freq("4")
                .build();
        DataPoint dataPoint_6 = DataPoint.builder().id("81280").nid("100003").calculation(cal_6).src("CDAPI")
                .name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dataPoint_8 = DataPoint.builder().id("81280").nid("100002").calculation(cal_6).src("CDAPI")
                .name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();

        when(currencyConversion.convertReturn(anyDouble(),any(),any(),anyString(),anyString(),anyString(),any())).thenReturn("3453");
        String result = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint2,value,endDateStr,hisCurrency,target,inceptionDate);
        String result_3 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_3,value,endDateStr,hisCurrency,target,inceptionDate);
        String result_4 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_4,value,endDateStr,hisCurrency,target,inceptionDate);
        String result_5 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_5,value,endDateStr,hisCurrency,target,inceptionDate);
        String result_6 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_6,value,endDateStr,hisCurrency,target,inceptionDate);
        String result_7 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_6,value,"",hisCurrency,target,inceptionDate);
        String result_8 = currencyConversionHelper.convertCDExchangeRate(secId,dataPoint_8,value,"",hisCurrency,target,inceptionDate);
        assertEquals("3453",result);
        assertEquals("3453",result_3);
        assertEquals("3453",result_4);
        assertEquals("66.53810",result_5);
        assertEquals("12.63841",result_6);
        assertEquals("",result_7);
        assertEquals("",result_8);
    }

    @ParameterizedTest
    @DisplayName("when cal dpId is 100001 or 100002 and target is USD should convert CD exchange rate")
    @ValueSource(strings ={"100001", "100002"})
    void givenCalId100001Or100002TargetUSD_shouldHandleConvertCDExchangeRate(String nid) {
        //given
        target = "USD";
        String value = "1";
        DataPoint calDataPoint = DataPoint.builder()
                .id("81280").nid(nid).src("CDAPI").name("Performance Currency Change")
                .groupName("1dOperationGroup").group(true).subDataPoints(Collections.emptyList()).build();
        Calculation cal = Calculation.builder()
                .dp(calDataPoint).trailingPeriod("30").offset("2").freq("0")
                .build();

        DataPoint dataPoint = DataPoint.builder()
                .id("81280").nid(nid)
                .calculation(cal)
                .src("CDAPI")
                .name("Performance Currency Change")
                .groupName("1dOperationGroup")
                .group(true)
                //.subDataPoints(dps)
                .build();
        //when
        String result = currencyConversionHelper.convertCDExchangeRate("0P00000ALB", dataPoint, value, endDateStr, hisCurrency,
                target, inceptionDate);
        //then
        assertEquals(value,result);
    }

    @ParameterizedTest
    @DisplayName("when cal dpId is 100001 or 100002 and target is non USD should convert CD exchange rate")
    @ValueSource(strings ={"100001", "100002"})
    void givenCalId100001Or100002TargetNotUSD_shouldHandleConvertCDExchangeRate(String nid) {
        //given
        target = "EUR";
        String value = "1";
        DataPoint calDataPoint = DataPoint.builder()
                .id("81280").nid(nid).src("CDAPI").name("Performance Currency Change")
                .groupName("1dOperationGroup").group(true).subDataPoints(Collections.emptyList()).build();
        Calculation cal = Calculation.builder()
                .dp(calDataPoint).trailingPeriod("30").offset("2").freq("0")
                .build();

        DataPoint dataPoint = DataPoint.builder()
                .id("81280").nid(nid)
                .calculation(cal)
                .src("CDAPI")
                .name("Performance Currency Change")
                .groupName("1dOperationGroup")
                .group(true)
                //.subDataPoints(dps)
                .build();
        when(currencyConversion.convertExchangeRates(any(V.class), anyString(), anyString(), anyString())).thenReturn("999");
        //when
        String result = currencyConversionHelper.convertCDExchangeRate("0P00000ALB", dataPoint, value, endDateStr, hisCurrency,
                target, inceptionDate);
        //then
        assertEquals("999",result);
    }

    @Test
    void shouldHandleConversionTest() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String baseCurrency = "CU$$$$$USD";
        List<V> dpValues = Arrays.asList(new V("44701","44701"));
        List<V> endDateValues = Arrays.asList(new V("44705","44705"));
        String[] historyCurrency = {"CU$$$$$USD"};
        String[] historyDate = {"2020-11-06"};
        List<V> result = currencyConversionHelper.handleConversion("CU$$$$$USA", "CU$$$$$USA", baseCurrency, secId, dpValues, endDateValues, historyCurrency, historyDate, dataPoint2);
        assertEquals(1,result.size());
    }

    @Test
    void shouldHandleConversion2Test() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("100000").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100000").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100000").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String baseCurrency = "CU$$$$$USD";
        List<V> dpValues = Arrays.asList(new V("44701","44701"));
        List<V> endDateValues = Arrays.asList(new V("44705","44705"));
        String[] historyCurrency = {"CU$$$$$USD"};
        String[] historyDate = {"2020-11-06"};
        List<V> result = currencyConversionHelper.handleConversion("CU$$$$$USA", "CU$$$$$USA", baseCurrency, secId, dpValues, endDateValues, historyCurrency, historyDate, dataPoint2);
        assertEquals(1,result.size());
    }
    @Test
    void shouldHandleConversion3Test() {
        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("100003").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100003").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100003").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String baseCurrency = "CU$$$$$USD";
        List<V> dpValues = Arrays.asList(new V("44701","44701"));
        List<V> endDateValues = Arrays.asList(new V("44705","44705"));
        String[] historyCurrency = {"CU$$$$$USD"};
        String[] historyDate = {"2020-11-06"};
        List<V> result = currencyConversionHelper.handleConversion("CU$$$$$USA", "CU$$$$$USA", baseCurrency, secId, dpValues, endDateValues, historyCurrency, historyDate, dataPoint2);
        assertEquals(1,result.size());
    }
}
