package com.morningstar.dataac.martgateway.data.customdata.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor @AllArgsConstructor
public class CustomDataPoint {

    private String dataPointId;
    private String setId;
    private String entityIdType;
    private String dataType;
    private String fallbackDataPointId;
    private String dimension;
    private LocalDateTime updatedTimestamp;
}
