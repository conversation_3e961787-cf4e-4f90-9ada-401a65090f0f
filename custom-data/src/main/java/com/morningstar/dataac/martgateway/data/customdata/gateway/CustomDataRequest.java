package com.morningstar.dataac.martgateway.data.customdata.gateway;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPoint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor @AllArgsConstructor
public class CustomDataRequest {

    private String userId;

    private List<String> ids;
    private List<String> dps;
    private List<IdMapper>  idMappers;
    private List<CustomDataPoint> entitledDataPoints;
}
