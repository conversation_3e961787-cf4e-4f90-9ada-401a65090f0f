package com.morningstar.dataac.martgateway.data.customdata.repository;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.Map;

@Slf4j
public class CustomDataAsyncRepo {

    private final Scheduler scheduler;

    private final CustomDataRepo customDataRepo;

    public CustomDataAsyncRepo(Scheduler scheduler, CustomDataRepo customDataRepo) {
        this.scheduler = scheduler;
        this.customDataRepo = customDataRepo;
    }

    public Flux<Map<String, Object>> getCurrentData(String datapointIds, String entityIds) {
        return Mono.fromCallable(() -> {
            try {
                return customDataRepo.getCurrentData(datapointIds, entityIds);
            } catch (DataAccessException e) {
                log.error("failed to get custom current data due to {}", e.getMessage());
                return null;
            }
        }).subscribeOn(scheduler).flatMapMany(Flux::fromIterable);
    }
}
