package com.morningstar.dataac.martgateway.data.customdata.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.data.customdata.gateway.CustomDataRequest;
import com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPoint;
import com.morningstar.dataac.martgateway.data.customdata.repository.CustomDataAsyncRepo;
import com.morningstar.dataac.martgateway.data.customdata.repository.helper.SimpleQueryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.COLUMN_DATA_POINT_ID;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.COLUMN_VALUE;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.DIMENSION_TIMESERIES;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.DIMENSION_CULTURE;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.UNIQUEID;

@Slf4j
public class CustomDataServiceImpl implements CustomDataService{

    private final CustomDataAsyncRepo customDataAsyncRepo;

    public CustomDataServiceImpl(CustomDataAsyncRepo customDataAsyncRepo) {
        this.customDataAsyncRepo = customDataAsyncRepo;
    }

    @Override
    public Flux<Result> getData(CustomDataRequest customDataRequest, String idLevel) {

        List<CustomDataPoint> currentDatapoints = new ArrayList<>();
        List<CustomDataPoint> timeseriesDatapoints = new ArrayList<>();
        List<CustomDataPoint> languageDatapoints = new ArrayList<>();

        for (CustomDataPoint customDataPoint : customDataRequest.getEntitledDataPoints()) {
            switch (customDataPoint.getDimension().toLowerCase()) {
                case DIMENSION_TIMESERIES -> timeseriesDatapoints.add(customDataPoint);
                case DIMENSION_CULTURE -> languageDatapoints.add(customDataPoint);
                default -> currentDatapoints.add(customDataPoint);
            }
        }

        List<String> idList = IdMapUtil.getByIdLevel(customDataRequest.getIdMappers(), idLevel);
        String ids = SimpleQueryBuilder.buildIdsQuery(idList);

        Flux<Result> currentResultFlux = fetchCurrentResult(currentDatapoints, ids);
        Flux<Result> timeseriesResultFlux = fetchTimeseriesResult(currentDatapoints, ids);
        Flux<Result> languageResultFlux = fetchLanguageResult(currentDatapoints, ids);

        log.debug("fetched custom data");

        return Flux.merge(currentResultFlux, timeseriesResultFlux, languageResultFlux);
    }

    private Flux<Result> fetchCurrentResult(List<CustomDataPoint> currentDatapoints, String ids) {
        if (!CollectionUtils.isEmpty(currentDatapoints)) {
            Flux<Map<String, Object>> rawResults = customDataAsyncRepo.getCurrentData(SimpleQueryBuilder.buildDpsQuery(currentDatapoints), ids);
            return rawResults.map(this::transformResult);
        }
        return Flux.empty();
    }

    private Flux<Result> fetchTimeseriesResult(List<CustomDataPoint> timeseriesDatapoints, String ids) {
        return Flux.empty();
    }

    private Flux<Result> fetchLanguageResult(List<CustomDataPoint> languageDatapoints, String ids) {
        return Flux.empty();
    }

    // this transform method converts all supported data type values into string or json value
    private Result transformResult(Map<String, Object> rawResult) {
        final Map<String, String> stringValuesMap = new HashMap<>();
        String uniqueId = (String)rawResult.get(UNIQUEID);
        String dataPointId = null;
        String dataPointValue = null;
        for (Map.Entry<String, Object> entry : rawResult.entrySet()) {
            String col = entry.getKey();
            //TODO handle json string
            String val = String.valueOf(entry.getValue());
            if (COLUMN_DATA_POINT_ID.equals(col)) {
                dataPointId = val;
            } else if (COLUMN_VALUE.equals(col)) {
                dataPointValue = val;
            }
        }
        stringValuesMap.put(dataPointId, dataPointValue);
        return new CurrentResult(uniqueId, stringValuesMap);
    }
}
