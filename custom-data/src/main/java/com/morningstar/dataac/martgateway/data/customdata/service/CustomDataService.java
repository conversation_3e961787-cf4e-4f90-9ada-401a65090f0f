package com.morningstar.dataac.martgateway.data.customdata.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.data.customdata.gateway.CustomDataRequest;
import reactor.core.publisher.Flux;

/**
 * A service interface for fetching custom data by given a custom data request
 */
public interface CustomDataService {

    Flux<Result> getData(CustomDataRequest customDataRequest, String idLevel);
}
