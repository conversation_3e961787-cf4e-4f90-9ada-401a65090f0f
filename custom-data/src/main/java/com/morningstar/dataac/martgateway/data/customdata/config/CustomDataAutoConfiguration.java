package com.morningstar.dataac.martgateway.data.customdata.config;

import com.morningstar.dataac.martgateway.core.common.util.MDCInheritedThreadPoolExecutor;
import com.morningstar.dataac.martgateway.data.customdata.cache.CustomDatapointCache;
import com.morningstar.dataac.martgateway.data.customdata.gateway.CustomDataGateway;
import com.morningstar.dataac.martgateway.data.customdata.repository.CustomDataAsyncRepo;
import com.morningstar.dataac.martgateway.data.customdata.repository.CustomDataRepo;
import com.morningstar.dataac.martgateway.data.customdata.service.CustomDataService;
import com.morningstar.dataac.martgateway.data.customdata.service.CustomDataServiceImpl;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import javax.sql.DataSource;
import java.util.concurrent.ExecutorService;

@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(name = "martgateway.custom-data.enabled", havingValue = "true")
@MapperScan(basePackages = "com.morningstar.dataac.martgateway.data.customdata.repository",
        sqlSessionFactoryRef = "customDataSqlSessionFactory")
@EnableConfigurationProperties(CustomDataProperties.class)
@Slf4j
public class CustomDataAutoConfiguration {

    // *** data source configuration ***//
    @Bean(name = "customDataDataSource")
    @ConditionalOnMissingBean(name = "customDataDataSource")
    @ConfigurationProperties(prefix = "martgateway.custom-data")
    public DataSource customDataDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = "customDataSqlSessionFactory")
    @ConditionalOnMissingBean(name = "customDataSqlSessionFactory")
    public SqlSessionFactory customDataSqlSessionFactory(@Qualifier("customDataDataSource") DataSource customDataDataSource)
            throws Exception {
        final SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(customDataDataSource);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean(name = "customDataScheduler")
    @ConditionalOnMissingBean(name = "customDataScheduler")
    public Scheduler customDataScheduler(@Value("${martgateway.custom-data.maximumPoolSize}") int maximumPoolSize) {
        int minPoolSize = Math.min(20, maximumPoolSize);
        ExecutorService executorService= new MDCInheritedThreadPoolExecutor(minPoolSize, maximumPoolSize,
                "CD-ThreadPool-");
        return Schedulers.fromExecutor(executorService);
    }
    // *** data source configuration end ***//

    @Bean(name = "customDatapointCache")
    @ConditionalOnMissingBean(name = "customDatapointCache")
    public CustomDatapointCache customDatapointCache(@Qualifier("metaDataRedisTemplate") RedisTemplate<String, String>  metaDataRedisTemplate) {
        return  new CustomDatapointCache(metaDataRedisTemplate);
    }

    @Bean(name = "customDataAsyncRepo")
    @ConditionalOnMissingBean(name = "customDataAsyncRepo")
    public CustomDataAsyncRepo customDataAsyncRepo(@Qualifier("customDataScheduler") Scheduler scheduler, CustomDataRepo customDataRepo) {
        return new CustomDataAsyncRepo(scheduler, customDataRepo);
    }

    @Bean(name = "customDataService")
    @ConditionalOnMissingBean(name = "customDataService")
    public CustomDataService customDataService(@Qualifier("customDataAsyncRepo") CustomDataAsyncRepo customDataAsyncRepo) {
        return new CustomDataServiceImpl(customDataAsyncRepo);
    }

    @Bean(name = "customDataGateway")
    @ConditionalOnMissingBean(name = "customDataGateway")
    public CustomDataGateway customDataGateway(@Qualifier("customDataService") CustomDataService customDataService,
                                               @Qualifier("customDatapointCache") CustomDatapointCache customDatapointCache) {
        return new CustomDataGateway(customDatapointCache, customDataService);
    }
}