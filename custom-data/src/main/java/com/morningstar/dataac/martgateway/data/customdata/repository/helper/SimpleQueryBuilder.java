package com.morningstar.dataac.martgateway.data.customdata.repository.helper;

import com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPoint;
import org.springframework.util.StringUtils;

import java.util.List;

public class SimpleQueryBuilder {

    private SimpleQueryBuilder() {
        //void
    }

    public static String buildIdsQuery(List<String> idList) {
        return "'{" + String.join(",", idList) + "}'";
    }

    public static String buildDpsQuery(List<CustomDataPoint> customDataPoints) {
        return "'{" + customDataPoints.stream().map(CustomDataPoint::getDataPointId)
                .reduce("", (a, b) -> StringUtils.hasText(a) ? a + "," + b : b) + "}'";
    }
}
