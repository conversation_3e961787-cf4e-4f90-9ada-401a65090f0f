package com.morningstar.dataac.martgateway.data.customdata.gateway;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapperConstants;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.dataac.martgateway.data.customdata.cache.CustomDatapointCache;
import com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPoint;
import com.morningstar.dataac.martgateway.data.customdata.service.CustomDataService;
import com.morningstar.dataac.martgateway.service.Gateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.FUND_ID;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.SEC_ID;

/**
 * A gateway data service for fetching custom data. It leverages the cache to retrieve custom datapoint metadata
 */
@Slf4j
public class CustomDataGateway implements Gateway<Result, MartRequest> {

    private final CustomDatapointCache customDatapointCache;

    private final CustomDataService customDataService;

    public CustomDataGateway(CustomDatapointCache customDatapointCache, CustomDataService customDataService) {
        this.customDatapointCache = customDatapointCache;
        this.customDataService = customDataService;
    }

    @Override
    public Flux<Result> retrieve(MartRequest martRequest) {
        log.debug("retrieve custom data");
        if (CollectionUtils.isEmpty(martRequest.getDps()) || CollectionUtils.isEmpty(martRequest.getIds())) {
            return Flux.empty();
        }

        CustomDataRequest customDataRequest = buildCustomDataRequest(martRequest);
        /* step 0: retrieve metadata */
        List<CustomDataPoint> customDataPointList = customDatapointCache.getMetadata(customDataRequest.getDps());
        if (customDataPointList.isEmpty()) {
            return Flux.empty();
        }

        /* step 1: entitlement check */
        //TODO use object service to check entitlement
        customDataRequest.setEntitledDataPoints(customDataPointList);

        /* step 2: fetch data for entitled datapoint by different entity id type*/
        //split the datapoints by entity id type
        Map<String, List<CustomDataPoint>> groupedDatapointMap = customDataRequest.getEntitledDataPoints().stream()
                .collect(Collectors.groupingBy(CustomDataPoint::getEntityIdType));

        Flux<Result> resultFlux = Flux.merge(groupedDatapointMap.entrySet().stream()
                .map(entry -> retrieve(customDataRequest, entry.getKey(), entry.getValue())).toList())
                //** use a dedicated scheduler if found performance issue **//
                .subscribeOn(SchedulerConfiguration.getScheduler());

        /* step 3: error handling for result */
        //TODO output error result for unentitled datapoint

        return Flux.merge(resultFlux);
    }

    private CustomDataRequest buildCustomDataRequest(MartRequest martRequest) {
        return CustomDataRequest.builder()
                .userId(martRequest.getUserId())
                .ids(martRequest.getIds())
                .dps(martRequest.getDps())
                .idMappers(martRequest.getIdMappers())
                .build();
    }

    private Flux<Result> retrieve(CustomDataRequest customDataRequest, String entityIdType, List<CustomDataPoint> customDataPointList) {
        customDataRequest.setEntitledDataPoints(customDataPointList);
        String idLevel = convertEntityIdTypeToIdLevel(entityIdType);
        return customDataService.getData(customDataRequest, idLevel);
    }

    private String convertEntityIdTypeToIdLevel(String entityIdType) {
        return switch (entityIdType) {
            case SEC_ID -> IdMapperConstants.SEC_ID;
            case FUND_ID -> IdMapperConstants.FUND_ID;
            default -> throw new IllegalStateException("Unexpected value: " + entityIdType);
        };
    }
}
