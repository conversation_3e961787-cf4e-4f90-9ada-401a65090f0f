package com.morningstar.dataac.martgateway.data.customdata.repository;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.UNIQUEID;

@Mapper
public interface CustomDataRepo {

    /**
     * This is the query to fetch custom data values by datapoint ids and entity ids.
     *
     * @return list of custom data point
     */
    @Select("select t.data_point_id, t.entity_id as " + UNIQUEID + ", t.value from  custom_data.f_ivs_data_values_get(${dps}, ${ids}) t;")
    List<Map<String, Object>> getCurrentData(@Param("dps") String dps, @Param("ids") String ids);
}
