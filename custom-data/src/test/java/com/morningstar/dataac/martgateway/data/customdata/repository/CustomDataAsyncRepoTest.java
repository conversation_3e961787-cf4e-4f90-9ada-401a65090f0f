package com.morningstar.dataac.martgateway.data.customdata.repository;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.SQLWarningException;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;
import reactor.test.StepVerifier;

import java.sql.SQLWarning;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CustomDataAsyncRepoTest {

    private static final String ID1 = "0P00000E2Y";
    private static final String ID2 = "0P00000E2Z";
    private static final String CDP1 = "UP000001";
    private static final String CDP2 = "UP000002";

    @Mock
    private CustomDataRepo customDataRepo;

    private CustomDataAsyncRepo customDataAsyncRepo;

    @BeforeEach
    void setup() {
        customDataAsyncRepo = new CustomDataAsyncRepo(Schedulers.boundedElastic(), customDataRepo);
    }

    @Test
    @DisplayName("get custom current data")
    void givenValidIds_shouldGetResult() {
        //given
        String ids = String.format("{%s,%s}", ID1, ID2);
        String datapointIds = String.format("{%s,%s}", CDP1, CDP2);
        Map<String, Object> resultMap = Map.of("data_point_id", CDP1, "entity_id", ID1, "value", "1234567890");

        when(customDataRepo.getCurrentData(datapointIds, ids)).thenReturn(List.of(resultMap));

        //when
        Flux<Map<String, Object>> rawResults = customDataAsyncRepo.getCurrentData(datapointIds, ids);

        //then
        StepVerifier.create(rawResults)
                .assertNext(result -> assertEquals(CDP1, result.get("data_point_id")))
                .verifyComplete();
    }

    @Test
    @DisplayName("when throw dataAccessException should get empty result")
    void shouldGetEmptyResultWhenThrowDataAccessException() {
        //given
        String ids = String.format("{%s,%s}", ID1, ID2);
        String datapointIds = String.format("{%s,%s}", CDP1, CDP2);

        when(customDataRepo.getCurrentData(datapointIds, ids)).thenThrow(new SQLWarningException("test", new SQLWarning("error")));

        //when
        Flux<Map<String, Object>> rawResults = customDataAsyncRepo.getCurrentData(datapointIds, ids);

        //then
        StepVerifier.create(rawResults)
                .expectNextCount(0)
                .verifyComplete();
    }
}
