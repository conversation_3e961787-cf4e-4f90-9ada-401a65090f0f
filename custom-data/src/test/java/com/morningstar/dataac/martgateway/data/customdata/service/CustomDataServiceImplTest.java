package com.morningstar.dataac.martgateway.data.customdata.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.util.IdMapperConstants;
import com.morningstar.dataac.martgateway.data.customdata.gateway.CustomDataRequest;
import com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPoint;
import com.morningstar.dataac.martgateway.data.customdata.repository.CustomDataAsyncRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.COLUMN_DATA_POINT_ID;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.COLUMN_VALUE;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.DIMENSION_NONE;
import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.SEC_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CustomDataServiceImplTest {
    private static final String ID1 = "0P00000E2Y";
    private static final String CDP1 = "CDP00001";
    private static final String CDV1 = "CDV00001";

    @Mock
    private CustomDataAsyncRepo customDataAsyncRepo;

    private CustomDataService customDataService;

    @BeforeEach
    void setUp() {
        customDataService = new CustomDataServiceImpl(customDataAsyncRepo);
    }

    @Test
    @DisplayName("should fetch custom current data")
    void shouldFetchCustomCurrentData() {
        //given
        String idLevel = IdMapperConstants.SEC_ID;
        List<IdMapper> idMappers = List.of(
                new NonEmptyIdMapper(ID1, "{\"FundId\":\"0P00000E2Z\",\"SecId\":\"0P00000E2Y\"}")
        );
        List<CustomDataPoint> entitledDataPoints = List.of(CustomDataPoint.builder()
                .dataPointId(CDP1).entityIdType(SEC_ID).dimension(DIMENSION_NONE)
                .build());
        CustomDataRequest customDataRequest = CustomDataRequest.builder()
                .ids(List.of(ID1)).dps(List.of(CDP1))
                .entitledDataPoints(entitledDataPoints)
                .idMappers(idMappers)
                .build();
        when(customDataAsyncRepo.getCurrentData(anyString(), anyString())).thenReturn(Flux.just(Map.of(COLUMN_DATA_POINT_ID, CDP1, COLUMN_VALUE, CDV1)));

        //when
        Flux<Result> resultFlux = customDataService.getData(customDataRequest, idLevel);

        //then
        StepVerifier.create(resultFlux)
                .expectSubscription()
                .assertNext(value -> {
                    assertInstanceOf(CurrentResult.class, value);
                    assertEquals(CDV1, value.getValues().get(CDP1));
                })
                .verifyComplete();
    }
}
