package com.morningstar.dataac.martgateway.data.customdata.config;

import com.morningstar.dataac.martgateway.data.customdata.cache.CustomDatapointCache;
import com.morningstar.dataac.martgateway.data.customdata.gateway.CustomDataGateway;
import com.morningstar.dataac.martgateway.data.customdata.repository.CustomDataAsyncRepo;
import com.morningstar.dataac.martgateway.data.customdata.repository.CustomDataRepo;
import com.morningstar.dataac.martgateway.data.customdata.service.CustomDataService;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import reactor.core.scheduler.Scheduler;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CustomDataAutoConfigurationTest {
    @Mock
    private DataSource mockDataSource;
    @Mock
    private Scheduler mockScheduler;
    @Mock
    private CustomDataRepo mockRepo;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;
    @Mock
    private CustomDataAsyncRepo mockAsyncRepo;
    @Mock
    private CustomDataService mockService;
    @Mock
    private CustomDatapointCache mockCache;

    @Test
    public void customDataDataSourceReturnsHikariDataSource() {
        DataSource dataSource = new CustomDataAutoConfiguration().customDataDataSource();
        assertInstanceOf(HikariDataSource.class, dataSource);
    }

    @Test
    public void customDataSqlSessionFactoryReturnsNonNullInstance() throws Exception {
        SqlSessionFactory sqlSessionFactory = new CustomDataAutoConfiguration().customDataSqlSessionFactory(mockDataSource);
        assertNotNull(sqlSessionFactory);
    }

    @Test
    public void customDataSchedulerReturnsSchedulerWithValidPoolSize() {
        Scheduler scheduler = new CustomDataAutoConfiguration().customDataScheduler(10);
        assertNotNull(scheduler);
    }

    @Test
    public void customDatapointCacheReturnsNonNullInstance() {
        CustomDatapointCache cache = new CustomDataAutoConfiguration().customDatapointCache(mockRedisTemplate);
        assertNotNull(cache);
    }

    @Test
    public void customDataAsyncRepoReturnsNonNullInstance() {
        CustomDataAsyncRepo asyncRepo = new CustomDataAutoConfiguration().customDataAsyncRepo(mockScheduler, mockRepo);
        assertNotNull(asyncRepo);
    }

    @Test
    public void customDataServiceReturnsNonNullInstance() {
        CustomDataService service = new CustomDataAutoConfiguration().customDataService(mockAsyncRepo);
        assertNotNull(service);
    }

    @Test
    public void customDataGatewayReturnsNonNullInstance() {
        CustomDataGateway gateway = new CustomDataAutoConfiguration().customDataGateway(mockService, mockCache);
        assertNotNull(gateway);
    }
}
