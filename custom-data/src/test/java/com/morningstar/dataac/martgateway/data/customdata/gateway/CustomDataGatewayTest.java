package com.morningstar.dataac.martgateway.data.customdata.gateway;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.data.customdata.cache.CustomDatapointCache;
import com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPoint;
import com.morningstar.dataac.martgateway.data.customdata.service.CustomDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.data.customdata.model.CustomDataPointConstants.SEC_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class CustomDataGatewayTest {
    private static final String ID1 = "0P00000E2Y";
    private static final String CDP1 = "CDP00001";
    private static final String CDV1 = "CDV00001";
    private List<IdMapper> idMappers = null;

    @Mock
    private CustomDatapointCache customDatapointCache;
    @Mock
    private CustomDataService customDataService;

    private CustomDataGateway customDataGateway;

    @BeforeEach
    void setUp() {
        idMappers = List.of(
                new NonEmptyIdMapper(ID1, "{\"FundId\":\"0P00000E2Z\",\"SecId\":\"0P00000E2Y\"}")
        );
        customDataGateway = new CustomDataGateway(customDatapointCache, customDataService);
    }

    @Test
    @DisplayName("should retrieve custom data")
    @Order(1)
    void shouldGetCustomData() {
        //given
        List<CustomDataPoint> entitledDataPoints = List.of(CustomDataPoint.builder()
                .dataPointId(CDP1).entityIdType(SEC_ID)
                .build());
        MartRequest martRequest = MartRequest.builder()
                .ids(List.of(ID1)).dps(List.of(CDP1))
                .idMappers(idMappers)
                .build();

        when(customDatapointCache.getMetadata(anyList())).thenReturn(entitledDataPoints);
        when(customDataService.getData(any(CustomDataRequest.class), anyString()))
                .thenReturn(Flux.just(new CurrentResult(ID1, Map.of(CDP1, CDV1))));

        //when
        Flux<Result> resultFlux = customDataGateway.retrieve(martRequest);

        StepVerifier.create(resultFlux)
                .assertNext(result -> assertEquals(ID1, result.getId()))
                .verifyComplete();
    }

    @Test
    @DisplayName("should get empty flux if idMapper is empty")
    @Order(2)
    void shouldGetEmptyFluxWithEmptyIdMapper() {
        //given
        MartRequest martRequest = MartRequest.builder()
                .ids(List.of(ID1)).dps(List.of(CDP1))
                .build();
        //when
        Flux<Result> resultFlux = customDataGateway.retrieve(martRequest);
        //then
        StepVerifier.create(resultFlux)
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    @DisplayName("should get empty flux if ids is empty")
    @Order(3)
    void shouldGetEmptyFluxWithEmptyIds() {
        //given
        MartRequest martRequest = MartRequest.builder()
                .dps(List.of(CDP1))
                .idMappers(idMappers)
                .build();
        //when
        Flux<Result> resultFlux = customDataGateway.retrieve(martRequest);
        //then
        StepVerifier.create(resultFlux)
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    @DisplayName("should get empty flux if dps is empty")
    @Order(4)
    void shouldGetEmptyFluxWithEmptyDps() {
        //given
        MartRequest martRequest = MartRequest.builder()
                .ids(List.of(ID1))
                .idMappers(idMappers)
                .build();
        //when
        Flux<Result> resultFlux = customDataGateway.retrieve(martRequest);
        //then
        StepVerifier.create(resultFlux)
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }
}
