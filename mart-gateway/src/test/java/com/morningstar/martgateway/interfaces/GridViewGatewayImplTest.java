package com.morningstar.martgateway.interfaces;

import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.EXPORT;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.VIEW;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.never;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.CurrentPair;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.applications.apiproxy.DirectApiGateway;
import com.morningstar.martgateway.applications.delta.DeltaGateway;
import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint;
import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint.TimePeriod;
import com.morningstar.martgateway.domains.apiproxy.entity.DatapointValue;
import com.morningstar.martgateway.domains.apiproxy.entity.DoApiInvestment;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesResponseEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.TimeSerialsValue;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.DataPointSource;
import com.morningstar.martgateway.domains.calc.CustomCalcRequestUtil;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementInvestmentApiRequestWrapper;
import com.morningstar.martgateway.interfaces.model.FormatConverter;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.martgateway.util.apiproxy.DataPointSourceUtil;
import com.morningstar.martgateway.util.apiproxy.InvestmentHandledDataPointUtil;
import com.morningstar.martgateway.util.apiproxy.WhiteListCache;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.collections4.MapUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import org.junit.jupiter.api.Disabled;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class GridViewGatewayImplTest {
    @Mock
    private MartDataPipeline martDataPipeline;
    @Mock
    private DataEntitlementService dataEntitlementService;
    @Mock
    private EntitlementRequestFilterService<InvestmentApiRequest, EntitlementInvestmentApiRequestWrapper> entitlementInvestmentApiRequestFilterService;
    @Mock
    private DirectApiGateway directApiGateway;
    @Mock
    private DeltaGateway deltaGateway;
    @Mock
    private DataPointSourceUtil dataPointSourceUtil;
    @Mock
    private IdMapUtil idMapUtil;

    private final FormatConverter formatConverter = new FormatConverter();

    private GridViewGatewayImpl gridViewGateway;

    private static final String DUMMY_INVEST_ID = "F00001IIYM";

    private static FilteredRequestData<InvestmentApiRequest> getFilteredRequestData(InvestmentApiRequest investmentApiRequest) {
        return new FilteredRequestData<>(investmentApiRequest, new ArrayList<>());
    }

    private static TimePeriod createTimePeriod(String start, String end) {
        TimePeriod tp = new TimePeriod();
        tp.setStartDate(start);
        tp.setEndDate(end);
        return tp;
    }

    @BeforeEach
    public void beforeEachTest() {
        gridViewGateway = new GridViewGatewayImpl(
                martDataPipeline,
                dataEntitlementService,
                entitlementInvestmentApiRequestFilterService,
                directApiGateway,
                deltaGateway,
                dataPointSourceUtil,
                idMapUtil,
                formatConverter, false);
    }

    @Test
    public void testResponseInvestmentApi() throws JsonProcessingException {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointId("3010").build())))
                .investments(new ArrayList<>(List.of(Investment.builder().id(DUMMY_INVEST_ID).build())))
                .readCache("false")
                .productId("product_id")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(true)
                .skipProxy(true)
                .build();

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult(DUMMY_INVEST_ID, Map.of("3010", "USD"))));
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementInvestmentApiRequestWrapper.class)))
                .thenReturn(Flux.just(new CurrentResult(DUMMY_INVEST_ID, Map.of("3010", "USD"))));
        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, investmentApiRequest.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(investmentApiRequest));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(investmentApiRequest);
        assertEquals(DUMMY_INVEST_ID, response.getInvestments().get(0).getId());
    }

    @Test
    public void testResponseDirectApi() throws JsonProcessingException {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("HP002").build(),
                        GridviewDataPoint.builder().dataPointId("DT042").build(),
                        GridviewDataPoint.builder().dataPointId("OF059").build()
                ))
                .investments(new ArrayList<>(List.of(Investment.builder().id(DUMMY_INVEST_ID).build())))
                .readCache("false")
                .productId("mds")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(true)
                .skipProxy(true)
                .build();
        when(martDataPipeline.execute(any())).thenReturn(Flux.empty());
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementInvestmentApiRequestWrapper.class))).thenReturn(
                Flux.just(
                        new CurrentResult(DUMMY_INVEST_ID, Map.of("DT042", "11-01-1996")),
                        new GroupResult(DUMMY_INVEST_ID, Map.of("OF059", List.of(
                                new CurrentResult(DUMMY_INVEST_ID, Map.of("OF059", "Andrea Frazzini")),
                                new CurrentResult(DUMMY_INVEST_ID, Map.of("OF059", "Michele L. Aghassi"))
                        ))),
                        new TimeSeriesResult(DUMMY_INVEST_ID, Map.of("HP002", List.of(
                                new V("0", "0"),
                                new V("1", "1.23"),
                                new V("2", "1.40"),
                                new V("3", "-0.07"),
                                new V("4", "-1.17")
                        )))
                )
        );

        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, new ArrayList<>());
        sourceMap.put(DataPointSource.DIRECT_API, investmentApiRequest.getDataPoints());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(directApiGateway.asyncRetrieveSecurities(any())).thenReturn(Mono.just(setupSecuritiesResponseEntity()));
        FilteredRequestData<InvestmentApiRequest> filteredRequestData = getFilteredRequestData(investmentApiRequest);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(filteredRequestData);
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(getFilteredRequestData(investmentApiRequest));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(investmentApiRequest);
        assertEquals(DUMMY_INVEST_ID, response.getInvestments().get(0).getId());
        assertEquals(1, response.getInvestments().get(0).getGroupDataList().size());
        assertEquals(1, response.getInvestments().get(0).getCurrentPairList().size());
        assertEquals(1, response.getInvestments().get(0).getTimeseriesDataList().size());
        assertEquals(5, response.getInvestments().get(0).getTimeseriesDataList().get(0).getTimeseriesPairList().size());
    }

    @Test
    public void testResponseInvestmentApi_cachEntitlement() {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointId("3010").build())))
                .investments(new ArrayList<>((List.of(Investment.builder().id(DUMMY_INVEST_ID).build()))))
                .readCache("false")
                .productId("product_id")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(true)
                .skipProxy(true)
                .build();

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult(DUMMY_INVEST_ID, Map.of("3010", "USD"))));
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementInvestmentApiRequestWrapper.class)))
                .thenReturn(Flux.just(new CurrentResult(DUMMY_INVEST_ID, Map.of("3010", "USD"))));
        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, investmentApiRequest.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);


        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(investmentApiRequest));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(investmentApiRequest);
        assertEquals(DUMMY_INVEST_ID, response.getInvestments().get(0).getId());
    }

    @Test
    public void testDeltaResponse() {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .productId("product_id")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(true)
                .deltaStartTime(Instant.now())
                .dataPoints(List.of(
                                GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                                GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                                new Investment("F00000JU03"),
                                new Investment("0P0000PZ28")
                        )
                )
                .build();
        Flux<Result> deltaResultFlux = Flux.just((new CurrentResult("F00000JU03", Collections.singletonMap("TFC2N", "5.01"))), new TimeSeriesResult("0P0000PZ28", Map.of("TFC0N", List.of(new V("2020-01-01", "6.78")))));
        when(deltaGateway.retrieve(any())).thenReturn(deltaResultFlux);
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(getFilteredRequestData(investmentApiRequest));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementInvestmentApiRequestWrapper.class)))
                .thenReturn(deltaResultFlux);
        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, investmentApiRequest.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);
        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(investmentApiRequest);
        Assertions.assertEquals(2, response.getInvestments().size());
    }

    @Test
    public void testAliases() {
        InvestmentHandledDataPointUtil investmentHandledDataPointUtil = Mockito.mock(InvestmentHandledDataPointUtil.class);
        WhiteListCache whiteListCache = Mockito.mock(WhiteListCache.class);
        when(investmentHandledDataPointUtil.isInvestmentApiHandledDataPoint(any())).thenReturn(true);


        gridViewGateway = new GridViewGatewayImpl(
                martDataPipeline,
                dataEntitlementService,
                entitlementInvestmentApiRequestFilterService,
                directApiGateway,
                deltaGateway,
                new DataPointSourceUtil(investmentHandledDataPointUtil, whiteListCache),
                idMapUtil,
                formatConverter, false
        );
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(List.of(GridviewDataPoint.builder()
                                        .dataPointIds(List.of("190236", "190237", "190239"))
                                        .aliases(Map.of("190236", "AAA", "190239", "BBB"))
                                        .build(),
                                GridviewDataPoint.builder()
                                        .dataPointId("190236")
                                        .frequency("m")
                                        .startDate("2010-01-01")
                                        .endDate("2024-01-01")
                                        .build(),
                                GridviewDataPoint.builder()
                                        .dataPointId("500051")
                                        .frequency("m")
                                        .startDate("2010-01-01")
                                        .endDate("2024-01-01")
                                        .build()
                        )
                )
                .investments(new ArrayList<>(List.of(Investment.builder().id(DUMMY_INVEST_ID).build())))
                .readCache("false")
                .productId("product_id")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(false)
                .skipProxy(true)
                .build();
        when(martDataPipeline.execute(ArgumentMatchers.argThat(martRequest -> Objects.nonNull(martRequest) && MapUtils.isEmpty(martRequest.getAliasMap()))))
                .thenReturn(Flux.just());
        when(martDataPipeline.execute(ArgumentMatchers.argThat(martRequest -> Objects.nonNull(martRequest) && MapUtils.isNotEmpty(martRequest.getAliasMap()))))
                .thenReturn(Flux.just(new CurrentResult(DUMMY_INVEST_ID, Map.of("AAA", "123", "BBB", "456"))));

        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(request));

        when(whiteListCache.contains(any())).thenReturn(true);

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(request);

        assertEquals(DUMMY_INVEST_ID, response.getInvestments().get(0).getId());
        ArgumentCaptor<MartRequest> captor = ArgumentCaptor.forClass(MartRequest.class);
        Mockito.verify(martDataPipeline, times(2)).execute(captor.capture());
        List<MartRequest> martRequests = captor.getAllValues();

        Optional<MartRequest> martRequestOpt = martRequests.stream()
                .filter(r -> MapUtils.isNotEmpty(r.getAliasMap()))
                .findFirst();
        assertTrue(martRequestOpt.isPresent());
        MartRequest requestWithAliases = martRequestOpt.get();
        assertTrue(MapUtils.isNotEmpty(requestWithAliases.getAliasMap()));
        Map<String, Set<String>> aliasMap = requestWithAliases.getAliasMap();
        assertEquals(Set.of("AAA"), aliasMap.get("190236"));
        assertEquals(Set.of("BBB"), aliasMap.get("190239"));
    }

    @Test
    public void testCodeMappingFormatConverter() {
        Map<String, Map<String, String>> codeMappings = Map.of(
                "MMR01", Map.of("1", "A", "2", "B", "3", "C")
        );
        CodeMappings.setCodeMappings(codeMappings);
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder()
                                .dataPointId("MMR01")
                                .alias("TEST_MMR01")
                                .build(),
                        GridviewDataPoint.builder()
                                .dataPointId("MMR02")
                                .build()
                )))
                .investments(new ArrayList<>(List.of(Investment.builder().id(DUMMY_INVEST_ID).build())))
                .readCache("false")
                .readCache("false")
                .productId("product_id")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(false)
                .skipProxy(true)
                .build();

        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, request.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(martDataPipeline.execute(any()))
                .thenReturn(Flux.just(new CurrentResult(DUMMY_INVEST_ID, Map.of("TEST_MMR01", "1", "MMR02", "45"))));
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(request));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(request);

        assertEquals(DUMMY_INVEST_ID, response.getInvestments().get(0).getId());
        Optional<CurrentPair> currentPairOpt = response.getInvestments().get(0).getCurrentPairList().stream().filter(cp -> "1".equals(cp.getCode())).findFirst();
        assertTrue(currentPairOpt.isPresent());
        CurrentPair currentPair = currentPairOpt.get();
        assertEquals("A", currentPair.getValue());
    }

    @Test
    public void testLanguageRequest() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointId("OS01W").build())))
                .investments(new ArrayList<>(List.of(Investment.builder().id("\t\t\t\"id\": \"F0000023YM\"\n").build())))
                .readCache("false")
                .productId("mds")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(false)
                .language("FRC")
                .skipProxy(true)
                .build();

        Map<String, String> rdbValues = new HashMap<>();
        rdbValues.put("OS01W", "Canada Life Balanced T5");
        CurrentResult rdbResult = new CurrentResult("F0000023YM", rdbValues);

        Map<String, String> languageValues = new HashMap<>();
        languageValues.put("OS01W", "Canada Vie Portefeuille équilibré T5");
        Map<String, Map<String, String>> languageMetaData = new HashMap<>();
        Map<String, String> metaValues = new HashMap<>();
        metaValues.put("OS01W", "");
        languageMetaData.put("LanguageCurrentData", metaValues);
        CurrentResult languageResult = new CurrentResult("F0000023YM", languageValues, languageMetaData);

        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, request.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(rdbResult, languageResult));

        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(request));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(request);

        assertEquals(1, response.getInvestments().size());
        assertEquals("F0000023YM", response.getInvestments().get(0).getId());
        assertEquals(1, response.getInvestments().get(0).getCurrentPairList().size());
        assertEquals(1, response.getInvestments().get(0).getCurrentPairList().size());
        assertEquals("OS01W", response.getInvestments().get(0).getCurrentPairList().get(0).getDatapointId());
        assertEquals("Canada Vie Portefeuille équilibré T5", response.getInvestments().get(0).getCurrentPairList().get(0).getValue());
    }

    @Test
    public void testExtPerfRequest() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointId("HP010").extendedPerformance("1").build())))
                .investments(new ArrayList<>(List.of(Investment.builder().id("\t\t\t\"id\": \"F0000023YM\"\n").build())))
                .readCache("false")
                .productId("mds")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(false)
                .skipProxy(true)
                .build();

        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, request.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(mockPerfResult(), mockExtPerfResult()));

        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(request));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(request);

        assertEquals(1, response.getInvestments().size());
        assertEquals("F0000023YM", response.getInvestments().get(0).getId());
        assertEquals(1, response.getInvestments().get(0).getTimeseriesDataList().size());
        assertEquals("HP010", response.getInvestments().get(0).getTimeseriesDataList().get(0).getDatapointId());
        assertEquals(12, response.getInvestments().get(0).getTimeseriesDataList().get(0).getTimeseriesPairList().size());
    }

    @Test
    public void testLanguageMultiValueRequest() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointId("OF015").build())))
                .investments(new ArrayList<>(List.of(Investment.builder().id("\t\t\t\"id\": \"F0000023YM\"\n").build())))
                .readCache("false")
                .productId("mds")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(false)
                .language("FRC")
                .skipProxy(true)
                .build();

        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, request.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(mockEngMultiValueResult(), mockNonEngMultiValueResult()));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(request);

        assertEquals(1, response.getInvestments().size());
        assertEquals("F0000023YM", response.getInvestments().get(0).getId());
        assertEquals(1, response.getInvestments().get(0).getGroupDataList().size());
        assertEquals(3, response.getInvestments().get(0).getGroupDataList().get(0).getMultipleValueDataEntries().size());
        assertTrue(response.getInvestments().get(0).getGroupDataList().get(0).getMultipleValueDataEntries().stream().anyMatch(e -> "PERKSD.EDWARD".equals(e.value)));
        assertTrue(response.getInvestments().get(0).getGroupDataList().get(0).getMultipleValueDataEntries().stream().anyMatch(e -> "BRIGHTONTODD".equals(e.value)));
        assertTrue(response.getInvestments().get(0).getGroupDataList().get(0).getMultipleValueDataEntries().stream().anyMatch(e -> "CIRCLEBRENDAN".equals(e.value)));
    }

    private GroupResult mockEngMultiValueResult() {

        Map<String, List<CurrentResult>> values = new HashMap<>();

        List<CurrentResult> results = new ArrayList<>();

        Map<String, String> val1 = new HashMap<>();
        val1.put("OF015", "Edward D. Perks");
        CurrentResult res1 = new CurrentResult("OF015", val1);
        results.add(res1);

        Map<String, String> val2 = new HashMap<>();
        val2.put("OF015", "Todd Brighton");
        CurrentResult res2 = new CurrentResult("OF015", val2);
        results.add(res2);

        Map<String, String> val3 = new HashMap<>();
        val3.put("OF015", "Brendan Circle");
        CurrentResult res3 = new CurrentResult("OF015", val3);
        results.add(res3);

        values.put("OF015", results);

        GroupResult groupResult = new GroupResult("F0000023YM", values);
        groupResult.setMultipleValues(true);

        return groupResult;
    }

    private GroupResult mockNonEngMultiValueResult() {

        Map<String, List<CurrentResult>> values = new HashMap<>();

        List<CurrentResult> results = new ArrayList<>();

        Map<String, String> val1 = new HashMap<>();
        val1.put("OF015", "PERKSD.EDWARD");
        CurrentResult res1 = new CurrentResult("OF015", val1);
        results.add(res1);

        Map<String, String> val2 = new HashMap<>();
        val2.put("OF015", "BRIGHTONTODD");
        CurrentResult res2 = new CurrentResult("OF015", val2);
        results.add(res2);

        Map<String, String> val3 = new HashMap<>();
        val3.put("OF015", "CIRCLEBRENDAN");
        CurrentResult res3 = new CurrentResult("OF015", val3);
        results.add(res3);

        values.put("OF015", results);

        Map<String, Map<String, String>> metaData = new HashMap<>();
        Map<String, String> metaValues = new HashMap<>();
        metaValues.put("OF015", "");
        metaData.put("LanguageCurrentData", metaValues);

        GroupResult groupResult = new GroupResult("F0000023YM", values, metaData);
        groupResult.setMultipleValues(true);

        return groupResult;
    }

    private TimeSeriesResult mockPerfResult() {
        Map<String, List<V>> valueMap = new HashMap<>();

        List<V> values = new ArrayList<>();
        values.add(new V("2023-01-31", "4.17971"));
        values.add(new V("2023-02-28", "1.10827"));
        values.add(new V("2023-03-31", "0.56471"));
        values.add(new V("2023-04-30", "-3.57503"));
        valueMap.put("HP010", values);

        return new TimeSeriesResult("F0000023YM", valueMap);
    }

    private TimeSeriesResult mockExtPerfResult() {
        Map<String, List<V>> valueMap = new HashMap<>();

        List<V> values = new ArrayList<>();
        values.add(new V("2023-05-31", "2.98762"));
        values.add(new V("2023-06-30", "-0.98451"));
        values.add(new V("2023-07-31", "1.86246"));
        values.add(new V("2023-08-31", "1.16438"));
        values.add(new V("2023-09-30", "3.27654"));
        values.add(new V("2023-10-31", "-5.49287"));
        values.add(new V("2023-11-30", "2.23142"));
        values.add(new V("2023-12-31", "6.75423"));
        valueMap.put("HP010", values);

        return new TimeSeriesResult("F0000023YM", valueMap);
    }

    private SecuritiesResponseEntity setupSecuritiesResponseEntity() {
        SecuritiesResponseEntity responseEntity = new SecuritiesResponseEntity();
        responseEntity.setTotal(1);

        Datapoint timeSeriesDp = new Datapoint();
        timeSeriesDp.setAlias("HP002");
        timeSeriesDp.setId("HP002");
        timeSeriesDp.setStartDate("2011-01-01");
        timeSeriesDp.setEndDate("2011-01-31");
        timeSeriesDp.setFrequency("w");
        timeSeriesDp.setSourceId("HP002");
        timeSeriesDp.setTimePeriods(List.of(
                createTimePeriod("2010-12-26", "2011-01-01"),
                createTimePeriod("2011-01-02", "2011-01-08"),
                createTimePeriod("2011-01-09", "2011-01-15"),
                createTimePeriod("2011-01-16", "2011-01-22"),
                createTimePeriod("2011-01-23", "2011-01-29"),
                createTimePeriod("2011-01-30", "2011-02-05")
        ));
        Datapoint currentDp = new Datapoint();
        currentDp.setAlias("DT042");
        currentDp.setId("DT042");

        Datapoint groupDp = new Datapoint();
        currentDp.setAlias("OF059");
        currentDp.setId("OF059");

        responseEntity.setDatapoints(List.of(timeSeriesDp, currentDp, groupDp));

        DoApiInvestment doApiInvestment = new DoApiInvestment();
        doApiInvestment.setId("FOUSA00KZH;FO");
        DatapointValue timeSeriesDpv = new DatapointValue();
        timeSeriesDpv.setAlias("HP002");
        timeSeriesDpv.setId("HP002");
        timeSeriesDpv.setValue(List.of(
                new TimeSerialsValue(null, 0, 0),
                new TimeSerialsValue(null, 1.23, 1),
                new TimeSerialsValue(null, 1.40, 2),
                new TimeSerialsValue(null, -0.07, 3),
                new TimeSerialsValue(null, -1.17, 4),
                new TimeSerialsValue(null, null, 5)
        ));
        DatapointValue currentDpv = new DatapointValue();
        currentDpv.setAlias("DT042");
        currentDpv.setId("DT042");
        currentDpv.setValue("11-01-1996");

        DatapointValue groupDpv = new DatapointValue();
        groupDpv.setAlias("OF059");
        groupDpv.setId("OF059");
        groupDpv.setValue(List.of(
                new HashMap() {{
                    put("value", "Andrea Frazzini");
                }},
                new HashMap() {{
                    put("value", "Michele L. Aghassi");
                }}
        ));

        doApiInvestment.setValues(List.of(timeSeriesDpv, currentDpv, groupDpv));
        responseEntity.setDoApiInvestments(List.of(doApiInvestment));

        return responseEntity;
    }

    @ParameterizedTest
    @DisplayName("should get response with view/export useCase without any entitlement")
    @ValueSource(strings = {VIEW, EXPORT})
    public void testNoUserEntitlementWithConfigId(String useCase) {
        String dpId = "3010";
        Result result = new CurrentResult(DUMMY_INVEST_ID, Map.of(dpId, "test"));
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase(useCase)
                .productId("MDS")
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId(dpId).build()))
                .investments(new ArrayList<>(List.of(Investment.builder().id(DUMMY_INVEST_ID).build())))
                .userId("user_id")
                .checkEntitlement(true)
                .build();

        when(dataEntitlementService.getEntitlement(anyString(), anyString())).thenThrow(new NullPointerException());
        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(investmentApiRequest));
        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, investmentApiRequest.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, Collections.emptyList());
        when(dataPointSourceUtil.splitDataPointsPerSource(anyList(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(martDataPipeline.execute(any(MartRequest.class)))
                .thenReturn(Flux.just(result));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementInvestmentApiRequestWrapper.class)))
                .thenReturn(Flux.just(result));

        try (MockedStatic<CustomCalcRequestUtil> CustomCalcRequestUtilMock = Mockito.mockStatic(CustomCalcRequestUtil.class)) {
            CustomCalcRequestUtilMock.when(() -> CustomCalcRequestUtil
                    .filterCustomCalcDataPoints(investmentApiRequest, false)).thenReturn(Collections.emptyList());
            CustomCalcRequestUtilMock.when(() -> CustomCalcRequestUtil
                    .buildMartRequestsForCustomCalc(investmentApiRequest, Collections.emptyList())).thenReturn(null);

            InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(investmentApiRequest);

            assertNotNull(response);
            assertEquals(response.getInvestments().size(), 1);
        }
    }

    @Test
    public void testResponseInvestmentApiMixedIdType_cacheEntitlement() {
        InvestmentApiRequest investmentApiRequest = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .idType("MorningstarIndustryCode")
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointId("HS730").build())))
                .investments(new ArrayList<>(new ArrayList<>(Arrays.asList((Investment.builder().id("0P0000068D").build()), (Investment.builder().id("20620020").build())))))
                .readCache("false")
                .productId("product_id")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(true)
                .skipProxy(true)
                .build();
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult("0P0000068D", Map.of())));
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(idMapUtil.getIdMappers(investmentApiRequest.getAllInvestmentIds())).thenReturn(List.of(new NonEmptyIdMapper("0P0000068D", "{\"SecurityType\":\"ST\", \"MorningstarIndustryCode\":\"31020010\", \"SecId\":\"0P0000068D\"}")));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementInvestmentApiRequestWrapper.class))).thenReturn(Flux.just(new CurrentResult("20620020", Map.of("HS730", "value"))));
        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, investmentApiRequest.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(investmentApiRequest));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(investmentApiRequest);

        assertEquals("HS730", response.getInvestments().get(0).getCurrentPairList().get(0).getDatapointId());
    }

    @Test
    public void testResponseInvestmentApiNoIdType_cacheEntitlement() {
        Map<String, Map<String, String>> codeMappings = Map.of(
                "HS730", Map.of("1", "A", "2", "B", "3", "C")
        );
        CodeMappings.setCodeMappings(codeMappings);
        InvestmentApiRequest investmentApiRequest2 = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(List.of(GridviewDataPoint.builder().dataPointId("HS730").aliases(Map.of("HS730", "AAA", "190239", "BBB")).build())))
                .investments(new ArrayList<>(Arrays.asList((Investment.builder().id("0P0000068D").build()), (Investment.builder().id("20620020").build()),  (Investment.builder().id("20620020").build()),  (Investment.builder().id("0A5CB6D9-4FCF-45CC-89A6-E7C7EF9179AE").build()))))
                .readCache("false")
                .productId("direct")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(true)
                .skipProxy(true)
                .build();
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult("0P0000068D", Map.of())));
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(idMapUtil.getIdMappers(investmentApiRequest2.getAllInvestmentIds()))
                .thenReturn(List.of(new NonEmptyIdMapper("0P0000068D", "{\"SecurityType\":\"ST\", \"MorningstarIndustryCode\":\"31020010\", \"SecId\":\"0P0000068D\"}")));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementInvestmentApiRequestWrapper.class)))
                .thenReturn(Flux.just(new CurrentResult("0P0000068D", Map.of("HS730", "value"))));
        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, investmentApiRequest2.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);


        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(investmentApiRequest2));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(investmentApiRequest2);

        assertEquals("HS730", response.getInvestments().get(0).getCurrentPairList().get(0).getDatapointId());
        assertEquals(4, investmentApiRequest2.getInvestments().size());
    }

    @Test
    public void syncRetrieveSecurities_shouldReturnErrorResponseWhenEntitlementCheckFails() {
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(List.of(GridviewDataPoint.builder().dataPointId("HP002").build()))
                .investments(List.of(Investment.builder().id(DUMMY_INVEST_ID).build()))
                .userId("testUser")
                .configId("testConfig")
                .checkEntitlement(true)
                .build();

        EntitlementException expectedException = new EntitlementException(
                Status.REDIS_CONNECTION_FAILED, null
        );

        when(dataEntitlementService.getEntitlement(request.getUserId(), request.getConfigId()))
                .thenThrow(expectedException);

        try {
            gridViewGateway.syncRetrieveSecurities(request);
        } catch (EntitlementException e) {
            Assert.assertEquals("Redis connection failed", e.getMessage());
        }

        verify(martDataPipeline, never()).execute(any());
    }

    @Test
    @Disabled
    public void testParameterRequest() {
        List<String> datapointsIds = List.of("EU8ZT", "EUVFZ");
        List<String> euTaxonomyObjective = List.of("Climate Change Adaptation (CCA)");
        InvestmentApiRequest request = InvestmentApiRequest.builder()
                .useCase(VIEW)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointId("EUVFZ").build())))
                .investments(new ArrayList<>(List.of(Investment.builder().id("\t\t\t\"id\": \"0C000006V5\"\n").build())))
                .readCache("false")
                .productId("mds")
                .requestId("request_id")
                .userId("user_id")
                .checkEntitlement(false)
                .dataPoints(new ArrayList<>(Arrays.asList(GridviewDataPoint.builder().dataPointIds(datapointsIds).euTaxonomyObjective(euTaxonomyObjective).build())))
                .build();


        Map<String, String> paramterValues = new HashMap<>();
        paramterValues.put("EUVFZ", "0.0");
        Map<String, Map<String, String>> primaryKeys = new HashMap<>();
        Map<String, String> matchedEntries = new HashMap<>();
        Map<String, String> metaValues = new HashMap<>();
        metaValues.put("EUVFZ", "");
        matchedEntries.put("euTaxonomyObjective", "Climate Change Adaptation (CCA)");
        primaryKeys.put("primaryKeys", matchedEntries);
        CurrentResult parameterResult = new CurrentResult("0C000006V5", paramterValues, primaryKeys);

        Map<DataPointSource, List<GridviewDataPoint>> sourceMap = new HashMap<>();
        sourceMap.put(DataPointSource.INVESTMENT_API, request.getDataPoints());
        sourceMap.put(DataPointSource.DIRECT_API, new ArrayList<>());
        when(dataPointSourceUtil.splitDataPointsPerSource(any(), anyString(), anyBoolean())).thenReturn(sourceMap);

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(parameterResult));

        when(entitlementInvestmentApiRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(request));

        InvestmentResponse response = gridViewGateway.syncRetrieveSecurities(request);

        assertEquals(1, response.getInvestments().size());
        assertEquals("0C000006V5", response.getInvestments().get(0).getId());
        assertEquals(1, response.getInvestments().get(0).getCurrentPairList().size());
        assertEquals(1, response.getInvestments().get(0).getCurrentPairList().size());
        assertEquals("EUVFZ", response.getInvestments().get(0).getCurrentPairList().get(0).getDatapointId());
    }
}