package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.C;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.G;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.GV;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.domains.core.entity.holdingresponse.HoldingResponse;
import com.morningstar.martgateway.domains.core.entity.holdingresponse.Investment;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdDatePair;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class HoldingDataGatewayImplTest {

    private MartDataPipeline martDataPipeline;

    private HoldingDataGatewayImpl holdingDataGatewayImpl;

    @Before
    public void setup() {
        this.martDataPipeline = Mockito.mock(MartDataPipeline.class);
        this.holdingDataGatewayImpl = new HoldingDataGatewayImpl(martDataPipeline);
    }

    @Test
    public void testSyncRetrieveSecurities() {

        IdDatePair idDatePair = new IdDatePair();
        idDatePair.setId("610498");
        idDatePair.setDates(Arrays.asList(LocalDate.now()));
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("90403"))
                .idPairs(List.of(idDatePair))
                .top(3)
                .build();

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(getTestResult()));

        HoldingResponse actualResponse = holdingDataGatewayImpl.syncRetrieveSecurities(martRequest);
        Assert.assertNotNull(actualResponse);
        List<Investment> investments = actualResponse.getInvestments();
        Assert.assertTrue(investments.size() == 1);
        Assert.assertTrue(investments.get(0).getValues().size() == 3);
    }

    @Test
    public void testSyncTopRetrieveSecurities() {

        IdDatePair idDatePair = new IdDatePair();
        idDatePair.setId("610498");
        idDatePair.setDates(Arrays.asList(LocalDate.now()));
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("90403"))
                .idPairs(List.of(idDatePair))
                .top(2)
                .build();

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(getTestResult()));

        HoldingResponse actualResponse = holdingDataGatewayImpl.syncRetrieveSecurities(martRequest);
        Assert.assertNotNull(actualResponse);
        List<Investment> investments = actualResponse.getInvestments();
        Assert.assertTrue(investments.size() == 1);
        Assert.assertTrue(investments.get(0).getValues().size() == 2);
    }

    @Test
    public void testSyncTopRetrieveSecuritiesBadRequest() {

        IdDatePair idDatePair = new IdDatePair();
        idDatePair.setId("610498");
        idDatePair.setDates(Arrays.asList(LocalDate.now()));
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("90403"))
                .idPairs(List.of(idDatePair,idDatePair))
                .top(2)
                .build();

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(getTestResult()));

        HoldingResponse actualResponse = holdingDataGatewayImpl.syncRetrieveSecurities(martRequest);
        Assert.assertNotNull(actualResponse);
        Assert.assertTrue(actualResponse.getStatus().getCode().equals("400"));
    }

    private TimeSeriesResult getTestResult() {

        List<V> vl1 = new ArrayList<>();
        vl1.add(new V("90404","Euro Schatz Future"));
        vl1.add(new V("90414","29.06315"));
        vl1.add(new V("90409","3519"));
        C c1 = new C(vl1);

        List<V> vl2 = new ArrayList<>();
        vl2.add(new V("90404","Federal National Mortgage"));
        vl2.add(new V("90414","19.37566"));
        vl2.add(new V("90409","26810000"));
        C c2 = new C(vl2);

        List<V> vl3 = new ArrayList<>();
        vl3.add(new V("90404","PIMCO Euro Short Mat ETF"));
        vl3.add(new V("90414","8.63905"));
        vl3.add(new V("90409","1121900"));
        C c3 = new C(vl3);

        G g = new G("90403", Arrays.asList(c1, c2, c3));

        GV gv = new GV("90403", Arrays.asList(g));

        Map<String, List<V>> dataMap = new HashMap<>();

        dataMap.put("90403", Arrays.asList(gv));

        return new TimeSeriesResult("610498", dataMap);
    }
}
