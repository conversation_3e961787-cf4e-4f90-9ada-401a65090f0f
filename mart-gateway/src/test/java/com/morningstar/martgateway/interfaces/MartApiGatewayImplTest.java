package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.martgateway.applications.current.CurrentGateway;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import reactor.core.publisher.Flux;

import java.util.Arrays;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

public class MartApiGatewayImplTest {
    private MartDataPipeline martDataPipeline;
    private CurrentGateway currentGateway;
    private MartApiGatewayImpl martApiGateway;

    @Before
    public void beforeEachTest() {
        martDataPipeline = Mockito.mock(MartDataPipeline.class);
        currentGateway = Mockito.mock(CurrentGateway.class);
        martApiGateway = new MartApiGatewayImpl(martDataPipeline, currentGateway, false);
    }

    @Test
    public void testResponse() {
        MartRequest martRequest = MartRequest.builder().dps(Arrays.asList("3010")).ids(Arrays.asList("F00000ONLR")).build();
        Mockito.when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult("F00000ONLR", Map.of("3010", "USD"))));
        MartResponse response = martApiGateway.syncRetrieveSecurities(martRequest);
        Assert.assertEquals("F00000ONLR", response.getData().getResponse().get(0).getI());
    }

    @Test
    public void testResponseFilter() {
        MartRequest martRequest = MartRequest.builder().dps(Arrays.asList("3010")).ids(Arrays.asList("F00000ONLR", "privateindex")).productId("APICenter").build();
        Mockito.when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult("F00000ONLR", Map.of("3010", "USD"))));
        MartResponse response = martApiGateway.syncRetrieveSecurities(martRequest);
        Assert.assertEquals("F00000ONLR", response.getData().getResponse().get(0).getI());
    }

}
