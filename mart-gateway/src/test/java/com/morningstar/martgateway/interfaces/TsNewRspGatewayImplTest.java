package com.morningstar.martgateway.interfaces;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.never;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.applications.tscacheproxy.TsCacheProxyApplication;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementMartRequestWrapper;
import com.morningstar.martgateway.domains.timeseries.entity.TSContent;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import reactor.core.publisher.Flux;

public class TsNewRspGatewayImplTest {

    private MartDataPipeline martDataPipeline;
    private TsCacheProxyApplication tsCacheProxyApplication;
    private IdMapUtil idMapUtil;

    private DataEntitlementService<MartRequest> dataEntitlementService;
    private EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService;
    private TsNewRspGatewayImpl tsNewRspGateway;

    @Before
    public void setup() {
        this.martDataPipeline = Mockito.mock(MartDataPipeline.class);
        this.tsCacheProxyApplication = Mockito.mock(TsCacheProxyApplication.class);
        this.idMapUtil = Mockito.mock(IdMapUtil.class);
        this.dataEntitlementService = Mockito.mock(DataEntitlementService.class);
        this.entitlementMartRequestFilterService = Mockito.mock(EntitlementRequestFilterService.class);
        this.tsNewRspGateway = new TsNewRspGatewayImpl(martDataPipeline, tsCacheProxyApplication, idMapUtil, dataEntitlementService, entitlementMartRequestFilterService);
        DataPointRepository.setDataPointMap(buildDpMap());
    }

    @Test
    public void syncRetrieveSecurities() {
        MartRequest martRequest = setupData();
        InvestmentResponse actualResponse = tsNewRspGateway.syncRetrieveSecurities(martRequest);
        Assert.assertNotNull(actualResponse);
        List<Investment> investments = actualResponse.getInvestments();
        assertTrue(investments.stream().allMatch(item -> "5PUSA00003".equals(item.getId())));
    }

    @Test
    public void testEmptyDps() {
        MartRequest martRequest = setupData();
        martRequest.setDps(null);
        InvestmentResponse actualResponse = tsNewRspGateway.syncRetrieveSecurities(martRequest);
        Assert.assertNull(actualResponse);
    }

    @Test
    public void testMostRecentDataFilter() {
        MartRequest martRequest = setupMostRecentData();
        InvestmentResponse actualResponse = tsNewRspGateway.syncRetrieveSecurities(martRequest);
        Assert.assertNotNull(actualResponse);
    }

    @Test
    public void testNoDateData() {
        MartRequest martRequest = setupNoDateRequest();
        InvestmentResponse actualResponse = tsNewRspGateway.syncRetrieveSecurities(martRequest);
        Assert.assertNotNull(actualResponse);
    }

    private MartRequest setupData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012","XX123"))
                .ids(Arrays.asList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponse()));
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190012", Collections.singletonList(new V("2023-01-01","500.00")));
        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }

    private MartRequest setupNoDateRequest() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Arrays.asList("5PUSA00003"))
                .startDate("2020-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponse()));
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("8PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"8PUSA00003\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190012", Collections.singletonList(new V("2023-01-01","500.00")));
        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }

    private MartRequest setupMostRecentData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Arrays.asList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponse()));
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190012", Collections.singletonList(new V("2023-01-01","500.00")));
        CurrentResult result = new CurrentResult("5PUSA00003", new HashMap<>());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }

    private TSResponse buildTsResponse() {
        TSResponse tsResponse = new TSResponse();
        TSStatus status = new TSStatus();
        status.setCode("0");
        status.setMsg("");
        tsResponse.setStatus(status);
        TSContent tsContent = new TSContent();

        TSItem tsItem = new TSItem();
        tsItem.setSecid("5PUSA00003;CP");
        tsItem.setDataid("HP010");
        TSData tsData = new TSData();
        tsData.setDate(10012);
        tsData.setValue(100.00);
        tsItem.setData(Collections.singletonList(tsData));

        TSItem tsItem2 = new TSItem();
        tsItem2.setSecid("5PUSA00003;CP");
        tsItem2.setDataid("HP012");
        TSData tsData2 = new TSData();
        tsData2.setDate(10015);
        tsData2.setValue(105.05);
        tsItem2.setData(Collections.singletonList(tsData2));

        tsContent.setItems(List.of(tsItem, tsItem2));
        tsResponse.setContent(tsContent);
        return tsResponse;
    }

    private Map<String, DataPoint> buildDpMap() {
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        DataPoint dataPoint1 = DataPoint.builder().nid("HP010").id("HP010").src("CDAPI").tsRdb(RdbDataPoint.builder().build()).build();
        DataPoint dataPoint2 = DataPoint.builder().nid("190012").id("190012").contentType("decimal").src("RDB").tsRdb(RdbDataPoint.builder().build()).build();
        dataPointMap.put("HP010", dataPoint1);
        dataPointMap.put("190012", dataPoint2);
        return dataPointMap;
    }

    @Test
    public void testResponseWithNoEntitlementFiltered() {
        MartRequest martRequest = MartRequest.builder().ids(new ArrayList<>(Arrays.asList("F00001IIYM"))).dps(new ArrayList<>(Arrays.asList("3010")))
                .checkEntitlement(true)
                .build();

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult("F00001IIYM", Map.of("3010", "USD"))));
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementMartRequestWrapper.class))).thenReturn(Flux.just(new CurrentResult("F00001IIYM", Map.of("3010", "USD"))));
        when(entitlementMartRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(martRequest));

        InvestmentResponse response = tsNewRspGateway.syncRetrieveSecurities(martRequest);
        Assert.assertEquals("F00001IIYM", response.getInvestments().get(0).getId());
    }

    @Test
    public void testResponseWithEntitlementFiltered() {
        MartRequest martRequest = MartRequest.builder().ids(new ArrayList<>(Arrays.asList("F00001IIYM", "5PUSA00003"))).dps(new ArrayList<>(Arrays.asList("3010", "HPD10")))
                .checkEntitlement(true)
                .build();

        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new CurrentResult("F00001IIYM", Map.of("3010", "USD"))));
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenReturn(new CachedEntitlement());
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementMartRequestWrapper.class)))
                .thenReturn(Flux.just(new CurrentResult("F00001IIYM", Map.of("3010", "USD")),
                                        new ErrorResult("F00001IIYM", "HPD10", "403"),
                                        new ErrorResult("5PUSA00003", "HPD10", "403")));
        when(entitlementMartRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(martRequest));

        InvestmentResponse response = tsNewRspGateway.syncRetrieveSecurities(martRequest);
        Assert.assertEquals("200200", response.getStatus().getCode());
        Assert.assertEquals("F00001IIYM", response.getInvestments().get(0).getId());
        Assert.assertEquals("USD", response.getInvestments().get(0).getCurrentPairList().get(0).getValue());
        Assert.assertEquals(1, response.getInvestments().get(0).getErrors().size());
        Assert.assertEquals("5PUSA00003", response.getInvestments().get(1).getId());
        assertTrue( CollectionUtils.isEmpty(response.getInvestments().get(1).getCurrentPairList()));
        Assert.assertEquals(1, response.getInvestments().get(1).getErrors().size());
    }

    private static FilteredRequestData<MartRequest> getFilteredRequestData(MartRequest martRequest) {
        return new FilteredRequestData<>(martRequest, new ArrayList<>());
    }

    @Test
    public void syncRetrieveSecurities_shouldReturnErrorStatusWhenEntitlementFails() {
        MartRequest martRequest = MartRequest.builder()
                .ids(List.of("F00001IIYM"))
                .dps(List.of("3010"))
                .userId("testUser")
                .configId("testConfig")
                .checkEntitlement(true)
                .build();

        EntitlementException expectedException = new EntitlementException(
                Status.REDIS_CONNECTION_FAILED, null
        );
        when(dataEntitlementService.getEntitlement(martRequest.getUserId(), martRequest.getConfigId()))
                .thenThrow(expectedException);

        try {
            tsNewRspGateway.syncRetrieveSecurities(martRequest);
        } catch (EntitlementException e) {
            Assert.assertEquals("Redis connection failed", e.getMessage());
        }
        // Verify no downstream processing occurred
        verify(martDataPipeline, never()).execute(any());
        verify(tsCacheProxyApplication, never()).retrieve(any());
    }
}
