package com.morningstar.martgateway.interfaces.model;

import static org.junit.Assert.*;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdDatePair;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.sun.jdi.request.InvalidRequestStateException;
import java.util.Arrays;
import org.junit.Before;
import org.junit.Test;


public class MartRequestValidatorTest {
    private MartRequestValidator martRequestValidator;
    private MartRequest martRequest;

    @Before
    public void setUp() {
        martRequestValidator =  new MartRequestValidator();
        String dps = "53118,53190";
        String investmentId="F00000ZO9A";
        String start="2021-01-01";
        String end="2021-08-06";
        String currency="USD";
        String readCache="false";
        martRequest = MartRequest.builder()
                .currency(currency)
                .dps(Arrays.asList(dps.split(",")))
                .ids(Arrays.asList(investmentId.split(",")))
                .startDate(start)
                .endDate(end)
                .preCurrency(currency)
                .readCache(readCache)
                .build();
    }

    @Test(expected = InvalidRequestStateException.class)
    public void validateInvalidRequestStateException() {
        IdDatePair idDatePair = IdDatePair.builder().id("$FOCA$UB$$").build();
        martRequest.setIdPairs(Arrays.asList(idDatePair));
        martRequestValidator.validate(martRequest);
    }

    @Test(expected = MartException.class)
    public void validateDateException() {
        martRequest.setEndDate("2021-01-01");
        martRequest.setStartDate("2021-08-06");
        martRequestValidator.validate(martRequest);
    }

    @Test(expected = MartException.class)
    public void validateParseDateException() {
        martRequest.setEndDate("2021");
        martRequest.setStartDate("08-06");
        martRequestValidator.validate(martRequest);
    }

    @Test
    public void validate() {
        boolean pass = true;
        try{
            martRequestValidator.validate(martRequest);
        } catch (Exception e) {
            pass = false;
        }
        assertTrue(pass);


    }
}