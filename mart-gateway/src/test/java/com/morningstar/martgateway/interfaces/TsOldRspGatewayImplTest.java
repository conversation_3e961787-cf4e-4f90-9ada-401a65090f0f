package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.calculationlib.conversion.ExchangeRateLoader;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.exception.EntitlementException;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementMartRequestWrapper;
import com.morningstar.martgateway.applications.tscacheproxy.TsCacheProxyApplication;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.martgateway.domains.rdb.helper.ExchangeRateCache;
import com.morningstar.martgateway.domains.timeseries.entity.TSContent;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TsOldRspGatewayImplTest {

    @Mock
    private MartDataPipeline martDataPipeline;
    @Mock
    private TsCacheProxyApplication tsCacheProxyApplication;
    @Mock
    private IdMapUtil idMapUtil;
    @Mock
    private DataEntitlementService<MartRequest> dataEntitlementService;
    @Mock
    private EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService;

    @Mock
    ExchangeRateLoader exchangeRateLoader;

    ExchangeRateCache exchangeRateCache;

    private TsOldRspGatewayImpl tsOldRspGateway;

    @BeforeEach
    void setup() {
        exchangeRateCache = new ExchangeRateCache(exchangeRateLoader, 10000);
        this.tsOldRspGateway = new TsOldRspGatewayImpl(martDataPipeline, tsCacheProxyApplication, idMapUtil, dataEntitlementService, entitlementMartRequestFilterService, exchangeRateCache);
        DataPointRepository.setDataPointMap(buildDpMap());
    }

    @Test
    public void syncRetrieveSecurities() {
        MartRequest martRequest = setupData();
        TSResponse actualResponse = tsOldRspGateway.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        // Test using timeSeriesDatas field instead of content
        List<TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertTrue(timeSeriesDataList.stream().allMatch(data -> "5PUSA00003".equals(data.getSecId())));

        // Test data for datapoint 190012
        TimeSeriesData data190012 = findTimeSeriesData(actualResponse, "5PUSA00003", "190012");
        assertNotNull(data190012);
        assertEquals(500.00, getFirstValue(data190012), 0.01);

        // Test data for datapoint HP010
        TimeSeriesData dataHP010 = findTimeSeriesData(actualResponse, "5PUSA00003", "HP010");
        assertNotNull(dataHP010);
        assertEquals(100.00, getFirstValue(dataHP010), 0.01);

        // Test data for datapoint HP012
        TimeSeriesData dataHP012 = findTimeSeriesData(actualResponse, "5PUSA00003", "HP012");
        assertNotNull(dataHP012);
        assertEquals(105.05, getFirstValue(dataHP012), 0.01);
    }

    @Test
    public void syncRetrieveSecuritiesEmptyReturn() {
        MartRequest martRequest = setupEmptyData();
        TSResponse actualResponse = tsOldRspGateway.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
        assertEquals("0", actualResponse.getStatus().getCode());
    }

    @Test
    public void testEmptyDps() {
        MartRequest martRequest = setupEmptyData();
        martRequest.setDps(null);
        TSResponse actualResponse = tsOldRspGateway.syncRetrieveSecurities(martRequest);
        assertNull(actualResponse);
    }

    @Test
    public void testMostRecentDataFilter() {
        MartRequest martRequest = setupMostRecentData();
        TSResponse actualResponse = tsOldRspGateway.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);
    }

    private MartRequest setupData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Arrays.asList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponse()));
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190012", Collections.singletonList(new V("2023-01-01","500.00")));
        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }

    private MartRequest setupEmptyData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Arrays.asList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        when(martDataPipeline.execute(any())).thenReturn(Flux.empty());
        return martRequest;
    }

    private MartRequest setupMostRecentData() {
        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP010","190012","HP012"))
                .ids(Arrays.asList("5PUSA00003"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.just(buildTsResponse()));
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("5PUSA00003", "{\"SecurityType\":\"CP\",\"SecId\":\"5PUSA00003\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190012", Collections.singletonList(new V("2023-01-01","500.00")));
//        TimeSeriesResult result = new TimeSeriesResult("5PUSA00003", values);
        CurrentResult result = new CurrentResult("5PUSA00003", new HashMap<>());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result));
        return martRequest;
    }

    private TSResponse buildTsResponse() {
        TSResponse tsResponse = new TSResponse();
        TSStatus status = new TSStatus();
        status.setCode("0");
        status.setMsg("");
        tsResponse.setStatus(status);
        TSContent tsContent = new TSContent();

        TSItem tsItem = new TSItem();
        tsItem.setSecid("5PUSA00003;CP");
        tsItem.setDataid("HP010");
        TSData tsData = new TSData();
        tsData.setDate(10012);
        tsData.setValue(100.00);
        tsItem.setData(Collections.singletonList(tsData));

        TSItem tsItem2 = new TSItem();
        tsItem2.setSecid("5PUSA00003;CP");
        tsItem2.setDataid("HP012");
        TSData tsData2 = new TSData();
        tsData2.setDate(10015);
        tsData2.setValue(105.05);
        tsItem2.setData(Collections.singletonList(tsData2));

        tsContent.setItems(List.of(tsItem, tsItem2));
        tsResponse.setContent(tsContent);
        return tsResponse;
    }

    private Map<String, DataPoint> buildDpMap() {
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        DataPoint dataPoint1 = DataPoint.builder().nid("HP010").id("HP010").src("CDAPI").tsRdb(RdbDataPoint.builder().build()).build();
        DataPoint dataPoint2 = DataPoint.builder().nid("190012").id("190012").contentType("decimal").src("RDB").tsRdb(RdbDataPoint.builder().build()).build();
        dataPointMap.put("HP010", dataPoint1);
        dataPointMap.put("190012", dataPoint2);
        return dataPointMap;
    }

    @Test
    public void whenNoUserEntitlement_shouldGetResponse(){
        MartRequest martRequest = MartRequest.builder()
                .ids(new ArrayList<>(Arrays.asList("F00001IIYM")))
                .dps(new ArrayList<>(Arrays.asList("3010")))
                .userId("user_id")
                .checkEntitlement(true)
                .build();
        Map<String, List<V>> values = new HashMap<>();
        values.put("AU002", Collections.singletonList(new V("2023-01-01","500.00")));

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(dataEntitlementService.getEntitlement(Mockito.anyString(), Mockito.anyString())).thenThrow(new NullPointerException());
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values)));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementMartRequestWrapper.class)))
                .thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values),
                        new ErrorResult("F00001IIYM", "HPD10", "403"),
                        new ErrorResult("5PUSA00003", "HPD10", "403")));
        when(entitlementMartRequestFilterService.filterRequest(any(), any())).thenReturn(getFilteredRequestData(martRequest));

        TSResponse response = tsOldRspGateway.syncRetrieveSecurities(martRequest);
        assertNotNull(response.getTimeSeriesDatas());
        //assertEquals(Status.NO_ENTITLEMENT_INFO.getCode(), response.getStatus().getCode());
    }

    @Test
    public void testResponseWithEntitlementFiltered() {
        MartRequest martRequest = MartRequest.builder().ids(new ArrayList<>(Arrays.asList("F00001IIYM", "5PUSA00003"))).dps(new ArrayList<>(Arrays.asList("AU002", "HPD10")))
                .checkEntitlement(true)
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        Map<String, List<V>> values = new HashMap<>();
        values.put("AU002", Collections.singletonList(new V("2023-01-01","500.00")));
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values)));
        when(dataEntitlementService.applyDataEntitlement(any(), any(), any(EntitlementMartRequestWrapper.class)))
                .thenReturn(Flux.just(new TimeSeriesResult("F00001IIYM", values),
                        new ErrorResult("F00001IIYM", "HPD10", "403"),
                        new ErrorResult("5PUSA00003", "HPD10", "403")));
        when(entitlementMartRequestFilterService.filterRequest(any(), any())).thenReturn(
                getFilteredRequestData(martRequest));

        TSResponse response = tsOldRspGateway.syncRetrieveSecurities(martRequest);
        assertEquals("200200", response.getStatus().getCode());

        // Test using timeSeriesDatas field instead of content
        List<TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(response);
        assertEquals(3, timeSeriesDataList.size());

        // Test first item (F00001IIYM, AU002)
        TimeSeriesData dataAU002 = findTimeSeriesData(response, "F00001IIYM", "AU002");
        assertNotNull(dataAU002);
        assertEquals("F00001IIYM", dataAU002.getSecId());
        assertEquals("AU002", dataAU002.getDataId());
        assertEquals(500.0, getFirstValue(dataAU002), 0.01);

        // Test second item (F00001IIYM, HPD10) - error case
        TimeSeriesData dataHPD10_1 = findTimeSeriesData(response, "F00001IIYM", "HPD10");
        assertNotNull(dataHPD10_1);
        assertEquals("F00001IIYM", dataHPD10_1.getSecId());
        assertEquals("HPD10", dataHPD10_1.getDataId());
        assertEquals("403", getErrorCode(dataHPD10_1));

        // Test third item (5PUSA00003, HPD10) - error case
        TimeSeriesData dataHPD10_2 = findTimeSeriesData(response, "5PUSA00003", "HPD10");
        assertNotNull(dataHPD10_2);
        assertEquals("5PUSA00003", dataHPD10_2.getSecId());
        assertEquals("HPD10", dataHPD10_2.getDataId());
        assertEquals("403", getErrorCode(dataHPD10_2));
    }

    private static FilteredRequestData<MartRequest> getFilteredRequestData(MartRequest martRequest) {
        return new FilteredRequestData<>(martRequest, new ArrayList<>());
    }

    private MartRequest setupDataWithInvalidDataType(){

        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList("HP026,190240"))
                .ids(Arrays.asList("F00000S7JB"))
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();

        when(tsCacheProxyApplication.retrieve(any())).thenReturn(Flux.empty());
        when(idMapUtil.getIdMappers(any())).thenReturn(Collections.singletonList(new NonEmptyIdMapper("F00000S7JB", "{\"SecurityType\":\"FO\",\"SecId\":\"F00000S7JB\"}")));
        Map<String, List<V>> values = new HashMap<>();
        values.put("190240", Collections.singletonList(new V("2023-01-01","Alex Lucas")));

        Map<String, List<V>> values2 = new HashMap<>();
        values2.put("HP026", Collections.singletonList(new V("2023-12-31","1600.00")));
        TimeSeriesResult result = new TimeSeriesResult("F00000S7JB", values);
        TimeSeriesResult result2 = new TimeSeriesResult("F00000S7JB", values2);
        when(martDataPipeline.execute(any())).thenReturn(Flux.just(result, result2));
        return martRequest;
    }

    @Test
    public void syncRetrieveSecuritiesWithDataTypeError() {
        MartRequest martRequest = setupDataWithInvalidDataType();
        TSResponse actualResponse = tsOldRspGateway.syncRetrieveSecurities(martRequest);
        assertNotNull(actualResponse);

        // Test using timeSeriesDatas field instead of content
        assertEquals(Status.INVALID_DATATYPE_IN_DATAPOINT_FOR_TS.getCode(), actualResponse.getStatus().getCode());

        List<TimeSeriesData> timeSeriesDataList = getTimeSeriesDataList(actualResponse);
        assertTrue(timeSeriesDataList.stream().allMatch(data -> "F00000S7JB".equals(data.getSecId())));

        // Test data for datapoint HP026
        TimeSeriesData dataHP026 = findTimeSeriesData(actualResponse, "F00000S7JB", "HP026");
        assertNotNull(dataHP026);
        assertEquals(1600.00, getFirstValue(dataHP026), 0.01);
    }

    @Test
    public void syncRetrieveSecurities_shouldReturnErrorStatusWhenEntitlementFails() {
        MartRequest martRequest = MartRequest.builder()
                .ids(List.of("F00001IIYM"))
                .dps(List.of("3010"))
                .userId("testUser")
                .configId("testConfig")
                .checkEntitlement(true)
                .build();

        EntitlementException expectedException = new EntitlementException(
                Status.REDIS_CONNECTION_FAILED, null
        );

        when(dataEntitlementService.getEntitlement(martRequest.getUserId(), martRequest.getConfigId()))
                .thenThrow(expectedException);

        try {
            tsOldRspGateway.syncRetrieveSecurities(martRequest);
        } catch (EntitlementException e) {
            Assert.assertEquals("Redis connection failed", e.getMessage());
        }
        // Verify no downstream processing occurred
        verify(martDataPipeline, never()).execute(any());
        verify(tsCacheProxyApplication, never()).retrieve(any());
    }

    // Helper methods to extract data from timeSeriesDatas field for testing
    private List<TimeSeriesData> getTimeSeriesDataList(TSResponse response) {
        if (response.getTimeSeriesDatas() == null) {
            return Collections.emptyList();
        }
        return response.getTimeSeriesDatas().getValuesList();
    }

    private TimeSeriesData findTimeSeriesData(TSResponse response, String secId, String dataId) {
        return getTimeSeriesDataList(response).stream()
                .filter(data -> secId.equals(data.getSecId()) && dataId.equals(data.getDataId()))
                .findFirst()
                .orElse(null);
    }

    private Double getFirstValue(TimeSeriesData data) {
        if (data == null || data.getValuesList().isEmpty()) {
            return null;
        }
        TSValuePair firstPair = data.getValues(0);
        return firstPair.getValuesCount() > 0 ? firstPair.getValues(0) : null;
    }

    private String getErrorCode(TimeSeriesData data) {
        return data != null ? data.getErrorCode() : null;
    }
}
