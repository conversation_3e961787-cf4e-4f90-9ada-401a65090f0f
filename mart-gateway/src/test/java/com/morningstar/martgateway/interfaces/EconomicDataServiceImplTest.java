package com.morningstar.martgateway.interfaces;

import com.morningstar.martgateway.applications.economic.EconomicDataGateway;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataResult;
import com.morningstar.martgateway.domains.economic.entity.EconomicReferenceDataResponse;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

@ExtendWith(MockitoExtension.class)
class EconomicDataServiceImplTest {


    @Mock
    private EconomicDataGateway economicDataGateway;

    @InjectMocks
    private EconomicDataGatewayImpl economicDataGatewayImpl;

    @BeforeEach
    public void setup() {
        economicDataGatewayImpl = new EconomicDataGatewayImpl(economicDataGateway);
    }

    @Test
    void testEconomicReferenceDataWithFRED() {
        EconomicDataRequest request=new EconomicDataRequest();
        request.setEconomicDataFilter("FRED");
        EconomicDataResult mockResult = new EconomicDataResult();
        mockResult.setIndicators(List.of(new EconomicReferenceDataResponse(
                "DEXSIUS", "DEXSIUS", "Singapore Dollars to U.S. Dollar Spot Exchange Rate",
                "FRED, Singapore Dollars to U.S. Dollar Spot Exchange Rate, Not Seasonally Adjusted, Singapore Dollars to One U.S. Dollar", "",null, "","","","","","","","",""
        )));

        Mockito.when(economicDataGateway.retrieve(ArgumentMatchers.any()))
                .thenReturn(Flux.just(mockResult));

        EconomicDataResult response = economicDataGatewayImpl.syncRetrieveSecurities(request);
        Assertions.assertNotNull(response);
    }

    @Test
    void testEconomicMetaDataWith() {
        EconomicDataRequest request=new EconomicDataRequest();
        request.setIndicatorId("DEXSIUS");
        EconomicDataResult mockResult = new EconomicDataResult();
        mockResult.setIndicators(List.of(new EconomicReferenceDataResponse(
                "", "DEXSIUS", "",
                "", "",null, "Daily","Singapore Dollars to U.S. Dollar Spot Exchange Rate","FRED, Singapore","2025-07-07","Daily","Not Seasonally Adjusted","Singapore Dollars to One U.S. Dollar","Not Seasonally Adjusted","2025-07-03"
        )));

        Mockito.when(economicDataGateway.retrieve(ArgumentMatchers.any()))
                .thenReturn(Flux.just(mockResult));

        EconomicDataResult response = economicDataGatewayImpl.syncRetrieveSecurities(request);
        Assertions.assertNotNull(response);
    }
}