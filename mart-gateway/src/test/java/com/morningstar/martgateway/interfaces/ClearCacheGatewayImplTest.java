package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.common.entity.ClearCacheRequest;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdDatePair;
import com.morningstar.dataac.martgateway.core.common.repository.SqsClient;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ClearCacheGatewayImplTest {

    @Mock
    SqsClient sqsClient;

    private ClearCacheGatewayImpl clearCacheGatewayImpl;

    @Before
    public void setup() {
        this.clearCacheGatewayImpl = new ClearCacheGatewayImpl(sqsClient, "awsId", "rdb_sqs");
    }

    @Test
    public void testSyncRetrieveSecurities() {

        IdDatePair idDatePair = new IdDatePair();
        idDatePair.setId("610498");
        idDatePair.setDates(Arrays.asList(LocalDate.now()));
        ClearCacheRequest clearCacheRequest = ClearCacheRequest.builder().dataPoints(List.of("HS987")).investmentIds(List.of("0P98AHBG12")).build();
        when(sqsClient.sendMessage(anyString(), anyString(), anyString())).thenReturn(true);

        try(MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic = Mockito.mockStatic(DataPointRepository.class)) {
            DataPoint dp = DataPoint.builder().id("HS987").nid("HS987").src("RDB").name("Return (Qtr-end)").build();
            dataPointRepositoryMockedStatic.when(() -> DataPointRepository.getByNid("HS987")).thenReturn(dp);
            MartResponse response = clearCacheGatewayImpl.syncRetrieveSecurities(clearCacheRequest);
            Assert.assertTrue(response.getStatus().equals(Status.ACCEPTED));
        }
    }
}
