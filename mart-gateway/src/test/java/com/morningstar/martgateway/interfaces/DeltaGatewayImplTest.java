package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.martgateway.applications.delta.DeltaGateway;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.interfaces.model.FormatConverter;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import org.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

import java.time.Instant;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class DeltaGatewayImplTest {

    @Mock
    private DeltaGateway deltaGateway;
    @Mock
    private IdMapUtil idMapUtil;

    private DeltaGatewayImpl deltaGatewayImpl;

    @BeforeEach
    public void setup() {
        deltaGatewayImpl = new DeltaGatewayImpl(deltaGateway, idMapUtil, new FormatConverter());
    }

    @Test
    public void syncRetrieveSecurities() {
        InvestmentApiRequest request = buildRequest();
        Mockito.when(idMapUtil.getIdMappers(ArgumentMatchers.anyList())).thenReturn(buildIdMappers());
        Mockito.when(deltaGateway.retrieve(ArgumentMatchers.any())).thenReturn(Flux.just(new CurrentResult("F00000JU03", Collections.singletonMap("TFC2N", null))));
        InvestmentResponse response = deltaGatewayImpl.syncRetrieveSecurities(request);
        Assertions.assertNotNull(response);
    }

    private InvestmentApiRequest buildRequest() {
        return InvestmentApiRequest.builder()
                .deltaStartTime(Instant.now())
                .dataPoints(List.of(
                                GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                                GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                                new Investment("F00000JU03"),
                                new Investment("0P0000PZ28")
                        )
                )
                .build();
    }

    private List<IdMapper> buildIdMappers() {
        String investmentId1 = "F00000JU03";
        JSONObject jsonObject1 = new JSONObject()
                .put("SecId", investmentId1)
                .put("PerformanceId", "0P0000PZ99");
        IdMapper idMapper1 = new NonEmptyIdMapper(investmentId1, jsonObject1);

        String investmentId2 = "0P0000PZ28";
        JSONObject jsonObject2 = new JSONObject()
                .put("SecId", "F00000Z01")
                .put("PerformanceId", investmentId2);
        IdMapper idMapper2 = new NonEmptyIdMapper(investmentId2, jsonObject2);

        return List.of(idMapper1, idMapper2);
    }
}
