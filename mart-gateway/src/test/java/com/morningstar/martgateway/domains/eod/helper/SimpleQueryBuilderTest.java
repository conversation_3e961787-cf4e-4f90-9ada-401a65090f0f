package com.morningstar.martgateway.domains.eod.helper;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
public class SimpleQueryBuilderTest {

    @Test
    @DisplayName("should build a string with ids join")
    void shouldBuildIdsJoinString() {
        String query = SimpleQueryBuilder.buildIdsJoin(List.of("A", "B", "C"));
        assertEquals("'A','B','C'", query);
    }

    @Test
    @DisplayName("should build a string with column and alias")
    void shouldBuildColumnName() {
        String query = SimpleQueryBuilder.buildColumnName("col1", "alias1");
        assertEquals("col1 as alias1", query);
    }

    @Test
    @DisplayName("should build column list")
    void shouldBuildColumnList() {
        String query = SimpleQueryBuilder.buildColumnList(List.of("A", "B", "C"));
        assertEquals("A,B,C", query);
    }
}
