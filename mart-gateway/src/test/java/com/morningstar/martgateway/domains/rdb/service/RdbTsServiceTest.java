package com.morningstar.martgateway.domains.rdb.service;

import static com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil.parseLocalDate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.martgateway.domains.core.entity.DateRange;
import com.morningstar.martgateway.domains.rdb.cache.TsDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.TsFrequencyDataLoader;
import com.morningstar.martgateway.domains.rdb.helper.RdbTsCacheDataHelper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEventPublisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@RunWith(MockitoJUnitRunner.class)
public class RdbTsServiceTest {

	@Mock
	private RdbTsCacheDataHelper rdbTsCacheDataHelper;
	@Mock
	private TsFrequencyDataLoader tsFrequencyDataLoader;
	@Mock
	private TsDataLoader tsDataLoader;
	@Mock
	private ApplicationEventPublisher applicationEventPublisher;

	private RdbTsService rdbTsService;

	@Before
	public void setUp(){
		rdbTsService = new RdbTsService(rdbTsCacheDataHelper, tsFrequencyDataLoader, tsDataLoader, applicationEventPublisher, true);
	}

	@Test
	public void testGetDataNonPostTax(){
		IdMapper idMapper = new NonEmptyIdMapper("F00000008W", "{\"SecId\": \"F00000008W\"}");
		MartRequest martRequest = MartRequest.builder().startDate("2011-01-01").endDate("2020-01-01").idMappers(List.of(idMapper)).build();
		RdbDataPoint tsRdbDataPoint = RdbDataPoint.builder().groupName("SPGrossReturnMonth").build();
		DataPoint dataPoint = DataPoint.builder().id("HP010").nid("HP010").tsRdb(tsRdbDataPoint).build();
		List<DataPoint> dataPointList = List.of(dataPoint);
		when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(Mono.just(new HashMap<>()));
		Flux<Result> resultFlux = rdbTsService.loadBatchedData(martRequest, dataPointList, true);
		ArgumentCaptor<RdbDataPoint> rdbDataPointArgumentCaptor = ArgumentCaptor.forClass(RdbDataPoint.class);

		StepVerifier.create(resultFlux)
				.verifyComplete();

		verify(rdbTsCacheDataHelper, times(1)).getCacheData(any(), rdbDataPointArgumentCaptor.capture(), any(), any(), anyBoolean());
		assertEquals(tsRdbDataPoint, rdbDataPointArgumentCaptor.getValue());
	}

	@Test
	public void testGetDataPostTaxAndNonPostTax(){
		IdMapper idMapper1 = new NonEmptyIdMapper("F00000008W", "{\"SecId\": \"F00000008W\", \"PostTaxForMixSetting\": \"1\"}");
		IdMapper idMapper2 = new NonEmptyIdMapper("F0GBR04ATY", "{\"SecId\": \"F0GBR04ATY\"}");
		MartRequest martRequest = MartRequest.builder().startDate("2011-01-01").endDate("2020-01-01").idMappers(List.of(idMapper1, idMapper2)).postTax("1").build();
		RdbDataPoint tsRdbDataPoint = RdbDataPoint.builder().groupName("SPGrossReturnMonth").build();
		RdbDataPoint postTaxTsRdbDataPoint = RdbDataPoint.builder().groupName("TaxAdjustedReturnMonth").build();
		DataPoint dataPoint = DataPoint.builder().id("HP010").nid("HP010").tsRdb(tsRdbDataPoint).postTaxTsRdb(postTaxTsRdbDataPoint).build();
		List<DataPoint> dataPointList = List.of(dataPoint);
		when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(Mono.just(new HashMap<>()));
		Flux<Result> resultFlux = rdbTsService.loadBatchedData(martRequest, dataPointList, true);
		ArgumentCaptor<RdbDataPoint> rdbDataPointArgumentCaptor = ArgumentCaptor.forClass(RdbDataPoint.class);

		StepVerifier.create(resultFlux)
				.verifyComplete();

		verify(rdbTsCacheDataHelper, times(2)).getCacheData(any(), rdbDataPointArgumentCaptor.capture(), any(), any(), anyBoolean());
		assertEquals(tsRdbDataPoint, rdbDataPointArgumentCaptor.getAllValues().get(0));
		assertEquals(postTaxTsRdbDataPoint, rdbDataPointArgumentCaptor.getAllValues().get(1));
	}

	@Test
	public void testGetDataEmptyDataPointList(){
		IdMapper idMapper1 = new NonEmptyIdMapper("F00000008W", "{\"SecId\": \"F00000008W\", \"PostTaxForMixSetting\": \"1\"}");
		MartRequest martRequest = MartRequest.builder().ids(List.of("F00000008W")).startDate("2011-01-01").endDate("2020-01-01").idMappers(List.of(idMapper1)).postTax("1").build();
		Flux<Result> resultFlux = rdbTsService.loadBatchedData(martRequest, new ArrayList<>(), true);
		resultFlux.subscribe();
		ArgumentCaptor<RdbDataPoint> rdbDataPointArgumentCaptor = ArgumentCaptor.forClass(RdbDataPoint.class);
		verify(rdbTsCacheDataHelper, times(0)).getCacheData(any(), rdbDataPointArgumentCaptor.capture(), any(), any(), anyBoolean());
	}

	@Test
	public void testOverwriteDateRange(){

		RdbTsService rdbTsService = spy(new RdbTsService(rdbTsCacheDataHelper, tsFrequencyDataLoader, tsDataLoader, applicationEventPublisher, true));

		RdbDataPoint tsRdbDataPoint = RdbDataPoint.builder().groupName("SPGrossReturnMonth").build();
		DataPoint dataPoint = DataPoint.builder().id("HP010").nid("HP010").tsRdb(tsRdbDataPoint).build();
		List<DataPoint> dataPointList = List.of(dataPoint);

		MartRequest martRequest = MartRequest.builder().startDate("1730-01-01").endDate("2024-01-01").ids(new ArrayList<>()).dps(Arrays.asList("90325")).idMappers(new ArrayList<>()).build();
		MartRequest martRequest2 = MartRequest.builder().startDate("2011-01-01").endDate("3000-01-01").ids(new ArrayList<>()).dps(Arrays.asList("90325")).idMappers(new ArrayList<>()).build();
		MartRequest martRequest3 = MartRequest.builder().startDate("2014-01-01").endDate("2028-01-01").ids(new ArrayList<>()).dps(Arrays.asList("90325")).idMappers(new ArrayList<>()).build();

		Flux<Result> resultFlux = rdbTsService.loadBatchedData(martRequest, dataPointList, true);
		Flux<Result> resultFlux2 = rdbTsService.loadBatchedData(martRequest2, dataPointList, true);
		Flux<Result> resultFlux3 = rdbTsService.loadBatchedData(martRequest3, dataPointList, true);

		ArgumentCaptor<DateRange> dateRangeCaptor = ArgumentCaptor.forClass(DateRange.class);
		verify(rdbTsService, times(3)).loadBatchedData(any(), any(), dateRangeCaptor.capture(), any(), anyBoolean(), any());
		resultFlux.subscribe();
		resultFlux2.subscribe();
		resultFlux3.subscribe();

		Assert.assertEquals("1900-01-01", dateRangeCaptor.getAllValues().get(0).getStartDate());
		Assert.assertEquals("2024-01-01",dateRangeCaptor.getAllValues().get(0).getEndDate());
		Assert.assertEquals("2011-01-01",dateRangeCaptor.getAllValues().get(1).getStartDate());
		LocalDate expectedDate = LocalDate.now().plusYears(5).plusDays(1);
		Assert.assertTrue(expectedDate.isAfter(parseLocalDate(dateRangeCaptor.getAllValues().get(1).getEndDate())));
		Assert.assertEquals("2014-01-01",dateRangeCaptor.getAllValues().get(2).getStartDate());
		Assert.assertEquals("2028-01-01",dateRangeCaptor.getAllValues().get(2).getEndDate());
	}

	@Test
	public void testLoadBatchedDataEmptyDates(){
		IdMapper idMapper1 = new NonEmptyIdMapper("F00000008W", "{\"SecId\": \"F00000008W\", \"PostTaxForMixSetting\": \"1\"}");
		MartRequest martRequest = MartRequest.builder().ids(List.of("F00000008W")).startDate(null).endDate(null).idMappers(List.of(idMapper1)).build();
		Flux<Result> resultFlux = rdbTsService.loadBatchedData(martRequest, new ArrayList<>(), true);
		StepVerifier.create(resultFlux)
				.expectNextCount(1)
				.verifyComplete();

		martRequest = MartRequest.builder().ids(List.of("F00000008W")).startDate("2011-01-01").endDate(null).idMappers(List.of(idMapper1)).build();
		resultFlux = rdbTsService.loadBatchedData(martRequest, new ArrayList<>(), true);
		StepVerifier.create(resultFlux)
				.expectNextCount(1)
				.verifyComplete();

		martRequest = MartRequest.builder().startDate(null).endDate(null).build();
		resultFlux = rdbTsService.loadBatchedData(martRequest, new ArrayList<>(), true);
		StepVerifier.create(resultFlux)
				.expectNextCount(0)
				.verifyComplete();

	}
}
