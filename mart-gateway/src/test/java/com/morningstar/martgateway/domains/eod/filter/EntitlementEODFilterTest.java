package com.morningstar.martgateway.domains.eod.filter;

import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.entitlement.eod.data.entity.Status;
import com.morningstar.entitlement.eod.data.entity.entitlementrequest.EntitlementRequest;
import com.morningstar.entitlement.eod.data.entity.entitlementresponse.EntitlementResponse;
import com.morningstar.entitlement.eod.data.interfaces.EntitlementEODDataService;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.morningstar.martgateway.domains.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EntitlementEODFilterTest {

    private final static String DUMMY_ID = "dummyId";

    private MartRequest martRequest;
    @Mock
    private EntitlementEODDataService<EntitlementResponse, EntitlementRequest> entitlementEODDataService;
    @InjectMocks
    private EntitlementEODHandler entitlementEODHandler;

    @BeforeAll
    void setUp() {
        martRequest = MartRequest.builder()
                .userId(UUID.randomUUID().toString())
                .ids(List.of(DUMMY_ID))
                .build();
        List<Investment> investmentList = new ArrayList<>();
        investmentList.add(Investment.builder()
                .performanceId(DUMMY_ID).releaseTime(LocalTime.now())
                .build());
        EntitlementResponse response = new EntitlementResponse();
        response.setStatus(Status.OK);
        response.setProductCodes(List.of(1L));
        response.setInvestments(investmentList);

        when(entitlementEODDataService.retrieveUserInvestments(any(EntitlementRequest.class))).thenReturn(response);
    }

    @BeforeEach
    void applyToEach() {
        martRequest.setAttribute(ENTITLED_INVESTMENTS, null);
    }

    @ParameterizedTest
    @DisplayName("filter should be applied successfully with different user id and config id")
    @CsvSource({"7B480231-206B-4E7C-90D7-43CF3F3B9EEC, config_id", "7B480231-206B-4E7C-90D7-43CF3F3B9EEC,"})
    @Order(1)
    public void shouldApplyFilter(String userId, String configId) {
        martRequest.setUserId(userId);
        martRequest.setConfigId(configId);
        entitlementEODHandler.apply(martRequest);

        assertNotNull(martRequest.getAttribute(ENTITLED_INVESTMENTS));
    }

    @Test
    @DisplayName("filter should set empty list when entitlement return any status other than OK")
    @Order(2)
    void shouldSetEmptyListWhenEntitlementReturnNotOK() {
        EntitlementResponse response = new EntitlementResponse();
        response.setStatus(Status.BAD_REQUEST);
        when(entitlementEODDataService.retrieveUserInvestments(any(EntitlementRequest.class))).thenReturn(response);

        entitlementEODHandler.apply(martRequest);

        assertEquals(0, ((List<Investment>)martRequest.getAttribute(ENTITLED_INVESTMENTS)).size());
    }

    @Test
    @DisplayName("filter return immediately with empty performance id")
    @Order(3)
    public void shouldReturnImmediatelyWithEmptyPerformanceId() {
        martRequest.setIds(Collections.emptyList());

        entitlementEODHandler.apply(martRequest);

        assertEquals(0, ((List<Investment>)martRequest.getAttribute(ENTITLED_INVESTMENTS)).size());
    }

    @Test
    @DisplayName("filter should return immediately with neither user id nor config id")
    @Order(4)
    void shouldReturnImmediatelyWithoutUserIdAndConfigID() {
        martRequest.setUserId(null);
        martRequest.setConfigId(null);

        entitlementEODHandler.apply(martRequest);

        assertNull(martRequest.getAttribute(ENTITLED_INVESTMENTS));
    }

    @Test
    @DisplayName("filter should return immediately with null IdMappers")
    @Order(5)
    void shouldReturnImmediatelyWithNullIdMappers() {
        martRequest.setIdMappers(null);

        entitlementEODHandler.apply(martRequest);

        assertNull(martRequest.getAttribute(ENTITLED_INVESTMENTS));
    }

    @Test
    @DisplayName("filter should throw exception with invalid user id")
    @Order(6)
    void whenUserIdIsInvalidUUIT_shouldGetException() {
        martRequest.setIds(List.of("invest1"));

        martRequest.setUserId("invalid_user_id");

        assertThrows(IllegalArgumentException.class, () -> entitlementEODHandler.apply(martRequest));
    }
}
