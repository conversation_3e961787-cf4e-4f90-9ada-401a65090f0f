package com.morningstar.martgateway.domains.eod;

import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.martgateway.domains.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.martgateway.domains.eod.repository.EODMongodbAsyncRepo;
import com.morningstar.martgateway.domains.eod.service.EODDataServiceImpl;
import com.morningstar.martgateway.domains.eod.repository.EODAsyncRepo;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import org.bson.Document;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;

import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.*;
import static com.morningstar.martgateway.domains.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EODDataServiceImplTest {

    private final static String DUMMY = "dummy";
    private final static String DUMMY_VALUE = "dummy value";

    @Mock
    EODAsyncRepo eodAsyncRepo;
    @Mock
    EODMongodbAsyncRepo eodMongodbAsyncRepo;
    @InjectMocks
    EODDataServiceImpl eodDataService;

    private MartRequest martRequest;

    @BeforeAll
    void setUp() {
        martRequest = new MartRequest();
        martRequest.setIds(List.of("invest1"));
        martRequest.setAttribute(ENTITLED_INVESTMENTS, List.of(Investment.builder().performanceId("invest1").build()));
    }

    @Test
    @DisplayName("get result with request and data points")
    void givenRequestAndPostgresDataPoint_shouldGetFluxResult() {
        Flux<Map<String, Object>> fluxResult = Flux.just(Map.of(UNIQUEID, DUMMY_VALUE));

        when(eodAsyncRepo.executeSQL(anyString())).thenReturn(fluxResult);

        Flux<Result> resultFlux = eodDataService.getData(martRequest, List.of(EODDataPoint.builder()
                .id("dpId1").column(UNIQUEID)
                .databaseSchema(DUMMY).tables(DUMMY).src(EODPG).storeProcedure(DUMMY)
                .build()));

        StepVerifier.create(resultFlux)
                .assertNext(result -> assertEquals(DUMMY_VALUE, result.getId()))
                .verifyComplete();
    }

    @Test
    @DisplayName("get result with request and data points")
    void givenRequestAndMongoDataPoint_shouldGetFluxResult() {
        Flux<Document> fluxResult = Flux.just(new Document(UNIQUEID, DUMMY_VALUE));

        when(eodMongodbAsyncRepo.findAll(anyString(), anyString(), anyString(), anyList())).thenReturn(fluxResult);

        Flux<Result> resultFlux = eodDataService.getData(martRequest, List.of(EODDataPoint.builder()
                .id("dpId2").column(UNIQUEID)
                .databaseSchema(DUMMY).tables(DUMMY).src(EODMDB).storeProcedure(DUMMY)
                .build()));

        StepVerifier.create(resultFlux)
                .assertNext(result -> assertEquals(DUMMY_VALUE, result.getId()))
                .verifyComplete();
    }

}
