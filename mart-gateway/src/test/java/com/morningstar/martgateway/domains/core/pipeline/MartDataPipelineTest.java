package com.morningstar.martgateway.domains.core.pipeline;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.MultiUniverse;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementCacheService;
import com.morningstar.dataac.martgateway.core.common.repository.LocalIdMapperCache;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.martgateway.util.RequireIdUtil;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;

import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.Collections;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class MartDataPipelineTest {

    private MartRequestDataAssemblerService martRequestDataAssemblerService;
    private MartDataPipeline martDataPipeline;

    @Mock
    private IdMapUtil idMapUtil;

    @Mock
    private LocalIdMapperCache redisTemplate;

    @Mock
    private RequireIdUtil requireIdUtil;

    @Mock
    private EntitlementCacheService entitlementCacheService;

    @BeforeAll
    void setUp() {
        martRequestDataAssemblerService = mock(MartRequestDataAssemblerService.class);
        martDataPipeline = new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil, idMapUtil,
                entitlementCacheService);
    }

    @Test
    public void testMapHybridToCategoryId() {
        String hybridCategoryId = "$FOCA$LB$$;CA]FO";
        String categoryId = "$FOCA$LB$$";
        Map<String, List<V>> map = new HashMap<>();
        TimeSeriesResult result = new TimeSeriesResult(hybridCategoryId, map);
        Map<String, String> hybridToCategoryIdMapping = new HashMap<>();
        hybridToCategoryIdMapping.put(hybridCategoryId, categoryId);
        Result actual = martDataPipeline.mapHybridToCategoryId(result, hybridToCategoryIdMapping);
        assertEquals(result, actual);
        assertEquals(categoryId, actual.getId());
    }

    @Test
    //if no hybridCategoryId to categoryId mapping, values of Result object should remain as-is.
    public void testMapHybridToCategoryIdDoNothing() {
        Map<String, List<V>> map = new HashMap<>();
        TimeSeriesResult result = new TimeSeriesResult("A", map);
        Map<String, String> hybridToCategoryIdMapping = new HashMap<>();
        Result actual = martDataPipeline.mapHybridToCategoryId(result, hybridToCategoryIdMapping);
        assertEquals(result, actual);
        assertEquals(result.getId(), actual.getId());
        assertEquals(result.getValues(), actual.getValues());
    }

    @Test
    public void retrieveIdMappingIndustryCode() {
        MartRequest martRequest = MartRequest.builder()
                .dps(List.of("EQ0001"))
                .ids(List.of("10330020"))
                .idType("MorningstarIndustryCode")
                .build();

        IdMapUtil idMapUtil = new IdMapUtil(redisTemplate);
        MartDataPipeline martDataPipeline = new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil,
                idMapUtil, entitlementCacheService);

        martDataPipeline.retrieveIdMapping(martRequest);
        assertEquals(1, martRequest.getIdMappers().size());
        IdMapper idMapper = martRequest.getIdMappers().get(0);
        assertEquals("10330020", idMapper.getId("MorningstarIndustryCode"));
        assertEquals("10330020", idMapper.getInvestmentId());
    }

    @Test
    public void testRetrieveIdMappingCategoryCode() {
        MartRequest martRequest = new MartRequest();
        martRequest.setCategoryCode("$FOCA$LB$$");
        martRequest.setUniverse("FO");

        IdMapUtil idMapUtil = mock(IdMapUtil.class);
        martDataPipeline = new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil, idMapUtil, entitlementCacheService);

        ArgumentCaptor<List<String>> captor = ArgumentCaptor.forClass(List.class);
        martDataPipeline.applyCategoryIdMapping(martRequest);
        verify(idMapUtil, times(1)).getIdMappers(captor.capture());
        assertEquals("$FOCA$LB$$;CA]FO", captor.getValue().get(0));
    }

    @Test
    public void testRetrieveIdMappingCategoryCodeDoNothing() {
        MartRequest martRequest = new MartRequest();
        martRequest.setIds(List.of("A"));
        martRequest.setIdMappers(List.of(new NonEmptyIdMapper("A", "{}")));

        IdMapUtil idMapUtil = mock(IdMapUtil.class);
        martDataPipeline = new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil, idMapUtil, entitlementCacheService);

        ArgumentCaptor<List<String>> captor = ArgumentCaptor.forClass(List.class);
        martDataPipeline.applyCategoryIdMapping(martRequest);
        verify(idMapUtil, times(0)).getIdMappers(captor.capture());
    }

    @Test
    public void retrieveIdMappingIdMappersAlreadyExists() {
        IdMapUtil idMapUtil = mock(IdMapUtil.class);
        MartDataPipeline martDataPipeline = new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil, idMapUtil, entitlementCacheService);

        String id = "*********";
        JSONObject jsonObject = new JSONObject()
                .put("SecId", id)
                .put("SecurityType", "FO");
        List<IdMapper> idMappers = List.of(new NonEmptyIdMapper(id, jsonObject));
        MartRequest martRequest = MartRequest.builder()
                .idType(null)
                .idMappers(idMappers)
                .ids(List.of(id))
                .build();
        martDataPipeline.retrieveIdMapping(martRequest);
        verify(idMapUtil, times(0)).getIdMappers(anyList());
    }

    @Test
    public void retrieveIdMappingIdMappersNotYetExists() {
        IdMapUtil idMapUtil = mock(IdMapUtil.class);
        MartDataPipeline martDataPipeline = new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil, idMapUtil, entitlementCacheService);

        MartRequest martRequest = MartRequest.builder()
                .idType(null)
                .idMappers(null)
                .ids(List.of("*********"))
                .build();
        martDataPipeline.retrieveIdMapping(martRequest);
        verify(idMapUtil, times(1)).getIdMappers(anyList());
    }

    @Test
    public void retrievePrivateIdsForAPICenter() {
        IdMapUtil idMapUtil = mock(IdMapUtil.class);
        MartDataPipeline martDataPipeline = new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil, idMapUtil, entitlementCacheService);

        String id = "*********";
        JSONObject jsonObject = new JSONObject()
                .put("SecId", id)
                .put("SecurityType", "FO")
                .put("Status", "254");
        List<String> idList = new ArrayList<>();
        idList.add(id);
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper(id, jsonObject));
        when(idMapUtil.getIdMappers(idList)).thenReturn(idMappers);
        MartRequest martRequest = MartRequest.builder()
                .idType(null)
                .idMappers(null)
                .ids(idList)
                .productId("APICenter")
                .build();
        martDataPipeline.retrieveIdMapping(martRequest);
        verify(idMapUtil, times(1)).getIdMappers(anyList());
        assertTrue(martRequest.getIdMappers().isEmpty());
    }

    @Test
    public void retrieveSecuritiesWithSecurityTypeMultiUniverse() {
        prepareSecurityTypeMultiUniverseMaps();
        MartRequest martRequest = MartRequest.builder().dps(List.of("DPS1", "DPS2", "DPS3", "DPS4")).ids(List.of("F0000000FO", "F0000000FC", "F0000000AG", "F0000000VA")).build();
        prepareSecurityTypeMultiUniverseMartResponses();
        IdMapper idMapper1 = new NonEmptyIdMapper("F0000000FO", "{\"SecId\":\"F0000000FO\",\"Id\":\"F0000000FO\",\"SecurityType\":\"FO\"}");
        IdMapper idMapper2 = new NonEmptyIdMapper("F0000000FC", "{\"SecId\":\"F0000000FC\",\"Id\":\"F0000000FC\",\"SecurityType\":\"FC\"}");
        IdMapper idMapper3 = new NonEmptyIdMapper("F0000000AG", "{\"SecId\":\"F0000000AG\",\"Id\":\"F0000000AG\",\"SecurityType\":\"AG\"}");
        IdMapper idMapper4 = new NonEmptyIdMapper("F0000000VA", "{\"SecId\":\"F0000000VA\",\"Id\":\"F0000000VA\",\"SecurityType\":\"VA\"}");
        martRequest.setIdMappers(List.of(idMapper1, idMapper2, idMapper3, idMapper4));
        Flux<Result> response = martDataPipeline.execute(martRequest);

        StepVerifier.create(response)
                .expectSubscription()
                .consumeNextWith(r ->
                        assertFalse(r.getValues().containsKey("DPS2AG"))
                )
                .expectNextCount(11)
                .verifyComplete();
    }


    @Test
    public void retrieveSecuritiesWithPrivateModelMultiUniverse() {
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        List<MultiUniverse> dp1UniverseList = new ArrayList<>();
        MultiUniverse dp1Universe1 = MultiUniverse.builder().attribute("PrivateModel").type("true").srcid("DPS1LH").build();
        dp1UniverseList.add(dp1Universe1);
        DataPoint dataPoint1 = DataPoint.builder().id("DPS1").nid("DPS1").src("RDB").name("datapoint_name").multiUniverseList(dp1UniverseList).build();
        dataPointMap.put("DPS1", dataPoint1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, String> map1 = Map.of("DPS1", "DPS1");
        Map<String, String> map2 = Map.of("DPS1LH", "DPS1LH");

        when(martRequestDataAssemblerService.executeDataRetrieval(any()
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000FC", map1, Instant.now()),
                new CurrentResult("F0000000FO", map2, Instant.now())
        ));

        MartRequest martRequest = MartRequest.builder().dps(List.of("DPS1")).ids(List.of("F0000000FO", "F0000000FC")).build();
        IdMapper idMapper1 = new NonEmptyIdMapper("F0000000FO", "{\"SecId\":\"F0000000FO\",\"Id\":\"F0000000FO\",\"SecurityType\":\"FC\", \"IsPrivateModel\":\"true\"}");
        IdMapper idMapper2 = new NonEmptyIdMapper("F0000000FC", "{\"SecId\":\"F0000000FC\",\"Id\":\"F0000000FC\",\"SecurityType\":\"FC\"}");
        martRequest.setIdMappers(List.of(idMapper1, idMapper2));
        Flux<Result> response = martDataPipeline.execute(martRequest);
        StepVerifier.create(response)
                .expectSubscription()
                .consumeNextWith(r -> {
                    assertTrue(r.getValues().containsKey("DPS1"));
                })
                .assertNext(r -> {
                    assertTrue(r.getValues().containsKey("DPS1LH"));
                })
                .verifyComplete();
    }

    @Test
    public void retrieveSecuritiesWithAlias() {
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        DataPoint dataPoint1 = DataPoint.builder().id("100").nid("100").src("RDB").name("datapoint_name").build();
        DataPoint dataPoint2 = DataPoint.builder().id("102").nid("102").src("RDB").name("datapoint_name").build();
        dataPointMap.put("100", dataPoint1);
        dataPointMap.put("102", dataPoint2);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, String> currentValues = new HashMap<>();
        currentValues.put("100", "5.89");
        currentValues.put("102", "51.00");
        Result result = new CurrentResult("F0000000FC", currentValues);
        when(martRequestDataAssemblerService.executeDataRetrieval(any())).thenReturn(Flux.just(result));

        Map<String, Set<String>> aliasMap = Collections.singletonMap("100", Set.of("alias1"));
        MartRequest martRequest = MartRequest.builder()
                .dps(List.of("100", "102"))
                .ids(List.of("F0000000FC"))
                .aliasMap(aliasMap)
                .build();
        IdMapper idMapper = new NonEmptyIdMapper("F0000000FC", "{\"SecId\":\"F0000000FC\",\"Id\":\"F0000000FC\",\"SecurityType\":\"FC\"}");
        martRequest.setIdMappers(List.of(idMapper));

        Flux<Result> response = martDataPipeline.execute(martRequest);

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> assertTrue(r.getValues().containsKey("alias1") && r.getValues().containsKey("102")))
                .verifyComplete();
    }

    private void prepareSecurityTypeMultiUniverseMartResponses() {
        Map<String, String> map1 = new HashMap<>();
        map1.put("DPS1FC", "DPS1FC");
        Map<String, String> map2 = new HashMap<>();
        map2.put("DPS2AG", "DPS2AG");
        map2.put("DPS4AG", "DPS4AG");
        Map<String, String> map3 = new HashMap<>();
        map3.put("DPS1FO", "DPS1FO");
        Map<String, String> map4 = new HashMap<>();
        map4.put("DPS1", "DPS1");
        Map<String, String> map5 = new HashMap<>();
        map5.put("DPS3", "DPS3");
        Map<String, String> map6 = new HashMap<>();
        map6.put("DPS2", "DPS2");
        map6.put("DPS4", "DPS4");

        when(martRequestDataAssemblerService.executeDataRetrieval(any()
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000FC", map1, Instant.now())
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000AG", map2, Instant.now())
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000FO", map3, Instant.now())
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000AG", map4, Instant.now()),
                new CurrentResult("F0000000VA", map4, Instant.now())
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000AG", map5, Instant.now()),
                new CurrentResult("F0000000VA", map5, Instant.now()),
                new CurrentResult("F0000000FO", map5, Instant.now()),
                new CurrentResult("F0000000FC", map5, Instant.now())
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000FO", map6, Instant.now()),
                new CurrentResult("F0000000VA", map6, Instant.now()),
                new CurrentResult("F0000000FC", map6, Instant.now())
        ));
    }

    private void prepareSecurityTypeMultiUniverseMaps() {
        Map<String, DataPoint> dataPointMap = new HashMap<>();

        List<MultiUniverse> dp1UniverseList = new ArrayList<>();
        MultiUniverse dp1Universe1 = MultiUniverse.builder().attribute("SecurityType").type("FO").srcid("DPS1FO").build();
        MultiUniverse dp1Universe2 = MultiUniverse.builder().attribute("SecurityType").type("FC").srcid("DPS1FC").build();
        MultiUniverse dp1Universe3 = MultiUniverse.builder().attribute("SecurityType").type("ST").srcid("DPS1ST").build();
        dp1UniverseList.add(dp1Universe1);
        dp1UniverseList.add(dp1Universe2);
        dp1UniverseList.add(dp1Universe3);
        DataPoint dataPoint1 = DataPoint.builder().id("DPS1").nid("DPS1").src("RDB").name("datapoint_name").currentRdb(RdbDataPoint.builder().id("DPS1").nid("DPS1").build()).multiUniverseList(dp1UniverseList).build();
        dataPointMap.put("DPS1", dataPoint1);

        List<MultiUniverse> dp2UniverseList = new ArrayList<>();
        MultiUniverse dp2Universe1 = MultiUniverse.builder().attribute("SecurityType").type("AG").srcid("DPS2AG").build();
        dp2UniverseList.add(dp2Universe1);
        DataPoint dataPoint2 = DataPoint.builder().id("DPS2").nid("DPS2").src("RDB").name("datapoint_name").currentRdb(RdbDataPoint.builder().id("DPS2").nid("DPS2").build()).multiUniverseList(dp2UniverseList).build();
        dataPointMap.put("DPS2", dataPoint2);

        List<MultiUniverse> dp4UniverseList = new ArrayList<>();
        MultiUniverse dp4Universe1 = MultiUniverse.builder().attribute("SecurityType").type("AG").srcid("DPS4AG").build();
        dp4UniverseList.add(dp4Universe1);
        DataPoint dataPoint4 = DataPoint.builder().id("DPS4").nid("DPS4").src("RDB").name("datapoint_name").currentRdb(RdbDataPoint.builder().id("DPS4").nid("DPS4").build()).multiUniverseList(dp4UniverseList).build();
        dataPointMap.put("DPS4", dataPoint4);

        DataPoint dataPoint3 = DataPoint.builder().id("DPS3").nid("DPS3").src("RDB").name("datapoint_name").currentRdb(RdbDataPoint.builder().id("DPS3").nid("DPS3").build()).build();
        dataPointMap.put("DPS3", dataPoint3);

        DataPoint dataPoint5 = DataPoint.builder().id("DPS5").nid("DPS5").src("RDB").name("datapoint_name").mappingSrc(dataPoint1).currentRdb(RdbDataPoint.builder().id("DPS5").nid("DPS5").build()).build();
        dataPointMap.put("DPS5", dataPoint5);

        DataPoint dataPoint1FO = DataPoint.builder().id("DPS1FO").nid("DPS1FO").src("RDB").name("datapoint_name").mappingSrc(dataPoint1).currentRdb(RdbDataPoint.builder().id("DPS1FO").nid("DPS1FO").build()).build();
        dataPointMap.put("DPS1FO", dataPoint1FO);

        DataPoint dataPoint1FC = DataPoint.builder().id("DPS1FC").nid("DPS1FC").src("RDB").name("datapoint_name").mappingSrc(dataPoint1).currentRdb(RdbDataPoint.builder().id("DPS1FC").nid("DPS1FC").build()).build();
        dataPointMap.put("DPS1FC", dataPoint1FC);

        DataPoint dataPoint2AG = DataPoint.builder().id("DPS2AG").nid("DPS2AG").src("RDB").name("datapoint_name").mappingSrc(dataPoint1).currentRdb(RdbDataPoint.builder().id("DPS2AG").nid("DPS2AG").build()).build();
        dataPointMap.put("DPS2AG", dataPoint2AG);

        DataPoint dataPoint4AG = DataPoint.builder().id("DPS4AG").nid("DPS4AG").src("RDB").name("datapoint_name").mappingSrc(dataPoint1).currentRdb(RdbDataPoint.builder().id("DPS4AG").nid("DPS4AG").build()).build();
        dataPointMap.put("DPS4AG", dataPoint4AG);

        DataPoint dataPoint1ST = DataPoint.builder().id("DPS1ST").nid("DPS1ST").src("Equity").name("datapoint_name").mappingSrc(dataPoint1).currentRdb(RdbDataPoint.builder().id("DPS1ST").nid("DPS1ST").build()).build();
        dataPointMap.put("DPS1ST", dataPoint1ST);

        DataPointRepository.setDataPointMap(dataPointMap);
    }

    @Test
    public void retrieveSecuritiesWithMappingSrcWithUniverseId() {
        prepareSecurityTypeMultiUniverseMaps();
        MartRequest martRequest = MartRequest.builder().dps(List.of("DPS3", "DPS5")).ids(List.of("F0000000FO")).productId("APICenter").build();
        IdMapper idMapper1 = new NonEmptyIdMapper("F0000000FO", "{\"SecId\":\"F0000000FO\",\"Id\":\"F0000000FO\",\"SecurityType\":\"FO\"}");
        martRequest.setIdMappers(List.of(idMapper1));

        Map<String, List<V>> dps3Values = new HashMap<>();
        dps3Values.put("DPS3", Collections.singletonList(new V("2023-01-01", "500.00")));
        Map<String, List<V>> dps1FOValues = new HashMap<>();
        dps1FOValues.put("DPS1FO", Collections.singletonList(new V("2024-06-06", "100.00")));

        when(martRequestDataAssemblerService.executeDataRetrieval(any())).thenReturn(Flux.just(
                new TimeSeriesResult("F0000000FO", dps3Values), new TimeSeriesResult("F0000000FO", dps1FOValues)));

        Flux<Result> result = martDataPipeline.execute(martRequest);
        List<Map<String, Map>> actual = new ArrayList<>();
        StepVerifier.create(result)
                .recordWith(ArrayList::new)
                .thenConsumeWhile(x -> true)
                .consumeRecordedWith(elements -> {
                    elements.forEach(e -> {
                        Map<String, Map> map = new HashMap<>();
                        map.put(e.getId(), e.getValues());
                        actual.add(map);
                    });
                })
                .verifyComplete();
        assertEquals(2, actual.size());
        int count = 0;
        for (Map<String, Map> a : actual) {
            if ((a.get("F0000000FO").get("DPS3") != null && "[V(i=2023-01-01, v=500.00)]".equals(a.get("F0000000FO").get("DPS3").toString())) ||
                    (a.get("F0000000FO").get("DPS5") != null && "[V(i=2024-06-06, v=100.00)]".equals(a.get("F0000000FO").get("DPS5").toString()))) {
                count++;
            }
        }
        assertEquals(2, count);
    }

    @Test
    public void retrieveSecuritiesWithEquityMappingSrcWithUniverseId() {
        prepareSecurityTypeMultiUniverseMaps();
        MartRequest martRequest = MartRequest.builder().dps(List.of("DPS3", "DPS5")).ids(List.of("OP000000ST")).productId("APICenter").build();
        IdMapper idMapper1 = new NonEmptyIdMapper("OP000000ST", "{\"SecId\":\"OP000000ST\",\"Id\":\"OP000000ST\",\"SecurityType\":\"ST\"}");
        martRequest.setIdMappers(List.of(idMapper1));

        Map<String, List<V>> dps3Values = new HashMap<>();
        dps3Values.put("DPS3", Collections.singletonList(new V("2023-01-01", "500.00")));
        Map<String, List<V>> dps1FOValues = new HashMap<>();
        dps1FOValues.put("DPS1ST", Collections.singletonList(new V("2024-06-06", "100.00")));

        when(martRequestDataAssemblerService.executeDataRetrieval(any())).thenReturn(Flux.just(
                new TimeSeriesResult("OP000000ST", dps3Values), new TimeSeriesResult("OP000000ST", dps1FOValues)));

        Flux<Result> result = martDataPipeline.execute(martRequest);
        List<Map<String, Map>> actual = new ArrayList<>();
        StepVerifier.create(result)
                .recordWith(ArrayList::new)
                .thenConsumeWhile(x -> true)
                .consumeRecordedWith(elements -> {
                    elements.forEach(e -> {
                        Map<String, Map> map = new HashMap<>();
                        map.put(e.getId(), e.getValues());
                        actual.add(map);
                    });
                })
                .verifyComplete();
        assertEquals(2, actual.size());
        int count = 0;
        for (Map<String, Map> a : actual) {
            if ((a.get("OP000000ST").get("DPS3") != null && "[V(i=2023-01-01, v=500.00)]".equals(a.get("OP000000ST").get("DPS3").toString())) ||
                    (a.get("OP000000ST").get("DPS5") != null && "[V(i=2024-06-06, v=100.00)]".equals(a.get("OP000000ST").get("DPS5").toString()))) {
                count++;
            }
        }
        assertEquals(2, count);
    }


    @Test
    public void retrieveSecuritiesWithMappingSrcWithDoubleNesting() {
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        DataPoint dataPoint1 = DataPoint.builder().id("DPS1").nid("DPS1").src("RDB").name("datapoint_name").build();
        dataPointMap.put("DPS1", dataPoint1);
        DataPoint dataPoint2 = DataPoint.builder().id("DPS2").nid("DPS2").src("RDB").name("datapoint_name").mappingSrc(dataPoint1).build();
        dataPointMap.put("DPS2", dataPoint2);
        DataPoint dataPoint3 = DataPoint.builder().id("DPS3").nid("DPS3").src("RDB").name("datapoint_name").mappingSrc(dataPoint2).build();
        dataPointMap.put("DPS3", dataPoint3);

        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, String> dps1Values = new HashMap<>();
        dps1Values.put("DPS1", "DPS1Values");

        when(martRequestDataAssemblerService.executeDataRetrieval(any()
        )).thenReturn(Flux.just(
                new CurrentResult("F0000000FO", dps1Values, Instant.now())));

        IdMapper idMapper = new NonEmptyIdMapper("F0000000FC", "{\"SecId\":\"F0000000FC\",\"Id\":\"F0000000FC\",\"SecurityType\":\"FC\"}");
        MartRequest martRequest = MartRequest.builder().dps(List.of("DPS3")).ids(List.of("F0000000FC")).productId("APICenter").idMappers(List.of(idMapper)).build();
        Flux<Result> response = martDataPipeline.execute(martRequest);

        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> assertTrue(r.getValues().containsKey("DPS3") && r.getValues().get("DPS3").equals("DPS1Values")))
                .verifyComplete();
    }

    private void mockDataPointRepositoryForBackfill(RdbDataPoint caCurrent, RdbDataPoint caTimeSeries) {
        RdbDataPoint current = RdbDataPoint.builder().id("AA03K").nid("AA03K").src("RDB").column("BasicMaterials").name("BasicMaterials").groupName("StockSummary").idLevel("SecId").storeProcedure("sp1").rdbCacheFlag("2").build();
        RdbDataPoint timeSeries = RdbDataPoint.builder().id("AA03K").nid("AA03K").src("RDB").column("BasicMaterials").name("BasicMaterials").groupName("TSGlobalStockSectorBreakdownLong").idLevel("MasterPortfolioId").dateColumn("AADATE").storeProcedure("sp2").build();

        MultiUniverse mu1 = MultiUniverse.builder().srcid("AA03KCA").attribute("SecurityType").type("CA").build();
        MultiUniverse mu2 = MultiUniverse.builder().srcid("AA03KST").attribute("SecurityType").type("ST").build();
        DataPoint dp = DataPoint.builder().id("AA03K").nid("AA03K").src("RDB").name("BasicMaterials").groupName("Group123").multiUniverseList(List.of(mu1, mu2)).currentRdb(current).tsRdb(timeSeries).build();

        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("AA03K", dp);

        DataPoint dpCa = DataPoint.builder().id("AA03KCA").nid("AA03KCA").src("RDB").name("BasicMaterials").groupName("Group456").currentRdb(caCurrent).tsRdb(caTimeSeries).build();
        dataPointMap.put("AA03KCA", dpCa);

        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("BasicMaterials", Pair.of("AA03K", ""));

        rdbGroupColDpsMap.put("StockSummary", colToDpsMap);
        rdbGroupColDpsMap.put("TSGlobalStockSectorBreakdownLong", colToDpsMap);

        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    @Test
    public void testMultiUniverseBackfill() {

        RdbDataPoint caCurrent = RdbDataPoint.builder().id("AA03KCA").nid("AA03KCA").src("RDB").column("BasicMaterials").name("BasicMaterials").groupName("CDxv_PortfolioSummary_ICATAVG").idLevel("SecId").storeProcedure("sp1").rdbCacheFlag("2").build();
        RdbDataPoint caTimeSeries = RdbDataPoint.builder().id("AA03KCA").nid("AA03KCA").src("RDB").column("BasicMaterials").name("BasicMaterials").groupName("TSGlobalStockSectorBreakdownCA").idLevel("MasterPortfolioId").dateColumn("AADATE").storeProcedure("sp2").build();

        // TS no backfill
        testRetrieveSecuritiesWithMultiUniverseBackfill(caCurrent, caTimeSeries, "2020-10-01", "2021-10-01", "AA03KCA");

        // TS backfill
        testRetrieveSecuritiesWithMultiUniverseBackfill(caCurrent, null, "2020-10-01", "2021-10-01", "AA03K");

        // Current no backfill
        testRetrieveSecuritiesWithMultiUniverseBackfill(caCurrent, caTimeSeries, null, null, "AA03KCA");

        // Current backfill
        testRetrieveSecuritiesWithMultiUniverseBackfill(null, caTimeSeries, null, null, "AA03K");
    }

    public void testRetrieveSecuritiesWithMultiUniverseBackfill(RdbDataPoint caCurrent, RdbDataPoint caTimeSeries, String startDate, String endDate, String expectedDp) {

        MartRequestDataAssemblerService martRequestDataAssemblerService = mock(MartRequestDataAssemblerService.class);
        RequireIdUtil reqIdUtil = mock(RequireIdUtil.class);
        IdMapUtil idMapUtil = mock(IdMapUtil.class);

        MartDataPipeline mdPipeline = new MartDataPipeline(martRequestDataAssemblerService, reqIdUtil, idMapUtil, entitlementCacheService);

        MartRequest martRequest = MartRequest.builder().dps(List.of("AA03K")).ids(List.of("$FOCA$5S$$")).startDate(startDate).endDate(endDate).build();
        prepareSecurityTypeMultiUniverseMartResponses();
        IdMapper idMapper1 = new NonEmptyIdMapper("$FOCA$5S$$", "{\"SecId\":\"$FOCA$5S$$\",\"MasterPortfolioId\":\"516208\",\"SecurityType\":\"CA\"}");
        martRequest.setIdMappers(List.of(idMapper1));
        mockDataPointRepositoryForBackfill(caCurrent, caTimeSeries);
        mdPipeline.execute(martRequest);

        ArgumentCaptor<MartRequest> argumentCaptor2 = ArgumentCaptor.forClass(MartRequest.class);
        verify(martRequestDataAssemblerService, times(1)).executeDataRetrieval(argumentCaptor2.capture());
        assertEquals(1, argumentCaptor2.getValue().getDps().size());
        assertTrue(argumentCaptor2.getValue().getDps().contains(expectedDp));
    }
}
