package com.morningstar.martgateway.domains.calc;


import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import org.apache.commons.io.FileUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import reactor.test.StepVerifier;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CalculationServiceTest {

    private CalculationService calculationService;
    @Mock
    private CalcAPICaller calcAPICaller;
    @Mock
    CurrentDataRetrievalService currentDataRetrievalService;

    private static MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic;
    private static MockedStatic<CalcAPICaller> calcAPICallerMockedStatic;

    @Before
    public void setUp() {
        dataPointRepositoryMockedStatic = Mockito.mockStatic(DataPointRepository.class, Mockito.RETURNS_DEEP_STUBS);
        calcAPICallerMockedStatic = Mockito.mockStatic(CalcAPICaller.class, Mockito.RETURNS_DEEP_STUBS);
        calculationService = new CalculationService(currentDataRetrievalService, calcAPICaller);
    }

    @After
    public void afterEachTest(){
        dataPointRepositoryMockedStatic.close();
        calcAPICallerMockedStatic.close();
    }

    @Test
    public void shouldConvertCurrencySeriesNaNTest() {
        String secId = "F000010TIH";
        Map<String, String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD", "US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        List<String> requestCalList = Arrays.asList("3", "5");
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        List<String> requestCalcDps = Arrays.asList("1", "2");
        CalcRequest request = CalcRequest.builder().adjustment("1").currency("CU$$$$$USA")
                .preCurrency("CU$$$$$USA").calcDps(requestCalcDps).startDate("2020-11-01").endDate("2020-11-03").idList(requestCalList)
                .build();
        Map<String, String> values = new HashMap<>();
        values.put("25200", "US Dollar");
        values.put("23083", "test name");

        String dsDataPointXml = getMockDataPoint("testData/status_meta.txt");

        when(calcAPICaller.post(any(), any())).thenReturn(Mono.just(dsDataPointXml));
        when(DataPointRepository.getByNid(anyString())).thenReturn(dataPoint2);
        Flux<Result> result = calculationService.retrieveCalc(request);
        result.subscribe();
        assertNotNull(result);
    }

    @Test
    public void shouldConvertCurrencySeriesTwoTest() {
        String secId = "F000010TIH";
        Map<String, String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD", "US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        List<String> requestCalList = Arrays.asList("3", "5");
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dataPoint3 = DataPoint.builder().id("81281").nid("100003").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dataPoint5 = DataPoint.builder().id("81285").nid("100005").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();

        List<String> requestCalcDps = Arrays.asList("81280", "81281","81285");
        CalcRequest request = CalcRequest.builder().adjustment("1").currency("CU$$$$$USA")
                .preCurrency("CU$$$$$USA").calcDps(requestCalcDps).startDate("2020-11-01").endDate("2020-11-03").idList(requestCalList)
                .build();
        Map<String, String> values = new HashMap<>();
        values.put("25200", "US Dollar");
        values.put("23083", "test name");

        String dsDataPointXml = getMockDataPoint("testData/status_meta.txt");

        when(calcAPICaller.post(any(), any())).thenReturn(Mono.just(dsDataPointXml));
        ReflectionTestUtils.setField(calcAPICaller, "readTimeout", 50);
        when(DataPointRepository.getByNid(anyString())).thenReturn(dataPoint2).thenReturn(dataPoint3).thenReturn(dataPoint5);
        Flux<Result> result = calculationService.retrieveCalc(request);
        List<String> idList = new ArrayList<>();
        result.subscribe(res -> idList.add(res.getId()));
        result.subscribe();
        assertNotNull(result);
    }

    @Test
    public void shouldPostBodyBuilderTest() {
        String startDate = "";
        String endDate = "";
        boolean result = calculationService.verifyDate(startDate,endDate);
        assertEquals(false,result);
    }
    @Test
    public void shouldPostBodyBuilderHaveDateTest() {
        String startDate = "2020-09-08";
        String endDate = "2020-09-30";
        boolean result = calculationService.verifyDate(startDate,endDate);
        assertEquals(true,result);
    }
    @Test
    public void shouldGetDateByFreqTest() {
        String startDate = "2020-09-09";
        String freq = "";
        String freq1 = "1";
        String freq2 = "2";
        String freq3 = "3";
        String freq4 = "4";
        String result = calculationService.getDateByFreq(startDate,freq);
        String result1 = calculationService.getDateByFreq(startDate,freq1);
        String result2 = calculationService.getDateByFreq(startDate,freq2);
        String result3 = calculationService.getDateByFreq(startDate,freq3);
        String result4 = calculationService.getDateByFreq(startDate,freq4);
        assertEquals("2020-09-09",result);
        assertEquals("2020-09-06",result1);
        assertEquals("2020-09-01",result2);
        assertEquals("2020-07-01",result3);
        assertEquals("2020-01-01",result4);
    }

    @Test
    public void testResetMapping() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        String secId = "F000010TIH";
        Map<String, String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD", "US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        List<String> requestCalList = Arrays.asList("3", "5");
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dataPoint3 = DataPoint.builder().id("81281").nid("100003").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dataPoint5 = DataPoint.builder().id("81285").nid("100005").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();

        List<String> requestCalcDps = Arrays.asList("81280", "81281","81285");
        CalcRequest request = CalcRequest.builder().adjustment("1").currency("CU$$$$$USA")
                .preCurrency("CU$$$$$USA").calcDps(requestCalcDps).startDate("2020-11-01").endDate("2020-11-03").idList(requestCalList)
                .build();
        Map<String, String> values = new HashMap<>();
        values.put("25200", "US Dollar");
        values.put("23083", "test name");
        calculationService.buildQueryWithIdList(request,requestCalcDps,dataPoint5,new ArrayList<>());
    }

    @Test
    public void testResetMappingTwo() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        String secId = "F000010TIH";
        Map<String, String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD", "US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("TSAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        String value = "3";
        List<String> requestCalList = Arrays.asList("3", "5");
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("12").offset("2").freq("2")
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("TSAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dataPoint3 = DataPoint.builder().id("81281").nid("100003").calculation(cal).src("TSAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dataPoint5 = DataPoint.builder().id("81285").nid("100005").calculation(cal).src("TSAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();

        List<String> requestCalcDps = Arrays.asList("81280", "81281","81285");
        CalcRequest request = CalcRequest.builder().adjustment("1").currency("CU$$$$$USA")
                .preCurrency("CU$$$$$USA").calcDps(requestCalcDps).startDate("2020-11-01").endDate("2020-11-03").idList(requestCalList)
                .build();
        Map<String, String> values = new HashMap<>();
        values.put("25200", "US Dollar");
        values.put("23083", "test name");
        calculationService.buildQueryWithIdList(request,requestCalcDps,dataPoint5, new ArrayList<>());
    }

    private String getMockDataPoint(String fileName) {
        StringBuilder sb = new StringBuilder();
        try {
            File file = FileUtils.toFile(ResourceUtils.getURL("classpath:" + fileName));
            BufferedReader bw = new BufferedReader(new FileReader(file, StandardCharsets.UTF_8));
            String line = "";
            while ((line = bw.readLine()) != null) {
                sb.append(line);
            }
            bw.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    @Test
    public void testRetrieveCalcEmptyDps(){
        CalcRequest entity = CalcRequest.builder().calcDps(List.of()).build();
        Flux<Result> resultFlux = calculationService.retrieveCalc(entity);
        StepVerifier.create(resultFlux)
                .verifyComplete();
    }
}
