package com.morningstar.martgateway.domains.calclib;

import static com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper.CURCHANGE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.spy;

import com.morningstar.dataac.martgateway.core.calculationlib.CalculationLibConversionService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.MultiUniverse;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.applications.rdb.RdbGateway;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.calculationlib.entity.CalcMetaData;
import com.morningstar.martgateway.domains.current.CurrentManager;
import com.morningstar.martgateway.domains.rdb.service.RdbTsService;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.service.TScacheService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
public class CalculationLibServiceTest {
    private CalculationLibService calculationLibService;
    @Mock
    private CurrentManager currentManager;
    @Mock
    private TScacheService tScacheService;
    @Mock
    private CurrencyConversionHelper currencyConversionHelper;
    private CalculationLibConversionService calculationLibConversionService;
    @Mock
    private RdbGateway rdbGateway;

    @Mock
    RdbTsService rdbTsService;
    private MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic;

    @BeforeEach
    void setUp() {
        dataPointRepositoryMockedStatic = mockStatic(DataPointRepository.class, Mockito.RETURNS_DEEP_STUBS);
        calculationLibConversionService = spy(new CalculationLibConversionService(currencyConversionHelper));
        calculationLibService = new CalculationLibService(currentManager, tScacheService, calculationLibConversionService, rdbGateway, rdbTsService);
    }

    @AfterEach
    void afterEachTest(){
        dataPointRepositoryMockedStatic.close();
    }

    @Test
    void shouldConvertCurrencySeriesNaNTest() {
        Map<String, String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD", "US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build(),
                DataPoint.builder().nid("25200").name("Currency").src("TSAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dataPoint = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        List<String> requestCalList = Arrays.asList("3", "5");
        Calculation cal = Calculation.builder()
                .dp(dataPoint).trailingPeriod("-1").offset("2").freq("2").cur(dataPoint).endDate(dataPoint)
                .build();
        Calculation cal2 = Calculation.builder()
                .dp(dataPoint).trailingPeriod("-1").offset("2").freq("2").cur(dataPoint).endDate(dataPoint)
                .build();
        DataPoint dataPoint2 = DataPoint.builder().id("81280").nid("100002").calculation(cal).src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(false).subDataPoints(dps).build();
        DataPoint dataPoint3 = DataPoint.builder().id("81281").nid("100002").calculation(cal2).src("TSAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        List<String> requestCalcDps = Arrays.asList("25200", "23083");
        CalcRequest request = CalcRequest.builder().adjustment("1").currency("CU$$$$$USA")
                .preCurrency("CU$$$$$USA").calcDps(requestCalcDps).startDate("2020-11-01").endDate("2020-11-03").idList(requestCalList).idMappers(new ArrayList<>())
                .build();
        Map<String, String> values = new HashMap<>();
        /*List< V > dpValues = Arrays.asList(new V("44701","44701"));
        List< V > dpValuesTwo = Arrays.asList(new V("44708","44709"));*/
        values.put("25200", "0.54");
        values.put("23083", null);
        Mockito.when(tScacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.just(new CurrentResult("0P00009T67", values, Instant.now())));
        Mockito.when(currentManager.retrieveCurrentResult(anyList(),anyList(),anyBoolean(), any())).thenReturn(Flux.just(new CurrentResult("0P00009T68", values,Instant.now()),new CurrentResult("0P00009T68", values,Instant.now())));
        Mockito.when(currentManager.retrieveGroupResult(anyList(),anySet(),anyList(),anyBoolean(), any())).thenReturn(Flux.just(new CurrentResult("0P00009T69", values,Instant.now())));
        Mockito.when(rdbGateway.retrieve(any(), anyList(), anyList())).thenReturn(Flux.just(new CurrentResult("0P00009T70", values,Instant.now())));
        Mockito.when(rdbTsService.loadBatchedData(any(), any(), anyBoolean())).thenReturn(Flux.just(new CurrentResult("0P00009T70", values,Instant.now())));

        when(DataPointRepository.getByNid("25200")).thenReturn(dataPoint2);
        when(DataPointRepository.getByNid("23083")).thenReturn(dataPoint3);
        when(DataPointRepository.getByNid("81280")).thenReturn(dataPoint2);
        when(DataPointRepository.getByNid("5764")).thenReturn(dataPoint3);
        when(DataPointRepository.getByNid("3010")).thenReturn(dataPoint3);
        Flux<Result> result = calculationLibService.retrieveCalcLib(request);
        result.subscribe(System.out::println);
        assertNotNull(result);
    }
    //moved tests to CalculationLibConversionServiceTest.java
    @Test
    void rdbCalcLibCurrentTest() {
        Map<String, String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD", "US Dollar");

        DataPoint calclDp1 = DataPoint.builder().nid("100002").name("Trailing1DayReturn").src("DSAPI").build();
        DataPoint calclDp2 = DataPoint.builder().nid("100003").name("Trailing1WeekReturn").src("DSAPI").build();

        DataPoint endDateDp = DataPoint.builder().nid("4706").name("endDate").src("RDB").build();
        Calculation calc1 = Calculation.builder().dp(calclDp1).endDate(endDateDp).build();
        Calculation calc2 = Calculation.builder().dp(calclDp2).endDate(endDateDp).build();

        DataPoint dp6498 = DataPoint.builder().nid("6498").name("Trailing1DayReturn").src("RDB").calculation(calc1).build();
        DataPoint dp6499 = DataPoint.builder().nid("6499").name("Trailing1DayReturn").src("RDB").calculation(calc2).build();
        DataPoint baseCurDp = DataPoint.builder().nid("3010").name("Base Currency Id").src("CDAPI").build();

        List<String> idList = Arrays.asList("0P0000NCP7","0P0000PSNG");
        List<String> requestDps = Arrays.asList("6498", "6499");

        CalcRequest request = CalcRequest.builder().currency("CU$$$$$USA")
                .preCurrency("CU$$$$$USA").calcDps(requestDps).idList(idList)
                .build();

        Map<String, String> values1 = new HashMap<>();
        values1.put("4706", "2015-08-18");
        values1.put("6498", "1.11");
        values1.put("6499", "2.22");

        Map<String, String> values3 = new HashMap<>();
        values3.put("4706", "2015-09-22");
        values3.put("6498", "3.33");
        values3.put("6499", "4.44");

        Map<String, String> values2 = new HashMap<>();
        values2.put("3010", "MXN");

        Mockito.when(tScacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.empty());
        Mockito.when(currentManager.retrieveCurrentResult(anyList(),anyList(),anyBoolean(), any())).thenReturn(Flux.just(new CurrentResult("0P0000NCP7", values2,Instant.now()),new CurrentResult("0P0000PSNG", values2,Instant.now())));
        Mockito.when(currentManager.retrieveGroupResult(anyList(),anySet(),anyList(),anyBoolean(), any())).thenReturn(Flux.empty());
        Mockito.when(rdbGateway.retrieve(any(), anyList(), anyList())).thenReturn(Flux.just(new CurrentResult("0P0000NCP7", values1,Instant.now()),new CurrentResult("0P0000PSNG", values3,Instant.now())));
        Mockito.when(rdbTsService.loadBatchedData(any(), any(), anyBoolean())).thenReturn(Flux.empty());

        when(DataPointRepository.getByNid("6498")).thenReturn(dp6498);
        when(DataPointRepository.getByNid("6499")).thenReturn(dp6499);
        when(DataPointRepository.getByNid("3010")).thenReturn(baseCurDp);
        when(DataPointRepository.getByNid("4706")).thenReturn(endDateDp);

        when(currencyConversionHelper.convertCDExchangeRate("0P0000NCP7", dp6498, "1.11","2015-08-18", "MXN", "CU$$$$$USA", null)).thenReturn("9.11");
        when(currencyConversionHelper.convertCDExchangeRate("0P0000NCP7", dp6499, "2.22","2015-08-18", "MXN", "CU$$$$$USA", null)).thenReturn("9.22");

        when(currencyConversionHelper.convertCDExchangeRate("0P0000PSNG", dp6498, "3.33","2015-09-22", "MXN", "CU$$$$$USA", null)).thenReturn("9.33");
        when(currencyConversionHelper.convertCDExchangeRate("0P0000PSNG", dp6499, "4.44","2015-09-22", "MXN", "CU$$$$$USA", null)).thenReturn("9.44");

        Flux<Result> result = calculationLibService.retrieveCalcLib(request);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P0000NCP7", d.getId());
                    assertEquals("9.11", d.getValues().get("6498"));
                    assertEquals("9.22", d.getValues().get("6499"));
                })
                .assertNext(d ->{
                    assertEquals("0P0000PSNG", d.getId());
                    assertEquals("9.33", d.getValues().get("6498"));
                    assertEquals("9.44", d.getValues().get("6499"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    void rdbCalcLibTsTest() {
        Map<String, String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD", "US Dollar");

        DataPoint calcDp = DataPoint.builder().id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(false).build();

        DataPoint endDateDp = DataPoint.builder().nid("4706").name("endDate").src("RDB").build();
        Calculation calc = Calculation.builder().dp(calcDp).endDate(endDateDp).cur(calcDp).build();

        DataPoint dp6498 = DataPoint.builder().nid("6498").name("Trailing1DayReturn").src("RDB").calculation(calc).build();
        DataPoint dp6499 = DataPoint.builder().nid("6499").name("Trailing1DayReturn").src("RDB").calculation(calc).build();
        DataPoint baseCurDp = DataPoint.builder().nid("3010").name("Base Currency Id").src("CDAPI").build();

        List<String> idList = Arrays.asList("0P0000NCP7","0P0000PSNG");
        List<String> requestDps = Arrays.asList("6498", "6499");

        List<IdMapper> idMappers = new ArrayList<>();
        CalcRequest request = CalcRequest.builder().currency("CU$$$$$USA")
                .preCurrency("CU$$$$$USA").calcDps(requestDps).idList(idList)
                .startDate("2020-07-20").endDate("2020-07-22")
                .idMappers(idMappers)
                .build();

        Map<String, String> values3 = new HashMap<>();
        values3.put("81280", "MXN");

        Map<String, List<V>> tsValues1 = new HashMap<>();
        tsValues1.put("6498",Arrays.asList(new V("2020-07-20","1.31"),new V("2020-07-21","2.76"),new V("2020-07-22","3.53")));
        tsValues1.put("6499",Arrays.asList(new V("2020-07-20","7.98"),new V("2020-07-21","3.18"),new V("2020-07-22","6.02")));

        Map<String, List<V>> tsValues2 = new HashMap<>();
        tsValues2.put("6498",Arrays.asList(new V("2020-07-20","8.10"),new V("2020-07-21","9.12"),new V("2020-07-22","5.74")));
        tsValues2.put("6499",Arrays.asList(new V("2020-07-20","4.33"),new V("2020-07-21","1.88"),new V("2020-07-22","7.07")));

        Mockito.when(tScacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.empty());
        Mockito.when(currentManager.retrieveCurrentResult(anyList(),anyList(),anyBoolean(), any())).thenReturn(Flux.empty());
        Mockito.when(currentManager.retrieveGroupResult(anyList(),anySet(),anyList(),anyBoolean(), any())).thenReturn(Flux.empty());
        Mockito.when(rdbGateway.retrieve(any(), anyList(), anyList())).thenReturn(Flux.empty());
        Mockito.when(rdbTsService.loadBatchedData(any(), any(), anyBoolean())).thenReturn(Flux.just(new TimeSeriesResult("0P0000NCP7",tsValues1),new TimeSeriesResult("0P0000PSNG",tsValues2)));

        when(DataPointRepository.getByNid("6498")).thenReturn(dp6498);
        when(DataPointRepository.getByNid("6499")).thenReturn(dp6499);
        when(DataPointRepository.getByNid("81280")).thenReturn(calcDp);
        when(DataPointRepository.getByNid("3010")).thenReturn(baseCurDp);

        calculationLibService.retrieveCalcLib(request);

        verify(rdbTsService,times(1)).loadBatchedData(any(), any(), anyBoolean());
    }

    @Test
    void rdbCalcLibCurrentPenceTradeTest() {
        DataPoint penceTradedDp = DataPoint.builder().nid("23485").name("PenceTraded").src("CDAPI").build();
        DataPoint ccyDp = DataPoint.builder().nid("3010").name("Currency").src("CDAPI").build();
        DataPoint reqDp1 = DataPoint.builder().nid("275033").name("Jasper Data Point").src("RDB").calculation(Calculation.builder()
                                                                                                                        .penceTraded(penceTradedDp)
                                                                                                                        .ccy(ccyDp)
                                                                                                                        .calSrc("CUSTCALC")
                                                                                                                        .build()).build();
        DataPoint reqDp2 = DataPoint.builder().nid("275031").name("Jasper Data Point 2").src("RDB").calculation(Calculation.builder()
                .penceTraded(penceTradedDp)
                .ccy(ccyDp)
                .calSrc("CUSTCALC")
                .build()).build();

        List<String> idList = Collections.singletonList("0P0000NCP7");
        List<String> requestDps = Arrays.asList("275033","275031");
        List<IdMapper> idMappers = new ArrayList<>();

        CalcRequest request = CalcRequest.builder().calcDps(requestDps).idList(idList).idMappers(idMappers)
                                                   .build();

        Map<String, String> values1 = new HashMap<>();
        values1.put("23485", "1");
        values1.put("3010", "GBP");

        Map<String, String> values2 = new HashMap<>();
        values2.put("275033", "10.521");
        values2.put("275031", "GBP");

        Mockito.when(tScacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.empty());
        Mockito.when(currentManager.retrieveCurrentResult(anyList(),anyList(),anyBoolean(), anyList())).thenReturn(Flux.just(new CurrentResult("0P0000NCP7", values1, Instant.now())));
        Mockito.when(currentManager.retrieveGroupResult(anyList(),anySet(),anyList(),anyBoolean(), anyList())).thenReturn(Flux.empty());
        Mockito.when(rdbGateway.retrieve(any(), anyList(), anyList())).thenReturn(Flux.just(new CurrentResult("0P0000NCP7", values2, Instant.now())));


        Mockito.when(rdbTsService.loadBatchedData(any(), any(), anyBoolean())).thenReturn(Flux.empty());

        when(DataPointRepository.getByNid("275033")).thenReturn(reqDp1);
        when(DataPointRepository.getByNid("275031")).thenReturn(reqDp2);
        when(DataPointRepository.getByNid("23485")).thenReturn(penceTradedDp);
        when(DataPointRepository.getByNid("3010")).thenReturn(ccyDp);

        Flux<Result> result = calculationLibService.retrieveCalcLib(request);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P0000NCP7", d.getId());
                    assertEquals("GBX", d.getValues().get("275031"));
                    assertTrue(d.getValues().containsKey("275033"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    void changeCalcMetaByUniverseTest() {
        DataPoint currencyDp = DataPoint.builder().nid("OS05M")
                .name("Currency Dp")
                .src("RDB")
                .multiUniverseList(List.of(MultiUniverse.builder().srcid("ST05M").attribute("SecurityType").type("ST").build()))
                .build();

        DataPoint currencyDp2 = DataPoint.builder().nid("100")
                .name("Currency Dp 2")
                .src("RDB")
                .build();

        DataPoint currencyDpForUniverse = DataPoint.builder().nid("ST05M")
                .name("Currency Dp for ST Universe")
                .currentRdb(RdbDataPoint.builder().build())
                .src("RDB")
                .build();

        DataPoint endDateDp = DataPoint.builder()
                .nid("OF2AS")
                .src("RDB")
                .multiUniverseList(List.of(MultiUniverse.builder().srcid("ST2AS").attribute("SecurityType").type("ST").build()))
                .build();

        DataPoint endDateDpForUniverse = DataPoint.builder().nid("ST2AS")
                .name("EndDate Dp for ST Universe")
                .currentRdb(RdbDataPoint.builder().build())
                .src("RDB")
                .build();

        DataPoint reqDp1 = DataPoint.builder().nid("CS011").name("Datapoint 1").src("RDB").calculation(Calculation.builder()
                .cur(currencyDp)
                .endDate(endDateDp)
                .calSrc("CALCLIB")
                .build()).build();

        DataPoint reqDp2 = DataPoint.builder().nid("CS012").name("Datapoint 2").src("RDB").calculation(Calculation.builder()
                .cur(currencyDp2)
                .endDate(DataPoint.builder().nid("OF2AS").build())
                .calSrc("CALCLIB")
                .build()).build();

        List<String> idList = Collections.singletonList("0P0000NCP7");
        List<String> requestDps = List.of("CS011","CS012");
        List<IdMapper> idMappers = List.of(buildIdMapper("0P0000NCP7", "ST"));

        CalcRequest request = CalcRequest.builder().calcDps(requestDps).idList(idList).idMappers(idMappers)
                .build();

        Map<String, String> values1 = new HashMap<>();
        values1.put("CS011", "1");

        Mockito.when(tScacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.empty());
        Mockito.when(currentManager.retrieveCurrentResult(anyList(),anyList(),anyBoolean(), anyList())).thenReturn(Flux.just(new CurrentResult("0P0000NCP7", values1, Instant.now())));
        Mockito.when(currentManager.retrieveGroupResult(anyList(),anySet(),anyList(),anyBoolean(), anyList())).thenReturn(Flux.empty());
        Mockito.when(rdbGateway.retrieve(any(), anyList(), anyList())).thenReturn(Flux.empty());


        Mockito.when(rdbTsService.loadBatchedData(any(), any(), anyBoolean())).thenReturn(Flux.empty());

        when(DataPointRepository.getByNid("CS011")).thenReturn(reqDp1);
        when(DataPointRepository.getByNid("CS012")).thenReturn(reqDp2);
        when(DataPointRepository.getByNid("OS05M")).thenReturn(currencyDp);
        when(DataPointRepository.getByNid("ST05M")).thenReturn(currencyDpForUniverse);
        when(DataPointRepository.getByNid("OF2AS")).thenReturn(endDateDp);
        when(DataPointRepository.getByNid("ST2AS")).thenReturn(endDateDpForUniverse);

        Flux<Result> result = calculationLibService.retrieveCalcLib(request);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P0000NCP7", d.getId());
                    assertEquals("1", d.getValues().get("CS011"));
                })
                .expectComplete()
                .verify();

        ArgumentCaptor<CalcMetaData> calcMetaDataCaptor  = ArgumentCaptor.forClass(CalcMetaData.class);
        Mockito.verify(calculationLibConversionService, times(1)).convertCurrent(any(), any(), any(), calcMetaDataCaptor.capture());
        CalcMetaData calcMetaData = calcMetaDataCaptor.getValue();
        assertEquals(2, calcMetaData.getBaseCurrencyMap().size());
        assertEquals("ST05M", calcMetaData.getBaseCurrencyMap().get("CS011").getNid());
        assertEquals("100", calcMetaData.getBaseCurrencyMap().get("CS012").getNid());
        assertEquals(2, calcMetaData.getEndDateDpMap().size());
        assertEquals("ST2AS", calcMetaData.getEndDateDpMap().get("CS011").getNid());
    }

    @Test
    void changeCalcMetaDataByUniverseWithoutIdMapperTest() {
        DataPoint currencyDp = DataPoint.builder().nid("OS05M")
                .name("Currency Dp")
                .src("RDB")
                .multiUniverseList(List.of(MultiUniverse.builder().srcid("ST05M").attribute("SecurityType").type("ST").build()))
                .build();

        DataPoint endDateDp = DataPoint.builder()
                .nid("OF2AS")
                .src("RDB")
                .multiUniverseList(List.of(MultiUniverse.builder().srcid("ST2AS").attribute("SecurityType").type("ST").build()))
                .build();

        DataPoint reqDp1 = DataPoint.builder().nid("CS011").name("Datapoint 1").src("RDB").calculation(Calculation.builder()
                .cur(currencyDp)
                .endDate(DataPoint.builder().nid("OF2AS").build())
                .calSrc("CALCLIB")
                .build()).build();

        List<String> idList = Collections.singletonList("0P0000NCP7");
        List<String> requestDps = List.of("CS011");

        CalcRequest request = CalcRequest.builder().calcDps(requestDps).idList(idList).idMappers(Collections.emptyList())
                .build();

        Map<String, String> values1 = new HashMap<>();
        values1.put("CS011", "1");

        Mockito.when(tScacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.empty());
        Mockito.when(currentManager.retrieveCurrentResult(anyList(),anyList(),anyBoolean(), anyList())).thenReturn(Flux.just(new CurrentResult("0P0000NCP7", values1, Instant.now())));
        Mockito.when(currentManager.retrieveGroupResult(anyList(),anySet(),anyList(),anyBoolean(), anyList())).thenReturn(Flux.empty());
        Mockito.when(rdbGateway.retrieve(any(), anyList(), anyList())).thenReturn(Flux.empty());


        Mockito.when(rdbTsService.loadBatchedData(any(), any(), anyBoolean())).thenReturn(Flux.empty());

        when(DataPointRepository.getByNid("CS011")).thenReturn(reqDp1);
        when(DataPointRepository.getByNid("OS05M")).thenReturn(currencyDp);
        when(DataPointRepository.getByNid("OF2AS")).thenReturn(endDateDp);

        Flux<Result> result = calculationLibService.retrieveCalcLib(request);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P0000NCP7", d.getId());
                    assertEquals("1", d.getValues().get("CS011"));
                })
                .expectComplete()
                .verify();

        ArgumentCaptor<CalcMetaData> calcMetaDataCaptor  = ArgumentCaptor.forClass(CalcMetaData.class);
        Mockito.verify(calculationLibConversionService, times(1)).convertCurrent(any(), any(), any(), calcMetaDataCaptor.capture());
        CalcMetaData calcMetaData = calcMetaDataCaptor.getValue();
        assertEquals(1, calcMetaData.getBaseCurrencyMap().size());
        assertEquals("OS05M", calcMetaData.getBaseCurrencyMap().get("CS011").getNid());
        assertEquals(1, calcMetaData.getEndDateDpMap().size());
        assertEquals("OF2AS", calcMetaData.getEndDateDpMap().get("CS011").getNid());
    }

    private IdMapper buildIdMapper(String investmentId, String securityType) {
        return new NonEmptyIdMapper(investmentId, String.format("{\"SecurityType\": \"%s\"}", securityType));
    }

    @Test
    @DisplayName("when request differen type of results should convert currency respectively")
    void shouldConvertCurrencyForGroupResults() {
        //given
        String setId = "TEST00000A";
        String dpId1 = "CR001";
        String dpId2 = "TS001";
        DataPoint curDp = DataPoint.builder()
                .id("81280").nid("100002").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true)
                .build();
        Calculation calc = Calculation.builder().cur(curDp).build();

        DataPoint dataPoint1 = DataPoint.builder().nid(dpId1).src("RDB").calculation(calc).build();
        DataPoint dataPoint2 = DataPoint.builder().nid(dpId2).src("TSAPI").calculation(calc).build();

        List<String> requestDps = List.of(dpId1, dpId2);
        List<String> idList = List.of(setId);
        CalcRequest request = CalcRequest.builder()
                .calcDps(requestDps).idList(idList).idMappers(Collections.emptyList())
                .build();

        when(DataPointRepository.getByNid(dpId1)).thenReturn(dataPoint1);
        when(DataPointRepository.getByNid(dpId2)).thenReturn(dataPoint2);
        when(DataPointRepository.getByNid(CURCHANGE)).thenReturn(null);

        Map<String, List<CurrentResult>> groupResultMap = new HashMap<>();
        groupResultMap.put(dpId1, List.of(new CurrentResult(dpId1, Map.of(dpId1, "123.0000"))));
        Map<String, List<V>> tsResultMap = new HashMap<>();
        tsResultMap.put(dpId2, List.of(new V("2020-01-01", "123.0000")));

        when(currentManager.retrieveCurrentResult(any(), any(), anyBoolean(), anyList())).thenReturn(Flux.empty());
        when(currentManager.retrieveGroupResult(anyList(), anySet(), anyList(), anyBoolean(), anyList()))
                .thenReturn(Flux.just(new GroupResult(setId, groupResultMap)));
        when(rdbGateway.retrieve(any(MartRequest.class), anyList(), anyList())).thenReturn(Flux.empty());
        when(tScacheService.retrieveTimeSeriesData(any(TSRequest.class)))
                .thenReturn(Flux.just(new TimeSeriesResult(setId, tsResultMap)));
        when(rdbTsService.loadBatchedData(any(), any(), anyBoolean())).thenReturn(Flux.empty());

        //when
        Flux<Result> results = calculationLibService.retrieveCalcLib(request);

        //then
        StepVerifier.create(results)
                .assertNext(result -> {
                    assertTrue(result.getClass().equals(CurrentResult.class) || result.getClass().equals(TimeSeriesResult.class));
                })
                .assertNext(Assertions::assertNotNull)
                .verifyComplete();
    }

}
