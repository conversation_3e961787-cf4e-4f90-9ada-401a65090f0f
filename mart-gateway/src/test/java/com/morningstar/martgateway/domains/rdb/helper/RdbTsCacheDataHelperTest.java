package com.morningstar.martgateway.domains.rdb.helper;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.google.common.base.Strings;
import com.morningstar.dataac.martgateway.core.common.util.Lz4Util;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.repository.RedisTsRepo;
import com.morningstar.martgateway.domains.core.entity.DateRange;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsData;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsDataList;
import com.morningstar.martgateway.domains.rdb.cache.event.DataLoadedFromSourceEvent;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Semaphore;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RdbTsCacheDataHelperTest {
    @Mock
    RedisTsRepo redisTsRepo;

    RdbTsCacheDataHelper rdbTsCacheDataHelper;

    Semaphore semaphore = new Semaphore(1);

    @Before
    public void setUp() throws JsonMappingException {
        rdbTsCacheDataHelper = new RdbTsCacheDataHelper(redisTsRepo, semaphore);
    }

    @Test
    public void testHandleDataLoadedFromSourceEvent() {
        when(redisTsRepo.setHash(any(), any(), anyLong())).thenReturn(Flux.empty());
        rdbTsCacheDataHelper.handleDataLoadedFromSourceEvent(mockDataLoadedFromSourceEvent());
        verify(redisTsRepo, times(1)).setHash(any(), any(), anyLong());
    }

    @Test
    public void testHandleDataLoadedFromSourceEventWithoutSemaphore() {
        semaphore.tryAcquire();
        rdbTsCacheDataHelper.handleDataLoadedFromSourceEvent(mockDataLoadedFromSourceEvent());
        verify(redisTsRepo, times(0)).setHash(any(), any(), anyLong());
        semaphore.release();
    }

    private DataLoadedFromSourceEvent mockDataLoadedFromSourceEvent() {

        RdbDataPoint rdbDp = RdbDataPoint.builder().id("SF456").nid("SF456").src("RDB").column("r_ReturnForMonth").name("r_ReturnForMonth").groupName("ExtendedMonthlyReturn").idLevel("PerformanceId").storeProcedure("sp2").columnPrefix("r_ReturnForMonth").frequency("m").build();

        Map<String, List<TsDataList>> tsData = new HashMap<>();

        List<TsData> values = new ArrayList<>();

        values.add(TsData.builder().dateEpoc(16555).value("4.87856").build());
        values.add(TsData.builder().dateEpoc(16586).value("0.64310").build());
        values.add(TsData.builder().dateEpoc(16616).value("3.83750").build());

        TsDataList tsDataList = TsDataList.<TsData>builder().dpId("92383").investmentId("F00000SXV4").values(values).build();

        tsData.put("F00000SXV4", List.of(tsDataList));
        DateRange dateRange = new DateRange("2015-04-01", "2015-07-05", "yyyy-MM-dd");
        return new DataLoadedFromSourceEvent(tsData, rdbDp, dateRange, Set.of("F00000SXV4"));
    }

    @Test
    public void testAddYearsForEmptyDataMissingMiddle(){
        DateRange dateRange = new DateRange("2014-01-01", "2022-06-30", "yyyy-MM-dd");
        Map<String, Map<String, TsDataList>> yearSubsetMap = new HashMap<>();
        yearSubsetMap.put("2011", null);
        yearSubsetMap.put("2021", null);
        rdbTsCacheDataHelper.addYearsForEmptyData(yearSubsetMap, dateRange);
        assertEquals(3, yearSubsetMap.size());
        assertTrue(yearSubsetMap.containsKey("2011"));
        assertTrue(yearSubsetMap.containsKey("2016"));
        assertTrue(yearSubsetMap.containsKey("2021"));
    }

    @Test
    public void testAddYearsForEmptyDataMissingEnd(){
        DateRange dateRange = new DateRange("2017-01-01", "2022-06-30", "yyyy-MM-dd");
        Map<String, Map<String, TsDataList>> yearSubsetMap = new HashMap<>();
        yearSubsetMap.put("2016", null);
        rdbTsCacheDataHelper.addYearsForEmptyData(yearSubsetMap, dateRange);
        assertEquals(2, yearSubsetMap.size());
        assertTrue(yearSubsetMap.containsKey("2016"));
        assertTrue(yearSubsetMap.containsKey("2021"));
    }

    @Test
    public void testGetYearBasedOnFrequency() {
        assertEquals("2021", rdbTsCacheDataHelper.getYearBasedOnFrequency("HS212:2021"));
        assertEquals("2016", rdbTsCacheDataHelper.getYearBasedOnFrequency("HP010:2018"));
        assertEquals("2016", rdbTsCacheDataHelper.getYearBasedOnFrequency("HP010:2020"));
    }

    @Test
    public void testGetYearsForFrequency(){
        DateRange dateRange = new DateRange("2014-01-01", "2023-01-01", "yyyy-MM-dd");
        List<String> years = rdbTsCacheDataHelper.getYearsForFrequency(dateRange);
        assertEquals(3, years.size());
        assertTrue(years.contains("2011"));
        assertTrue(years.contains("2016"));
        assertTrue(years.contains("2021"));
    }

    @Test
    public void testGetCacheData_WithCacheEnabled() {
        List<DataPoint> dataPoints = createDataPoint("2", "2");
        RdbDataPoint rdbDp = createRdbDataPoint("2", "ts");
        Set<String> idList = new HashSet<>(Arrays.asList("id1", "id2"));
        DateRange dateRange = new DateRange("2020-01-01", "2021-12-31", DateFormatUtil.YYYY_MM_DD);

        when(redisTsRepo.getHashValue(any(), any())).thenReturn(Flux.just(Lz4Util.compress(new byte[0])));
        Mono<Map<String, Collection<TsDataList>>> result = rdbTsCacheDataHelper.getCacheData(dataPoints, rdbDp, idList, dateRange, true);

        StepVerifier.create(result)
                .expectNextMatches(map -> !map.isEmpty())
                .verifyComplete();

        verify(redisTsRepo, times(4)).getHashValue(any(), any());
    }

    @Test
    public void testGetCacheData_WithCacheDisabled() {
        List<DataPoint> dataPoints = createDataPoint("3", "3");
        RdbDataPoint rdbDp = createRdbDataPoint("3", "ts");
        Set<String> idList = new HashSet<>(Arrays.asList("id1", "id2"));
        DateRange dateRange = new DateRange("2020-01-01", "2021-12-31", DateFormatUtil.YYYY_MM_DD);
        Mono<Map<String, Collection<TsDataList>>> result = rdbTsCacheDataHelper.getCacheData(dataPoints, rdbDp, idList, dateRange, false);

        StepVerifier.create(result)
                .expectNextMatches(Map::isEmpty)
                .verifyComplete();
    }

    private List<DataPoint> createDataPoint(String currentCacheFlag, String tsCacheFlag)
    {
        return List.of(DataPoint
                .builder()
                .currentRdb(createRdbDataPoint(currentCacheFlag, "Current"))
                .tsRdb(createRdbDataPoint(tsCacheFlag, "Ts"))
                .build());
    }
    private RdbDataPoint createRdbDataPoint(String cacheFlag, String name) {
        return Strings.isNullOrEmpty(cacheFlag) ? null : RdbDataPoint.builder().rdbCacheFlag(cacheFlag).name(name).build();
    }
}
