package com.morningstar.martgateway.domains.rdb.helper;

import com.google.common.base.Strings;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import org.junit.Assert;
import org.junit.Test;
import java.util.List;




public class RdbDataPointHelperTest {

    private static final String ILLEGAL_CACHE_FLAG = "3";
    private static final String ALLOWED_CACHE_FLAG = "2";

    @Test
    public void testIsNotMartApi() {
        DataPoint dataPoint = createDataPoint(ALLOWED_CACHE_FLAG, ALLOWED_CACHE_FLAG);
        boolean isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, true, false);
        Assert.assertFalse(isFiltered);
    }

    @Test
    public void testFilterIfNoData() {
        DataPoint dataPoint = createDataPoint(null, null);

        boolean isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, true, true);
        Assert.assertTrue(isFiltered);

        isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, false, true);
        Assert.assertTrue(isFiltered);
    }

    @Test
    public void testWithCurrentOnly() {
//        DataPoint dataPoint = createDataPoint(ILLEGAL_CACHE_FLAG, null);
//        boolean isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, true, true);
//        Assert.assertTrue(isFiltered);
//        isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, false, true);
//        Assert.assertTrue(isFiltered);

        DataPoint dataPoint = createDataPoint(ALLOWED_CACHE_FLAG, null);
        boolean isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, true, true);
        Assert.assertFalse(isFiltered);
        isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, false, true);
        Assert.assertFalse(isFiltered);
    }

    @Test
    public void testWithTsOnly()
    {
//        DataPoint dataPoint = createDataPoint(null, ILLEGAL_CACHE_FLAG);
//        boolean isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, true, true);
//        Assert.assertTrue(isFiltered);
//        isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, false, true);
//        Assert.assertTrue(isFiltered);

        DataPoint dataPoint = createDataPoint(null, ALLOWED_CACHE_FLAG);
        boolean isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, true, true);
        Assert.assertFalse(isFiltered);
        isFiltered = RdbDataPointHelper.filterMartApiData(dataPoint, false, true);
        Assert.assertTrue(isFiltered); // Drop if time series is false, even if flag is allowed
    }

    @Test
    public void testWithBoth() {
        DataPoint dataPoint = createDataPoint(ILLEGAL_CACHE_FLAG, ALLOWED_CACHE_FLAG);
        boolean isFilteredForTimeSeries = RdbDataPointHelper.filterMartApiData(dataPoint, true, true);
        Assert.assertFalse(isFilteredForTimeSeries);
//        boolean isFilteredForCurrent = RdbDataPointHelper.filterMartApiData(dataPoint, false, true);
//        Assert.assertTrue(isFilteredForCurrent);
    }

    @Test
    public void testSelectRdpDatapoint() {
        DataPoint dataPointOnlyCurrent = createDataPoint(ALLOWED_CACHE_FLAG, null);
        RdbDataPoint result = RdbDataPointHelper.selectRdbDataPoint(dataPointOnlyCurrent, true, DataPoint::getCurrentRdb);
        Assert.assertEquals(result.getName(), "Current");

        DataPoint dataPointOnlyTs = createDataPoint(null, ALLOWED_CACHE_FLAG);
        result = RdbDataPointHelper.selectRdbDataPoint(dataPointOnlyTs, true, DataPoint::getTsRdb);
        Assert.assertEquals(result.getName(), "Ts");

        DataPoint dataPointBoth = createDataPoint(ALLOWED_CACHE_FLAG, ALLOWED_CACHE_FLAG);
        result = RdbDataPointHelper.selectRdbDataPoint(dataPointBoth, true, DataPoint::getTsRdb);
        Assert.assertEquals(result.getName(), "Ts");

        result = RdbDataPointHelper.selectRdbDataPoint(dataPointBoth, false, DataPoint::getCurrentRdb);
        Assert.assertEquals(result.getName(), "Current");

        DataPoint neither = createDataPoint(null, null);
        MartException exception = Assert.assertThrows(MartException.class, () -> RdbDataPointHelper.selectRdbDataPoint(neither, true, DataPoint::getCurrentRdb));
        Assert.assertTrue(exception.getMessage().contains("contains neither Current or TS data!"));
    }

    @Test
    public void testIsTimeSeries() {
        DataPoint dataPointOnlyCurrent = createDataPoint(ALLOWED_CACHE_FLAG, null);
        boolean isTimeSeries = RdbDataPointHelper.isTimeSeries(dataPointOnlyCurrent, false);
        Assert.assertFalse(isTimeSeries);
        isTimeSeries = RdbDataPointHelper.isTimeSeries(dataPointOnlyCurrent, true);
        Assert.assertFalse(isTimeSeries);

        DataPoint dataPointOnlyTs = createDataPoint(null, ALLOWED_CACHE_FLAG);
        isTimeSeries = RdbDataPointHelper.isTimeSeries(dataPointOnlyTs, false);
        Assert.assertFalse(isTimeSeries);
        isTimeSeries = RdbDataPointHelper.isTimeSeries(dataPointOnlyTs, true);
        Assert.assertTrue(isTimeSeries);


        DataPoint dataPointBoth = createDataPoint(ALLOWED_CACHE_FLAG, ALLOWED_CACHE_FLAG);
        isTimeSeries = RdbDataPointHelper.isTimeSeries(dataPointBoth, false);
        Assert.assertFalse(isTimeSeries);
        isTimeSeries = RdbDataPointHelper.isTimeSeries(dataPointBoth, true);
        Assert.assertTrue(isTimeSeries);

        DataPoint dataPointNone = createDataPoint(ALLOWED_CACHE_FLAG, ALLOWED_CACHE_FLAG);
        isTimeSeries = RdbDataPointHelper.isTimeSeries(dataPointNone, false);
        Assert.assertFalse(isTimeSeries);
    }

    @Test
    public void testContainsTimeSeries() {
        DataPoint dataPointOnlyCurrent1 = createDataPoint(ALLOWED_CACHE_FLAG, null);
        DataPoint dataPointOnlyCurrent2 = createDataPoint(ALLOWED_CACHE_FLAG, null);

        Assert.assertFalse(RdbDataPointHelper.containsTimeSeriesDataPoint(
                List.of(dataPointOnlyCurrent1, dataPointOnlyCurrent2),
                true));

        DataPoint dataPointOnlyTs = createDataPoint(null, ALLOWED_CACHE_FLAG);
        Assert.assertFalse(RdbDataPointHelper.containsTimeSeriesDataPoint(
                List.of(dataPointOnlyCurrent1, dataPointOnlyCurrent2, dataPointOnlyTs),
                false));

        Assert.assertTrue(RdbDataPointHelper.containsTimeSeriesDataPoint(
                List.of(dataPointOnlyCurrent1, dataPointOnlyCurrent2, dataPointOnlyTs),
                true));
    }

    @Test
    public void testGetCacheExpireTime() {
        RdbDataPoint rdbDataPoint = RdbDataPoint.builder().groupName("group1").build();
        Assert.assertEquals(4L, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint));

        rdbDataPoint.setExpireTime("10");
        Assert.assertEquals(10L, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint));

        /* Comment for now until logic finalized 07-15-2024
        rdbDataPoint.setGroupFrequency("d");
        Assert.assertEquals(24L, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint));

        rdbDataPoint.setGroupFrequency("w");
        Assert.assertEquals(168L, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint));

        rdbDataPoint.setGroupFrequency("m");
        Assert.assertEquals(720L, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint));

        rdbDataPoint.setGroupFrequency("q");
        Assert.assertEquals(2160L, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint));

        rdbDataPoint.setGroupFrequency("y");
        Assert.assertEquals(8760L, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint));
         */
    }

    private DataPoint createDataPoint(String currentCacheFlag, String tsCacheFlag)
    {
        return DataPoint
                .builder()
                .currentRdb(createRdbDataPoint(currentCacheFlag, "Current"))
                .tsRdb(createRdbDataPoint(tsCacheFlag, "Ts"))
                .build();
    }

    private RdbDataPoint createRdbDataPoint(String cacheFlag, String name) {
        return Strings.isNullOrEmpty(cacheFlag) ? null : RdbDataPoint.builder().rdbCacheFlag(cacheFlag).name(name).build();
    }
}
