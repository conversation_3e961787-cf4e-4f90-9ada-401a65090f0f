package com.morningstar.martgateway.domains.calc;

import static com.morningstar.martgateway.domains.calc.CustomCalculationRequestBuilder.END_DATE_KEY;
import static com.morningstar.martgateway.domains.calc.CustomCalculationRequestBuilder.INVESTMENT_API_PRODUCT_ID;
import static com.morningstar.martgateway.domains.calc.CustomCalculationRequestBuilder.START_DATE_KEY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.ScaleType;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.ValueAtRisk;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.calc.model.CalculationFrequency;
import com.morningstar.martgateway.domains.calc.model.CalculationWindowType;
import com.morningstar.martgateway.domains.calc.model.Compounding;
import com.morningstar.martgateway.domains.calc.model.CustomCalcDataPointRequest;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.morningstar.martgateway.domains.calc.model.CustomCalcRequestNew;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@RunWith(MockitoJUnitRunner.class)
public class CustomCalculationRequestBuilderTest {

	@Test
	public void testInitNewCalcRequestDto(){
		String id = "testId";
		String userId = "user123";
		// When
		CustomCalcRequestNew result = CustomCalculationRequestBuilder.initNewCalcRequestDto(List.of(id), userId, "");

		// Then
		assertNotNull(result);
		assertEquals(userId, result.getUid());
		assertEquals(List.of(id), result.getInvestmentIds());
		assertEquals(INVESTMENT_API_PRODUCT_ID, result.getPid());
		assertTrue(result.getDatapoints().isEmpty());
	}

	@Test
	public void testCreateCurrentDataPointRequestDto(){
		Calculation calculation = Calculation.builder().dp(DataPoint.builder().nid("1").build()).source(DataPoint.builder().nid("2").build()).freq("3").build();
		DataPoint dataPoint = DataPoint.builder().calculation(calculation).nid("4").build();
		Map<String, String> dateResultMap = new HashMap<>();
		String startDate = "2024-10-01";
		String endDate = "2024-09-30";
		dateResultMap.put(START_DATE_KEY, startDate);
		dateResultMap.put(END_DATE_KEY, endDate);

		CustomCalcDataPointRequest customCalcDataPointRequest = CustomCalculationRequestBuilder.createCurrentDataPointRequest(null, dataPoint, null, null, CalcRequest.builder().currency("USD").annualized("false").build(), dateResultMap);

		assertNotNull(customCalcDataPointRequest);
		assertEquals("4", customCalcDataPointRequest.getId());
		assertEquals("2", customCalcDataPointRequest.getSourceId());
		assertEquals("USD", customCalcDataPointRequest.getCurrency());
		assertTrue(CalculationFrequency.QUARTERLY.equals(customCalcDataPointRequest.getFrequency()));
		assertEquals(endDate, customCalcDataPointRequest.getEndDate());
		assertEquals(startDate, customCalcDataPointRequest.getStartDate());
		assertEquals("1", customCalcDataPointRequest.getCalcId());
	}

	@Test
	public void testCreateCurrentDataPointRequestDtoBmkRfId(){
		Calculation calculation = Calculation.builder().dp(DataPoint.builder().nid("1").build()).source(DataPoint.builder().nid("2").build())
				.bmk(DataPoint.builder().nid("24712").build())
				.rf(DataPoint.builder().nid("14131").build())
				.freq("3").build();
		DataPoint dataPoint = DataPoint.builder().calculation(calculation).nid("4").build();
		Map<String, String> dateResultMap = new HashMap<>();
		String startDate = "2024-10-01";
		String endDate = "2024-09-30";
		dateResultMap.put(START_DATE_KEY, startDate);
		dateResultMap.put(END_DATE_KEY, endDate);
		CustomCalcDataPointRequest customCalcDataPointRequest = CustomCalculationRequestBuilder.createCurrentDataPointRequest(null, dataPoint, "XIUSA04CGI", "XIUSA000OC",
				CalcRequest.builder().currency("USD").annualized("false").skipHoliday("true").windowType("2").extendedPerformance("false").build(), dateResultMap);

		assertNotNull(customCalcDataPointRequest);
		assertEquals("4", customCalcDataPointRequest.getId());
		assertEquals("2", customCalcDataPointRequest.getSourceId());
		assertEquals("USD", customCalcDataPointRequest.getCurrency());
		assertTrue(CalculationFrequency.QUARTERLY.equals(customCalcDataPointRequest.getFrequency()));
		assertEquals(endDate, customCalcDataPointRequest.getEndDate());
		assertEquals(startDate, customCalcDataPointRequest.getStartDate());
		assertEquals("1", customCalcDataPointRequest.getCalcId());
		assertEquals("XIUSA04CGI", customCalcDataPointRequest.getBenchmarkId());
		assertEquals("XIUSA000OC", customCalcDataPointRequest.getRiskFreeProxyId());
		assertEquals(false, customCalcDataPointRequest.getIsAnnualize());
		assertEquals(true, customCalcDataPointRequest.getIsSkipHoliday());
		assertEquals(CalculationWindowType.ROLLING, customCalcDataPointRequest.getWindowType());
		assertEquals(false, customCalcDataPointRequest.getIsExtendedPerformance());
	}

	@Test
	public void testCreateCurrentDataPointRequestDtoMissingBmkRfId(){
		Calculation calculation = Calculation.builder().dp(DataPoint.builder().nid("1").build()).source(DataPoint.builder().nid("2").build())
				.bmk(DataPoint.builder().nid("24712").build())
				.rf(DataPoint.builder().nid("14131").build())
				.freq("3").build();
		DataPoint dataPoint = DataPoint.builder().calculation(calculation).nid("4").build();
		Map<String, String> dateResultMap = new HashMap<>();
		String startDate = "2024-10-01";
		String endDate = "2024-09-30";
		dateResultMap.put(START_DATE_KEY, startDate);
		dateResultMap.put(END_DATE_KEY, endDate);
		try(MockedStatic<StringUtils> stringUtilsMockedStatic = Mockito.mockStatic(StringUtils.class)) {
			stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(anyString())).thenReturn(true).thenReturn(true);
			CustomCalcDataPointRequest customCalcDataPointRequest = CustomCalculationRequestBuilder.createCurrentDataPointRequest(null, dataPoint, "", "XIUSA000OC", CalcRequest.builder().currency("USD").annualized("false").build(), dateResultMap);
			assertNotNull(customCalcDataPointRequest);
		}
	}

	@Test
	public void testMergeResultsAsSingleValue(){
		String secId = "TEST00001";
		Map<String, String> result1Map = new HashMap<>();
		result1Map.put("A", "B");
		Result result1 = new CurrentResult(secId, result1Map);

		Map<String, String> result2Map = new HashMap<>();
		result2Map.put("C", "D");
		Result result2 = new CurrentResult(secId, result2Map);
		Flux<Result> resultFlux = Flux.just(result1, result2);

		Mono<Result> resultMono = CustomCalculationRequestBuilder.mergeResultsAsSingleValue(resultFlux);
		StepVerifier.create(resultMono)
				.consumeNextWith(res -> {
					assertEquals(2, res.getValues().size());
					assertEquals(secId, res.getId());
					assertEquals("B", res.getValues().get("A"));
					assertEquals("D", res.getValues().get("C"));
				})
				.verifyComplete();
	}

	@Test
	public void testCreateTimeSeriesDataPointRequestDto(){

		Calculation calculation = Calculation.builder().dp(DataPoint.builder().nid("1").build()).source(DataPoint.builder().nid("2").build()).freq("3").requireFullHistory("false").build();
		DataPoint dataPoint = DataPoint.builder().calculation(calculation).nid("4").build();
		String startDate = "2024-01-01";
		String endDate = "2024-08-30";

		CustomCalcDataPointRequest customCalcDataPointRequest = CustomCalculationRequestBuilder.createTimeSeriesDataPointRequestDto(dataPoint, startDate, endDate, CalcRequest.builder().currency("USD").annualized("false").requireContinueData("false").build());

		assertNotNull(customCalcDataPointRequest);
		assertEquals("4", customCalcDataPointRequest.getId());
		assertEquals("2", customCalcDataPointRequest.getSourceId());
		assertEquals("USD", customCalcDataPointRequest.getCurrency());
		assertTrue(CalculationFrequency.QUARTERLY.equals(customCalcDataPointRequest.getFrequency()));
		assertEquals("2024-09-30", customCalcDataPointRequest.getEndDate());
		assertEquals(startDate, customCalcDataPointRequest.getStartDate());
		assertEquals("1", customCalcDataPointRequest.getCalcId());
		assertEquals("DEM", customCalcDataPointRequest.getPreEuroCurrency());
		assertEquals(false, customCalcDataPointRequest.getIsRequireFullHistory());
		assertEquals("", customCalcDataPointRequest.getBenchmarkId());
		assertEquals("", customCalcDataPointRequest.getRiskFreeProxyId());
		assertEquals(false, customCalcDataPointRequest.getIsSkipHoliday());
		assertEquals(CalculationWindowType.SINGLE, customCalcDataPointRequest.getWindowType());
		assertEquals(false, customCalcDataPointRequest.getIsExtendedPerformance());

		customCalcDataPointRequest = CustomCalculationRequestBuilder.createTimeSeriesDataPointRequestDto(dataPoint, startDate, endDate, CalcRequest.builder().currency("USD").preCurrency("SKR").benchmark("abc").riskFree("def").annualized("false").skipHoliday("true").windowType("3").extendedPerformance("true").build());
		assertEquals("SKR", customCalcDataPointRequest.getPreEuroCurrency());
		assertEquals("abc", customCalcDataPointRequest.getBenchmarkId());
		assertEquals("def", customCalcDataPointRequest.getRiskFreeProxyId());
		assertEquals(CalculationWindowType.FORWARD, customCalcDataPointRequest.getWindowType());
		assertEquals(true, customCalcDataPointRequest.getIsSkipHoliday());
		assertEquals(true, customCalcDataPointRequest.getIsExtendedPerformance());
	}

	@Test
	public void testHasNonEmptyDates(){
		assertFalse(CustomCalculationRequestBuilder.areBothNonEmptyDates("2024-09-30", ""));
		assertFalse(CustomCalculationRequestBuilder.areBothNonEmptyDates("", "2024-06-30"));
		assertTrue(CustomCalculationRequestBuilder.areBothNonEmptyDates("2024-06-30", "2024-09-30"));
	}

	@Test
	public void testGetDateByFreq(){
		String startDate = "2024-09-30";

		String actual = CustomCalculationRequestBuilder.getDateByFreq(startDate, "4");
		assertEquals("2024-01-01", actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq(startDate, "3");
		assertEquals("2024-07-01", actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq(startDate, "2");
		assertEquals("2024-09-01", actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq(startDate, "1");
		assertEquals("2024-09-29", actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq(startDate, null);
		assertEquals(startDate, actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq("20240930", "1");
		assertNull(actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq("2024-12-11", "3");
		assertEquals("2024-10-01", actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq("2024-05-20", "3");
		assertEquals("2024-04-01", actual);

		actual = CustomCalculationRequestBuilder.getDateByFreq("2024-02-08", "3");
		assertEquals("2024-01-01", actual);
	}
	@Test
	public void testBuildDateResultsNZeroOrNegativeTrailingVal(){
		String secId = "TEST00001";
		String endDateNid = "4194";
		Map<String, String> map = new HashMap<>();
		map.put(endDateNid, "2024-09-30");
		CurrentResult currentResult = new CurrentResult(secId, map);

		Map<String, String> actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, -1, null);
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("-1", actual.get(START_DATE_KEY));

		actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, 0, null);
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2024-01-01", actual.get(START_DATE_KEY));

		actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, -2, null);
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2024-09-01", actual.get(START_DATE_KEY));

		actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, -3, null);
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2024-07-01", actual.get(START_DATE_KEY));
	}

	@Test
	public void testBuildDateResultsPositiveTrailingVal(){
		String secId = "TEST00001";
		String endDateNid = "4194";
		Map<String, String> map = new HashMap<>();
		map.put(endDateNid, "2024-09-30");
		Integer trailingPeriodVal = 3;
		CurrentResult currentResult = new CurrentResult(secId, map);

		Map<String, String> actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, trailingPeriodVal, "0"); //daily
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2024-09-27", actual.get(START_DATE_KEY));

		actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, trailingPeriodVal, "1"); //weekly
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2024-09-08", actual.get(START_DATE_KEY));

		actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, trailingPeriodVal, "2"); //monthly
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2024-07-01", actual.get(START_DATE_KEY));

		actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, trailingPeriodVal, "3"); //quarterly
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2024-01-01", actual.get(START_DATE_KEY));

		actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, trailingPeriodVal, "4"); //yearly
		assertEquals("2024-09-30", actual.get(END_DATE_KEY));
		assertEquals("2021-01-01", actual.get(START_DATE_KEY));
	}

	@Test(expected = IllegalArgumentException.class)
	public void testBuildDateResultsExceptionThrown(){
		String secId = "TEST00001";
		String endDateNid = "4194";
		Map<String, String> map = new HashMap<>();
		map.put(endDateNid, "2024-09-30");
		Integer trailingPeriodVal = 3;
		CurrentResult currentResult = new CurrentResult(secId, map);
		Map<String, String> actual = CustomCalculationRequestBuilder.buildDateResults(currentResult, endDateNid, trailingPeriodVal, "5"); //yearly
	}

	@Test
	public void testConvertCalcRequestToDpRequest(){
		String dataPointId = "4";
		String sid = "HP010";
		String frequency = "m";
		String startDate = "2021-02-01";
		String endDate = "2024-05-31";
		String currency = "USD";
		String windowType = "1";
		String benchmark = "XIUSA04G92;XI";
		String riskFree = "XIUSA000O8;XI";
		String annualized = "false";
		String requireContinueData = "true";

		ValueAtRisk valueAtRisk = ValueAtRisk.builder().confidenceValue(0.99).fitType(1).type(1).scaleType(ScaleType.PERCENTAGE).initialAmount(1.1).timeHorizon(1).build();

		CustomCalcDataPointRequest actual = CustomCalculationRequestBuilder.convertToCalcDpRequest(GridviewDataPoint.builder().dataPointId(dataPointId).sourceId(sid).frequency(frequency)
				.startDate(startDate).endDate(endDate).currency(currency).windowType(windowType).benchmark(benchmark).riskFree(riskFree).annualized(annualized).requireContinueData(requireContinueData)
				.compounding("1").valueAtRisk(valueAtRisk).calcBestReturnNum("3").calcWorstReturnNum("2").build());
		assertNotNull(actual);
		assertEquals(dataPointId, actual.getId());
		assertEquals(sid, actual.getSourceId());
		assertEquals(CalculationFrequency.MONTHLY, actual.getFrequency());
		assertEquals(startDate, actual.getStartDate());
		assertEquals(endDate, actual.getEndDate());
		assertEquals(currency, actual.getCurrency());
		assertEquals(CalculationWindowType.SINGLE, actual.getWindowType());
		assertEquals(benchmark, actual.getBenchmarkId());
		assertEquals(riskFree, actual.getRiskFreeProxyId());
		assertFalse(actual.getIsAnnualize());
		assertTrue(actual.getIsRequireFullHistory());
		assertEquals(Compounding.LOGARITHMIC.getShortName(), actual.getCompoundingMethod());

		DecimalFormat df2 = new DecimalFormat("0.00");
		assertEquals("0.01", df2.format(actual.getValueAtRiskSetting().getConfidenceValue()));
		assertTrue(actual.getValueAtRiskSetting().getFitType().equals(1));
		assertTrue(actual.getValueAtRiskSetting().getType().equals(1));
		assertTrue(actual.getValueAtRiskSetting().getScaleType().equals(0));
		assertTrue(actual.getValueAtRiskSetting().getInitialAmount().equals(1.1));
		assertTrue(actual.getValueAtRiskSetting().getTimeHorizon().equals(1));
		assertTrue(actual.getExcludeFromSource().getBestNum().equals(3));
		assertTrue(actual.getExcludeFromSource().getWorstNum().equals(2));
	}

	@Test
	public void testConvertCalcRequestToDpRequestEmptyValues(){
		String dataPointId = "4";
		String sid = "HP010";
		String frequency = null;
		String startDate = "";
		String endDate = "";
		CalcRequest calcRequest = CalcRequest.builder()
				.calcDps(List.of(dataPointId))
				.sourceId(sid)
				.frequency(frequency)
				.startDate(startDate)
				.endDate(endDate).build();

		CustomCalcDataPointRequest actual = CustomCalculationRequestBuilder.convertToCalcDpRequest(GridviewDataPoint.builder().dataPointId("987654").frequency(frequency).startDate(startDate).endDate(endDate).build());
		assertNotNull(actual);
		assertEquals(null, actual.getFrequency());
		assertEquals("", actual.getStartDate());
		assertEquals("", actual.getEndDate());
	}

	@Test
	public void testConvertCalcRequestToDpRequestWeeklyFreq(){
		String dataPointId = "4";
		String sid = "HP010";
		String frequency = "w";
		String startDate = "2024-09-01";
		String endDate = "2025-01-31";
		CalcRequest calcRequest = CalcRequest.builder()
				.calcDps(List.of(dataPointId))
				.sourceId(sid)
				.frequency(frequency)
				.startDate(startDate)
				.endDate(endDate).build();

		CustomCalcDataPointRequest actual = CustomCalculationRequestBuilder.convertToCalcDpRequest(GridviewDataPoint.builder().dataPointId(dataPointId).sourceId(sid).frequency(frequency).startDate(startDate).endDate(endDate).build());
		assertNotNull(actual);
		assertEquals(CalculationFrequency.WEEKLY, actual.getFrequency());
		assertEquals("2024-09-01", actual.getStartDate());
		assertEquals("2025-02-02", actual.getEndDate());
 	}
}
