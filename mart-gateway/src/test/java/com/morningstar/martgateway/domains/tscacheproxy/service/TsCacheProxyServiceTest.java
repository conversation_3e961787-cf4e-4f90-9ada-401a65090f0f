package com.morningstar.martgateway.domains.tscacheproxy.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.morningstar.martgateway.domains.timeseries.entity.TSContent;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import java.nio.ByteBuffer;
import java.util.Collections;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

public class TsCacheProxyServiceTest {

    private WebClient tsClient;

    private TsCacheProxyService service;

    @Before
    public void setup() {
        this.tsClient = Mockito.mock(WebClient.class);
        this.service = new TsCacheProxyService(tsClient);
    }

    @Test
    public void getTsCacheData() {
        TSRequest tsRequest = TSRequest.builder().build();
        WebClient.RequestHeadersUriSpec requestHeadersUriSpec = mock(WebClient.RequestHeadersUriSpec.class);
        when(tsClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersUriSpec);
        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
        TSResponse tsResponse = buildTsResponse();
        ByteBuffer tsResponseBinary = tsResponse.toProtobuf().toByteString().asReadOnlyByteBuffer();
        when(responseSpec.toEntity(ByteBuffer.class)).thenReturn(Mono.just(new ResponseEntity<>(tsResponseBinary, HttpStatus.OK)));
        TSResponse actualTsResponse = service.getTsCacheData(tsRequest).blockFirst();
        verify(responseSpec, times(1)).toEntity(ByteBuffer.class);
        Assert.assertTrue(actualTsResponse != null && actualTsResponse.getContent() != null);
        Assert.assertEquals(tsResponse.getContent().getItems().get(0).getSecid(), actualTsResponse.getContent().getItems().get(0).getSecid());
    }

    @Test(expected = MartException.class)
    public void getTsCacheDataError() {
        TSRequest tsRequest = TSRequest.builder().build();
        WebClient.RequestHeadersUriSpec requestHeadersUriSpec = mock(WebClient.RequestHeadersUriSpec.class);
        when(tsClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersUriSpec);
        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
        ByteBuffer tsResponseBinary = ByteBuffer.wrap("NOT PROTOBUF".getBytes());
        when(responseSpec.toEntity(ByteBuffer.class)).thenReturn(Mono.just(new ResponseEntity<>(tsResponseBinary, HttpStatus.OK)));
        service.getTsCacheData(tsRequest).blockFirst();
        verify(responseSpec, times(1)).toEntity(ByteBuffer.class);
    }

    @Test
    public void getTsCacheDataPostResponse() {
        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        when(tsClient.post()).thenReturn(requestBodyUriSpec);
        WebClient.RequestHeadersUriSpec requestHeadersUriSpec = mock(WebClient.RequestHeadersUriSpec.class);
        when(requestBodyUriSpec.bodyValue(anyString())).thenReturn(requestHeadersUriSpec);
        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
        when(responseSpec.onStatus(any(), any())).thenReturn(responseSpec);
        service.getTsCacheDataPostResponse("secids=OP0001L74W;FE&universe=&adjustment=0&dataid=HS459&startdate=2020-6-16&enddate=2023-11-8");
        verify(responseSpec, times(1)).toEntity(String.class);
    }

    private TSResponse buildTsResponse() {
        TSResponse tsResponse = new TSResponse();
        TSStatus status = new TSStatus();
        status.setCode("0");
        status.setMsg("");
        tsResponse.setStatus(status);
        TSContent tsContent = new TSContent();

        TSItem tsItem = new TSItem();
        tsItem.setSecid("5PUSA00003;CP");
        tsItem.setDataid("HP010");
        TSData tsData = new TSData();
        tsData.setDate(10012);
        tsData.setValue(100.00);
        tsItem.setData(Collections.singletonList(tsData));

        TSItem tsItem2 = new TSItem();
        tsItem2.setSecid("5PUSA00003;CP");
        tsItem2.setDataid("HP012");
        TSData tsData2 = new TSData();
        tsData2.setDate(10015);
        tsData2.setValue(105.05);
        tsItem2.setData(Collections.singletonList(tsData2));

        tsContent.setItems(List.of(tsItem, tsItem2));
        tsResponse.setContent(tsContent);
        return tsResponse;
    }

}
