package com.morningstar.martgateway.domains.calclib.conversion;

import com.amazonaws.services.s3.AmazonS3;
import com.morningstar.calculationlibrary.CalcLibraryDefine;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.repository.S3Client;
import com.morningstar.martgateway.MartGatewayAutoConfiguration;
import com.morningstar.martgateway.MartGatewayProperties;
import com.morningstar.martgateway.applications.calculation.CalcServiceGateway;
import com.morningstar.martgateway.applications.current.CurrentGateway;
import com.morningstar.martgateway.applications.dataservice.DSGateway;
import com.morningstar.martgateway.applications.fixincome.FIGateway;
import com.morningstar.martgateway.applications.lakehouse.LakeHouseGateway;
import com.morningstar.martgateway.applications.timeseries.TSGateway;
import com.morningstar.martgateway.domains.calc.CurrentDataRetrievalService;
import com.morningstar.martgateway.domains.calc.CalcAPICaller;
import com.morningstar.martgateway.domains.calc.CalculationService;
import com.morningstar.martgateway.domains.calclib.CalculationLibConversionService;
import com.morningstar.martgateway.domains.calclib.CalculationLibService;
import com.morningstar.martgateway.domains.calclib.util.CurrencyIdUtil;
import com.morningstar.martgateway.domains.current.CategoryIdLevelDataService;
import com.morningstar.martgateway.domains.current.CurrentManager;
import com.morningstar.martgateway.domains.current.CurrentService;
import com.morningstar.martgateway.domains.dataservice.customcalc.CustomCalcProcessor;
import com.morningstar.martgateway.domains.dataservice.dao.DataResultDao;
import com.morningstar.martgateway.domains.dataservice.service.DataServiceCaller;
import com.morningstar.martgateway.domains.dataservice.service.DataServiceTsCaller;
import com.morningstar.martgateway.domains.fi.service.FICaller;
import com.morningstar.martgateway.domains.privatemodel.PrivateModelPortfolioService;
import com.morningstar.martgateway.domains.rdb.service.RdbTsService;
import com.morningstar.martgateway.util.RequireIdUtil;
import com.morningstar.martgateway.domains.timeseries.service.CloudHistoricalService;
import com.morningstar.martgateway.domains.timeseries.service.TScacheService;
import com.morningstar.martgateway.applications.rdb.RdbGateway;
import com.morningstar.martgateway.infrastructures.repo.data.S3DataRepo;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.redshift.jdbc.RedshiftRepo;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CurrencyConversionTest {
    private V data;
    private final String hisCurr = "2";
    private final String hisDate = "2020-10-22";
    private final String target = "1";
    private final String precurrency = "ESP";
    private final List<V> dataList = new ArrayList<>();
    private final String[] hisCurrs = {"2","1"};
    private final String[] hisDates =  {"2020-10-07","2020-11-08"};
    private final String freq = "66";
    private final double value = 1.300;
    private Date start = new Date();
    private Date end = new Date();

    private int index = (int) (LocalDate.parse("1999-01-01").toEpochDay() - LocalDate.parse("1900-01-01").toEpochDay());
    @Mock
    private ExchangeRateLoader exchangeRateLoader;
    @Mock
    private RedisTemplate<String, String> redisTemplate;
    @Mock
    private ValueOperations mockvalueOperations;
    private ExchangeRateLoader getExchangeRateLoader;
    private CurrencyConversion currencyConversion;
    @Mock
    private ConversionCenter conversionCenter;
    private ConversionCenter conversionCenterTest;

    private MartGatewayAutoConfiguration martGatewayAutoConfiguration;
    @Mock
    private MartGatewayProperties martGatewayProperties;
    @Mock
    private MartGatewayProperties.AWS aws;
    @Mock
    private MartGatewayProperties.Calculation calculation;
    @Mock
    private CurrentManager currentManager;
    @Mock
    private CalcAPICaller calcAPICaller;
    @Mock
    private TScacheService tscacheService;
    @Mock
    private CalculationLibConversionService calculationLibConversionService;
    @Mock
    private CurrentDataRetrievalService currentDataRetrievalService;
    @Mock
    private CategoryIdLevelDataService categoryIdLevelDataService;
    @Mock
    private S3DataRepo s3DataRepo;
    @Mock
    private MartGatewayProperties.S3 s3;
    @Mock
    private RedisTemplate<String, String> syncDataStorageTemplate;
    @Mock
    private RedisTemplate<String, String> syncDataCacheTemplate;
    @Mock
    private S3Client s3Client;
    @Mock
    private CurrentService currentService;
    @Mock
    private DataResultDao dataResultDao;
    @Mock
    private CustomCalcProcessor customCalcProcessor;
    @Mock
    private AmazonS3 amazonS3;
    @Mock
    private CalculationService calculationService;
    @Mock
    private CalculationLibService calculationLibService;
    @Mock
    private CurrentManager manager;
    @Mock
    private DataServiceCaller dataServiceCaller;
    @Mock
    private DataServiceTsCaller dataServiceTsCaller;
    @Mock
    private FICaller FICaller;
    @Mock
    private CloudHistoricalService cloudHistoricalService;
    @Mock
    private TScacheService tsCacheService;
    @Mock
    private CurrentGateway currentGateway;
    @Mock
    private TSGateway tsGateway;
    @Mock
    private CalcServiceGateway csGateway;
    @Mock
    private DSGateway dsGateWay;
    @Mock
    private FIGateway fiGateWay;
    @Mock
    private LakeHouseGateway lakeHouseGateway;
    @Mock
    private RdbGateway rdbGateway;
    @Mock
    private MartGatewayProperties.Tscache tscache;
    @Mock
    private RedisTemplate<String, String> factory1;

    @Mock
    private MartGatewayProperties.Redshift redshift;

    @Mock
    private RedshiftRepo redshiftRepo;
    @Mock
    private PrivateModelPortfolioService privateModelPortfolioService;

    @Mock
    RdbTsService rdbTsService;

    @Mock
    IdMapUtil idMapUtil;

    @Mock
    RequireIdUtil requireIdUtil;

    private String host = "8080";
    private String region = "east-1";
    private String baseUrl = "test";
    private String uri = "test2";

    private static MockedStatic<CurrencyIdUtil> currencyIdUtilMockedStatic;


    @Before
    public void setUp(){
        data = new V("2020-10-23","6.982");
        dataList.add(data);
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, 5);
        end = ca.getTime();

        currencyIdUtilMockedStatic = Mockito.mockStatic(CurrencyIdUtil.class, Mockito.RETURNS_DEEP_STUBS);

        Map<String, Double> preEurMap = new HashMap<>();
        preEurMap.put("234",1.5453);
        Map<String, Double> preEurMap2 = new HashMap<>();
        preEurMap2.put("EUR",1.5453);
        getExchangeRateLoader = new ExchangeRateLoader(redisTemplate);
        currencyConversion = new CurrencyConversion(conversionCenter);
    }
    @After
    public void afterEachTest(){
        currencyIdUtilMockedStatic.close();
    }

    @Test
    public void shouldConvertCurrencySeriesNaNTest() {
        when(conversionCenter.convertCurrency(any(),any(),any(),any(),any())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidOutput);
        String result = currencyConversion.convertCurrencySeries(data,hisCurr,hisDate,target,precurrency);
        assertEquals("",result);
    }

    @Test
    public void shouldConvertCurrencyEmptyTest() {
        when(conversionCenter.convertReturn(any(),any(),any(),any(),any(),any())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidOutput);
        currencyConversion.convertReturn(dataList,hisCurrs,hisDates,target,precurrency,freq);
        assertEquals("",dataList.get(0).getV());
    }

    @Test
    public void shouldConvertReturnTest() {
        String hisCurr = "2020-10-30";
        String precurrency = "BEF";
        String target = "EUR";
        String hisDate = "2020-08-20";
        when(conversionCenter.convertReturn(any(),any(),anyDouble(),any(),any(),anyString(),anyList())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidParameter);
        String result = currencyConversion.convertReturn(value,start,end,hisCurr,hisDate,target,precurrency);
        assertEquals("",result);
    }
    @Test
    public void shouldConvertExchangeRatesTest() {
        String precurrency = "BEF";
        String target = "EUR";
        V v = new V("44071","44071");
        String base = "1";
        when(conversionCenter.convertExchangeRates(any(),anyString(),anyString(),any(),anyList())).thenReturn(CalcLibraryDefine.ErrorCode.InvalidOutput);
        String result = currencyConversion.convertExchangeRates(v,target,base,precurrency);
        assertEquals("",result);
    }

    @Test
    public void shouldGetExchangeRatesQ1Test() {
        String currencyId = "CU$$$$$EUR";
        String startDate = "2020-10-22";
        String endDate = "2020-11-22";
        ValueOperations<String,String> vo1 = mock(ValueOperations.class);
        when(redisTemplate.opsForValue()).thenReturn(vo1);
        double[] result = getExchangeRateLoader.getExchangeRates(currencyId,startDate,endDate);
        assertEquals(32, result.length);
    }
    @Test
    public void shouldGetExchangeRatesQ2Test() {
        String currencyId = "CU$$$$$EUR";
        String startDate = "2020-10-22";
        String endDate = "2020-11-22";
        String jsonStr = "{\"startDate\":\"1\",\"exchangeRateList\":\"1,2,3\"}";
        when(redisTemplate.opsForValue()).thenReturn(mockvalueOperations);
        doReturn(jsonStr).when(mockvalueOperations).get(anyString());
        double[] result = getExchangeRateLoader.getExchangeRates(currencyId,startDate,endDate);
        assertNotNull(result[1]);
    }
    //ConversionCenter
    @Test
    public void setExchangeRate() {
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        String currency = "CU$$$$$ERU";
        double[] exchangeRate = {0.98,0.765,1.0966};
        Date startDate = new Date();
        conversionCenterTest.setExchangeRate(currency,exchangeRate,startDate);
    }

    @Test
    public void convertCurrency() {
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        CalcLibraryDefine.DoubleTS[] priceSeries = new CalcLibraryDefine.DoubleTS[]{};
        String[] historyCurrency = {"CU$$$$$ERU","CU$$$$$USA"};
        int[] historyCurrencyDate = {1,2};
        String target = "CU$$$$$ERU";
        List<CalcLibraryDefine.DoubleTS> convertedValue = new ArrayList<>();
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        CalcLibraryDefine.ErrorCode code = conversionCenterTest.convertCurrency(priceSeries,historyCurrency,historyCurrencyDate,target,convertedValue);
    }

    @Test
    public void convertReturn() {
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        CalcLibraryDefine.DoubleTS[] priceSeries = new CalcLibraryDefine.DoubleTS[]{};
        String freq = "12";
        String[] historyCurrency = {"CU$$$$$ERU","CU$$$$$ETF"};
        int[] historyCurrencyDate = {1,2};
        String target = "CU$$$$$ERU";
        List<CalcLibraryDefine.DoubleTS> convertedValue = new ArrayList<>();
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        conversionCenterTest.convertReturn(priceSeries,freq,historyCurrency,historyCurrencyDate,target,convertedValue);

    }


    @Test
    public void testConvertReturn() {
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        Date startDate = new Date();
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, 5);
        Date endDate = ca.getTime();
        double value = 0.98;
        String[] historyCurrency = {"ERU","AUS"};
        int[] historyCurrencyDate = {1492,7853};
        String target = "CU$$$$$ERU";
        List<Double> convertedValue = new ArrayList<>();
        convertedValue.add(value);
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        conversionCenterTest.convertReturn(startDate,endDate,value,historyCurrency,historyCurrencyDate,target,convertedValue);
    }

    @Test
    public void convertExchangeRates() {
        double[] rates = {0.98,1.076};
        String base = "CU$$$$$ERU";
        String target = "CU$$$$$ERU";
        Date date = new Date();
        double value = 0.98;
        List<Double> convertedValue  = new ArrayList<>();
        convertedValue.add(value);
        exchangeRateLoader = mock(ExchangeRateLoader.class);
        conversionCenterTest = new ConversionCenter(exchangeRateLoader);
        conversionCenterTest.convertExchangeRates(rates,base,target,date,convertedValue);
    }
    @Test
    @Ignore
    public void shouldcalculationServiceTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.calculationService(currentDataRetrievalService, calcAPICaller);
    }
    @Test
    @Ignore
    public void shouldExchangeRateLoaderTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.exchangeRateLoader(redisTemplate);
    }
    @Test
    @Ignore
    public void shouldConversionCenterTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.conversionCenter(exchangeRateLoader);
    }
    @Test
    @Ignore
    public void shouldcurrencyConversionTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.currencyConversion(conversionCenter);
    }
    @Test
    @Ignore
    public void shouldconversionHelperTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.conversionHelper(currencyConversion);
    }

    @Test
    @Ignore
    public void shouldcalculationLibServiceTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.calculationLibService(currentManager,tscacheService, calculationLibConversionService, rdbGateway, rdbTsService);
    }

    @Test
    @Ignore
    public void shouldcurrentManagerTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.currentManager(currentService,categoryIdLevelDataService);
    }
    @Test
    @Ignore
    public void shouldcurrentServiceTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.currentService(s3DataRepo,syncDataStorageTemplate,syncDataCacheTemplate);
    }
    @Test
    @Ignore
    public void shouldcategoryIdLevelDataServiceTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.categoryIdLevelDataService(s3DataRepo,syncDataStorageTemplate,syncDataCacheTemplate);
    }
    @Test
    @Ignore
    public void shoulds3GetServiceTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.s3GetService(s3Client);
    }

    @Test
    @Ignore
    public void shoulddataServiceCallerTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.dataServiceCaller(currentService,syncDataCacheTemplate,dataResultDao,customCalcProcessor);
    }
    @Test
    @Ignore
    public void shoulddataServiceTsCallerTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.dataServiceTsCaller(syncDataCacheTemplate,dataResultDao,customCalcProcessor);
    }
    @Test
    @Ignore
    public void shouldcsGatewayTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.calcServiceGateway(calculationService,calculationLibService);
    }
    @Test
    @Ignore
    public void shouldcurrentGatewayTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.currentGateway(manager);
    }
    @Test
    @Ignore
    public void shoulddsGateWayTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.dsGateWay(dataServiceCaller,dataServiceTsCaller);
    }
    @Test
    @Ignore
    public void shouldfiGateWayTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.fiGateWay(FICaller);
    }
    @Test
    @Ignore
    public void shouldtsGatewayTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.tsGateway(tsCacheService);
    }
    @Test
    @Ignore
    public void shouldcloudHistoricalServiceTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.cloudHistoricalService(s3DataRepo,currentService,syncDataCacheTemplate);
    }

    @Test
    @Ignore
    public void shouldcustomCalcProcessorTest() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        martGatewayAutoConfiguration.customCalcProcessor();
    }

    @Test
    @Ignore
    public void shouldGetRedshiftRepo() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        ReflectionTestUtils.setField(martGatewayAutoConfiguration, "martGatewayProperties", martGatewayProperties);
        when(martGatewayProperties.getRedshift()).thenReturn(redshift);
        RedshiftRepo redshiftRepo = martGatewayAutoConfiguration.redshiftRepo();
        Assert.assertNotNull(redshiftRepo);
    }

    @Test
    @Ignore
    public void shouldGetPrivateModelPortfolioService() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        Assert.assertNotNull(martGatewayAutoConfiguration.privateModelPortfolioService(redshiftRepo, "", customCalcProcessor));
    }

    @Test
    @Ignore
    public void shouldGetLakeHouseGateway() {
        martGatewayAutoConfiguration = new MartGatewayAutoConfiguration(martGatewayProperties);
        Assert.assertNotNull(martGatewayAutoConfiguration.lakeHouseGateway(privateModelPortfolioService));
    }

}
