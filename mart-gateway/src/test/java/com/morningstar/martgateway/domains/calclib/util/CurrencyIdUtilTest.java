package com.morningstar.martgateway.domains.calclib.util;

import com.morningstar.martgateway.domains.calclib.util.CurrencyIdUtil;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class CurrencyIdUtilTest {

    @Test
    public void getCurrencyIdTest(){
        String currencyId = CurrencyIdUtil.getCurrencyId("CU$$$$$EUR");
        assertEquals("EUR", currencyId);
    }

    @Test
    public void getIndexByITest(){
        int day = CurrencyIdUtil.getIndexByI("2000-07-21");
        assertEquals(36726, day);
    }

    @Test
    public void getIdListTest(){
        List<String> idList = CurrencyIdUtil.getIdList();
        assertEquals(257, idList.size());
        assertEquals("CU$$$$$EUR", idList.get(0));
    }

    @Test
    public void isExchangeIdTest(){
        boolean isExchangeId = CurrencyIdUtil.isExchangeId("CU$$$$$EUR");
        assertTrue(isExchangeId);
    }
}
