package com.morningstar.martgateway.domains.core.pipeline;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdDatePair;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.martgateway.applications.calculation.CalcServiceGateway;
import com.morningstar.martgateway.applications.calculation.CustomCalculationServiceGateway;
import com.morningstar.martgateway.applications.current.CurrentGateway;
import com.morningstar.martgateway.applications.dataservice.DSGateway;
import com.morningstar.dataac.martgateway.data.eod.service.gateway.EODGateway;
import com.morningstar.martequity.EquityGateway;
import com.morningstar.martgateway.applications.extendedperformance.ExtendedPerformanceGateway;
import com.morningstar.martgateway.applications.fixincome.FIGateway;
import com.morningstar.dataac.morningstar.data.fixedincome.service.gateway.FixedIncomeGateway;
import com.morningstar.martgateway.applications.lakehouse.LakeHouseGateway;
import com.morningstar.martgateway.applications.language.LanguageGateway;
import com.morningstar.martgateway.applications.rdb.RdbGateway;
import com.morningstar.martgateway.applications.timeseries.TSGateway;
import com.morningstar.dataac.martgateway.core.common.repository.LocalIdMapperCache;
import com.morningstar.martgateway.applications.rdb.DynamicRdbGateway;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;

import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class MartRequestDataAssemblerServiceTest {

    @Mock
    DynamicRdbGateway dynamicRdbGateway;

    @Mock
    private CurrentGateway currentGateway;
    @Mock
    private TSGateway tsGateway;
    @Mock
    private CalcServiceGateway csGateway;
    @Mock
    private EODGateway eodGateway;
    @Mock
    private CustomCalculationServiceGateway customCalculationServiceGateway;
    @Mock
    private DSGateway dsGateway;
    @Mock
    private FIGateway fiGateway;
    @Mock
    private LakeHouseGateway lakeHouseGateway;
    @Mock
    private ExtendedPerformanceGateway extPerfGateway;
    @Mock
    private RdbGateway rdbGateway;
    @Mock
    private LanguageGateway languageGateway;
    @Mock
    private FixedIncomeGateway fixedIncomeGateway;
    @Mock
    private EquityGateway equityApplication;
    @Mock
    private LocalIdMapperCache redisTemplate;

    private MartRequestDataAssemblerService dataAssembleService;

    @BeforeEach
    public void setUp() {
        List<Gateway<Result, MartRequest>> gateways = List.of(dynamicRdbGateway,currentGateway,
                tsGateway, eodGateway, csGateway, customCalculationServiceGateway,
                dsGateway, fiGateway, rdbGateway, languageGateway, lakeHouseGateway,
                extPerfGateway, fixedIncomeGateway, equityApplication);

        dataAssembleService = new MartRequestDataAssemblerService(gateways);

        gateways.forEach(g ->
            when(g.handleRequest(any())).thenCallRealMethod()
        );
    }

    @Test
    public void retrieveSecuritiesTest() {
        MartRequest martRequest = MartRequest.builder().dps(List.of("90325")).ids(List.of("F00000ONLR")).build();
        Map<String, String> map = new HashMap<>();
        map.put("90325", "Taxable Bond");
        when(currentGateway.retrieve(martRequest)).thenReturn(Flux.just(new CurrentResult("F00000ONLR", map, Instant.now())));
        when(tsGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(csGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(customCalculationServiceGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(eodGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(dsGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(fiGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(lakeHouseGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(rdbGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(extPerfGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(languageGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(fixedIncomeGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(equityApplication.retrieve(martRequest)).thenReturn(Flux.empty());
        when(dynamicRdbGateway.retrieve(martRequest)).thenReturn(Flux.empty());

        when(redisTemplate.getIdMappersBySecIds(any())).thenReturn(List.of(new NonEmptyIdMapper("F00000ONLR", "{\"SecId\":\"F00000ONLR\",\"Id\":\"F00000ONLR\"}")));

        Flux<Result> response = dataAssembleService.executeDataRetrieval(martRequest);
        StepVerifier.create(response)
                .expectSubscription()
                .consumeNextWith(r ->
                    assertEquals("Taxable Bond", r.getValues().get("90325"))
                )
                .verifyComplete();
    }

    @Test
    public void retrieveSecuritiesForIdPairs() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        IdDatePair pair1 = IdDatePair.builder().id("63027").dates(Arrays.asList(LocalDate.parse("2021-01-31", formatter), LocalDate.parse("2019-03-31", formatter))).build();
        List<IdDatePair> idDatePairs = Collections.singletonList(pair1);
        MartRequest martRequest = MartRequest.builder().dps(Arrays.asList("94417", "94418")).idPairs(idDatePairs).requestId(UUID.randomUUID().toString()).build();
        Map<String, List<V>> values = new HashMap<>();
        values.put("94417", List.of(new V("2019-03-31", "93.68618")));
        when(dsGateway.retrieve(martRequest)).thenReturn(Flux.just(new TimeSeriesResult("63027", values)));
        when(currentGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(tsGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(csGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(eodGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(fiGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(lakeHouseGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(rdbGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(extPerfGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(languageGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(fixedIncomeGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(equityApplication.retrieve(martRequest)).thenReturn(Flux.empty());
        when(dynamicRdbGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        Flux<Result> response = dataAssembleService.executeDataRetrieval(martRequest);
        StepVerifier.create(response)
                .expectSubscription()
                .consumeNextWith(r ->
                    assertEquals("93.68618", ((TimeSeriesResult) r).getValues().get("94417").get(0).getV())
                )
                .verifyComplete();
    }

    @Test
    public void retrieveLanguageData() {
        MartRequest martRequest = MartRequest.builder().dps(List.of("OS01W")).requestId(UUID.randomUUID().toString()).build();
        when(dsGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(currentGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(tsGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(eodGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(csGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(fiGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(lakeHouseGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(fixedIncomeGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(extPerfGateway.retrieve(martRequest)).thenReturn(Flux.empty());
        when(dynamicRdbGateway.retrieve(martRequest)).thenReturn(Flux.empty());

        Map<String, String> rdbValues = new HashMap<>();
        rdbValues.put("OS01W", "Canada Life Balanced T5");
        Map<String, Map<String, String>> rdbMetaData = new HashMap<>();
        Map<String, String> metaValues1 = new HashMap<>();
        metaValues1.put("OS01W", "");
        rdbMetaData.put("rdbCurrentData", metaValues1);
        when(rdbGateway.retrieve(martRequest)).thenReturn(Flux.just(new CurrentResult("F0000023YM", rdbValues, rdbMetaData)));

        Map<String, String> languageValues = new HashMap<>();
        languageValues.put("OS01W", "Canada Vie Portefeuille équilibré T5");
        Map<String, Map<String, String>> languageMetaData = new HashMap<>();
        Map<String, String> metaValues2 = new HashMap<>();
        metaValues2.put("OS01W", "");
        languageMetaData.put("LanguageCurrentData", metaValues2);
        when(languageGateway.retrieve(martRequest)).thenReturn(Flux.just(new CurrentResult("F0000023YM", languageValues, languageMetaData)));

        when(equityApplication.retrieve(martRequest)).thenReturn(Flux.empty());

        Flux<Result> response = dataAssembleService.executeDataRetrieval(martRequest);
        StepVerifier.create(response)
                .expectSubscription()
                .assertNext(r -> {
                    Map valueMap = r.getValues();
                    String value = (String)valueMap.get("OS01W");
                    assertEquals("F0000023YM", r.getId());
                    assertEquals(1, valueMap.size());
                    assertTrue("Canada Life Balanced T5".equals(value) ||
                            "Canada Vie Portefeuille équilibré T5".equals(value));
                    assertTrue(r.getPrimaryKeys().containsKey("LanguageCurrentData") ||
                            r.getPrimaryKeys().containsKey("rdbCurrentData"));
                })
                .assertNext(r -> {
                    Map valueMap = r.getValues();
                    String value = (String)valueMap.get("OS01W");
                    assertEquals("F0000023YM", r.getId());
                    assertEquals(1, valueMap.size());
                    assertTrue("Canada Life Balanced T5".equals(value) ||
                            "Canada Vie Portefeuille équilibré T5".equals(value));
                    assertTrue(r.getPrimaryKeys().containsKey("LanguageCurrentData") ||
                            r.getPrimaryKeys().containsKey("rdbCurrentData"));
                })
                .verifyComplete();
    }


}
