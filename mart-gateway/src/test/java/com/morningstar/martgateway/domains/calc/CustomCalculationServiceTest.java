package com.morningstar.martgateway.domains.calc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.calc.model.CalculationFrequency;
import com.morningstar.martgateway.domains.calc.model.CalculationWindowType;
import com.morningstar.martgateway.domains.calc.model.CustomCalcDataPointRequest;
import com.morningstar.martgateway.domains.calc.model.CustomCalcRequestNew;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@RunWith(MockitoJUnitRunner.class)
public class CustomCalculationServiceTest {

	@Mock
	CurrentDataRetrievalService currentDataRetrievalService;

	@Test
	public void testRetrieveCalc(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		when(customCalculationAPICaller.post(any())).thenReturn(Mono.just(getPostResponse()));
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);

		try(MockedStatic<CustomCalculationRequestBuilder> customCalculationRequestBuilderMockedStatic =
				Mockito.mockStatic(CustomCalculationRequestBuilder.class, Mockito.CALLS_REAL_METHODS);
				MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic = Mockito.mockStatic(DataPointRepository.class, Mockito.RETURNS_DEEP_STUBS);
				MockedStatic<CustomCalcResponseParser> customCalcResponseParserMockedStatic = Mockito.mockStatic(CustomCalcResponseParser.class, Mockito.RETURNS_DEEP_STUBS)
		) {
			dataPointRepositoryMockedStatic.when(() -> DataPointRepository.getByNid("4196")).thenReturn(getDataPoint4196());
			dataPointRepositoryMockedStatic.when(() -> DataPointRepository.getByNid("4197")).thenReturn(getDataPoint4197());

			customCalculationRequestBuilderMockedStatic.when(() -> CustomCalculationRequestBuilder.createCurrentDataPointRequest(
							any(), any(),any(),any(), any(), any())).thenReturn(getCustomCalcDataPointRequestDto4196())
					.thenReturn(getCustomCalcDataPointRequestDto4197());

			Map<String, String> bmkRfValues = new HashMap<>();
			bmkRfValues.put("24712", "XIUSA04CGI");
			bmkRfValues.put("14131", "XIUSA000OC");
			Result bmkRfResult = new CurrentResult("F0GBR04B7A", bmkRfValues);
			when(currentDataRetrievalService.getCurrentData(anyList(), anyList(), anyBoolean(), anyList())).thenReturn(Flux.just(bmkRfResult));

			Map<String, String> values = new HashMap<>();
			values.put("4194", "2024-09-30");
			Result currentDataResult1 = new CurrentResult("F0GBR04B7A", values);
			Flux<Result> currentDataMockFlux = Flux.just(currentDataResult1);
			when(currentDataRetrievalService.getCurrentDataByDpsString(anyList(), anyList(), anyBoolean(), anyList())).thenReturn(currentDataMockFlux);
			customCalcResponseParserMockedStatic.when(() -> CustomCalcResponseParser.parseDocument(any(), any(), anyList()))
					.thenReturn(getResponse());

			Flux<Result> actualFlux = customCalculationService.retrieveCalc(getCalcRequest());
			StepVerifier.create(actualFlux)
					.expectSubscription()
					.assertNext(result -> {
						assertEquals("F0GBR04B7A", result.getId());
						assertEquals("0.04", result.getValues().get("4196"));
					}).assertNext(result -> {
						assertEquals("F0GBR04B7A", result.getId());
						assertEquals("0.01", result.getValues().get("4197"));
					})
					.verifyComplete();
		}
	}

	@Test
	public void testRetrieveCalcSrc(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		when(customCalculationAPICaller.post(any())).thenReturn(Mono.just(getPostCalcSrcResponse()));
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);

		try(MockedStatic<DataPointRepository> dataPointRepositoryMockedStatic = Mockito.mockStatic(DataPointRepository.class, Mockito.RETURNS_DEEP_STUBS)) {
			dataPointRepositoryMockedStatic.when(() -> DataPointRepository.getByNid("5")).thenReturn(getDataPoint5());
			dataPointRepositoryMockedStatic.when(() -> DataPointRepository.getByNid("10")).thenReturn(getDataPoint10());

			Flux<Result> actualFlux = customCalculationService.retrieveCalc(getCalcSrcRequest());
			StepVerifier.create(actualFlux)
					.expectSubscription()
					.assertNext(result -> {
						assertEquals("F0GBR04B7A", result.getId());
						assertEquals("3.146657068545604", ((V)((ArrayList)result.getValues().get("Geo Mean")).get(0)).getV());
						assertEquals("2024-1-1;2024-6-30", ((V)((ArrayList)result.getValues().get("Geo Mean")).get(0)).getI());
						assertEquals("0.8549054701817219", ((V)((ArrayList)result.getValues().get("Geo Mean")).get(1)).getV());
						assertEquals("2024-10-1;2025-3-31", ((V)((ArrayList)result.getValues().get("Geo Mean")).get(1)).getI());
					})
					.assertNext(result -> {
						assertEquals("F0GBR04B7A", result.getId());
						assertEquals("50.0", ((V)((ArrayList)result.getValues().get("Batting Average")).get(0)).getV());
						assertEquals("2024-6-1;2024-7-31", ((V)((ArrayList)result.getValues().get("Batting Average")).get(0)).getI());
						assertEquals("0.0", ((V)((ArrayList)result.getValues().get("Batting Average")).get(1)).getV());
						assertEquals("2024-9-1;2024-10-31", ((V)((ArrayList)result.getValues().get("Batting Average")).get(1)).getI());
						assertEquals("0.0", ((V)((ArrayList)result.getValues().get("Batting Average")).get(2)).getV());
						assertEquals("2024-12-1;2025-1-31", ((V)((ArrayList)result.getValues().get("Batting Average")).get(2)).getI());
					})
					.assertNext(result -> {
						assertEquals("F000015D8L", result.getId());
						assertEquals("5.1071973987533115", ((V)((ArrayList)result.getValues().get("Geo Mean")).get(0)).getV());
						assertEquals("2024-1-1;2024-6-30", ((V)((ArrayList)result.getValues().get("Geo Mean")).get(0)).getI());

					}).assertNext(result -> {
						assertEquals("F000015D8L", result.getId());
						assertEquals("50.0", ((V)((ArrayList)result.getValues().get("Batting Average")).get(0)).getV());
						assertEquals("2024-6-1;2024-7-31", ((V)((ArrayList)result.getValues().get("Batting Average")).get(0)).getI());
						assertEquals("0.0", ((V)((ArrayList)result.getValues().get("Batting Average")).get(1)).getV());
						assertEquals("2024-9-1;2024-10-31", ((V)((ArrayList)result.getValues().get("Batting Average")).get(1)).getI());
						assertEquals("50.0", ((V)((ArrayList)result.getValues().get("Batting Average")).get(2)).getV());
						assertEquals("2024-12-1;2025-1-31", ((V)((ArrayList)result.getValues().get("Batting Average")).get(2)).getI());
					})
					.verifyComplete();
		}
	}

	private Flux<Result> getResponse(){
		Map<String, String> map1 = new HashMap<>();
		map1.put("4196", "0.04");
		Result result1 = new CurrentResult("F0GBR04B7A",map1);
		Map<String, String> map2 = new HashMap<>();
		map2.put("4197", "0.01");
		Result result2 = new CurrentResult("F0GBR04B7A", map2);
		return Flux.just(result1, result2);
	}

	private String getPostResponse(){
		return "{\n"
				+ "    \"datapoints\": [\n"
				+ "        {\n"
				+ "            \"investmentId\": \"F0GBR04B7A\",\n"
				+ "            \"datapointId\": \"4196\",\n"
				+ "            \"returnCode\": 0,\n"
				+ "            \"startDate\": \"2021-10-01\",\n"
				+ "            \"results\": [\n"
				+ "                {\n"
				+ "                    \"returnCode\": 0,\n"
				+ "                    \"resultType\": 1,\n"
				+ "                    \"doubleValue\": 0.04,\n"
				+ "                    \"stringValue\": \"\"\n"
				+ "                }\n"
				+ "            ]\n"
				+ "        }\n,"
				+ "        {\n"
				+ "            \"investmentId\": \"F0GBR04B7A\",\n"
				+ "            \"datapointId\": \"4197\",\n"
				+ "            \"returnCode\": 0,\n"
				+ "            \"startDate\": \"2021-10-01\",\n"
				+ "            \"results\": [\n"
				+ "                {\n"
				+ "                    \"returnCode\": 0,\n"
				+ "                    \"resultType\": 1,\n"
				+ "                    \"doubleValue\": 0.01,\n"
				+ "                    \"stringValue\": \"\"\n"
				+ "                }\n"
				+ "            ]\n"
				+ "        }\n"
				+ "    ],\n"
				+ "    \"returnCode\": 0\n"
				+ "}";
	}


	private String getPostCalcSrcResponse(){
		return "{ \"datapoints\": [ { \"investmentId\": \"F0GBR04B7A\", \"datapointId\": \"Geo Mean\", \"returnCode\": 0, \"startDate\": \"2024-1-1\", \"results\": [ { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 3.146657068545604, \"stringValue\": \"\", \"startDate\": \"2024-1-1\", \"endDate\": \"2024-6-30\" }, { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 0.8549054701817219, \"stringValue\": \"\", \"startDate\": \"2024-10-1\", \"endDate\": \"2025-3-31\" } ] }, { \"investmentId\": \"F0GBR04B7A\", \"datapointId\": \"Batting Average\", \"returnCode\": 0, \"startDate\": \"2024-6-1\", \"results\": [ { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 50, \"stringValue\": \"\", \"startDate\": \"2024-6-1\", \"endDate\": \"2024-7-31\" }, { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 0, \"stringValue\": \"\", \"startDate\": \"2024-9-1\", \"endDate\": \"2024-10-31\" }, { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 0, \"stringValue\": \"\", \"startDate\": \"2024-12-1\", \"endDate\": \"2025-1-31\" } ] }, { \"investmentId\": \"F000015D8L\", \"datapointId\": \"Geo Mean\", \"returnCode\": 0, \"startDate\": \"2024-1-1\", \"results\": [ { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 5.1071973987533119, \"stringValue\": \"\", \"startDate\": \"2024-1-1\", \"endDate\": \"2024-6-30\" }, { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": \"NaN\", \"stringValue\": \"\", \"startDate\": \"2024-10-1\", \"endDate\": \"2025-3-31\" } ] }, { \"investmentId\": \"F000015D8L\", \"datapointId\": \"Batting Average\", \"returnCode\": 0, \"startDate\": \"2024-6-1\", \"results\": [ { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 50, \"stringValue\": \"\", \"startDate\": \"2024-6-1\", \"endDate\": \"2024-7-31\" }, { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 0, \"stringValue\": \"\", \"startDate\": \"2024-9-1\", \"endDate\": \"2024-10-31\" }, { \"returnCode\": 0, \"resultType\": 1, \"doubleValue\": 50, \"stringValue\": \"\", \"startDate\": \"2024-12-1\", \"endDate\": \"2025-1-31\" } ] } ], \"returnCode\": 0, \"message\": \"\", \"details\": \"\" }";
	}


	private CustomCalcDataPointRequest getCustomCalcDataPointRequestDto4196(){
		return CustomCalcDataPointRequest.builder().id("4196").sourceId("3001").currency("USD").frequency(CalculationFrequency.fromValue(2))
				.startDate("2021-10-01").endDate("2024-09-30").preEuroCurrency("DEM").calcId("4").isRequireFullHistory(true).isAnnualize(true).isSkipHoliday(false).windowType(CalculationWindowType.SINGLE).stepSize(0).windowSize(0).isExtendedPerformance(true).build();
	}

	private CustomCalcDataPointRequest getCustomCalcDataPointRequestDto4197(){
		return CustomCalcDataPointRequest.builder().id("4197").sourceId("3001").currency("USD").frequency(CalculationFrequency.fromValue(2))
				.startDate("2021-10-01").endDate("2024-09-30").preEuroCurrency("DEM").calcId("4").isRequireFullHistory(true).isAnnualize(true).isSkipHoliday(false).windowType(CalculationWindowType.SINGLE).stepSize(0).windowSize(0).isExtendedPerformance(true).benchmarkId("XIUSA04CGI").riskFreeProxyId("XIUSA000OC").build();
	}


	private DataPoint getDataPoint4196(){
		DataPoint dp = DataPoint.builder().id("4").nid("4").src("CALC").name("mean").build();
		DataPoint endDate = DataPoint.builder().id("DPCA178").nid("4194").src("RDB").name("Category Risk Statistics End Date (Mo-End)").build();
		DataPoint cur = DataPoint.builder().id("DPDAVL8").nid("71197").src("RDB").name("Category Base Currency Id").build();
		Calculation calc = Calculation.builder().dp(dp).endDate(endDate).cur(cur).freq("2").trailingPeriod("36").build();
		return DataPoint.builder().id("DPCA180").nid("4196").src("RDB").name("Category Arithmetic Mean 3 Yr (Mo-End)")
				.calculation(calc).build();
	}

	private DataPoint getDataPoint4197(){
		DataPoint dp = DataPoint.builder().id("4").nid("4").src("CALC").name("mean").build();
		DataPoint endDate = DataPoint.builder().id("DPCA178").nid("4194").src("RDB").name("Category Risk Statistics End Date (Mo-End)").build();
		DataPoint cur = DataPoint.builder().id("DPDAVL8").nid("71197").src("RDB").name("Category Base Currency Id").build();
		DataPoint bmk = DataPoint.builder().nid("24712").build();
		DataPoint rf = DataPoint.builder().nid("14131").build();
		Calculation calc = Calculation.builder().dp(dp).endDate(endDate).cur(cur).bmk(bmk).rf(rf).freq("2").trailingPeriod("60").build();
		return DataPoint.builder().id("DPCA181").nid("4197").src("RDB").name("Category Arithmetic Mean 5 Yr (Mo-End)")
				.calculation(calc).build();
	}

	private DataPoint getDataPoint5(){
		return DataPoint.builder().id("5").nid("5").src("CALC").name("geometry mean").build();
	}

	private DataPoint getDataPoint10(){
		return DataPoint.builder().id("10").nid("10").src("CALC").name("Batting Average").build();
	}

	private CalcRequest getCalcRequest(){
		return CalcRequest.builder().idList(List.of("F0GBR04B7A")).calcDps(List.of("4196", "4197"))
				.currency("USD").startDate("").endDate("").annualized("true").readCache("").idMappers(List.of(getIdMapper())).build();
	}


	private CalcRequest getCalcSrcRequest(){
		return CalcRequest.builder().idList(List.of("F0GBR04B7A","F000015D8L")).calcDps(List.of("5","10")).customCalcDps(List.of(getGridviewDataPoint5(), getGridviewDataPoint10())).readCache("").idMappers(List.of(getIdMapper(), getIdMapper2(), getIdMapper3())).build();
	}


	private IdMapper getIdMapper(){
		String jsonIdMapper = "{\"CategoryId\":\"EUCA000565_2_1\",\"SecurityType\":\"FO\",\"StrategyId\":\"STUSA05DGR\",\"AggregateId\":\"AG000000ZH\","
				+ "\"ShareClassId\":\"F0GBR04B7A\",\"CalculationTargetRegionId\":\"2\",\"MorningstarCategoryId\":\"EUCA000565\","
				+ "\"CountryForSale\":\"CU$$$$$BEL,CU$$$$$DEU,CU$$$$$FRA,CU$$$$$LUX,CU$$$$$SWE\",\"PerformanceId\":\"0P00000BFR\","
				+ "\"PostTaxForMixSetting\":\"0\",\"GlobalCategoryId\":\"$GC$MODALL\",\"Status\":1,\"ExchangeTradedShare\":\"0\","
				+ "\"CompanyId\":\"0C00001OJY\",\"PriceReady\":1,\"SecId\":\"F0GBR04B7A\",\"FundFamilyCode\":\"0C00001OJY\","
				+ "\"CalculationClassificationType\":\"1\",\"MasterPortfolioId\":\"55363\",\"PeerGroupId\":\"1\",\"PerformanceReady\":1,"
				+ "\"CalculationClassificationId\":\"EUCA000565\",\"FundId\":\"**********\",\"CalculationPeerGroupId\":\"1\",\"DomicileCountry\":\"LUX\"}";
		return new NonEmptyIdMapper("F0GBR04B7A", jsonIdMapper);
	}

	private IdMapper getIdMapper2(){
		String jsonIdMapper = "{\"CategoryId\":\"EUCA000565_2_1\",\"SecurityType\":\"FO\",\"StrategyId\":\"STUSA10DGR\",\"AggregateId\":\"AG000000TH\","
				+ "\"ShareClassId\":\"F0GBR04B7B\",\"CalculationTargetRegionId\":\"2\",\"MorningstarCategoryId\":\"EUCA000565\","
				+ "\"CountryForSale\":\"CU$$$$$BEL,CU$$$$$DEU,CU$$$$$FRA,CU$$$$$LUX,CU$$$$$SWE\",\"PerformanceId\":\"0P00000BGR\","
				+ "\"PostTaxForMixSetting\":\"0\",\"GlobalCategoryId\":\"$GC$MODALL\",\"Status\":1,\"ExchangeTradedShare\":\"0\","
				+ "\"CompanyId\":\"0C00001OJY\",\"PriceReady\":1,\"SecId\":\"F000015D8L\",\"FundFamilyCode\":\"0C00001OJY\","
				+ "\"CalculationClassificationType\":\"1\",\"MasterPortfolioId\":\"55363\",\"PeerGroupId\":\"1\",\"PerformanceReady\":1,"
				+ "\"CalculationClassificationId\":\"EUCA000565\",\"FundId\":\"FSGBR05879\",\"CalculationPeerGroupId\":\"1\",\"DomicileCountry\":\"LUX\"}";
		return new NonEmptyIdMapper("F000015D8L", jsonIdMapper);
	}

	private IdMapper getIdMapper3(){
		String jsonIdMapper = "{\"CategoryId\":\"EUCA000565_2_1\",\"SecurityType\":\"FO\",\"StrategyId\":\"STUSA863GR\",\"AggregateId\":\"AG000000TB\","
				+ "\"ShareClassId\":\"F0GBR04B89\",\"CalculationTargetRegionId\":\"2\",\"MorningstarCategoryId\":\"EUCA000565\","
				+ "\"CountryForSale\":\"CU$$$$$BEL,CU$$$$$DEU,CU$$$$$FRA,CU$$$$$LUX,CU$$$$$SWE\",\"PerformanceId\":\"0P00000VEH\","
				+ "\"PostTaxForMixSetting\":\"0\",\"GlobalCategoryId\":\"$GC$MODALL\",\"Status\":1,\"ExchangeTradedShare\":\"0\","
				+ "\"CompanyId\":\"0C00001OJY\",\"PriceReady\":1,\"SecId\":\"F000002XO6\",\"FundFamilyCode\":\"0C00001OJY\","
				+ "\"CalculationClassificationType\":\"1\",\"MasterPortfolioId\":\"55363\",\"PeerGroupId\":\"1\",\"PerformanceReady\":1,"
				+ "\"CalculationClassificationId\":\"EUCA000565\",\"FundId\":\"FSGBR05879\",\"CalculationPeerGroupId\":\"1\",\"DomicileCountry\":\"LUX\"}";
		return new NonEmptyIdMapper("F000002XO6", jsonIdMapper);
	}

	private GridviewDataPoint getGridviewDataPoint5() {
		return GridviewDataPoint.builder().dataPointId("5").alias("Geo Mean").frequency("q").startDate("2024-02-01").endDate("2025-01-31").currency("CAD").sourceId("HPD20").windowType("2").windowSize("2").stepSize("3").build();
	}

	private GridviewDataPoint getGridviewDataPoint10() {
		return GridviewDataPoint.builder().dataPointId("10").alias("Batting Average").frequency("m").startDate("2024-06-01").endDate("2025-01-31").sourceId("HPD10").benchmark("XIUSA04G92;XI").windowType("2").windowSize("2").stepSize("3").build();
	}

	@Test
	public void testBuildQueryWithIdList(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
		DataPoint tsTempDp = DataPoint.builder().src("TSAPI").build();
		CalcRequest calcRequest = CalcRequest.builder().idList(List.of("F0GBR04B7A")).calcDps(List.of("TSTEMP"))
				.currency("USD").startDate("").endDate("").annualized("true").readCache("").idMappers(List.of(getIdMapper())).build();
		when(currentDataRetrievalService.getCurrentDataByDpsString(anyList(), anyList(), anyBoolean(), anyList())).thenReturn(Flux.empty());
		Flux<CustomCalcRequestNew> customCalcRequestFlux = customCalculationService.buildQueryWithIdList(calcRequest, List.of(tsTempDp));

		StepVerifier.create(customCalcRequestFlux)
				.expectSubscription()
				.assertNext(res -> {
					assertTrue(res.getDatapoints().isEmpty());
				}).verifyComplete();
	}

	@Test
	public void testHandleTsDp(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
		try(MockedStatic<CustomCalculationRequestBuilder> customCalculationRequestBuilderMockedStatic = Mockito.mockStatic(CustomCalculationRequestBuilder.class)){
			CalcRequest entity = CalcRequest.builder().startDate("2020-01-01").endDate("2021-09-30").build();
			Calculation calc = Calculation.builder().freq("3").build();
			DataPoint dataPoint = DataPoint.builder().calculation(calc).build();
			CustomCalcDataPointRequest customCalcDataPointRequest = CustomCalcDataPointRequest.builder().build();
			customCalculationRequestBuilderMockedStatic.when(() -> CustomCalculationRequestBuilder.areBothNonEmptyDates(any(), any())).thenReturn(true);
			customCalculationRequestBuilderMockedStatic.when(() -> CustomCalculationRequestBuilder.getDateByFreq(any(), any())).thenReturn("2020-01-01");
			customCalculationRequestBuilderMockedStatic.when(()->CustomCalculationRequestBuilder.createTimeSeriesDataPointRequestDto(any(), any(), any(),any()))
					.thenReturn(customCalcDataPointRequest);
			Mono<CustomCalcDataPointRequest> customCalcDataPointRequestMono = customCalculationService.handleTSDp(entity, dataPoint);
			StepVerifier.create(customCalcDataPointRequestMono)
					.expectSubscription()
					.assertNext(c -> {
						assertEquals(customCalcDataPointRequest, c);
					})
					.verifyComplete();

			customCalculationRequestBuilderMockedStatic.when(() -> CustomCalculationRequestBuilder.areBothNonEmptyDates(any(), any())).thenReturn(false);
			customCalcDataPointRequestMono = customCalculationService.handleTSDp(entity, dataPoint);
			StepVerifier.create(customCalcDataPointRequestMono)
					.verifyComplete();

		}
	}

	@Test
	public void testConvertDtoToJson(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
		CustomCalcRequestNew customCalcRequestDto = CustomCalcRequestNew.builder().investmentIds(List.of("F0GBR04B7A")).build();
		Mono<String> stringMono = customCalculationService.convertRequestToJson(customCalcRequestDto);
		StepVerifier.create(stringMono)
				.expectSubscription()
				.assertNext(res -> {
					assertEquals("{\"portfolioOrInvestmentIds\":[\"F0GBR04B7A\"]}", res);
				})
				.verifyComplete();
	}

	@Test
	public void testGetDatesFromOtherGatewaysEmpty(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
		Flux<Result> resultFlux = customCalculationService.getDatesFromOtherGateways(List.of(), List.of(), false, List.of());
		StepVerifier.create(resultFlux)
				.verifyComplete();
	}

	@Test
	public void testGetDatesFromOtherGateways(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
		DataPoint endDateDp = DataPoint.builder().id("DPHS327").nid("13445").src("RDB").name("Est Net Flow Date (Qtr-end)").build();
		Calculation calc = Calculation.builder().endDate(endDateDp).build();
		DataPoint dp = DataPoint.builder().id("DPHS328").nid("13446").src("RDB").name("Est Net Flow 1 Mo (Qtr-End)").calculation(calc).build();
		ArgumentCaptor<List<String>> listArgumentCaptor = ArgumentCaptor.forClass(List.class);
		Map<String, String> values = new HashMap<>();
		values.put("13445", "2024-09-30");
		when(currentDataRetrievalService.getCurrentDataByDpsString(anyList(), anyList(), anyBoolean(), anyList())).thenReturn(Flux.just(new CurrentResult("F0GBR04B7A", values)));
		Flux<Result> resultFlux = customCalculationService.getDatesFromOtherGateways(List.of("F0GBR04B7A"), List.of(dp), false, List.of());
		StepVerifier.create(resultFlux)
				.expectSubscription()
				.assertNext(result -> {
					verify(currentDataRetrievalService).getCurrentDataByDpsString(anyList(), listArgumentCaptor.capture(), anyBoolean(), anyList());
					assertTrue(listArgumentCaptor.getValue().contains("13445"));
				})
				.verifyComplete();
	}

	@Test
	public void testGetStartAndEndDatesFromCalculationDatesEmptyDateMap(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
		Mono<Map<String, Result>> calculationDateResults = Mono.just(new HashMap<>());
		Mono<Map<String, String>> resultMono = customCalculationService.getStartAndEndDatesFromCalculationDates("F0GBR04B7A", "3", "13445", "3" , calculationDateResults);
		StepVerifier.create(resultMono)
				.verifyComplete();
	}

	@Test
	public void testGetStartAndEndDatesFromCalculationDatesEmptyFields(){
		CustomCalculationAPICaller customCalculationAPICaller = mock(CustomCalculationAPICaller.class);
		CustomCalculationService customCalculationService = new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
		Mono<Map<String, String>> resultMono = customCalculationService.getStartAndEndDatesFromCalculationDates("", "", "", "" , null);
		StepVerifier.create(resultMono)
				.verifyComplete();
	}
}
