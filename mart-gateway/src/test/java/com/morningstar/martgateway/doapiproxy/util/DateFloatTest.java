package com.morningstar.martgateway.doapiproxy.util;

import com.morningstar.martgateway.domains.apiproxy.entity.enums.FloatTypeEnum;
import com.morningstar.martgateway.util.apiproxy.DateFloatUtil;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;

public class DateFloatTest {

	@Autowired
    DateUtil dateUtil;

	 
	@Test
	public void testCalculateDateNotFloat() {
		String sourceId = "HP010";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.DO_NOT_FLOAT;
		FloatTypeEnum floatEndType = FloatTypeEnum.DO_NOT_FLOAT;
		int floatStartLag = 0;
		int floatEndLag = 0;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateDaily() {
		String sourceId = "HP010";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.FLOAT_DAILY;
		FloatTypeEnum floatEndType = FloatTypeEnum.FLOAT_DAILY;
		int floatStartLag = 1;
		int floatEndLag = 1;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateWeekly() {
		String sourceId = "HP010";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.FLOAT_WEEKLY;
		FloatTypeEnum floatEndType = FloatTypeEnum.FLOAT_WEEKLY;
		int floatStartLag = 1;
		int floatEndLag = 1;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateMonthly() {
		String sourceId = "HP010";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.FLOAT_MONTHLY;
		FloatTypeEnum floatEndType = FloatTypeEnum.FLOAT_MONTHLY;
		int floatStartLag = 1;
		int floatEndLag = 1;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateQuartorly() {
		String sourceId = "HP010";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.FLOAT_QUARTILE;
		FloatTypeEnum floatEndType = FloatTypeEnum.FLOAT_QUARTILE;
		int floatStartLag = 1;
		int floatEndLag = 1;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateYearly() {
		String sourceId = "HP010";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.FLOAT_YEARLY;
		FloatTypeEnum floatEndType = FloatTypeEnum.FLOAT_YEARLY;
		int floatStartLag = 1;
		int floatEndLag = 1;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateDaily1() {
		String sourceId = "HP002";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.DO_NOT_FLOAT;
		FloatTypeEnum floatEndType = FloatTypeEnum.DO_NOT_FLOAT;
		int floatStartLag = 0;
		int floatEndLag = 0;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateWeekly1() {
		String sourceId = "HP002";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.DO_NOT_FLOAT;
		FloatTypeEnum floatEndType = FloatTypeEnum.DO_NOT_FLOAT;
		int floatStartLag = 0;
		int floatEndLag = 0;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateMonthly1() {
		String sourceId = "HP002";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.DO_NOT_FLOAT;
		FloatTypeEnum floatEndType = FloatTypeEnum.DO_NOT_FLOAT;
		int floatStartLag = 0;
		int floatEndLag = 0;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateQuartorly1() {
		String sourceId = "HP002";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.DO_NOT_FLOAT;
		FloatTypeEnum floatEndType = FloatTypeEnum.DO_NOT_FLOAT;
		int floatStartLag = 0;
		int floatEndLag = 0;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateDateYearly1() {
		String sourceId = "HP002";
		LocalDate start = LocalDate.of(2001, 1, 1);
		LocalDate end = LocalDate.of(2020, 12, 31);
		LocalDate benchmarkDate = LocalDate.now();
		FloatTypeEnum floatStartType = FloatTypeEnum.DO_NOT_FLOAT;
		FloatTypeEnum floatEndType = FloatTypeEnum.DO_NOT_FLOAT;
		int floatStartLag = 0;
		int floatEndLag = 0;
		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertNotNull(result);
	}

	@Test
	public void testCalculateWeeklyReturn() {
		String sourceId = "HP002";
		LocalDate start = LocalDate.of(2020, 12, 1);
		LocalDate end = LocalDate.of(2021, 12, 31);
		LocalDate benchmarkDate = LocalDate.of(2022, 12, 31);
		FloatTypeEnum floatStartType = FloatTypeEnum.FLOAT_DAILY;
		FloatTypeEnum floatEndType = FloatTypeEnum.FLOAT_DAILY;
		int floatStartLag = 0;
		int floatEndLag = 0;

		LocalDate[] result = DateFloatUtil.calculateDate(sourceId,start,end,benchmarkDate,floatStartType,floatEndType,floatStartLag,floatEndLag);
		Assert.assertEquals(result[0], LocalDate.of(benchmarkDate.getYear() - 1, 11, benchmarkDate.getDayOfMonth() - 1));
		Assert.assertEquals(result[1], LocalDate.of(benchmarkDate.getYear() , benchmarkDate.getMonth(), benchmarkDate.getDayOfMonth() - 1));
	}

}