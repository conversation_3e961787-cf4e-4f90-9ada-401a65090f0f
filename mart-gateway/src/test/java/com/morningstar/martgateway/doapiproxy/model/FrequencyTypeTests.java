package com.morningstar.martgateway.doapiproxy.model;

import com.morningstar.martgateway.domains.apiproxy.entity.FrequencyTypeDaily;
import com.morningstar.martgateway.domains.apiproxy.entity.FrequencyTypeMonthly;
import com.morningstar.martgateway.domains.apiproxy.entity.FrequencyTypeWeekly;
import com.morningstar.martgateway.domains.apiproxy.entity.FrequencyTypeYearly;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import org.junit.Assert;
import org.junit.Test;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.Date;


public class FrequencyTypeTests {

    private static final String DATE_PATTERN = "yyyy-MM-dd";

    @Test
    public void testFrequencyTypeDaily() throws ParseException {
        FrequencyTypeDaily frequencyTypeDaily = new FrequencyTypeDaily();
        Date date = DateUtil.parse("2020-01-01", DATE_PATTERN);
        Date nextEndDate = frequencyTypeDaily.nextEndDate(date, "frequency");
        Assert.assertEquals(DateUtil.format(nextEndDate, DATE_PATTERN), "2020-01-02");

        LocalDate localDate = LocalDate.of(2020, 1, 1);
        LocalDate nextEndLocalDate = frequencyTypeDaily.nextEndDate(localDate, "frequency");
        Assert.assertEquals(DateUtil.formatToGeneralDate(nextEndLocalDate), "2020-01-02");
    }

    @Test
    public void testFrequencyTypeWeekly() throws ParseException {
        FrequencyTypeWeekly frequencyTypeWeekly = new FrequencyTypeWeekly();
        Date date = DateUtil.parse("2020-01-01", DATE_PATTERN);
        Date nextEndDate = frequencyTypeWeekly.nextEndDate(date, "frequency");
        Assert.assertEquals(DateUtil.format(nextEndDate, DATE_PATTERN), "2020-01-08");

        LocalDate localDate = LocalDate.of(2020, 1, 1);
        LocalDate nextEndLocalDate = frequencyTypeWeekly.nextEndDate(localDate, "frequency");
        Assert.assertEquals(DateUtil.formatToGeneralDate(nextEndLocalDate), "2020-01-08");
    }

    @Test
    public void testFrequencyTypeMonthly() throws ParseException {
        FrequencyTypeMonthly frequencyTypeMonthly = new FrequencyTypeMonthly();
        Date date = DateUtil.parse("2020-01-01", DATE_PATTERN);
        Date nextEndDate = frequencyTypeMonthly.nextEndDate(date, "frequency");
        Assert.assertEquals(DateUtil.format(nextEndDate, DATE_PATTERN), "2020-02-01");

        LocalDate localDate = LocalDate.of(2020, 1, 1);
        LocalDate nextEndLocalDate = frequencyTypeMonthly.nextEndDate(localDate, "frequency");
        Assert.assertEquals(DateUtil.formatToGeneralDate(nextEndLocalDate), "2020-02-01");
    }

    @Test
    public void testFrequencyTypeYearly() throws ParseException {
        FrequencyTypeYearly frequencyTypeYearly = new FrequencyTypeYearly();
        Date date = DateUtil.parse("2020-01-01", DATE_PATTERN);
        Date nextEndDate = frequencyTypeYearly.nextEndDate(date, "frequency");
        Assert.assertEquals(DateUtil.format(nextEndDate, DATE_PATTERN), "2021-01-01");

        LocalDate localDate = LocalDate.of(2020, 1, 1);
        LocalDate nextEndLocalDate = frequencyTypeYearly.nextEndDate(localDate, "frequency");
        Assert.assertEquals(DateUtil.formatToGeneralDate(nextEndLocalDate), "2021-01-01");
    }
}
