package com.morningstar.martgateway.doapiproxy.util;

import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import com.morningstar.martgateway.util.apiproxy.StringUtil;
import io.vavr.control.Option;
import org.junit.Assert;
import org.junit.Test;

import java.util.Calendar;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertAll;

public class StringUtilTest {


	@Test
	public void testGetFirstNotBlank() {
		Assert.assertEquals(Option.none(), StringUtil.getFirstNotBlank(null));
		Assert.assertEquals(Option.none(), StringUtil.getFirstNotBlank(null, "", " "));
		Assert.assertEquals(Option.none(), StringUtil.getFirstNotBlank(" ", "", " "));
		Assert.assertEquals(Option.some("hello"), StringUtil.getFirstNotBlank("hello", null));
		Assert.assertEquals(Option.some("hello"), StringUtil.getFirstNotBlank(" ", "hello", null));
		Assert.assertEquals(Option.some("hello"), StringUtil.getFirstNotBlank(" ", "hello", "hi"));
	}

	@Test
	public void testStringUtil() {

		Assert.assertEquals(StringUtil.isInteger("1"), true);
		Assert.assertEquals(StringUtil.isInteger("-1"), true);
		Assert.assertEquals(StringUtil.isDouble("1"), false);
		Assert.assertEquals(StringUtil.isDouble("-1"), false);
		Assert.assertEquals(StringUtil.isInteger("1.000"), false);
		Assert.assertEquals(StringUtil.isDouble("1.000"), true);
		Assert.assertEquals(StringUtil.isDouble("-1.000"), true);

		Assert.assertEquals(StringUtil.isInteger(""), false);
		Assert.assertEquals(StringUtil.isInteger(null), false);
		Assert.assertEquals(StringUtil.isDouble(null), false);
		Assert.assertEquals(StringUtil.isDouble(""), false);
		Assert.assertEquals(StringUtil.isInteger("AAA"), false);
		Assert.assertEquals(StringUtil.isInteger("1986-10-21"), false);
		Assert.assertEquals(StringUtil.isDouble("1986-10-21"), false);


	}

	@Test
	public void getFirstNotBlank() {
		assertAll(
				() -> Assert.assertEquals(Option.none(), StringUtil.getFirstNotBlank(null)),
				() -> Assert.assertEquals("aa", StringUtil.getFirstNotBlank(null, null, "aa", null, "bb").get())
		);
	}


	@Test
	public void isNullOrEmpty() {
		assertAll(
				() -> Assert.assertTrue(StringUtil.isNullOrEmpty("")),
				() -> Assert.assertTrue(StringUtil.isNullOrEmpty(null)),
				() -> Assert.assertTrue(StringUtil.isNullOrEmpty("    			      "))
		);
	}

	@Test
	public void splitString() {
		assertAll(
				() -> Assert.assertTrue(StringUtil.splitString(null, 10, ",").isEmpty()),
				() -> Assert.assertTrue(StringUtil.splitString("", 10, ",").isEmpty()),
				() -> assertAll(
						() -> Assert.assertEquals(1, StringUtil.splitString("aa", 100, ",").size()),
						() -> Assert.assertEquals("aa", StringUtil.splitString("aa", 100, ",").get(0)),
						() -> Assert.assertEquals(1, StringUtil.splitString("aa", 0, ",").size()),
						() -> Assert.assertEquals("aa", StringUtil.splitString("aa", 0, ",").get(0)),
						() -> Assert.assertEquals(1, StringUtil.splitString("aa", 1, ",").size()),
						() -> Assert.assertEquals("aa", StringUtil.splitString("aa", 1, ",").get(0))
				),
				() -> {
					List<String> strings = StringUtil.splitString("aaaa,bbbb,cccc", 6, ",");
					Assert.assertEquals(2, strings.size());
					Assert.assertEquals("aaaa,bbbb", strings.get(0));
					Assert.assertEquals("cccc", strings.get(1));
				}
		);
	}

	@Test
	public void contains() {
		assertAll(
				() -> Assert.assertFalse(StringUtil.contains(null, "a")),
				() -> Assert.assertFalse(StringUtil.contains("", "a")),
				() -> Assert.assertFalse(StringUtil.contains("a", null)),
				() -> Assert.assertFalse(StringUtil.contains("a", "")),
				() -> Assert.assertFalse(StringUtil.contains("b", "a")),
				() -> Assert.assertTrue(StringUtil.contains("ab", "a"))
		);
	}

	@Test
	public void base64() {
		assertAll(
				() -> Assert.assertEquals("ZG9hcGk=", StringUtil.base64Encode("doapi")),
				() -> Assert.assertEquals("doapi", StringUtil.base64Decode("ZG9hcGk="))
		);

	}

	@Test
	public void toAnyType() {
		Assert.assertEquals(StringUtil.toAnyType("1"), 1L);
		Assert.assertEquals(StringUtil.toAnyType("1.000"), 1.000);
		Assert.assertEquals(StringUtil.toAnyType("-1.000"), -1.000);

		Assert.assertEquals(StringUtil.toAnyType("AAA"), "AAA");
		Assert.assertEquals(StringUtil.toAnyType("AAA123"), "AAA123");
		Assert.assertEquals(StringUtil.toAnyType("1986-10-21"), "1986-10-21");

	}

	@Test
	public void replaceLineBreaksTest() {
		Assert.assertEquals("", StringUtil.replaceLineBreaks(null));
		Assert.assertEquals(StringUtil.replaceLineBreaks(""), "");
		Assert.assertEquals(StringUtil.replaceLineBreaks("Morningstar \\nRating \\nOverall"), "Morningstar Rating Overall");
	}

	@Test
	public void testEncodeKeyword() {
		Assert.assertNotNull(StringUtil.encodeKeyword("test keyword"));
	}

	@Test
	public void testStringUtil2() {
		Assert.assertNotNull(StringUtil.getFirstNotBlank((String) null));

		Assert.assertNotNull(StringUtil.getFirstNotBlank("test"));

		Assert.assertNotNull(StringUtil.b64decode("test"));

		Assert.assertNotNull(StringUtil.splitString(null, 10, ","));

		Assert.assertNotNull(StringUtil.splitString("1111", 5, ","));

		List<String> actualList = StringUtil.splitString("11111,22222,33333,44444,55555,66666,77777,88888", 5, ",");
		Assert.assertEquals(8, actualList.size());

		Assert.assertNotNull(StringUtil.base64Encode("test"));

		Assert.assertNotNull(StringUtil.toAnyType("1"));

		Assert.assertNotNull(StringUtil.toAnyType("1.21222"));

		Assert.assertNotNull(StringUtil.toAnyType("test"));

		Assert.assertTrue(!StringUtil.isInteger(null));

		Assert.assertTrue(StringUtil.isInteger("13123213"));

		Assert.assertTrue(!StringUtil.isDouble(null));

		Assert.assertTrue(StringUtil.isDouble("1.21222"));

		Assert.assertEquals("Annual Ret " + (Calendar.getInstance().get(Calendar.YEAR)-9), StringUtil.formatDpName("Annual Ret {year-8}"));

		Calendar date = Calendar.getInstance();
		date.add(Calendar.MONTH, -7);
		Assert.assertEquals("NAV \n" + DateUtil.format(date.getTime(), "yyyy-MM"), StringUtil.formatDpName("NAV \n{month-6}"));

		Assert.assertEquals("", StringUtil.formatDpName(null));
	}
	
	@Test
	public void testFormatDpName() {
		Assert.assertEquals("", StringUtil.formatDpName(null));
		Assert.assertEquals("Morningstar Rating Overall", StringUtil.formatDpName("Morningstar \\nRating \\nOverall"));
		Assert.assertEquals("Inflation Adjusted Ret Annlzd 15 Yr (Qtr-End)",
				StringUtil.formatDpName("Inflation Adjusted \\nRet Annlzd 15 Yr \\n(Qtr-End)"));

		Assert.assertEquals(String.format("Annual Ret %s", Calendar.getInstance().get(Calendar.YEAR) - 9),
				StringUtil.formatDpName("Annual Ret {year-8}"));
		Assert.assertEquals(String.format("Annual Inflation Adjusted Ret %s", Calendar.getInstance().get(Calendar.YEAR) - 1),
				StringUtil.formatDpName("Annual Inflation \\nAdjusted Ret \\n{year}"));
		Assert.assertEquals(String.format("Annual Inflation Adjusted Ret %s", Calendar.getInstance().get(Calendar.YEAR) - 4),
				StringUtil.formatDpName("Annual Inflation \\nAdjusted Ret \\n{year-3}"));
		Assert.assertEquals(String.format("Annual Inflation Adjusted Ret %s", Calendar.getInstance().get(Calendar.YEAR) - 11),
				StringUtil.formatDpName("Annual Inflation \\nAdjusted Ret \\n{year-10}"));

		Calendar date = Calendar.getInstance();
		date.add(Calendar.MONTH, -1);
		Assert.assertEquals(String.format("Monthly Return %s", DateUtil.format(date.getTime(), "yyyy-MM")),
				StringUtil.formatDpName("Monthly Return \\n{month}"));
		date = Calendar.getInstance();
		date.add(Calendar.MONTH, -3);
		Assert.assertEquals(String.format("Monthly Return %s", DateUtil.format(date.getTime(), "yyyy-MM")),
				StringUtil.formatDpName("Monthly Return \\n{month-2}"));
		date = Calendar.getInstance();
		date.add(Calendar.MONTH, -12);
		Assert.assertEquals(String.format("Monthly Return %s", DateUtil.format(date.getTime(), "yyyy-MM")),
				StringUtil.formatDpName("Monthly Return \\n{month-11}"));
	}
	
	@Test
	public void testGetContainYearInName() {
		Assert.assertNull(StringUtil.getContainYearInName("Morningstar \\nRating \\nOverall"));
		Assert.assertEquals("2020", StringUtil.getContainYearInName("Annual Ret 2020"));
	}
	
	@Test
	public void testIsOwnershipSecurity() {
		Assert.assertTrue(StringUtil.isOwnershipSecurity("1053860   ;MP"));
		Assert.assertTrue(StringUtil.isOwnershipSecurity("1053860 "));
		Assert.assertFalse(StringUtil.isOwnershipSecurity("FOUSA06RJ6;XI"));
		Assert.assertFalse(StringUtil.isOwnershipSecurity("FOUSA06RJ6"));
	}

}
