package com.morningstar.martgateway.doapiproxy.util.timeperiod;

import com.morningstar.martgateway.domains.apiproxy.entity.enums.DpSettingFrequency;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.FloatTypeEnum;
import com.morningstar.martgateway.domains.apiproxy.exceptions.DOAPIException;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointForColumns;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointForColumnsResponse;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import com.morningstar.martgateway.util.apiproxy.PairToEntityUtil;
import com.morningstar.martgateway.util.apiproxy.timeperiod.TimeSerialsCalculator;
import io.vavr.control.Try;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class TimeSerialsCalculatorTest {

	TimeSerialsCalculator timeSerialsCalculator;

    @Test(expected = DOAPIException.class)
    public void testCalcTotalColumns_startDate_endDateNull() {
        List<DataPointForColumns> dataPointList = new ArrayList<>();
        DataPointForColumns dataPointAllNull = DataPointForColumns.builder()
                .datapointId("42")
                .startDate(null)
                .endDate(null)
                .frequency("m")
                .build();
        dataPointList.add(dataPointAllNull);
        TimeSerialsCalculator.calcTotalColumns(dataPointList);
    }

    private List<Pair<String, String>> getResult(String sourceId, String dataPointId, String alias, String startDate, String endDate,
                                                 String benchmarkDate, String floatStart, String floatEnd, Integer startDelay, Integer endDelay, String windowType, Integer windowSize, Integer stepSize, String frequency, boolean isCustomCalc, boolean isTsdp) {
        List<DataPointForColumns> dataPointList = new ArrayList<>();
        DataPointForColumns dataPoint = DataPointForColumns.builder()
                .datapointId(dataPointId)
                .alias(alias)
                .startDate(startDate)
                .endDate(endDate)
                .sourceId(sourceId)
                .benchmarkDate(benchmarkDate==null? DateUtil.formatToGeneralDate(new Date()):benchmarkDate)
                .floatStart(floatStart)
                .floatEnd(floatEnd)
                .startDelay(startDelay)
                .endDelay(endDelay)
                .windowType(windowType)
                .windowSize(windowSize)
                .stepSize(stepSize)
                .frequency(frequency)
                .isCustomCalc(isCustomCalc)
                .isTsdp(isTsdp)
                .build();

        dataPointList.add(dataPoint);

        List<DataPointForColumnsResponse> dataPointResponseList = TimeSerialsCalculator.calcTotalColumns(dataPointList);
        List<Pair<String, String>> resp = new ArrayList<>();
        for (DataPointForColumnsResponse dataPointResponse : dataPointResponseList) {
        	if(dataPointResponse.getDatePairs() != null)
            	resp = PairToEntityUtil.startEndDate2Pair(dataPointResponse.getDatePairs());
        }
        return resp;
    }

    @Test
    public void testCalcTotalColumns() {
        List<Pair<String, String>> resp = getResult("HP010", "42", "test", "2019-10-31", "2020-03-01",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(), 0, 0, "1", 0, 0, DpSettingFrequency.MONTHLY.getShortName(), false, true);
        Assert.assertEquals(1, resp.size());
        Assert.assertEquals("2019-10-01", resp.get(0).getLeft());
        Assert.assertEquals("2020-03-31", resp.get(0).getRight());
    }

	@Test
	public void testCalcTotalColumns_Float() {
		List<Pair<String, String>> resp = getResult("HP010", "42", "test", "2019-10-31", "2020-03-01",
				"2020-07-01", FloatTypeEnum.FLOAT_MONTHLY.getId(), FloatTypeEnum.FLOAT_MONTHLY.getId(), 0, 0, "1", 0, 0, DpSettingFrequency.MONTHLY.getShortName(), false, true);
		Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2020-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2020-07-31", resp.get(0).getRight());
	}

    @Test
    public void testGetTimeSerialsDatePairsForDaily() {
        List<Pair<String, String>> resp = getResult("", "42", "Std Dev", "2020-04-18", "2020-04-20",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0, "2", 1, 1, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(3, resp.size());
        Assert.assertEquals("2020-04-18", resp.get(0).getLeft());
        Assert.assertEquals("2020-04-18", resp.get(0).getRight());
        Assert.assertEquals("2020-04-19", resp.get(1).getLeft());
        Assert.assertEquals("2020-04-19", resp.get(1).getRight());
        Assert.assertEquals("2020-04-20", resp.get(2).getLeft());
        Assert.assertEquals("2020-04-20", resp.get(2).getRight());
    }

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt1_ws0_s0() { // windowType=1
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "1", 0, 0, DpSettingFrequency.DAILY.getShortName(),
                true, true);
		Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws1_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.DAILY.getShortName(),
                true, true);
		Assert.assertEquals(731, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2000-01-01", resp.get(0).getRight());
		Assert.assertEquals("2000-01-02", resp.get(1).getLeft());
		Assert.assertEquals("2000-01-02", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws1_s2() { // windowType=2
        List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 2, DpSettingFrequency.DAILY.getShortName(),
                true, true);
		Assert.assertEquals(366, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2000-01-01", resp.get(0).getRight());
		Assert.assertEquals("2000-01-03", resp.get(1).getLeft());
		Assert.assertEquals("2000-01-03", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws1_s3() { // windowType=2
        List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 3, DpSettingFrequency.DAILY.getShortName(),
                true, true);
		Assert.assertEquals(244, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2000-01-01", resp.get(0).getRight());
		Assert.assertEquals("2000-01-04", resp.get(1).getLeft());
		Assert.assertEquals("2000-01-04", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws2_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2017-01-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 2, 1, DpSettingFrequency.DAILY.getShortName(),
                true, true);
		Assert.assertEquals(30, resp.size()); // 21
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-01-02", resp.get(0).getRight());
		Assert.assertEquals("2017-01-02", resp.get(1).getLeft());
		Assert.assertEquals("2017-01-03", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws2_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2017-01-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 2, 2, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(15, resp.size()); // 11
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-01-02", resp.get(0).getRight());
		Assert.assertEquals("2017-01-03", resp.get(1).getLeft());
		Assert.assertEquals("2017-01-04", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws2_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 2, 3, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(244, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2000-01-02", resp.get(0).getRight());
		Assert.assertEquals("2000-01-04", resp.get(1).getLeft());
		Assert.assertEquals("2000-01-05", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt3_ws0_s1() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-01",
                "2019-01-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 1, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(31, resp.size());
		Assert.assertEquals("2019-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-01", resp.get(0).getRight());
		Assert.assertEquals("2019-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-01-02", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt3_ws0_s2() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-01",
                "2019-01-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 2, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(15, resp.size());
		Assert.assertEquals("2019-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-02", resp.get(0).getRight());
		Assert.assertEquals("2019-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-01-04", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt3_ws0_s3() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-01",
                "2019-01-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 3, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(10, resp.size());
		Assert.assertEquals("2019-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-03", resp.get(0).getRight());
		Assert.assertEquals("2019-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-01-06", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt4_ws0_s1() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 1, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(731, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(0).getRight());
		Assert.assertEquals("2000-01-02", resp.get(1).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt4_ws0_s2() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 2, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(365, resp.size());
		Assert.assertEquals("2000-01-02", resp.get(0).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(0).getRight());
		Assert.assertEquals("2000-01-04", resp.get(1).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt4_ws0_s3() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 3, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(243, resp.size());
		Assert.assertEquals("2000-01-03", resp.get(0).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(0).getRight());
		Assert.assertEquals("2000-01-06", resp.get(1).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(1).getRight());

	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws7_s7() { // windowType=2
		List<Pair<String, String>> resp = getResult("HS793",
                "1007", "Bull Beta",
                "2020-06-28",
                "2020-07-04",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 7, 7, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2020-06-28", resp.get(0).getLeft());
		Assert.assertEquals("2020-07-04", resp.get(0).getRight());

	}

	@Test
	public void testGetTimeSerialsDatePairsForDailyCCSwt2_ws7_s7_0() { // windowType=2
		List<Pair<String, String>> resp = getResult("HS793",
                "1007", "Bull Beta",
                "2020-06-28",
                "2020-07-05",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 7, 7, DpSettingFrequency.DAILY.getShortName(),
                true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2020-06-28", resp.get(0).getLeft());
		Assert.assertEquals("2020-07-04", resp.get(0).getRight());

	}

	@Test
	public void testGetTimeSerialsDatePairsForWeekly() {
		List<Pair<String, String>> resp = getResult("HS793",
                "1007", "Bull Beta",
                "2020-04-05",
                "2020-05-09",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(5, resp.size());
		Assert.assertEquals("2020-04-05", resp.get(0).getLeft());
		Assert.assertEquals("2020-04-11", resp.get(0).getRight());
		Assert.assertEquals("2020-04-12", resp.get(1).getLeft());
		Assert.assertEquals("2020-04-18", resp.get(1).getRight());
		Assert.assertEquals("2020-04-19", resp.get(2).getLeft());
		Assert.assertEquals("2020-04-25", resp.get(2).getRight());
		Assert.assertEquals("2020-04-26", resp.get(3).getLeft());
		Assert.assertEquals("2020-05-02", resp.get(3).getRight());

	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt1_ws0_s0() { // windowType=1
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "1", 0, 0, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-28", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt2_ws1_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(51, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-12", resp.get(0).getRight());
		Assert.assertEquals("2019-01-13", resp.get(1).getLeft());
		Assert.assertEquals("2019-01-19", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt2_ws1_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 2, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(26, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-12", resp.get(0).getRight());
		Assert.assertEquals("2019-01-20", resp.get(1).getLeft());
		Assert.assertEquals("2019-01-26", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt2_ws1_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 3, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(17, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-12", resp.get(0).getRight());
		Assert.assertEquals("2019-01-27", resp.get(1).getLeft());
		Assert.assertEquals("2019-02-02", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt3_ws0_s1() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 1, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(51, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-12", resp.get(0).getRight());
		Assert.assertEquals("2019-01-06", resp.get(1).getLeft());
		Assert.assertEquals("2019-01-19", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt3_ws0_s2() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 2, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(25, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-19", resp.get(0).getRight());
		Assert.assertEquals("2019-01-06", resp.get(1).getLeft());
		Assert.assertEquals("2019-02-02", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt3_ws0_s3() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 3, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(17, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-01-26", resp.get(0).getRight());
		Assert.assertEquals("2019-01-06", resp.get(1).getLeft());
		Assert.assertEquals("2019-02-16", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt4_ws0_s1() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 1, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(106, resp.size());
		Assert.assertEquals("1999-12-26", resp.get(0).getLeft());
		Assert.assertEquals("2002-01-05", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForWeeklyCCSwt4_ws0_s3() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2019-01-06",
                "2019-12-28",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 3, DpSettingFrequency.WEEKLY.getShortName(),
                true, true);
        Assert.assertEquals(17, resp.size());
		Assert.assertEquals("2019-01-06", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-28", resp.get(0).getRight());
		Assert.assertEquals("2019-01-27", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-28", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthly2() {
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2020-01-15",
                "2020-04-20",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.MONTHLY.getShortName(),
                true, true);
        Assert.assertEquals(4, resp.size());
		Assert.assertEquals("2020-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2020-01-31", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthly() {
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2020-01-15",
                "2020-04-20",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.MONTHLY.getShortName(),
                true, true);
        Assert.assertEquals(4, resp.size());
		Assert.assertEquals("2020-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2020-01-31", resp.get(0).getRight());
		Assert.assertEquals("2020-02-01", resp.get(1).getLeft());
		Assert.assertEquals("2020-02-29", resp.get(1).getRight());
		Assert.assertEquals("2020-03-01", resp.get(2).getLeft());
		Assert.assertEquals("2020-03-31", resp.get(2).getRight());
		Assert.assertEquals("2020-04-01", resp.get(3).getLeft());
		Assert.assertEquals("2020-04-30", resp.get(3).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt1_ws0_s0() { // windowType=1
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2018-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "1", 0, 0, DpSettingFrequency.MONTHLY.getShortName(),
                true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2018-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws1_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.MONTHLY.getShortName(),
                true, true);
        Assert.assertEquals(36, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-01-31", resp.get(0).getRight());
		Assert.assertEquals("2017-02-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-02-28", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws1_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 2, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(18, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-01-31", resp.get(0).getRight());
		Assert.assertEquals("2017-03-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws1_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 3, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(12, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-01-31", resp.get(0).getRight());
		Assert.assertEquals("2017-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-04-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws2_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 2, 1, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(35, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-02-28", resp.get(0).getRight());
		Assert.assertEquals("2017-02-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws2_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 2, 2, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(18, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-02-28", resp.get(0).getRight());
		Assert.assertEquals("2017-03-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-04-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws2_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 2, 3, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(12, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-02-28", resp.get(0).getRight());
		Assert.assertEquals("2017-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-05-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws3_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 3, 1, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(34, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(0).getRight());
		Assert.assertEquals("2017-02-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-04-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws3_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 3, 2, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(17, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(0).getRight());
		Assert.assertEquals("2017-03-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-05-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws3_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 3, 3, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(12, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(0).getRight());
		Assert.assertEquals("2017-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-06-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws12_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 12, 1, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(25, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-02-01", resp.get(1).getLeft());
		Assert.assertEquals("2018-01-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws12_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 12, 2, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(13, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-03-01", resp.get(1).getLeft());
		Assert.assertEquals("2018-02-28", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt2_ws12_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 12, 3, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(9, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2018-03-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt3_ws0_s1() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 1, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(36, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-01-31", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-02-28", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt3_ws0_s2() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 2, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(18, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-02-28", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-04-30", resp.get(1).getRight());

	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt3_ws0_s3() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 3, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(12, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-06-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt3_ws0_s4() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 4, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(9, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-04-30", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-08-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt3_ws0_s5() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 5, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(7, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-05-31", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-10-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt4_ws0_s1() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 1, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(36, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-02-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt4_ws0_s2() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 2, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(18, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-03-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt4_ws0_s3() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 3, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(12, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForMonthlyWithCCSwt4_ws0_s4() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 4, DpSettingFrequency.MONTHLY.getShortName(),
				true, true);
        Assert.assertEquals(9, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-05-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterly() {
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2020-01-15",
                "2020-04-20",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(2, resp.size());
		Assert.assertEquals("2020-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2020-03-31", resp.get(0).getRight());
		Assert.assertEquals("2020-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2020-06-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt1_ws0_s0() { // windowType=1
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "1", 0, 0, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt2_ws1_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(12, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(0).getRight());
		Assert.assertEquals("2017-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-06-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCwt2_ws1_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 2, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(6, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(0).getRight());
		Assert.assertEquals("2017-07-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-09-30", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCwt2_ws1_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2016-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 3, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(6, resp.size());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt3_ws0_s1() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 1, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(12, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-03-31", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-06-30", resp.get(1).getRight());
		Assert.assertEquals("2017-01-01", resp.get(2).getLeft());
		Assert.assertEquals("2017-09-30", resp.get(2).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt3_ws0_s2() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 2, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(6, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-06-30", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2017-12-31", resp.get(1).getRight());
		Assert.assertEquals("2017-01-01", resp.get(2).getLeft());
		Assert.assertEquals("2018-06-30", resp.get(2).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt3_ws0_s3() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 3, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(4, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-09-30", resp.get(0).getRight());
		Assert.assertEquals("2017-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2018-06-30", resp.get(1).getRight());

	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt4_ws0_s1() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 1, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(8, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(0).getRight());
		Assert.assertEquals("2000-04-01", resp.get(1).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(1).getRight());
		Assert.assertEquals("2000-07-01", resp.get(2).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(2).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt4_ws0_s2() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 2, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(6, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-07-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairsForQuarterlyCCSwt4_ws0_s3() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 3, DpSettingFrequency.QUARTERLY.getShortName(),
				true, true);
        Assert.assertEquals(4, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2017-10-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearly() {
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2020-01-15",
                "2020-04-20",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "1", 0, 0, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2020-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2020-12-31", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt1_ws0_s0() { // windowType=1
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2001-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "1", 0, 0, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2001-12-31", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt2_ws1_s1() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 1, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(3, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-12-31", resp.get(0).getRight());
		Assert.assertEquals("2018-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2018-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt2_ws1_s2() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 2, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(2, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-12-31", resp.get(0).getRight());
		Assert.assertEquals("2019-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt2_ws1_s3() { // windowType=2
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2017-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "2", 1, 3, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(1, resp.size());
		Assert.assertEquals("2017-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2017-12-31", resp.get(0).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt3_ws0_s1() { // windowType=3
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2018-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "3", 0, 1, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(2, resp.size());
		Assert.assertEquals("2018-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2018-12-31", resp.get(0).getRight());
		Assert.assertEquals("2018-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt4_ws0_s1() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 1, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(20, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2001-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt4_ws0_s2() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 2, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(10, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2002-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt4_ws0_s3() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 3, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(6, resp.size());
		Assert.assertEquals("2002-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2005-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test
	public void testGetTimeSerialsDatePairForYearlyCCSwt4_ws0_s4() { // windowType=4
		List<Pair<String, String>> resp = getResult("HP010",
                "1007", "Bull Beta",
                "2000-01-01",
                "2019-12-31",
                null, FloatTypeEnum.DO_NOT_FLOAT.getId(), FloatTypeEnum.DO_NOT_FLOAT.getId(),
                0, 0,
                "4", 0, 4, DpSettingFrequency.YEARLY.getShortName(),
				true, true);
        Assert.assertEquals(5, resp.size());
		Assert.assertEquals("2000-01-01", resp.get(0).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(0).getRight());
		Assert.assertEquals("2004-01-01", resp.get(1).getLeft());
		Assert.assertEquals("2019-12-31", resp.get(1).getRight());
	}

	@Test(expected = InvocationTargetException.class)
	public void testCheckStartDateEndDateDataPointNullException() throws Exception {
		List<DataPointForColumns> dataPointList = new ArrayList<>();
		dataPointList.add(DataPointForColumns.builder().datapointId(null).alias("Test").build());
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkStartDateEndDate", List.class);
		method.setAccessible(true);
		method.invoke(timeSerialsCalculator, dataPointList);
	}

	@Test(expected = InvocationTargetException.class)
	public void testCheckStartDateEndDateAliasNullException() throws Exception {
		List<DataPointForColumns> dataPointList = new ArrayList<>();
		dataPointList.add(DataPointForColumns.builder().datapointId("Test").alias(null).build());
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkStartDateEndDate", List.class);
		method.setAccessible(true);
		method.invoke(timeSerialsCalculator, dataPointList);
	}

	@Test
	public void testCheckStartDateEndDateCustomCalc() throws Exception {
		List<DataPointForColumns> dataPointList = new ArrayList<>();
		dataPointList.add(DataPointForColumns.builder().datapointId("42").alias("Test")
				.frequency("m")
				.startDate("2020-01-01")
				.endDate("2020-07-20")
				.windowType("1").build());
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkStartDateEndDate", List.class);
		method.setAccessible(true);
		method.invoke(timeSerialsCalculator, dataPointList);
		Assert.assertEquals(1, dataPointList.size());
	}

	@Test
	public void testCheckStartDateEndDateTsDp() throws Exception {
		List<DataPointForColumns> dataPointList = new ArrayList<>();
		dataPointList.add(DataPointForColumns.builder().datapointId("F0001").alias("Test")
				.frequency("m")
				.startDate("2020-01-01")
				.endDate("2020-07-20")
				.windowType("1").build());
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkStartDateEndDate", List.class);
		method.setAccessible(true);
		method.invoke(timeSerialsCalculator, dataPointList);
		Assert.assertEquals(1, dataPointList.size());
	}

	@Test
	public void testCheckStartDateEndDateSingleDataPoint() throws Exception {
		List<DataPointForColumns> dataPointList = new ArrayList<>();
		dataPointList.add(DataPointForColumns.builder().datapointId("F0001").alias("Test")
				.build());
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkStartDateEndDate", List.class);
		method.setAccessible(true);
		method.invoke(timeSerialsCalculator, dataPointList);
		Assert.assertEquals(1, dataPointList.size());
	}

	@Test(expected = InvocationTargetException.class)
	public void testCheckStartDateEndDate1() throws Exception {
		List<DataPointForColumns> dataPointList = new ArrayList<>();
		dataPointList.add(DataPointForColumns.builder().isTsdp(true). datapointId("42").alias("Test")
				.build());
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkStartDateEndDate", List.class);
		method.setAccessible(true);
		method.invoke(timeSerialsCalculator, dataPointList);
	}

	@Test
	public void testCheckStartDateEndDate2() throws Exception {
		List<DataPointForColumns> dataPointList = new ArrayList<>();
		dataPointList.add(DataPointForColumns.builder().isTsdp(true).datapointId("42").alias("Test")
				.startDate("2020-07-21").endDate("2020-01-01").build());
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkStartDateEndDate", List.class);
		method.setAccessible(true);
		method.invoke(timeSerialsCalculator, dataPointList);
		Assert.assertEquals("1", dataPointList.get(0).getWindowType());
	}
	
	@Test(expected = DOAPIException.class)
	public void testCheckConditions1() throws Throwable {
		DataPointForColumns dpc = DataPointForColumns.builder().isTsdp(true).datapointId("42").alias("Test")
				.startDate("2020-07-21").endDate(null).build();
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkConditions", DataPointForColumns.class);
		method.setAccessible(true);
		Try.run(() -> method.invoke(timeSerialsCalculator, dpc)).getOrElseThrow(Throwable::getCause);
	}
	
	@Test(expected = DOAPIException.class)
	public void testCheckConditions2() throws Throwable {
		DataPointForColumns dpc = DataPointForColumns.builder().isTsdp(true).datapointId("42").alias("Test")
				.startDate("2020-01-01").endDate("2020-12-31").frequency("w").sourceId(null).build();
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkConditions", DataPointForColumns.class);
		method.setAccessible(true);
		Try.run(() -> method.invoke(timeSerialsCalculator, dpc)).getOrElseThrow(Throwable::getCause);
	}
	
	@Test(expected = DOAPIException.class)
	public void testCheckConditions3() throws Throwable {
		DataPointForColumns dpc = DataPointForColumns.builder().isTsdp(true).datapointId("42").alias("Test")
				.startDate("2020-01-01").endDate("2020-12-31").isCustomCalc(true).windowType("2").build();
		Method method = TimeSerialsCalculator.class.getDeclaredMethod("checkConditions", DataPointForColumns.class);
		method.setAccessible(true);
		Try.run(() -> method.invoke(timeSerialsCalculator, dpc)).getOrElseThrow(Throwable::getCause);
	}

}
