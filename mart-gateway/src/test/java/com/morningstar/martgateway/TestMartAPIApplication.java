package com.morningstar.martgateway;

import com.morningstar.martgateway.util.ExcludeFromTests;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@ComponentScan(excludeFilters = @ComponentScan.Filter(type = FilterType.ANNOTATION,
        value = ExcludeFromTests.class))
@SpringBootApplication
public class TestMartAPIApplication {
    public static void main(String[] args) {
        SpringApplication.run(TestMartAPIApplication.class, args);
    }
}