package com.morningstar.martgateway.applications.rdb;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import com.morningstar.martgateway.domains.rdb.cache.TsDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.TsFrequencyDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsData;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsDataList;
import com.morningstar.martgateway.domains.rdb.helper.RdbTsCacheDataHelper;
import com.morningstar.martgateway.domains.rdb.service.RdbTsService;
import com.morningstar.martgateway.domains.rdb.helper.RdbCurrentCacheDataHelper;
import com.morningstar.martgateway.domains.rdb.service.RdbCurrentService;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataRepo;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataAsyncRepo;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.context.ApplicationEventPublisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RdbGatewayTest {

    @Mock
    RdbDataRepo rdbDataRepo;

    @Mock
    RdbCurrentCacheDataHelper rdbCurrentCacheDataHelper;

    @Mock
    RdbTsCacheDataHelper rdbTsCacheDataHelper;

    @Mock
    RedisReactiveRepo appCacheRedisClient;

    TsFrequencyDataLoader tsFrequencyDataLoader;

    TsDataLoader tsDataLoader;

    private RdbTsService rdbTsService;

    @Mock
    ApplicationEventPublisher applicationEventPublisher;

    RdbGateway rdbGateway;
    RdbDataAsyncRepo rdbDataAsyncRepo;

    private static MockedStatic<MDC> mdcMockedStatic;

    private Semaphore semaphore = new Semaphore(10);
    @Before
    public void setUp() throws JsonMappingException {
        mdcMockedStatic = Mockito.mockStatic(MDC.class, Mockito.RETURNS_DEEP_STUBS);
        rdbDataAsyncRepo = spy(new RdbDataAsyncRepo(rdbDataRepo, 5, false));
        tsDataLoader = new TsDataLoader(rdbDataAsyncRepo);
        tsFrequencyDataLoader = new TsFrequencyDataLoader(rdbDataAsyncRepo);
        RdbCurrentService rdbCurrentService = new RdbCurrentService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, false);
        rdbTsService = new RdbTsService(rdbTsCacheDataHelper, tsFrequencyDataLoader, tsDataLoader, applicationEventPublisher, true);
        rdbGateway = new RdbGateway(rdbCurrentService, rdbTsService);
    }

    @After
    public void afterEachTest(){
        mdcMockedStatic.close();
    }

    @Test
    public void retrieveCurrentDataTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockCurrentMartRequest();
        martRequest.setIdMappers(mockIdMapper());
        setCurrentDataPointRepository("1", "2");
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(mockCurrentData());
        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals("ccc ddd", d.getValues().get("190252"));
                    assertEquals("value2", d.getValues().get("190999"));
                    assertEquals("22222", d.getValues().get("199999"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveTimeSeriesTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockTimeSeriesMartRequest();
        martRequest.setIdMappers(mockIdMapper());
        setTimeSeriesDataPointRepository();

        when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(mockTimeSeriesRedisData()).thenReturn(Mono.just(new HashMap<>()));
        when(rdbDataRepo.executeTsSqlWithoutLock(any(), any(), any(), any())).thenReturn(mockTimeSeriesRdbData());
        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result.collectSortedList((r1, r2) -> r2.getId().compareTo(r1.getId())).flatMapMany(Flux::fromIterable))
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(3, ((ArrayList<V>)d.getValues().get("190252")).size());
                    assertEquals(3, ((ArrayList<V>)d.getValues().get("190236")).size());
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("190252")).stream().filter(v -> "2019-10-22".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "Jan Nel");
                })
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertNotNull(d.getValues().get("190252"));
                    assertEquals(((ArrayList<V>)d.getValues().get("190252")).size(), 3);
                    assertEquals(((ArrayList<V>)d.getValues().get("190236")).size(), 3);
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("190252")).stream().filter(v -> "2018-04-19".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "George Georgiev");
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveMonthlyTimeSeriesTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockMonthlyTimeSeriesMartRequest();
        martRequest.setIdMappers(mockIdMapper());
        setMonthlyTimeSeriesDataPointRepository();

        when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(mockMonthlyTimeSeriesRedisData()).thenReturn(Mono.just(new HashMap<>()));
        when(rdbDataRepo.executeTsSqlWithoutLock(anyString(), anyString(), anyString(), anyString())).thenReturn(mockMonthlyTimeSeriesRdbData());

        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(((ArrayList<V>)d.getValues().get("SF456")).size(), 2);
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("SF456")).stream().filter(v -> "2014-05-31".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "6.40720");
                    val = ((ArrayList<V>)d.getValues().get("SF456")).stream().filter(v -> "2015-02-28".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "8.51004");
                })
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals(((ArrayList<V>)d.getValues().get("SF456")).size(), 17);
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("SF456")).stream().filter(v -> "2014-08-31".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "2.93578");
                    val = ((ArrayList<V>)d.getValues().get("SF456")).stream().filter(v -> "2015-05-31".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "1.09290");
                    val = ((ArrayList<V>)d.getValues().get("SF456")).stream().filter(v -> "2015-06-30".equals(v.getI())).findAny();
                    assertEquals(val.isPresent(), false);
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveQuarterlyTimeSeriesTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockQuarterlyTimeSeriesMartRequest();
        martRequest.setIdMappers(mockQuarterlyIdMapper());
        setQuarterlyTimeSeriesDataPointRepository();

        List<Object> blist = new ArrayList<>();
        blist.add(null);
        blist.add(null);
        when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(mockQuarterlyTimeSeriesRedisData()).thenReturn(Mono.just(new HashMap<>()));
        when(rdbDataRepo.executeTsSqlWithoutLock(anyString(), anyString(), anyString(), anyString())).thenReturn(mockQuarterlyTimeSeriesRdbData());

        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("F000002BMN", d.getId());
                    assertEquals(3, ((ArrayList<V>)d.getValues().get("SF123")).size());
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("SF123")).stream().filter(v -> "2014-09-30".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "6587071.71400");
                    val = ((ArrayList<V>)d.getValues().get("SF123")).stream().filter(v -> "2015-03-31".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "22454695.64200");
                })
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals(((ArrayList<V>)d.getValues().get("SF123")).size(), 6);
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("SF123")).stream().filter(v -> "2014-03-31".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "837365.39800");
                    val = ((ArrayList<V>)d.getValues().get("SF123")).stream().filter(v -> "2015-06-30".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "7263458.29600");
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveOnlyCacheTest() throws NoSuchFieldException, IllegalAccessException {
        List<String> idList = Arrays.asList("0P000038Y3");

        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P000038Y3", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        MartRequest martRequest = MartRequest.builder().ids(idList).dps(Arrays.asList("190252","190999","199999")).currency("USD").idMappers(idMappers).build();
        setCurrentDataPointRepository("1", "2");

        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(mockCurrentData());

        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals("ccc ddd", d.getValues().get("190252"));
                    assertEquals("value2", d.getValues().get("190999"));
                    assertEquals("22222", d.getValues().get("199999"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveRdbSelectTest() throws NoSuchFieldException, IllegalAccessException {
        List<String> idList = Arrays.asList("0P00009T69");

        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        MartRequest martRequest = MartRequest.builder().ids(idList).dps(Arrays.asList("190252", "190999", "199999")).currency("USD").idMappers(idMappers).build();

        RdbDataPoint rdbDp = RdbDataPoint.builder().id("190252").nid("190252").src("RDB").column("process_pillar_author").name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").build();
        DataPoint dp1 = DataPoint.builder().id("190252").nid("190252").src("RDB").name("process_pillar_author").groupName("MorningstarMedalistRatings").currentRdb(rdbDp).build();

        RdbDataPoint rdbDp2 = RdbDataPoint.builder().id("190999").nid("190999").src("RDB").column("ref_value").name("ref_value").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").build();
        DataPoint dp2 = DataPoint.builder().id("190999").nid("190999").src("RDB").name("ref_value").groupName("MorningstarMedalistRatings").mappingRef(mockAlias()).currentRdb(rdbDp2).build();

        RdbDataPoint rdbDp3 = RdbDataPoint.builder().id("199999").nid("199999").src("RDB").column("ref_value2").name("ref_value2").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").build();
        DataPoint dp3 = DataPoint.builder().id("199999").nid("199999").src("RDB").name("ref_value2").groupName("MorningstarMedalistRatings").mappingRef(mockAlias2()).mappingSrc(dp2).currentRdb(rdbDp3).build();

        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("190252", dp1);
        dataPointMap.put("190999", dp2);
        dataPointMap.put("199999", dp3);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("process_pillar_author", Pair.of("190252", ""));
        colToDpsMap.put("ref_value", Pair.of("190999", ""));
        colToDpsMap.put("ref_value2", Pair.of("199999", ""));
        rdbGroupColDpsMap.put("MorningstarMedalistRatings", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);

        List<Map<String, Object>> dbResult =  new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "E0ESP01AD4");
        row1.put("process_pillar_author", "aaa bbb");
        row1.put("ref_value", "1");
        row1.put("ref_value2", "");
        dbResult.add(row1);

        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());
        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .expectNextCount(0)
                /* Should not get data from rdb for now 05/15/2023 VELO-11003
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals("aaa bbb", d.getValues().get("190252"));
                    assertEquals("value1", d.getValues().get("190999"));
                    assertEquals("11111", d.getValues().get("199999"));
                })
                 */
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveOnlyRdbTest() throws NoSuchFieldException, IllegalAccessException {
        List<String> idList = Arrays.asList("0P00009T69");

        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        MartRequest martRequest = MartRequest.builder().ids(idList).dps(Arrays.asList("190252", "190999", "199999")).currency("USD").idMappers(idMappers).build();

        RdbDataPoint rdbDp = RdbDataPoint.builder().id("190252").nid("190252").src("RDB").column("process_pillar_author").name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("3").build();
        DataPoint dp1 = DataPoint.builder().id("190252").nid("190252").src("RDB").name("process_pillar_author").groupName("MorningstarMedalistRatings").currentRdb(rdbDp).build();

        RdbDataPoint rdbDp2 = RdbDataPoint.builder().id("190999").nid("190999").src("RDB").column("ref_value").name("ref_value").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("3").build();
        DataPoint dp2 = DataPoint.builder().id("190999").nid("190999").src("RDB").name("ref_value").groupName("MorningstarMedalistRatings").mappingRef(mockAlias()).currentRdb(rdbDp2).build();

        RdbDataPoint rdbDp3 = RdbDataPoint.builder().id("199999").nid("199999").src("RDB").column("ref_value2").name("ref_value2").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("3").build();
        DataPoint dp3 = DataPoint.builder().id("199999").nid("199999").src("RDB").name("ref_value2").groupName("MorningstarMedalistRatings").mappingRef(mockAlias2()).mappingSrc(dp2).currentRdb(rdbDp3).build();

        Map<String, DataPoint> dataPointMap = new HashMap();
        dataPointMap.put("190252", dp1);
        dataPointMap.put("190999", dp2);
        dataPointMap.put("199999", dp3);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("process_pillar_author", Pair.of("190252", "aaa bbb"));
        colToDpsMap.put("ref_value", Pair.of("190999", "1"));
        colToDpsMap.put("ref_value2", Pair.of("199999", ""));
        rdbGroupColDpsMap.put("MorningstarMedalistRatings", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);

        List<Map<String, Object>> dbResult =  new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "E0ESP01AD4");
        row1.put("process_pillar_author", "aaa bbb");
        row1.put("ref_value", "1");
        row1.put("ref_value2", "");
        dbResult.add(row1);

        doReturn(Mono.just(dbResult)).when(rdbDataAsyncRepo).executeSQL(anyString(), anyLong(), anyString(), anyString());
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());

        //        lenient().when(syncRedisCacheClient.multiGet(anyList())).thenReturn(new ArrayList<>());
        //        lenient().doNothing().when(syncRedisCacheClient).multiSet(any(), anyLong());

        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals("aaa bbb", d.getValues().get("190252"));
                    assertEquals("value1", d.getValues().get("190999"));
                    assertEquals("11111", d.getValues().get("199999"));
                })
                .expectComplete()
                .verify();

        RdbCurrentService rdbCurrentService = new RdbCurrentService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, true);
        RdbGateway rdbGateway2 = new RdbGateway(rdbCurrentService, rdbTsService);

        Flux<Result> newResult = rdbGateway2.retrieve(martRequest);

        StepVerifier.create(newResult)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    //Changed expected size to be 3 after allowing rdb-cache-flag = 3 to not be filtered. This change must be reverted to 0 when it is filtered again
                    assertEquals(3, d.getValues().size());
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveOnlyRdbTestMappedDatapoints() throws NoSuchFieldException, IllegalAccessException {
        List<String> idList = Arrays.asList("0P00009T69");

        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        MartRequest martRequest1 = MartRequest.builder().ids(idList).dps(Arrays.asList("190252", "190999", "199999")).currency("USD").idMappers(idMappers).build();
        MartRequest martRequest2 = MartRequest.builder().ids(idList).dps(Arrays.asList("199999")).currency("USD").idMappers(idMappers).build();
        MartRequest martRequest3 = MartRequest.builder().ids(idList).dps(Arrays.asList("199999", "190252")).currency("USD").idMappers(idMappers).build();
        RdbDataPoint rdbDp = RdbDataPoint.builder().id("190252").nid("190252").src("RDB").column("process_pillar_author").name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("2").build();
        DataPoint dp1 = DataPoint.builder().id("190252").nid("190252").src("RDB").name("process_pillar_author").groupName("MorningstarMedalistRatings").currentRdb(rdbDp).build();

        RdbDataPoint rdbDp2 = RdbDataPoint.builder().id("190999").nid("190999").src("RDB").column("ref_value").name("ref_value").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("2").build();
        DataPoint dp2 = DataPoint.builder().id("190999").nid("190999").src("RDB").name("ref_value").groupName("MorningstarMedalistRatings").mappingRef(mockAlias()).currentRdb(rdbDp2).build();

        RdbDataPoint rdbDp3 = RdbDataPoint.builder().id("199999").nid("199999").src("RDB").column("ref_value2").name("ref_value2").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("2").build();
        DataPoint dp3 = DataPoint.builder().id("199999").nid("199999").src("RDB").name("ref_value2").groupName("MorningstarMedalistRatings").mappingRef(mockAlias2()).mappingSrc(dp2).build();

        Map<String, DataPoint> dataPointMap = new HashMap();
        dataPointMap.put("190252", dp1);
        dataPointMap.put("190999", dp2);
        dataPointMap.put("199999", dp3);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("process_pillar_author", Pair.of("190252", "aaa bbb"));
        colToDpsMap.put("ref_value", Pair.of("190999", "1"));
        colToDpsMap.put("ref_value2", Pair.of("199999", ""));
        rdbGroupColDpsMap.put("MorningstarMedalistRatings", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);

        List<Map<String, Object>> dbResult =  new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "E0ESP01AD4");
        row1.put("process_pillar_author", "aaa bbb");
        row1.put("ref_value", "1");
        row1.put("ref_value2", "");
        dbResult.add(row1);

        doReturn(Mono.just(dbResult)).when(rdbDataAsyncRepo).executeSQL(anyString(), anyLong(), anyString(), anyString());
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());

        RdbCurrentService rdbCurrentService = new RdbCurrentService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, true);
        RdbGateway rdbGateway2 = new RdbGateway(rdbCurrentService, rdbTsService);

        Flux<Result> newResult1 = rdbGateway2.retrieve(martRequest1);

        StepVerifier.create(newResult1)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(3, d.getValues().size());
                })
                .expectComplete()
                .verify();

        Flux<Result> newResult2 = rdbGateway2.retrieve(martRequest2);

        StepVerifier.create(newResult2)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals("11111", d.getValues().get("199999"));
                })
                .expectComplete()
                .verify();

        Flux<Result> newResult3 = rdbGateway2.retrieve(martRequest3);

        StepVerifier.create(newResult3)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(2, d.getValues().size());
                    assertEquals("11111", d.getValues().get("199999"));
                    assertEquals("aaa bbb", d.getValues().get("190252"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveCacheGroupedDataTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockGroupedDataMartRequest();
        martRequest.setIdMappers(mockIdMapper());
        setCurrentDataPointRepository("1", "2");
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(mockCurrentData(mockGroupedRedisReturn()));
        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(true, MapUtils.isEmpty(d.getValues()));
                })
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals(1, ((List)d.getValues().get("23759")).size());
                    CurrentResult res = (CurrentResult)(((List)d.getValues().get("23759")).get(0));
                    assertEquals("swe", res.getValues().get("23760"));
                }).assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals(1, ((List)d.getValues().get("23759")).size());
                    CurrentResult res = (CurrentResult)(((List)d.getValues().get("23759")).get(0));
                    assertEquals("usa", res.getValues().get("23760"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveDBGroupedDataTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockGroupedDataMartRequest();
        martRequest.setIdMappers(mockIdMapper());
        setCurrentDataPointRepository("3", "3");
        List<Map<String, Object>> dbResult =  mockDBGroupedDataResult();

        when(rdbDataRepo.executeProcedureWithoutLock(anyString(),anyString())).thenReturn(dbResult);
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());

        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(true, MapUtils.isEmpty(d.getValues()));
                })
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals(3, ((List)d.getValues().get("23759")).size());
                    CurrentResult res = (CurrentResult)(((List)d.getValues().get("23759")).get(0));
                    assertEquals("swe", res.getValues().get("23760"));
                    assertEquals("swe", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(1));
                    assertEquals("usa", res.getValues().get("23760"));
                    assertEquals("usa", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(2));
                    assertEquals("can", res.getValues().get("23760"));
                    assertEquals("can", res.getValues().get("33798"));
                }).assertNext(d ->{
                    assertEquals(1, d.getValues().size());
                    assertEquals(3, ((List)d.getValues().get("23759")).size());
                    CurrentResult res = (CurrentResult)(((List)d.getValues().get("23759")).get(0));
                    assertEquals("gbr", res.getValues().get("23760"));
                    assertEquals("gbr", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(1));
                    assertEquals("fra", res.getValues().get("23760"));
                    assertEquals("fra", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(2));
                    assertEquals("ita", res.getValues().get("23760"));
                    assertEquals("ita", res.getValues().get("33798"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveDBGroupedDataPostTaxTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockGroupedDataMartRequest();
        martRequest.setIdMappers(mockIdMapperGroupPostTax());
        martRequest.setPostTax("1");
        setCurrentDataPointRepository("3", "3");
        List<Map<String, Object>> dbResult =  mockDBGroupedDataResult();

        when(rdbDataRepo.executeProcedureWithoutLock(anyString(),anyString())).thenReturn(dbResult);
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());

        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(true, MapUtils.isEmpty(d.getValues()));
                })
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals(3, ((List)d.getValues().get("23759")).size());
                    CurrentResult res = (CurrentResult)(((List)d.getValues().get("23759")).get(0));
                    assertEquals("swe", res.getValues().get("23760"));
                    assertEquals("swe", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(1));
                    assertEquals("usa", res.getValues().get("23760"));
                    assertEquals("usa", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(2));
                    assertEquals("can", res.getValues().get("23760"));
                    assertEquals("can", res.getValues().get("33798"));
                }).assertNext(d ->{
                    assertEquals(1, d.getValues().size());
                    assertEquals(3, ((List)d.getValues().get("23759")).size());
                    CurrentResult res = (CurrentResult)(((List)d.getValues().get("23759")).get(0));
                    assertEquals("gbr", res.getValues().get("23760"));
                    assertEquals("gbr", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(1));
                    assertEquals("fra", res.getValues().get("23760"));
                    assertEquals("fra", res.getValues().get("33798"));

                    res = (CurrentResult)(((List)d.getValues().get("23759")).get(2));
                    assertEquals("ita", res.getValues().get("23760"));
                    assertEquals("ita", res.getValues().get("33798"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void postTaxTest() {
        MartRequest martRequest = mockTimeSeriesMartRequestPostTax();
        martRequest.setIdMappers(mockIdMapperPostTax());
        setTimeSeriesDataPointRepositoryWithPostTax();

        when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(Mono.just(new HashMap<>())).thenReturn(Mono.just(new HashMap<>()));
        when(rdbDataRepo.executeTsSqlWithoutLock(any(), eq("E0ESP01AD4"), any(), any())).thenReturn(mockTimeSeriesRdbDataWithPostTax());
        when(rdbDataRepo.executeTsSqlWithoutLock(any(), eq("E0ESP02B6E"), any(), any())).thenReturn(mockTimeSeriesRdbData());

        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result.collectSortedList((r1, r2) -> r2.getId().compareTo(r1.getId())).flatMapMany(Flux::fromIterable))
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(3,((ArrayList<V>)d.getValues().get("190252")).size());
                    assertEquals(3, ((ArrayList<V>)d.getValues().get("190236")).size());
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("190252")).stream().filter(v -> "2018-04-19".equals(v.getI())).findAny();
                    assertEquals("George Georgiev1", val.get().getV());
                })
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals(3, ((ArrayList<V>)d.getValues().get("190236")).size());
                    Optional<V> val = ((ArrayList<V>)d.getValues().get("190252")).stream().filter(v -> "2018-04-19".equals(v.getI())).findAny();
                    assertEquals(val.get().getV(), "George Georgiev");
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void multipleValueTest() {
        MartRequest martRequest = mockMultipleValueMartRequest();
        martRequest.setIdMappers(mockMultipleValueIdMapper());
        setMultipleValueDataPointRepository();
        when(rdbDataRepo.executeProcedureWithoutLock(any(), eq("**********"))).thenReturn(mockMultipleValueData());
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());

        Flux<Result> resultFlux = rdbGateway.retrieve(martRequest);

        StepVerifier.create(resultFlux)
                .expectSubscription()
                .assertNext(result ->{
                    assertEquals("**********", result.getId());
                    assertTrue(MapUtils.isEmpty(result.getValues()));
                })
                .assertNext(result -> {
                    assertEquals("**********", result.getId());
                    assertTrue(result instanceof GroupResult);
                    GroupResult groupResult = (GroupResult) result;
                    assertTrue(groupResult.isMultipleValues());
                    List<String> values = groupResult.getValues().get("OF015").stream()
                            .map(r -> r.getValues().get("OF015"))
                            .collect(Collectors.toList());
                    assertEquals(3, values.size());
                    assertTrue(values.contains("test_name1"));
                    assertTrue(values.contains("test_name2"));
                    assertTrue(values.contains("test_name3"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void currentDataPostTaxTrueTest() {
        Flux<Result> result = currentDataPostTaxTest("1");
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());

        StepVerifier.create(result.collectSortedList((r1, r2) -> r2.getId().compareTo(r1.getId())).flatMapMany(Flux::fromIterable))
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals("2020-04-03 00:00:00.0", d.getValues().get("190236"));
                })
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals("George Georgiev", d.getValues().get("190252"));
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void currentDataPostTaxFalseTest() {
        Flux<Result> result = currentDataPostTaxTest("0");
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());
        StepVerifier.create(result.collectSortedList((r1, r2) -> r2.getId().compareTo(r1.getId())).flatMapMany(Flux::fromIterable))
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(2, d.getValues().size());
                    assertEquals("2020-04-03 00:00:00.0", d.getValues().get("190236"));
                    assertEquals("Eric Schultz", d.getValues().get("190252"));
                })
                .assertNext(d ->{
                    assertEquals("0P000038Y3", d.getId());
                    assertEquals(2, d.getValues().size());
                    assertEquals("2018-05-19 00:00:00.0", d.getValues().get("190236"));
                    assertEquals("George Georgiev", d.getValues().get("190252"));
                })
                .expectComplete()
                .verify();
    }




    @Test
    public void getMultipleValueFromCacheTest() {
        MartRequest martRequest = mockMultipleValueMartRequest();
        martRequest.setIdMappers(mockMultipleValueIdMapper());
        setMultipleValueDataPointRepository();

        RdbCurrentCacheDataHelper rdbCurrCacheDataHelper = new RdbCurrentCacheDataHelper(appCacheRedisClient, semaphore);
        RdbCurrentService rdbCurrService = new RdbCurrentService(rdbDataAsyncRepo, rdbCurrCacheDataHelper, false);
        RdbGateway rdbGtw = new RdbGateway(rdbCurrService, rdbTsService);

        Map<String, Object> cacheData = new HashMap<>();
        cacheData.put("OF015", "[{\"OF015\":\"Eli M. Salzmann\",\"LF015\":\"8449\",\"OF029\":\"2002-03-01 00:00:00.0\",\"id\":\"**********\"},{\"OF015\":\"Robert A. Lee\",\"LF015\":\"8797\",\"OF029\":\"2002-03-01 00:00:00.0\",\"id\":\"**********\"},{\"OF015\":\"Kenneth G. Fuller\",\"LF015\":\"9891\",\"OF029\":\"2002-06-03 00:00:00.0\",\"id\":\"**********\"}]");
        cacheData.put("id", "**********");
        when(appCacheRedisClient.multiGetHash(any(), any())).thenReturn(Flux.just(cacheData));

        Flux<Result> resultFlux = rdbGtw.retrieve(martRequest);

        StepVerifier.create(resultFlux)
                .expectSubscription()
                .assertNext(result ->{
                    assertEquals("**********", result.getId());
                    assertTrue(MapUtils.isEmpty(result.getValues()));
                })
                .assertNext(result -> {
                    assertEquals("**********", result.getId());
                    assertTrue(result instanceof GroupResult);
                    GroupResult groupResult = (GroupResult) result;
                    assertTrue(groupResult.isMultipleValues());
                    List<String> values = groupResult.getValues().get("OF015").stream()
                            .map(r -> r.getValues().get("OF015"))
                            .collect(Collectors.toList());
                    assertEquals(3, values.size());
                    assertTrue(values.contains("Eli M. Salzmann"));
                    assertTrue(values.contains("Robert A. Lee"));
                    assertTrue(values.contains("Kenneth G. Fuller"));
                })
                .expectComplete()
                .verify();
    }


    @Test
    public void cacheMultipleValueTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockMultipleValueMartRequest();
        martRequest.setUseCase("false");
        martRequest.setIdMappers(mockMultipleValueIdMapper2());
        setMultipleValueDataPointRepository();

        RdbCurrentCacheDataHelper rdbCurrCacheDataHelper = new RdbCurrentCacheDataHelper(appCacheRedisClient, semaphore);
        RdbCurrentService rdbCurrService = new RdbCurrentService(rdbDataAsyncRepo, rdbCurrCacheDataHelper, false);
        RdbGateway rdbGtw = new RdbGateway(rdbCurrService, rdbTsService);

        Flux<Result> resultFlux = rdbGtw.retrieve(martRequest);

        StepVerifier.create(resultFlux)
                .expectSubscription()
                .assertNext(result ->{
                    assertEquals("**********", result.getId());
                    assertTrue(MapUtils.isEmpty(result.getValues()));
                })
                .assertNext(result -> {

                    ArgumentCaptor<Map> argumentCaptor = ArgumentCaptor.forClass(Map.class);
                    verify(appCacheRedisClient, times(1)).multiSetHash(argumentCaptor.capture(), anyLong());

                    Map<String, Map<String, String>> cacheData = argumentCaptor.getValue();
                    assertEquals(true, cacheData.containsKey("rdb:DP_FundId/xv_FundManager:F0000000EK"));
                    assertEquals(2, cacheData.get("rdb:DP_FundId/xv_FundManager:F0000000EK").size());
                    assertEquals("F0000000EK", cacheData.get("rdb:DP_FundId/xv_FundManager:F0000000EK").get("id"));
                    assertEquals("[{\"OF015\":\"Eli M. Salzmann\",\"id\":\"F0000000EK\"},{\"OF015\":\"Robert A. Lee\",\"id\":\"F0000000EK\"},{\"OF015\":\"Kenneth G. Fuller\",\"id\":\"F0000000EK\"}]", cacheData.get("rdb:DP_FundId/xv_FundManager:F0000000EK").get("OF015"));

                    assertEquals("F0000000EK", result.getId());
                    assertTrue(result instanceof GroupResult);
                    GroupResult groupResult = (GroupResult) result;
                    assertTrue(groupResult.isMultipleValues());
                    List<String> values = groupResult.getValues().get("OF015").stream()
                            .map(r -> r.getValues().get("OF015"))
                            .collect(Collectors.toList());
                    assertEquals(3, values.size());
                    assertTrue(values.contains("Eli M. Salzmann"));
                    assertTrue(values.contains("Robert A. Lee"));
                    assertTrue(values.contains("Kenneth G. Fuller"));
                });
    }







    public Flux<Result> currentDataPostTaxTest(String postTax) {
        MartRequest martRequest = mockCurrentPostTaxRequest();
        martRequest.setIdMappers(mockIdMapperPostTax());
        setCurrentDataPointRepositoryWithPostTax();

        martRequest.setPostTax(postTax);

        when(rdbDataRepo.executeProcedureWithoutLock(any(), eq("E0ESP01AD4"))).thenReturn(mockCurrentDataPostTaxResult("E0ESP01AD4", "Eric Schultz", "2020-04-03 00:00:00.0", new ArrayList<>()));
        when(rdbDataRepo.executeProcedureWithoutLock(any(), eq("E0ESP02B6E"))).thenReturn(mockCurrentDataPostTaxResult("E0ESP02B6E", "George Georgiev", "2018-05-19 00:00:00.0", new ArrayList<>()));

        List<Map<String, Object>> combinedResult = new ArrayList<>();
        combinedResult = mockCurrentDataPostTaxResult("E0ESP01AD4", "Eric Schultz", "2020-04-03 00:00:00.0", combinedResult);
        combinedResult = mockCurrentDataPostTaxResult("E0ESP02B6E", "George Georgiev", "2018-05-19 00:00:00.0", combinedResult);
        when(rdbDataRepo.executeProcedureWithoutLock(any(), eq("E0ESP01AD4,E0ESP02B6E"))).thenReturn(combinedResult);

        return rdbGateway.retrieve(martRequest);
    }

    private void setMultipleValueDataPointRepository() {
        RdbDataPoint current = RdbDataPoint.builder().id("OF015").nid("OF015").src("RDB").column("ManagerName").name("ManagerName").groupName("xv_FundManager").idLevel("FundId").storeProcedure("sp1").rdbCacheFlag("2").multipleValues(true).build();
        DataPoint dp = DataPoint.builder().id("OF015").nid("OF015").src("RDB").name("ManagerName").groupName("xv_FundManager").currentRdb(current).build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("OF015", dp);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("ManagerName", Pair.of("OF015", ""));
        rdbGroupColDpsMap.put("xv_FundManager", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private MartRequest mockMultipleValueMartRequest() {
        List<String> idList = Collections.singletonList("**********");
        return MartRequest.builder().ids(idList).dps(Collections.singletonList("OF015")).idMappers(new ArrayList<>()).build();
    }

    private List<Map<String, Object>> mockMultipleValueData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "**********");
        row1.put("ManagerName", "test_name1");
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("UniqueId", "**********");
        row2.put("ManagerName", "test_name2");
        data.add(row2);

        Map<String, Object> row3  = new HashMap<>();
        row3.put("UniqueId", "**********");
        row3.put("ManagerName", "test_name3");
        data.add(row3);

        return data;
    }

    private void setCurrentDataPointRepository(String cacheFlag, String cacheFlag2) throws NoSuchFieldException, IllegalAccessException {
        RdbDataPoint current = RdbDataPoint.builder().id("190252").nid("190252").src("RDB").column("process_pillar_author").name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("sp1").rdbCacheFlag(cacheFlag).build();
        RdbDataPoint timeSeries = RdbDataPoint.builder().id("190252").nid("190252").src("RDB").column("process_pillar_author").name("process_pillar_author").groupName("TimeseriesMorningstarMedalistRatings").idLevel("ShareClassId").dateColumn("190236").storeProcedure("sp2").build();
        DataPoint dp = DataPoint.builder().id("190252").nid("190252").src("RDB").name("process_pillar_author").groupName("MorningstarMedalistRatings").currentRdb(current).tsRdb(timeSeries).build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("190252", dp);

        RdbDataPoint current2 = RdbDataPoint.builder().id("190999").nid("190999").src("RDB").column("ref_value").name("ref_value").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("sp1").rdbCacheFlag(cacheFlag).build();
        RdbDataPoint timeSeries2 = RdbDataPoint.builder().id("190999").nid("190999").src("RDB").column("ref_value").name("ref_value").groupName("TimeseriesMorningstarMedalistRatings").idLevel("ShareClassId").dateColumn("190236").storeProcedure("sp2").rdbCacheFlag(cacheFlag2).build();
        DataPoint dp2 = DataPoint.builder().id("190999").nid("190999").src("RDB").name("ref_value").groupName("MorningstarMedalistRatings").mappingRef(mockAlias()).currentRdb(current2).tsRdb(timeSeries2).build();
        dataPointMap.put("190999", dp2);

        RdbDataPoint current3 = RdbDataPoint.builder().id("199999").nid("199999").src("RDB").column("ref_value2").name("ref_value").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("sp1").rdbCacheFlag(cacheFlag).build();
        DataPoint dp3 = DataPoint.builder().id("199999").nid("199999").src("RDB").name("ref_value2").groupName("MorningstarMedalistRatings").mappingRef(mockAlias2()).currentRdb(current3).mappingSrc(dp2).build();
        dataPointMap.put("199999", dp3);

        DataPoint subDp1 = DataPoint.builder().id("DPDAV5S").nid("23760").src("RDB").name("Dealing Schedule Country").column("DealingScheduleCountry").groupName("1dOperationGroup").build();
        DataPoint subDp2 = DataPoint.builder().id("DPDAV5T").nid("23761").src("RDB").name("Dealing Schedule CutOff Time").column("DealingScheduleCutOffTime").groupName("1dOperationGroup").build();
        DataPoint subDp3 = DataPoint.builder().id("DPDAV5U").nid("23762").src("RDB").name("Dealing Schedule Dealing Type").column("DealingScheduleDealingType").groupName("1dOperationGroup").build();
        DataPoint subDp4 = DataPoint.builder().id("DPDAV99").nid("33798").src("RDB").name("MappedValue").column("col123").groupName("1dOperationGroup").mappingSrc(subDp1).build();
        RdbDataPoint current4 = RdbDataPoint.builder().id("23759").nid("23759").src("RDB").column("DealingScheduleContent").name("Dealing Schedule Content").multi(true).group(true).groupName("1dOperationGroup").idLevel("FundId").storeProcedure("sp1").rdbCacheFlag(cacheFlag).subDataPoints(List.of(subDp1, subDp2, subDp3, subDp4)).build();
        DataPoint dp4 = DataPoint.builder().id("23759").nid("23759").src("RDB").name("Dealing Schedule Content").multi(true).group(true).groupName("1dOperationGroup").currentRdb(current4).postTaxCurrentRdb(current4).build();
        dataPointMap.put("23759", dp4);

        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("process_pillar_author", Pair.of("190252", ""));
        colToDpsMap.put("overall_rating_publish_date_utc", Pair.of("190236", ""));
        colToDpsMap.put("ref_value", Pair.of("190999", ""));
        colToDpsMap.put("ref_value2", Pair.of("199999", ""));
        rdbGroupColDpsMap.put("MorningstarMedalistRatings", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private DataPoint getDataPoint(String id, String column, String group) {
        RdbDataPoint current = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("sp1").rdbCacheFlag("1").build();
        RdbDataPoint timeSeries = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName(group).idLevel("ShareClassId").dateColumn("190236").storeProcedure("sp2").rdbCacheFlag("2").build();
        return DataPoint.builder().id(id).nid(id).src("RDB").name(column).groupName(group).currentRdb(current).tsRdb(timeSeries).build();
    }

    private DataPoint getDataPointWithPostTax(String id, String column, String group) {
        RdbDataPoint current = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("sp1").rdbCacheFlag("1").build();
        RdbDataPoint timeSeries = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName(group).idLevel("ShareClassId").dateColumn("190236").storeProcedure("sp2").rdbCacheFlag("2").build();
        RdbDataPoint postTaxCurrentData = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName(group).idLevel("ShareClassId").dateColumn("190236").storeProcedure("sp3").rdbCacheFlag("2").build();
        RdbDataPoint postTaxTimeSeries = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName(group).idLevel("ShareClassId").dateColumn("190236").storeProcedure("sp3").rdbCacheFlag("2").build();
        return DataPoint.builder().id(id).nid(id).src("RDB").name(column).groupName(group).currentRdb(current).tsRdb(timeSeries).postTaxCurrentRdb(postTaxCurrentData).postTaxTsRdb(postTaxTimeSeries).build();
    }

    private DataPoint getDataPointWithoutPostTax(String id, String column, String group) {
        RdbDataPoint current = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("sp1").rdbCacheFlag("1").build();
        RdbDataPoint timeSeries = RdbDataPoint.builder().id(id).nid(id).src("RDB").column(column).name("process_pillar_author").groupName(group).idLevel("ShareClassId").dateColumn("190236").storeProcedure("sp2").rdbCacheFlag("2").build();
        return DataPoint.builder().id(id).nid(id).src("RDB").name(column).groupName(group).currentRdb(current).tsRdb(timeSeries).build();
    }

    private void setTimeSeriesDataPointRepositoryWithPostTax() {
        DataPoint dp1 = getDataPointWithPostTax("190236", "overall_rating_publish_date_utc", "TimeseriesMorningstarMedalistRatings");
        DataPoint dp2 = getDataPointWithPostTax("190252", "process_pillar_author", "TimeseriesMorningstarMedalistRatings");
        DataPoint dp3 = getDataPointWithPostTax("190999", "ref_value", "TimeseriesMorningstarMedalistRatings");
        dp3.setMappingRef(mockAlias());
        DataPoint dp4 = getDataPointWithPostTax("199999", "ref_value2", "TimeseriesMorningstarMedalistRatings");
        dp4.setMappingRef(mockAlias2());
        dp4.setMappingSrc(dp3);
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("190252", dp2);
        dataPointMap.put("190236", dp1);
        dataPointMap.put("190999", dp3);
        dataPointMap.put("199999", dp4);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("process_pillar_author", Pair.of("190252", ""));
        colToDpsMap.put("overall_rating_publish_date_utc", Pair.of("190236", ""));
        colToDpsMap.put("ref_value", Pair.of("190999", ""));
        colToDpsMap.put("ref_value2", Pair.of("199999", ""));
        rdbGroupColDpsMap.put("TimeseriesMorningstarMedalistRatings", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private void setCurrentDataPointRepositoryWithPostTax() {
        DataPoint dp1 = getDataPointWithPostTax("190236", "overall_rating_publish_date_utc", "MorningstarMedalistRatings");
        DataPoint dp2 = getDataPointWithoutPostTax("190252", "process_pillar_author", "MorningstarMedalistRatings");
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("190252", dp2);
        dataPointMap.put("190236", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("process_pillar_author", Pair.of("190252", ""));
        colToDpsMap.put("overall_rating_publish_date_utc", Pair.of("190236", ""));
        rdbGroupColDpsMap.put("MorningstarMedalistRatings", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private void setTimeSeriesDataPointRepository() throws NoSuchFieldException, IllegalAccessException {
        DataPoint dp1 = getDataPoint("190236", "overall_rating_publish_date_utc", "TimeseriesMorningstarMedalistRatings");
        DataPoint dp2 = getDataPoint("190252", "process_pillar_author", "TimeseriesMorningstarMedalistRatings");
        DataPoint dp3 = getDataPoint("190999", "ref_value", "TimeseriesMorningstarMedalistRatings");
        dp3.setMappingRef(mockAlias());
        DataPoint dp4 = getDataPoint("199999", "ref_value2", "TimeseriesMorningstarMedalistRatings");
        dp4.setMappingRef(mockAlias2());
        dp4.setMappingSrc(dp3);
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("190252", dp2);
        dataPointMap.put("190236", dp1);
        dataPointMap.put("190999", dp3);
        dataPointMap.put("199999", dp4);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("process_pillar_author", Pair.of("190252", ""));
        colToDpsMap.put("overall_rating_publish_date_utc", Pair.of("190236", ""));
        colToDpsMap.put("ref_value", Pair.of("190999", ""));
        colToDpsMap.put("ref_value2", Pair.of("199999", ""));
        rdbGroupColDpsMap.put("TimeseriesMorningstarMedalistRatings", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private void setMonthlyTimeSeriesDataPointRepository() throws NoSuchFieldException, IllegalAccessException {
        RdbDataPoint timeSeries = RdbDataPoint.builder().id("SF456").nid("SF456").src("RDB").column("r_ReturnForMonth").name("r_ReturnForMonth").groupName("ExtendedMonthlyReturn").idLevel("PerformanceId").storeProcedure("sp2").columnPrefix("r_ReturnForMonth").frequency("m").build();
        DataPoint dp = DataPoint.builder().id("SF456").nid("SF456").src("RDB").name("r_ReturnForMonth").groupName("ExtendedMonthlyReturn").tsRdb(timeSeries).build();

        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("SF456", dp);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("r_ReturnForMonth;SF456", Pair.of("SF456", ""));
        rdbGroupColDpsMap.put("ExtendedMonthlyReturn", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private void setQuarterlyTimeSeriesDataPointRepository() throws NoSuchFieldException, IllegalAccessException {
        RdbDataPoint timeSeries = RdbDataPoint.builder().id("SF123").nid("SF123").src("RDB").column("r_CashFlowForQuarter").name("r_CashFlowForQuarter").groupName("TimeSeriesCashFlowQuarter").idLevel("PerformanceId").storeProcedure("sp2").columnPrefix("r_CashFlowForQuarter").frequency("q").build();
        DataPoint dp = DataPoint.builder().id("SF123").nid("SF123").src("RDB").name("r_CashFlowForQuarter").groupName("TimeSeriesCashFlowQuarter").tsRdb(timeSeries).build();

        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("SF123", dp);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("r_CashFlowForQuarter;SF123", Pair.of("SF123", ""));
        rdbGroupColDpsMap.put("TimeSeriesCashFlowQuarter", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private List<Map<String, Object>> mockDBCurrentDataResult() {
        List<Map<String, Object>> dbResult =  new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "E0ESP01AD4");
        row1.put("process_pillar_author", "aaa bbb");
        row1.put("ref_value","1");
        row1.put("ref_value2","");
        dbResult.add(row1);
        return dbResult;
    }

    private Flux<Map<String, Object>> mockCurrentData() {
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", "E0ESP02B6E");
        row1.put("190252", "ccc ddd");
        row1.put("190999","2");
        row1.put("199999","");
        return Flux.just(row1);
    }

    private Flux<Map<String, Object>> mockCurrentData(List<String> listData) {
        return Flux.fromStream(listData.stream()
                .filter(Objects::nonNull)
                .map(json -> {
                    try {
                        String content = JsonUtils.retrieveContent(json);
                        Map<String, Object> objMap = JsonUtils.fromJsonString(content, new TypeReference<>() {});
                        return objMap;
                    } catch (Exception e) {
                        throw new IllegalStateException("RdbCacheDataHelper getCachedDataList can't parse json string.", e);
                    }
                })
        );
    }

    private List<Map<String, Object>> mockDBGroupedDataResult() {
        List<Map<String, Object>> dbResult =  new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "FSUSA001JH");
        row1.put("DealingScheduleCountry", "swe");
        dbResult.add(row1);
        Map<String, Object> row2 = new HashMap<>();
        row2.put("UniqueId", "FSUSA001JH");
        row2.put("DealingScheduleCountry", "usa");
        dbResult.add(row2);
        Map<String, Object> row3 = new HashMap<>();
        row3.put("UniqueId", "FSUSA001JH");
        row3.put("DealingScheduleCountry", "can");
        dbResult.add(row3);

        Map<String, Object> row4 = new HashMap<>();
        row4.put("UniqueId", "FSUSA002JH");
        row4.put("DealingScheduleCountry", "gbr");
        dbResult.add(row4);
        Map<String, Object> row5 = new HashMap<>();
        row5.put("UniqueId", "FSUSA002JH");
        row5.put("DealingScheduleCountry", "fra");
        dbResult.add(row5);
        Map<String, Object> row6= new HashMap<>();
        row6.put("UniqueId", "FSUSA002JH");
        row6.put("DealingScheduleCountry", "ita");
        dbResult.add(row6);

        return dbResult;
    }

    private List<Map<String, Object>> mockDBMultipleValueResult() {
        List<Map<String, Object>> dbResult =  new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "F0000000EK");
        row1.put("ManagerName", "Eli M. Salzmann");
        dbResult.add(row1);
        Map<String, Object> row2 = new HashMap<>();
        row2.put("UniqueId", "F0000000EK");
        row2.put("ManagerName", "Robert A. Lee");
        dbResult.add(row2);
        Map<String, Object> row3 = new HashMap<>();
        row3.put("UniqueId", "F0000000EK");
        row3.put("ManagerName", "Kenneth G. Fuller");
        dbResult.add(row3);

        return dbResult;
    }

    private Flux<IdMapper> mockIdMapperFlux() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        idMappers.add(new NonEmptyIdMapper("0P000038Y3", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA002JH\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        Flux<IdMapper> idMapperFlux = Flux.just(idMappers).flatMapIterable(d -> d);

        return idMapperFlux;
    }

    private List<IdMapper> mockIdMapper() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        idMappers.add(new NonEmptyIdMapper("0P000038Y3", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA002JH\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        return idMappers;
    }

    private List<IdMapper> mockIdMapperPostTax() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\", \"PostTaxForMixSetting\":\"1\"}"));
        idMappers.add(new NonEmptyIdMapper("0P000038Y3", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA002JH\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        return idMappers;
    }

    private List<IdMapper> mockIdMapperGroupPostTax() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\", \"PostTaxForMixSetting\":\"1\"}"));
        idMappers.add(new NonEmptyIdMapper("0P000038Y3", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA002JH\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\", \"PostTaxForMixSetting\":\"1\"}"));

        return idMappers;
    }
    private List<IdMapper> mockQuarterlyIdMapper() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P000038Y3", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA002JH\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        idMappers.add(new NonEmptyIdMapper("F000002BMN", "{\"CompanyId\":\"0C00003YSB\",\"FundId\":\"FSUSA003JH\",\"PerformanceId\":\"F000002BMN\",\"PolicyId\":null,\"SecId\":\"FOUSA00D12\",\"ShareClassId\":\"E0ESP01AD5\",\"StrategyId\":\"STUSA05FF2\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        return idMappers;
    }

    private List<IdMapper> mockMultipleValueIdMapper() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("**********", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"**********\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        return idMappers;
    }

    private List<IdMapper> mockMultipleValueIdMapper2() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("**********", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"**********\",\"PerformanceId\":\"0P000038Y3\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP02B6E\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        idMappers.add(new NonEmptyIdMapper("F0000000EK", "{\"GlobalCategoryId\":\"$GC$EUEQLC\",\"Status\":\"0\",\"SecurityType\":\"FO\",\"ExchangeTradedShare\":\"0\",\"CompanyId\":\"0C00001R3D\",\"PriceReady\":\"1\",\"ShareClassId\":\"F0000000EK\",\"SecId\":\"F0000000EK\",\"FundFamilyCode\":\"0C00001R3D\",\"MorningstarCategoryId\":\"EUCA000513\",\"CountryForSale\":\"CU$$$$$FRA\",\"PerformanceId\":\"0P00006PV5\",\"MasterPortfolioId\":\"193183\",\"PeerGroupId\":\"1\",\"PerformanceReady\":\"1\",\"FundId\":\"F0000000EK\",\"PostTaxForMixSetting\":\"0\",\"DomicileCountry\":\"FRA\"}"));
        return idMappers;
    }

    private MartRequest mockCurrentMartRequest() {
        List<String> idList = Arrays.asList("0P00009T69", "0P000038Y3");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("190252", "190999", "199999")).idMappers(new ArrayList<>()).build();
    }

    private MartRequest mockGroupedDataMartRequest() {
        List<String> idList = Arrays.asList("0P00009T69");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("23759")).idMappers(new ArrayList<>()).build();
    }

    private List<String> mockGroupedRedisReturn() {
        return Arrays.asList("{ \"23759\": [ { \"23760\": \"swe\" } ], \"id\": \"FSUSA001JH\" }", "{ \"23759\": [ { \"23760\": \"usa\" } ], \"id\": \"FSUSA002JH\" }");
    }

    private MartRequest mockTimeSeriesMartRequest() {
        List<String> idList = Arrays.asList("0P00009T69", "0P000038Y3");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("190236","190252")).startDate("2017-06-01").endDate("2020-07-31").idMappers(new ArrayList<>()).build();
    }

    private MartRequest mockTimeSeriesMartRequestPostTax() {
        List<String> idList = Arrays.asList("0P00009T69", "0P000038Y3");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("190236","190252")).startDate("2017-06-01").endDate("2020-07-31").idMappers(new ArrayList<>()).postTax("1").build();
    }
    private MartRequest mockCurrentPostTaxRequest() {
        List<String> idList = Arrays.asList("0P00009T69", "0P000038Y3");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("190236","190252")).idMappers(new ArrayList<>()).postTax("1").readCache("false").build();
    }


    private MartRequest mockMonthlyTimeSeriesMartRequest() {
        List<String> idList = Arrays.asList("0P00009T69", "0P000038Y3");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("SF456")).startDate("2014-01-01").endDate("2015-05-05").idMappers(new ArrayList<>()).build();
    }

    private MartRequest mockMonthlyTimeSeriesMartRequest2() {
        List<String> idList = Arrays.asList("0P00009T69", "0P000038Y3");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("SF456")).startDate("1900-01-01").endDate("2015-05-05").idMappers(new ArrayList<>()).build();
    }

    private MartRequest mockQuarterlyTimeSeriesMartRequest() {
        List<String> idList = Arrays.asList("F000002BMN", "0P000038Y3");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("SF123")).startDate("2014-01-01").endDate("2015-04-01").idMappers(new ArrayList<>()).build();
    }

    private List<Map<String, Object>> mockDBTimeSeriesResult() {
        List<Map<String, Object>> dbResult =  new ArrayList<>();
        List<String> dates = Arrays.asList("2020-01-01","2020-01-02","2020-01-03","2020-01-04","2020-01-05");
        dates.forEach(date -> {
            Map<String, Object> row = new HashMap<>();
            row.put("UniqueId", "E0ESP01AD4");
            row.put("process_pillar_author", "aaa bbb");
            row.put("overall_rating_publish_date_utc", date);
            row.put("ref_value", "2");
            row.put("ref_value2", "");
            dbResult.add(row);
        });

        return dbResult;
    }

    private Alias mockAlias() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "value1");
        items.put("2", "value2");
        return Alias.builder().id("numberToValue").items(items).build();
    }

    private Alias mockAlias2() {
        Map<String, String> items = new HashMap<>();
        items.put("1", "11111");
        items.put("2", "22222");
        return Alias.builder().id("numberToValue2").items(items).build();
    }

    private Mono<Map<String, Collection<TsDataList>>> mockTimeSeriesRedisData(){
        TsDataList tsDataList1 =TsDataList.<TsData>builder().dpId("190252").values(List.of(TsData.builder().dateEpoc(17168).value("Jan Nel").build(),
                TsData.builder().dateEpoc(17533).value("Jan Nel").build(), TsData.builder().dateEpoc(18191).value("Jan Nel").build(), TsData.builder().dateEpoc(18264).value("Jan Nel").build())).build();
        TsDataList tsDataList2 =TsDataList.<TsData>builder().dpId("190236").values(List.of(TsData.builder().dateEpoc(17168).value("2017-01-01 00:00:00.0").build(),
                TsData.builder().dateEpoc(17533).value("2018-01-02 00:00:00.0").build(), TsData.builder().dateEpoc(18191).value("2019-10-22 00:00:00.0").build(),
                TsData.builder().dateEpoc(18264).value("2020-01-03 00:00:00.0").build())).build();
        Map<String, Collection<TsDataList>> map = new HashMap<>();
        map.put("E0ESP01AD4:2016", List.of(tsDataList1, tsDataList2));
        return Mono.just(map);
    }

    private Mono<Map<String, Collection<TsDataList>>> mockMonthlyTimeSeriesRedisData() {
        TsDataList tsDataList1 =TsDataList.<TsData>builder().dpId("SF456").values(List.of(TsData.builder().dateEpoc(16221).value("6.40720").build())).build();
        TsDataList tsDataList2 =TsDataList.<TsData>builder().dpId("SF456").values(List.of(TsData.builder().dateEpoc(16494).value("8.51004").build())).build();
        Map<String, Collection<TsDataList>> map = new HashMap<>();
        map.put("0P00009T69:2011", List.of(tsDataList1, tsDataList2));
        return Mono.just(map);
    }

    private Mono<Map<String, Collection<TsDataList>>> mockQuarterlyTimeSeriesRedisData() {
        TsDataList tsDataList1 =TsDataList.<TsData>builder().dpId("SF123").values(List.of(TsData.builder().dateEpoc(16343).value("6587071.71400").build(),
                TsData.builder().dateEpoc(16525).value("22454695.64200").build(), TsData.builder().dateEpoc(16526).value("11111").build())).build();
        Map<String, Collection<TsDataList>> map = new HashMap<>();
        map.put("F000002BMN:2011", List.of(tsDataList1));
        return Mono.just(map);
    }

    private List<Map<String, Object>> mockTimeSeriesRdbData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "E0ESP02B6E");
        row1.put("overall_rating_publish_date_utc", "2020-04-03 00:00:00.0");
        row1.put("process_pillar_author", "Eric Schultz");
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("UniqueId", "E0ESP02B6E");
        row2.put("overall_rating_publish_date_utc", "2019-04-11 00:00:00.0");
        row2.put("process_pillar_author", "Shannon Yan");
        data.add(row2);

        Map<String, Object> row3  = new HashMap<>();
        row3.put("UniqueId", "E0ESP02B6E");
        row3.put("overall_rating_publish_date_utc", "2018-04-19 00:00:00.0");
        row3.put("process_pillar_author", "George Georgiev");
        data.add(row3);

        return data;
    }

    private List<Map<String, Object>> mockTimeSeriesRdbDataWithPostTax() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "E0ESP01AD4");
        row1.put("overall_rating_publish_date_utc", "2020-04-03 00:00:00.0");
        row1.put("process_pillar_author", "Eric Schultz1");
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("UniqueId", "E0ESP01AD4");
        row2.put("overall_rating_publish_date_utc", "2019-04-11 00:00:00.0");
        row2.put("process_pillar_author", "Shannon Yan1");
        data.add(row2);

        Map<String, Object> row3  = new HashMap<>();
        row3.put("UniqueId", "E0ESP01AD4");
        row3.put("overall_rating_publish_date_utc", "2018-04-19 00:00:00.0");
        row3.put("process_pillar_author", "George Georgiev1");
        data.add(row3);

        return data;
    }

    private List<Map<String, Object>> mockMonthlyTimeSeriesRdbData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "0P000038Y3");
        row1.put("r_ReturnForMonth1", "-3.30275");
        row1.put("r_ReturnForMonth2", "5.50285");
        row1.put("Year", 2015);
        row1.put("r_ReturnForMonth3", "-1.07914");
        row1.put("r_ReturnForMonth4", "-0.18182");
        row1.put("r_ReturnForMonth5", "1.09290");
        row1.put("r_ReturnForMonth6", "-1.44144");
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("UniqueId", "0P000038Y3");
        row2.put("r_ReturnForMonth1", "-4.33962");
        row2.put("r_ReturnForMonth2", "3.74753");
        row2.put("Year", 2014);
        row2.put("r_ReturnForMonth3", "1.71103");
        row2.put("r_ReturnForMonth10", "2.54545");
        row2.put("r_ReturnForMonth12", "-0.11352");
        row2.put("r_ReturnForMonth11", "2.30496");
        row2.put("r_ReturnForMonth4", "-0.18692");
        row2.put("r_ReturnForMonth5", "1.87266");
        row2.put("r_ReturnForMonth6", "1.10294");
        row2.put("r_ReturnForMonth7", "-0.90909");
        row2.put("r_ReturnForMonth8", "2.93578");
        row2.put("r_ReturnForMonth9", "-1.96078");
        data.add(row2);

        return data;
    }

    private List<Map<String, Object>> mockQuarterlyTimeSeriesRdbData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("r_CashFlowForQuarter1", "89273645.782");
        row1.put("r_CashFlowForQuarter2", "7263458.296");
        row1.put("r_CashFlowForQuarter3", "8273656.752");
        row1.put("r_CashFlowForQuarter4", "9276576.263");
        row1.put("Year", 2015);
        row1.put("UniqueId", "0P000038Y3");
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("r_CashFlowForQuarter1", "837365.398");
        row2.put("r_CashFlowForQuarter2", "975241.178");
        row2.put("r_CashFlowForQuarter3", "16235478.527");
        row2.put("r_CashFlowForQuarter4", "2863547.843");
        row2.put("Year", 2014);
        row2.put("UniqueId", "0P000038Y3");
        data.add(row2);

        return data;
    }

    private List<Map<String, Object>> mockCurrentDataPostTaxResult(String id, String name, String date, List<Map<String, Object>> dbResult) {
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", id);
        row1.put("overall_rating_publish_date_utc", date);
        row1.put("process_pillar_author", name);
        dbResult.add(row1);
        return dbResult;
    }

    @Test
    public void retrieveTimeSeriesGroupTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockTimeSeriesGroupMartRequest();
        martRequest.setIdMappers(mockGroupIdMapper());
        setTSGroupDataPointRepository();

        when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(mockTimeSeriesRedisGroupData()).thenReturn(Mono.just(new HashMap<>()));
        when(rdbDataRepo.executeTsSqlWithoutLock(any(), any(), any(), any())).thenReturn(mockTimeSeriesRdbGroupData());
        Flux<Result> result = rdbGateway.retrieve(martRequest);

        StepVerifier.create(result.collectSortedList((r1, r2) -> r2.getId().compareTo(r1.getId())))
                .expectSubscription()
                .assertNext(list -> {
                    assertEquals(2, list.size());
                    assertEquals("0C000006T1", list.get(0).getId());
                })
                .expectComplete()
                .verify();
    }

    private void setTSGroupDataPointRepository() throws NoSuchFieldException, IllegalAccessException {

        DataPoint subDp1 = DataPoint.builder()
                .id("N47C6").nid("N47C6").src("RDB")
                .name("theme").column("theme")
                .groupName("TSthematic_consensus_equities_group_dp")
                .build();

        DataPoint DP1 = DataPoint.builder()
                .id("YRA9YDATE").nid("YRA9YDATE").src("RDB")
                .name("effective_date").column("effective_date")
                .groupName("TSthematic_consensus_equities_group_dp")
                .build();

        RdbDataPoint groupRdb = RdbDataPoint.builder()
                .id("YRA9Y").nid("YRA9Y").src("RDB")
                .column("Content")
                .name("Thematic consensus equities")
                .multi(true)
                .group(true)
                .groupName("TSthematic_consensus_equities_group_dp")
                .idLevel("CompanyId")
                .storeProcedure("sp1")
                .rdbCacheFlag("false")
                .dateColumn("YRA9YDATE")
                .subDataPoints(List.of(subDp1))
                .build();

        DataPoint groupDp = DataPoint.builder()
                .id("YRA9Y").nid("YRA9Y").src("RDB")
                .name("Thematic consensus equities")
                .multi(true).group(true)
                .groupName("TSthematic_consensus_equities_group_dp")
                .tsRdb(groupRdb)
                .build();

        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("YRA9Y", groupDp);
        dataPointMap.put("N47C6", subDp1);
        dataPointMap.put("YRA9YDATE",DP1);
        DataPointRepository.setDataPointMap(dataPointMap);


        Map<String, Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();


        String subDpId = "N47C6";
        colToDpsMap.put("theme", Pair.of(subDpId, ""));
        colToDpsMap.put("Content",Pair.of("YRA9Y", ""));
        colToDpsMap.put("effective_date",Pair.of("YRA9YDATE", ""));// (left = ID, right = additional info if needed)

        rdbGroupColDpsMap.put("TSthematic_consensus_equities_group_dp", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(rdbGroupColDpsMap);
    }

    private MartRequest mockTimeSeriesGroupMartRequest() {
        List<String> idList = List.of("0C000006T1");
        return MartRequest.builder().ids(idList).dps(List.of("YRA9Y")).startDate("2022-01-01").endDate("2022-12-31").idMappers(new ArrayList<>()).build();
    }

    private List<Map<String, Object>> mockTimeSeriesRdbGroupData() {
        List<Map<String, Object>> data = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "0C000006T1");
        row1.put("effective_date", "2022-01-31 00:00:00.0");
        row1.put("theme", "Consumer");
        data.add(row1);
        return data;
    }

    private Mono<Map<String, Collection<TsDataList>>> mockTimeSeriesRedisGroupData(){
        TsDataList tsDataList1 =TsDataList.<TsData>builder().dpId("YRA9Y").values(List.of(TsData.builder().dateEpoc(17168).value("Consumer").build())).build();
        Map<String, Collection<TsDataList>> map = new HashMap<>();
        map.put("0C000006T1:2022", List.of(tsDataList1));
        return Mono.just(map);
    }

    private List<IdMapper> mockGroupIdMapper() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0C000006T1", "{\"CompanyId\":\"0C000006T1\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0C000006T1\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        return idMappers;
    }
}

