package com.morningstar.martgateway.applications.lakehouse;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martgateway.domains.privatemodel.PrivateModelPortfolioService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdDatePair;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

public class LakeHouseGatewayTest {

    private LakeHouseGateway lakeHouseGateway;

    private PrivateModelPortfolioService privateModelPortfolioService;

    @Before
    public void beforeEach() {
        privateModelPortfolioService = mock(PrivateModelPortfolioService.class);
        lakeHouseGateway = new LakeHouseGateway(privateModelPortfolioService);
    }

    @Test
    public void retrieve() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = MartRequest.builder().idPairs(List.of(IdDatePair.builder().id("2673392").build())).dps(Arrays.asList("98129")).build();

        DataPoint dp1 = DataPoint.builder().id("98129").nid("98129").src("LH").name("Primary Sector Breakdown Municipal Taxable Percentage Long").build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("98129", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, String> values = new HashMap<>();
        values.put("98129", "0.01406");
        when(privateModelPortfolioService.getMostRecentData(anyList(), anyList())).thenReturn(Flux.just(new CurrentResult("2673392", values)));

        Flux<Result> result = lakeHouseGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d -> {
                    assertEquals(1, d.getValues().size());
                })
                .verifyComplete();
    }
}