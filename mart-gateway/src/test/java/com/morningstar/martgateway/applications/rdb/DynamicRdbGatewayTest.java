package com.morningstar.martgateway.applications.rdb;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataAsyncRepo;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataRepo;
import com.morningstar.martgateway.domains.rdb.helper.RdbCurrentCacheDataHelper;
import com.morningstar.martgateway.domains.rdb.service.DynamicRdbService;
import com.morningstar.martgateway.domains.rdb.service.DynamicRdbTsService;
import java.util.*;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
public class DynamicRdbGatewayTest {

    @Mock
    RdbDataRepo rdbDataRepo;

    @Mock
    RdbCurrentCacheDataHelper rdbCurrentCacheDataHelper;

    RdbDataAsyncRepo rdbDataAsyncRepo;
    DynamicRdbGateway dynamicRdbGateway;
    DynamicRdbTsService rdbTsService;

    private MockedStatic<MDC> mdcMockedStatic;

    @BeforeEach
    public void setUp() throws JsonMappingException {
        mdcMockedStatic = Mockito.mockStatic(MDC.class, Mockito.RETURNS_DEEP_STUBS);
        rdbDataAsyncRepo = spy(new RdbDataAsyncRepo(rdbDataRepo, 5, false));
        DynamicRdbService dynamicRdbService = new DynamicRdbService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, false);
        dynamicRdbGateway = new DynamicRdbGateway(dynamicRdbService, rdbTsService);
    }

    @AfterEach
    public void afterEachTest() {
        mdcMockedStatic.close();
    }

    @Test
    public void retrieveCurrentDataParameterTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockParameterRequestData();
        martRequest.setIdMappers(mockIdMapperParameter());
        setCurrentDataPointRepositoryParameter("1");
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean()))
                .thenReturn(mockCurrentDataParameter());

        Flux<Result> result = dynamicRdbGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d -> assertEquals("0C000006V5", d.getId()))
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveCurrentDataParameterEmptyTest() {
        MartRequest martRequest = mockParameterEmptyData();
        Flux<Result> result = dynamicRdbGateway.retrieve(martRequest);
        assertEquals(Flux.empty(), result);
    }

    @Test
    public void retrieveCurrentDataRDBEmptyParameterTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = mockParameterRequestData();
        martRequest.setIdMappers(mockIdMapperParameter());
        setCurrentDataPointRepositoryEMptyParameter("1");
        Flux<Result> result = dynamicRdbGateway.retrieve(martRequest);
        assertEquals(Flux.empty(), result);
    }

    private List<IdMapper> mockIdMapperParameter() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0C000006V5",
                "{\"CompanyId\":\"0C000006V5\",\"SecId\":\"0C000006V5\",\"Status\":\"1\",\"SecurityType\":\"CO\"}"));
        return idMappers;
    }

    private MartRequest mockParameterRequestData() {
        List<String> idList = List.of("0C000006V5");
        List<String> datapointIds = List.of("EUVFZ");
        List<String> taxonomyObjective = List.of("Climate Change Adaptation (CCA)");
        return MartRequest.builder()
                .ids(idList)
                .dps(datapointIds)
                .datapointIds(datapointIds)
                .euTaxonomyObjective(taxonomyObjective)
                .idMappers(new ArrayList<>())
                .build();
    }

    private MartRequest mockParameterEmptyData() {
        return MartRequest.builder()
                .ids(new ArrayList<>())
                .dps(new ArrayList<>())
                .datapointIds(new ArrayList<>())
                .euTaxonomyObjective(List.of("Climate Change Adaptation (CCA)"))
                .idMappers(new ArrayList<>())
                .build();
    }

    private Map<String, Object> mockParameterResponse() {
        Map<String, Object> primaryKeys = Map.of("euTaxonomyObjective", "Climate Change Adaptation (CCA)");
        Map<String, Object> valueEntry = new HashMap<>();
        valueEntry.put("primaryKeys", primaryKeys);
        valueEntry.put("datapointId", "EUVFZ");
        valueEntry.put("value", "0.00");

        Map<String, Object> investment = new HashMap<>();
        investment.put("id", "0C000006V5");
        investment.put("values", List.of(valueEntry));

        return investment;
    }

    private Flux<Map<String, Object>> mockCurrentDataParameter() {
        return Flux.just(mockParameterResponse());
    }

    private void setCurrentDataPointRepositoryParameter(String cacheFlag) throws NoSuchFieldException, IllegalAccessException {
        RdbDataPoint current = RdbDataPoint.builder()
                .id("EUVFZ")
                .nid("EUVFZ")
                .dataType("ParameterCurrentData")
                .src("RDB")
                .column("eligible_not_aligned_local_currency_capex")
                .name("Analytic Date")
                .groupName("CDeu_taxonomy_objective")
                .idLevel("CompanyId")
                .storeProcedure("sp1")
                .paramName("euTaxonomyObjective")
                .rdbCacheFlag(cacheFlag)
                .build();

        DataPoint dp = DataPoint.builder()
                .id("EUVFZ")
                .nid("EUVFZ")
                .src("RDB")
                .name("Analytic Date")
                .groupName("CDeu_taxonomy_objective")
                .currentRdb(current)
                .parameterName("euTaxonomyObjective")
                .build();

        DataPointRepository.setDataPointMap(Map.of("EUVFZ", dp));
    }

    private void setCurrentDataPointRepositoryEMptyParameter(String cacheFlag) throws NoSuchFieldException, IllegalAccessException {
        DataPoint dp = DataPoint.builder().build();
        DataPointRepository.setDataPointMap(Map.of("EUVFZ", dp));
    }
}
