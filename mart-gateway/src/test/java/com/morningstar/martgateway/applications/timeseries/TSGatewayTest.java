package com.morningstar.martgateway.applications.timeseries;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.service.TScacheService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TSGatewayTest {
    private TScacheService tsCacheService;
    private TSGateway tsGateway;

    @Before
    public void setUp(){
        tsCacheService = mock(TScacheService.class);
        tsGateway = new TSGateway(tsCacheService);
    }

    @Test
    public void retrieveTest() {
        MartRequest martRequest = MartRequest.builder().ids(List.of("0P00009T69")).dps(List.of("117")).idMappers(List.of(new NonEmptyIdMapper("0P00009T69", "{\"SecId\":\"F0000040MZ\"}"))).build();

        DataPoint dp1 = DataPoint.builder().id("HS793").nid("117").src("TSAPI").name("Daily Return Index").build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("117", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, List<V>> values = new HashMap<>();
        values.put("117", List.of(new V("2020-07-20","86.16567"),new V("2020-07-21","86.27523")));
        ArgumentCaptor<Object> captor = ArgumentCaptor.forClass(TSRequest.class);
        when(tsCacheService.retrieveTimeSeriesData((TSRequest) captor.capture())).thenReturn(Flux.just(new TimeSeriesResult("F0000040MZ", values)));
        Flux<Result> result = tsGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    List<V> value = (List<V>)d.getValues().get("117");
                    assertEquals("86.16567",value.get(0).getV());
                })
                .expectComplete()
                .verify();
        TSRequest tsRequest = (TSRequest) captor.getValue();
        assertEquals("0", tsRequest.getAdjustment());
    }

    @Test
    public void retrieveTestMultiId() {
        MartRequest martRequest = MartRequest.builder().ids(
                List.of("0P00009T69")).dps(List.of("117")).idMappers(List.of(new NonEmptyIdMapper("0P00009T69,0P00009T68", "{\"SecId\":\"F0000040MZ\"}"))).build();

        DataPoint dp1 = DataPoint.builder().id("HS793").nid("117").src("TSAPI").name("Daily Return Index").build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("117", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, List<V>> values = new HashMap<>();
        values.put("117", List.of(new V("2020-07-20","86.16567"),new V("2020-07-21","86.27523")));

        when(tsCacheService.retrieveTimeSeriesData(any()))
                .thenReturn(Flux.just(new TimeSeriesResult("F0000040MZ", values)));

        Flux<Result> result = tsGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    List<V> value = (List<V>)d.getValues().get("117");
                    assertEquals("86.16567",value.get(0).getV());
                }).expectNextCount(1).expectComplete().verify();
    }

    @Test
    public void retrieveWithSameSecId() {

        Map<String, List<V>> values = new HashMap<>();
        values.put("117", List.of(new V("2020-07-20","86.16567"),new V("2020-07-21","86.27523")));
        IdMapper mapper1 = new NonEmptyIdMapper("0P00009T69", "{\"SecId\":\"F0000040MZ\"}");
        IdMapper mapper2 = new NonEmptyIdMapper("F0000040MZ", "{\"SecId\":\"F0000040MZ\"}");

        MartRequest martRequest = MartRequest.builder().ids(List.of("0P00009T69", "F0000040MZ")).dps(List.of("117")).idMappers(List.of(mapper1,mapper2)).build();

        DataPoint dp1 = DataPoint.builder().id("HS793").nid("117").src("TSAPI").name("Daily Return Index").build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("117", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        when(tsCacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.just(new TimeSeriesResult("F0000040MZ",values)));
        Flux<Result> result = tsGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    String id = d.getId();
                    assertEquals("0P00009T69",id);
                })
                .assertNext(d ->{
                    String id = d.getId();
                    assertEquals("F0000040MZ",id);
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void retrieveCategoryTest() {
        MartRequest martRequest = MartRequest.builder().categoryCode("$FOCA$SB$$").dps(List.of("117")).universe("FO").build();

        DataPoint dp1 = DataPoint.builder().id("HS793").nid("117").src("TSAPI").name("Daily Return Index").build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("117", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, List<V>> values = new HashMap<>();
        values.put("117", List.of(new V("2020-07-20","86.16567"),new V("2020-07-21","86.27523")));
        when(tsCacheService.retrieveTimeSeriesData(any())).thenReturn(Flux.just(new TimeSeriesResult("$FOCA$SB$$",values)));
        Flux<Result> result = tsGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    List<V> value = (List<V>)d.getValues().get("117");
                    assertEquals("86.16567",value.get(0).getV());
                })
                .expectComplete()
                .verify();
    }
}
