package com.morningstar.martgateway.applications.tscacheproxy;

import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.martgateway.domains.tscacheproxy.service.TsCacheProxyService;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

public class TsCacheProxyApplicationTest {

    private TsCacheProxyService tsCacheProxyService;

    private TsCacheProxyApplication tsCacheProxyApplication;

    @Before
    public void setup() {
        this.tsCacheProxyService = Mockito.mock(TsCacheProxyService.class);
        this.tsCacheProxyApplication = new TsCacheProxyApplication(tsCacheProxyService);
    }

    @Test
    public void retrieve() {
        TSRequest tsRequest = TSRequest.builder()
                .dataId("HP010,190012,HP012")
                .secIds("5PUSA00003")
                .startDate("2020-01-01")
                .endDate("2024-01-01")
                .build();
        Mockito.when(tsCacheProxyService.getTsCacheData(ArgumentMatchers.any())).thenReturn(Flux.just(buildTsResponse()));
        Flux<TSResponse> tsResponseFlux = tsCacheProxyApplication.retrieve(tsRequest);
        StepVerifier.create(tsResponseFlux)
                    .assertNext(tsResponse -> Assertions.assertEquals("0", tsResponse.getStatus().getCode()))
                    .expectComplete()
                    .verify();
    }

    private TSResponse buildTsResponse() {
        TSResponse tsResponse = new TSResponse();
        TSStatus status = new TSStatus();
        status.setCode("0");
        status.setMsg("");
        tsResponse.setStatus(status);
        return tsResponse;
    }

}
