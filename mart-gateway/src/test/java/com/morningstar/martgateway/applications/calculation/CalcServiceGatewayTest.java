package com.morningstar.martgateway.applications.calculation;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.martgateway.domains.calc.CalculationService;
import com.morningstar.martgateway.domains.calclib.CalculationLibService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

public class CalcServiceGatewayTest {

    private CalculationService calculationService;
    private CalculationLibService calculationLibService;
    private CalcServiceGateway csGateway;

    @Before
    public void setUp(){
        calculationService = mock(CalculationService.class);
        calculationLibService = mock(CalculationLibService.class);
        csGateway = new CalcServiceGateway(calculationService,calculationLibService);
    }

    @Test
    public void retrieveTest() {
        MartRequest martRequest = MartRequest.builder().ids(List.of("0P00009T69")).dps(List.of("1159","106")).currency("USD").build();

        DataPoint dp1 = DataPoint.builder().id("1159").nid("1159").src("CALC").name("Currency Conversion").build();
        DataPoint dp2 = DataPoint.builder().id("DPPM004").nid("106").src("CDAPI").name("Total Ret 1 Mo (Mo-End)").calculation(Calculation.builder().ann("0").calSrc("CALCLIB").build()).build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("1159", dp1);
        dataPointMap.put("106", dp2);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,String> values = new HashMap<>();
        values.put("1159","test");
        values.put("106","0.1");
        when(calculationService.retrieveCalc(any())).thenReturn(Flux.just(new CurrentResult("0P00009T69",values)));
        when(calculationLibService.retrieveCalcLib(any())).thenReturn(Flux.just(new CurrentResult("0P00009T69",values)));

        Flux<Result> result = csGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->
                    assertEquals(2,d.getValues().size())
                )
                .assertNext(d ->
                    assertEquals("0.1",d.getValues().get("106"))
                )
                .expectComplete()
                .verify();
    }

    @Test
    public void handleRequestTest() {
        { // handleRequest should return false when useNewCCS is set to true
            MartRequest martRequest = MartRequest.builder()
                    .ids(List.of("0P00009T69"))
                    .dps(List.of("1159", "106"))
                    .currency("USD")
                    .useNewCCS(true)
                    .build();

            assertFalse(csGateway.handleRequest(martRequest));
        }

        { // handleRequest should return true when useNewCCS is set to false
            MartRequest martRequest = MartRequest.builder()
                    .ids(List.of("0P00009T69"))
                    .dps(List.of("1159", "106"))
                    .currency("USD")
                    .useNewCCS(false)
                    .build();

            assertTrue(csGateway.handleRequest(martRequest));
        }
    }

    @Test
    public void retrieveSplitByUniverseTest() {
        MartRequest martRequest = MartRequest.builder()
                .ids(List.of("0P00009T69", "0P00006W6Q","0P00006W6A", "F00001ECYS"))
                .dps(List.of("CS011", "HS538"))
                .currency("USD")
                .idMappers(List.of(buildIdMapper("0P00009T69", "FO"), buildIdMapper("0P00006W6Q", "ST"), buildIdMapper("F00001ECYS", true)))
                .build();

        DataPoint dp1 = DataPoint.builder().id("CS011").nid("CS011").src("RDB").name("Data Point 1").calculation(Calculation.builder().ann("0").cur(DataPoint.builder().nid("OS05M").build()).calSrc("CALCLIB").build()).build();
        DataPoint dp2 = DataPoint.builder().id("HS538").nid("HS538").src("RDB").name("Data Point 2").calculation(Calculation.builder().ann("0").cur(DataPoint.builder().nid("OS05M").build()).calSrc("CALCLIB").build()).build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("CS011", dp1);
        dataPointMap.put("HS538", dp2);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String,String> values = new HashMap<>();
        values.put("CS011","1");
        values.put("HS538","0.1");
        when(calculationService.retrieveCalc(any())).thenReturn(Flux.empty());
        when(calculationLibService.retrieveCalcLib(any())).thenReturn(Flux.just(new CurrentResult("0P00009T69",values))).thenReturn(Flux.empty()).thenReturn(Flux.empty()).thenReturn(Flux.empty());

        Flux<Result> result = csGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals(2,d.getValues().size());
                    assertEquals("0.1",d.getValues().get("HS538"));
                })
                .expectComplete()
                .verify();
        Mockito.verify(calculationLibService, times(4)).retrieveCalcLib(any());
    }

    private IdMapper buildIdMapper(String investmentId, String securityType) {
        return new NonEmptyIdMapper(investmentId, String.format("{\"SecurityType\": \"%s\"}", securityType));
    }

    private IdMapper buildIdMapper(String investmentId, boolean isPrivateModel) {
        return new NonEmptyIdMapper(investmentId, String.format("{\"IsPrivateModel\": \"%s\"}", isPrivateModel));
    }
}
