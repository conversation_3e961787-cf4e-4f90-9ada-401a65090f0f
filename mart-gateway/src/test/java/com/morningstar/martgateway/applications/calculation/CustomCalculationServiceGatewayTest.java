package com.morningstar.martgateway.applications.calculation;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.martgateway.domains.calc.CustomCalculationService;
import com.morningstar.martgateway.domains.calclib.CalculationLibService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

public class CustomCalculationServiceGatewayTest {
	private CustomCalculationService calculationService;
	private CalculationLibService calculationLibService;
	private CustomCalculationServiceGateway customCalculationServiceGateway;

	@Before
	public void setUp(){
		calculationService = mock(CustomCalculationService.class);
		calculationLibService = mock(CalculationLibService.class);
		customCalculationServiceGateway = new CustomCalculationServiceGateway(calculationService,calculationLibService);
	}

	@Test
	public void retrieveTest() {
		MartRequest martRequest = MartRequest.builder().ids(List.of("0P00009T69")).dps(List.of("1159","106")).currency("USD").build();

		DataPoint dp1 = DataPoint.builder().id("1159").nid("1159").src("CALC").name("Currency Conversion").build();
		DataPoint dp2 = DataPoint.builder().id("DPPM004").nid("106").src("CDAPI").name("Total Ret 1 Mo (Mo-End)").calculation(Calculation.builder().ann("0").calSrc("CALCLIB").build()).build();
		Map<String, DataPoint> dataPointMap = new HashMap<>();
		dataPointMap.put("1159", dp1);
		dataPointMap.put("106", dp2);
		DataPointRepository.setDataPointMap(dataPointMap);

		Map<String,String> values = new HashMap<>();
		values.put("1159","test");
		values.put("106","0.1");
		when(calculationService.retrieveCalc(any())).thenReturn(Flux.just(new CurrentResult("0P00009T69",values)));
		when(calculationLibService.retrieveCalcLib(any())).thenReturn(Flux.just(new CurrentResult("0P00009T69",values)));

		Flux<Result> result = customCalculationServiceGateway.retrieve(martRequest);
		StepVerifier.create(result)
				.expectSubscription()
				.assertNext(d ->
					assertEquals(2,d.getValues().size())
				)
				.assertNext(d ->
					assertEquals("0.1",d.getValues().get("106"))
				)
				.expectComplete()
				.verify();
	}

	@Test
	public void handleRequestTest() {
		{ // handleRequest should return false when useNewCCS is set to false
			MartRequest martRequest = MartRequest.builder()
					.ids(List.of("0P00009T69"))
					.dps(List.of("1159", "106"))
					.currency("USD")
					.useNewCCS(false)
					.build();

			assertFalse(customCalculationServiceGateway.handleRequest(martRequest));
		}

		{ // handleRequest should return true when useNewCCS is set to true
			MartRequest martRequest = MartRequest.builder()
					.ids(List.of("0P00009T69"))
					.dps(List.of("1159", "106"))
					.currency("USD")
					.useNewCCS(true)
					.build();

			assertTrue(customCalculationServiceGateway.handleRequest(martRequest));
		}
	}
}
