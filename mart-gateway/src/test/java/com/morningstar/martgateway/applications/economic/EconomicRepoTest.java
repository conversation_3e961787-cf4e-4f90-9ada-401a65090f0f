package com.morningstar.martgateway.applications.economic;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.morningstar.martgateway.domains.economic.repository.EconomicRepo;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataRepo;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
public class EconomicRepoTest {

    @Mock
    private RdbDataRepo rdbDataRepo;

    @InjectMocks
    private EconomicRepo economicRepo;

    @Test
    void testSearchAPI_success() {
        List<Map<String, Object>> mockResult = new ArrayList<>();
        Map<String, Object> mockMap = new HashMap<>();
        mockMap.put("fred_series_id", "DEXSIUS");
        mockMap.put("mimSymbol", "Singapore Dollars to U.S. Dollar Spot Exchange Rate");
        mockResult.add(mockMap);

        when(rdbDataRepo.getEconomicData()).thenReturn(mockResult);

        Mono<List<Map<String, Object>>> resultMono = economicRepo.searchAPISql();

        StepVerifier.create(resultMono)
                .assertNext(result -> {
                    assertEquals(1, result.size());
                    assertEquals("DEXSIUS", result.get(0).get("fred_series_id"));
                    assertEquals("Singapore Dollars to U.S. Dollar Spot Exchange Rate", result.get(0).get("mimSymbol"));
                })
                .verifyComplete();
        verify(rdbDataRepo).getEconomicData();

    }

    @Test
    void testMetaDataSQL_success() {
        List<Map<String, Object>> mockResult = new ArrayList<>();
        Map<String, Object> mockMap = new HashMap<>();
        mockMap.put("fred_series_id", "DEXSIUS");
        mockMap.put("frequency", "Daily");
        mockResult.add(mockMap);
        String indicatorId="DEXSIUS";

        when(rdbDataRepo.getMetaDataByIndicatorId(anyString())).thenReturn(mockResult);

        Mono<List<Map<String, Object>>> resultMono = economicRepo.metaDataAPISQL(indicatorId);

        StepVerifier.create(resultMono)
                .assertNext(result -> {
                    assertEquals(1, result.size());
                    assertEquals("DEXSIUS", result.get(0).get("fred_series_id"));
                    assertEquals("Daily", result.get(0).get("frequency"));
                })
                .verifyComplete();

        ArgumentCaptor<String> sqlCaptor = ArgumentCaptor.forClass(String.class);
        verify(rdbDataRepo).getMetaDataByIndicatorId(sqlCaptor.capture());
        assertNotNull(sqlCaptor.getValue());
    }

    @Test
     void getEconomicDataTest() {
        List<Map<String, Object>> mockResult = new ArrayList<>();
        Map<String, Object> mockMap = new HashMap<>();
        mockMap.put("date", "2000-04-01");
        mockMap.put("value", "1.97");
        mockResult.add(mockMap);
        when(rdbDataRepo.getTimeSeriesEconomicData(anyString(), anyString(), anyString())).thenReturn(mockResult);
        Mono<List<Map<String, Object>>> resultMono = economicRepo.getTimeSeriesEconomicData("DEXSIUS", "2000-01-01", "2024-01-01");
        StepVerifier.create(resultMono)
                .assertNext(result -> {
                    assertEquals(1, result.size());
                    assertEquals("2000-04-01", result.get(0).get("date"));
                    assertEquals("1.97", result.get(0).get("value"));
                })
                .verifyComplete();
    }

}