package com.morningstar.martgateway.applications.delta;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.Dataset;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.DeltaConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.domains.delta.DeltaValueService;
import com.morningstar.martgateway.domains.delta.MostRecentDeltaDetectionService;
import com.morningstar.martgateway.domains.delta.TimeSeriesDeltaDetectionService;
import com.morningstar.martgateway.domains.delta.entity.DataPointDeltaDetection;
import com.morningstar.martgateway.domains.delta.entity.DeltaDataPoint;
import com.morningstar.martgateway.domains.delta.entity.DeltaDetection;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.json.JSONObject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class DeltaGatewayTest {

    @Mock
    private MostRecentDeltaDetectionService mostRecentDeltaDetectionService;
    @Mock
    private TimeSeriesDeltaDetectionService timeSeriesDeltaDetectionService;
    @Mock
    private DeltaValueService deltaValueService;

    private DeltaGateway deltaGateway;

    @BeforeEach
    public void setup() {
        deltaGateway = new DeltaGateway(mostRecentDeltaDetectionService, timeSeriesDeltaDetectionService, deltaValueService);
        DataPointRepository.setDataPointMap(buildDataPointMap());
    }

    @Test
    public void retrieveDeltaDetections() {
        InvestmentApiRequest request = buildRequest();
        request.setDeltaDetection(true);
        Map<String, GridviewDataPoint> gridviewDataPointMap = request.getDataPoints().stream().collect(Collectors.toMap(GridviewDataPoint::getDataPointId, Function.identity()));
        Mockito.when(mostRecentDeltaDetectionService.getDelta(ArgumentMatchers.anyList(), ArgumentMatchers.anyMap(), ArgumentMatchers.any())).thenReturn(Flux.just(new DataPointDeltaDetection(new DeltaDataPoint(gridviewDataPointMap.get("TFC2N"), DataPointRepository.getByNid("TFC2N")), new DeltaDetection("F00000JU03", null))));
        Mockito.when(timeSeriesDeltaDetectionService.getDelta(ArgumentMatchers.anyList(), ArgumentMatchers.anyMap(), ArgumentMatchers.any())).thenReturn(Flux.just(new DataPointDeltaDetection(new DeltaDataPoint(gridviewDataPointMap.get("TFC0N"), DataPointRepository.getByNid("TFC0N")), new DeltaDetection("0P0000PZ28", "2020-01-01"))));
        Flux<Result> resultFlux = deltaGateway.retrieve(request);
        StepVerifier.create(resultFlux.collectList())
                .consumeNextWith(results -> {
                    Assertions.assertTrue(results.stream().anyMatch(r -> r instanceof CurrentResult && "F00000JU03".equals(r.getId())));
                    Assertions.assertTrue(results.stream().anyMatch(r -> r instanceof TimeSeriesResult && "0P0000PZ28".equals(r.getId())));
                })
                .verifyComplete();
    }

    @Test
    public void retrieveDeltaValues() {
        InvestmentApiRequest request = buildRequest();
        Map<String, GridviewDataPoint> gridviewDataPointMap = request.getDataPoints().stream().collect(Collectors.toMap(GridviewDataPoint::getDataPointId, Function.identity()));
        Mockito.when(mostRecentDeltaDetectionService.getDelta(ArgumentMatchers.anyList(), ArgumentMatchers.anyMap(), ArgumentMatchers.any())).thenReturn(Flux.just(new DataPointDeltaDetection(new DeltaDataPoint(gridviewDataPointMap.get("TFC2N"), DataPointRepository.getByNid("TFC2N")), new DeltaDetection("F00000JU03", null))));
        Mockito.when(timeSeriesDeltaDetectionService.getDelta(ArgumentMatchers.anyList(), ArgumentMatchers.anyMap(), ArgumentMatchers.any())).thenReturn(Flux.just(new DataPointDeltaDetection(new DeltaDataPoint(gridviewDataPointMap.get("TFC0N"), DataPointRepository.getByNid("TFC0N")), new DeltaDetection("0P0000PZ28", "2020-01-01"))));
        Mockito.when(deltaValueService.getDeltaValues(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(Flux.just((new CurrentResult("F00000JU03", Collections.singletonMap("TFC2N", "5.01"))), new TimeSeriesResult("0P0000PZ28", Map.of("TFC0N", List.of(new V("2020-01-01", "6.78"))))));
        Flux<Result> resultFlux = deltaGateway.retrieve(request);
        StepVerifier.create(resultFlux.collectList())
                .consumeNextWith(results -> {
                    Assertions.assertTrue(results.stream().anyMatch(r -> r instanceof CurrentResult && "F00000JU03".equals(r.getId()) && "5.01".equals(r.getValues().get("TFC2N"))));
                    Assertions.assertTrue(results.stream().anyMatch(r -> r instanceof TimeSeriesResult timeSeriesResult && "0P0000PZ28".equals(r.getId()) && "6.78".equals(timeSeriesResult.getValues().get("TFC0N").get(0).getV())));
                })
                .verifyComplete();
    }

    @Test
    public void retrieveNone() {
        InvestmentApiRequest request = buildRequest();
        request.setDeltaStartTime(null);
        Flux<Result> resultFlux = deltaGateway.retrieve(request);
        StepVerifier.create(resultFlux.collectList())
                .consumeNextWith(results -> Assertions.assertTrue(results.isEmpty()))
                .verifyComplete();
    }

    private InvestmentApiRequest buildRequest() {
        return InvestmentApiRequest.builder()
                .deltaStartTime(Instant.now())
                .idMappers(buildIdMappers())
                .dataPoints(List.of(
                        GridviewDataPoint.builder().dataPointId("TFC0N").startDate("2000-01-01").endDate("2025-01-01").build(),
                        GridviewDataPoint.builder().dataPointId("TFC2N").build()
                        )
                )
                .investments(List.of(
                        new Investment("F00000JU03"),
                        new Investment("0P0000PZ28")
                        )
                )
                .build();
    }

    private Map<String, DataPoint> buildDataPointMap() {
        return Map.of(
                "TFC0N", DataPoint.builder().nid("TFC0N")
                        .src("RDB")
                        .currentRdb(
                                    RdbDataPoint.builder()
                                            .deltaConfiguration(new DeltaConfiguration("rdb_group1",List.of(Dataset.builder().name("CurrentData.dbo.vw_TSPrice").idLevel("SecId").build())))
                                            .build()
                        )
                        .tsRdb(
                                RdbDataPoint.builder()
                                        .deltaConfiguration(new DeltaConfiguration("rdb_group2",List.of(Dataset.builder().name("TimeSeries.dbo.vw_TSPrice").idLevel("SecId").build())))
                                        .build()
                        )
                        .build(),
                "TFC2N", DataPoint.builder().nid("TFC2N")
                        .src("RDB")
                        .currentRdb(
                                RdbDataPoint.builder()
                                        .deltaConfiguration(new DeltaConfiguration("rdb_group3",List.of(Dataset.builder().name("CurrentData.dbo.vw_TSPrice2").idLevel("PerformanceId").build())))
                                        .build()
                        )
                        .tsRdb(
                                RdbDataPoint.builder()
                                        .deltaConfiguration(new DeltaConfiguration("rdb_group4",List.of(Dataset.builder().name("TimeSeries.dbo.vw_TSPrice2").idLevel("PerformanceId").build())))
                                        .build()
                        )
                        .build()
        );
    }

    private List<IdMapper> buildIdMappers() {
        String investmentId1 = "F00000JU03";
        JSONObject jsonObject1 = new JSONObject()
                .put("SecId", investmentId1)
                .put("PerformanceId", "0P0000PZ29");
        IdMapper idMapper1 = new NonEmptyIdMapper(investmentId1, jsonObject1);

        String investmentId2 = "0P0000PZ28";
        JSONObject jsonObject2 = new JSONObject()
                .put("SecId", "F00000JU02")
                .put("PerformanceId", investmentId2);
        IdMapper idMapper2 = new NonEmptyIdMapper(investmentId2, jsonObject2);

        return List.of(idMapper1, idMapper2);
    }
}
