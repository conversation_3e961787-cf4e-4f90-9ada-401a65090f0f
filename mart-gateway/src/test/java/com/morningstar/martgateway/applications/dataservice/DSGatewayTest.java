package com.morningstar.martgateway.applications.dataservice;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martgateway.domains.dataservice.service.DataServiceCaller;
import com.morningstar.martgateway.domains.dataservice.service.DataServiceTsCaller;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdDatePair;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import org.junit.Before;
import org.junit.Test;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DSGatewayTest {

    private DataServiceCaller dataServiceCaller;
    private DataServiceTsCaller dataServiceTsCaller;
    private DSGateway dsGateWay;
    private IdMapUtil idMap;

    @Before
    public void setUp(){
        dataServiceCaller = mock(DataServiceCaller.class);
        dataServiceTsCaller = mock(DataServiceTsCaller.class);
        dsGateWay = new DSGateway(dataServiceCaller,dataServiceTsCaller);
    }

    @Test
    public void retrieveTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = MartRequest.builder().ids(Arrays.asList("F00000ORQ9")).dps(Arrays.asList("91001")).idMappers(new ArrayList<>()).build();

        DataPoint dp1 = DataPoint.builder().id("91001").nid("91001").src("DSAPI").name("AircraftLoansLong").build();
        Map<String, DataPoint> datapointMap = new HashMap<>();
        datapointMap.put("91001", dp1);
        DataPointRepository.setDataPointMap(datapointMap);

        Map<String, String> values = new HashMap<>();
        values.put("91001","0.01406");
        when(dataServiceCaller.getFullDataByIdList(Arrays.asList("F00000ORQ9"),Arrays.asList(dp1),true, new ArrayList<>())).thenReturn(Flux.just(new CurrentResult("F00000ORQ9",values)));
        Flux<Result> result  = dsGateWay.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals(1,d.getValues().size());
                })
                .verifyComplete();
    }

    @Test
    public void retrieveTSTestForIdPairs() throws NoSuchFieldException, IllegalAccessException {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        IdDatePair pair1 = IdDatePair.builder().id("63027").dates(Arrays.asList(LocalDate.parse("2021-01-31", formatter), LocalDate.parse("2019-03-31", formatter))).build();
        List<IdDatePair> idDatePairs = Arrays.asList(pair1);

        MartRequest martRequest = MartRequest.builder().dps(Arrays.asList("94417")).idPairs(idDatePairs).build();
        DataPoint dp = DataPoint.builder().id("94417").nid("94417").src("DSAPI").build();
        Map<String, DataPoint> datapointMap = new HashMap<>();
        datapointMap.put("94417", dp);
        DataPointRepository.setDataPointMap(datapointMap);

        Map<String, List<V>> values = new HashMap<>();
        values.put("94417", Arrays.asList(new V("2019-03-31", "93.68618")));
        when(dataServiceTsCaller.getFullDataByIdPairs(Arrays.asList(dp), martRequest)).thenReturn(Flux.just(new TimeSeriesResult("63027", values)));

        Flux<Result> result  = dsGateWay.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals(1,d.getValues().size());
                })
                .verifyComplete();
    }

    @Test
    public void retrieveTSTest() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = MartRequest.builder().ids(Arrays.asList("F00000ORQ9")).dps(Arrays.asList("117")).startDate("2020-07-20").endDate("2020-07-21").idMappers(new ArrayList<>()).build();

        DataPoint dp1 = DataPoint.builder().id("HS793").nid("117").src("DSAPI").name("Daily Return Index").build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("117", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, List<V>> values = new HashMap<>();
        values.put("117",Arrays.asList(new V("2020-07-20","86.16567"),new V("2020-07-21","86.27523")));
        when(dataServiceTsCaller.getFullDataByIdList(Arrays.asList("F00000ORQ9"),Arrays.asList(dp1),true,"2020-07-20","2020-07-21", new ArrayList<>())).thenReturn(Flux.just(new TimeSeriesResult("F00000ORQ9",values)));
        Flux<Result> result  = dsGateWay.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals(1,d.getValues().size());
                })
               .verifyComplete();
    }
}
