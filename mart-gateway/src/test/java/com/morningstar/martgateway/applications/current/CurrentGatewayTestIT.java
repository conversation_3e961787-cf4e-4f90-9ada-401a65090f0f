package com.morningstar.martgateway.applications.current;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.martgateway.TestMartAPIApplication;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestMartAPIApplication.class)
public class CurrentGatewayTestIT {

    @Autowired
    private CurrentGateway currentGateway;

    @Ignore
    @Test
    public void retrieveTest() {
        CountDownLatch count = new CountDownLatch(1);
        List<String> idList = Arrays.asList("0P00000J90", "0P00001DET", "0P00001DPG");
        List<String> dpList = Arrays.asList("4198", "4199", "29225", "4197", "60915", "67268", "7069", "7072");
        MartRequest request = MartRequest.builder().ids(idList).dps(dpList).build();
        Flux<Result> results = currentGateway.retrieve(request);

        Mono.when(results).block();
        StepVerifier.create(results).expectFusion(3);

//        results.blockLast(Duration.ofSeconds(10));
    }
}
