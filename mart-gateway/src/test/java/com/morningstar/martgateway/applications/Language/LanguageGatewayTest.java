package com.morningstar.martgateway.applications.Language;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataAsyncRepo;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataRepo;
import com.morningstar.martgateway.applications.language.LanguageGateway;
import com.morningstar.martgateway.domains.language.LanguageService;
import com.morningstar.martgateway.domains.rdb.helper.RdbCurrentCacheDataHelper;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LanguageGatewayTest {

    @Mock
    RdbDataRepo rdbDataRepo;

    @Mock
    RdbCurrentCacheDataHelper rdbCurrentCacheDataHelper;

    LanguageGateway languageGateway;

    RdbDataAsyncRepo rdbDataAsyncRepo;

    @Before
    public void setUp() throws JsonMappingException {
        rdbDataAsyncRepo = spy(new RdbDataAsyncRepo(rdbDataRepo, 5, false));
        LanguageService languageService = new LanguageService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, false);
        languageGateway = new LanguageGateway(languageService);
    }

    @Test
    public void retrieveLanguageDataTest() {
        MartRequest martRequest = mockLanguageMartRequest("OS01W", "FRA");
        martRequest.setIdMappers(mockIdMapper());
        DataPointRepository.setDataPointMap(mockDataPointRepository());

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("Name", Pair.of("OS01W", ""));
        rdbGroupColDpsMap.put("NonEnglishName", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(mockRdbGroupColDpsMap());

        doReturn(Mono.just(mockDbResult())).when(rdbDataAsyncRepo).executeProcedure(anyString(), any(), anyString(), anyLong(), anyString());
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());
        Flux<Result> result = languageGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals("Name123", d.getValues().get("OS01W"));
                    assertEquals(1, d.getPrimaryKeys().size());
                    assertEquals(true, d.getPrimaryKeys().containsKey("LanguageCurrentData"));
                })
               .expectComplete()
               .verify();
    }

    @Test
    public void retrieveLanguageDataNoLanguageConfigTest() {
        MartRequest martRequest = mockLanguageMartRequest("OS01W", "SWE");
        Flux<Result> result = languageGateway.retrieve(martRequest);
        assertEquals(Flux.empty(), result);
    }

    @Test
    public void retrieveLanguageDataNoLanguageDpTest() {
        MartRequest martRequest = mockLanguageMartRequest("190252", "FRA");
        Flux<Result> result = languageGateway.retrieve(martRequest);
        assertEquals(Flux.empty(), result);
    }

    @Test
    public void retrieveLanguageDataEnglishOrEmptyTest() {
        MartRequest martRequest = mockLanguageMartRequest("OS01W", "ENG");
        Flux<Result> result = languageGateway.retrieve(martRequest);
        assertEquals(Flux.empty(), result);

        martRequest = mockLanguageMartRequest("OS01W", "");
        result = languageGateway.retrieve(martRequest);
        assertEquals(Flux.empty(), result);

        martRequest = mockLanguageMartRequest("OS01W", null);
        result = languageGateway.retrieve(martRequest);
        assertEquals(Flux.empty(), result);
    }

    @Test
    public void retrieveLanguageMultiValueDataTest() {
        MartRequest martRequest = mockLanguageMartRequest("OF015", "CHS");
        martRequest.setIdMappers(mockIdMapper());
        DataPointRepository.setDataPointMap(mockDataPointRepository());

        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("Name", Pair.of("OF015", ""));
        rdbGroupColDpsMap.put("FundManagerNonEnglishName", colToDpsMap);
        DataPointRepository.setRdbGroupColDpsMap(mockRdbGroupColDpsMap());

        doReturn(Mono.just(mockMultiValueDbResult())).when(rdbDataAsyncRepo).executeProcedure(anyString(), any(), anyString(), anyLong(), anyString());
        when(rdbCurrentCacheDataHelper.getCachedData(any(), any(), any(), any(), anyBoolean())).thenReturn(Flux.empty());
        Flux<Result> result = languageGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                })
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals(3, ((GroupResult)d).getValues().get("OF015").size());
                    assertEquals(1, d.getPrimaryKeys().size());
                    assertEquals(true, d.getPrimaryKeys().containsKey("LanguageCurrentData"));
                })
                .expectComplete()
                .verify();
    }

    private MartRequest mockLanguageMartRequest(String dp, String lang) {
        List<String> idList = Arrays.asList("0P00009T69");
        return MartRequest.builder().ids(idList).dps(Arrays.asList(dp)).language(lang).idMappers(new ArrayList<>()).build();
    }

    private List<IdMapper> mockIdMapper() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        return idMappers;
    }

    private Map<String, DataPoint> mockDataPointRepository() {
        RdbDataPoint currentRdb = RdbDataPoint.builder().id("OS01W").nid("OS01W").dataType("CurrentData").src("RDB").column("Name").name("Name").groupName("EnglishName").idLevel("SecId").storeProcedure("sp1").rdbCacheFlag("3").build();
        RdbDataPoint languageRdb = RdbDataPoint.builder().id("OS01W").nid("OS01W").dataType("LanguageCurrentData").src("RDB").column("Name").name("Name").groupName("NonEnglishName").idLevel("SecId").storeProcedure("sp2").rdbCacheFlag("3").build();
        DataPoint dp = DataPoint.builder().id("OS01W").nid("OS01W").src("RDB").name("Name").currentRdb(currentRdb).languageCurrentRdb(languageRdb).build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("OS01W", dp);

        RdbDataPoint rdbDp = RdbDataPoint.builder().id("190252").nid("190252").src("RDB").column("process_pillar_author").name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("3").build();
        DataPoint dp2 = DataPoint.builder().id("190252").nid("190252").src("RDB").name("process_pillar_author").groupName("MorningstarMedalistRatings").currentRdb(rdbDp).build();
        dataPointMap.put("190252", dp2);

        RdbDataPoint currentRdb2 = RdbDataPoint.builder().id("OF015").multipleValues(true).nid("OF015").dataType("CurrentData").src("RDB").column("Name").name("Name").groupName("FundManagerEnglishName").idLevel("SecId").storeProcedure("sp1").rdbCacheFlag("3").build();
        RdbDataPoint languageRdb2 = RdbDataPoint.builder().id("OF015").multipleValues(true).nid("OF015").dataType("LanguageCurrentData").src("RDB").column("Name").name("Name").groupName("FundManagerNonEnglishName").idLevel("SecId").storeProcedure("sp2").rdbCacheFlag("3").build();
        DataPoint dp3 = DataPoint.builder().id("OF015").nid("OF015").src("RDB").name("Name").currentRdb(currentRdb2).languageCurrentRdb(languageRdb2).build();
        dataPointMap.put("OF015", dp3);

        return dataPointMap;
    }

    private List<Map<String, Object>> mockDbResult() {
       List<Map<String, Object>> dbResult = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "FOUSA00DOU");
        row1.put("Name", "Name123");
        dbResult.add(row1);
        return dbResult;
    }

    private List<Map<String, Object>> mockMultiValueDbResult() {
        List<Map<String, Object>> dbResult = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "FOUSA00DOU");
        row1.put("FundManagerName", "PERKSD.EDWARD");
        dbResult.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("UniqueId", "FOUSA00DOU");
        row2.put("FundManagerName", "BRIGHTONTODD");
        dbResult.add(row2);

        Map<String, Object> row3 = new HashMap<>();
        row3.put("UniqueId", "FOUSA00DOU");
        row3.put("FundManagerName", "CIRCLEBRENDAN");
        dbResult.add(row3);

        return dbResult;
    }

    private Map<String,Map<String, Pair<String, String>>> mockRdbGroupColDpsMap() {
        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("Name", Pair.of("OS01W", ""));
        rdbGroupColDpsMap.put("NonEnglishName", colToDpsMap);

        colToDpsMap.put("FundManagerName", Pair.of("OF015", ""));
        rdbGroupColDpsMap.put("FundManagerNonEnglishName", colToDpsMap);

        return rdbGroupColDpsMap;
    }
}

