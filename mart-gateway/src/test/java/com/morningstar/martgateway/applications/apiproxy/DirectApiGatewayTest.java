package com.morningstar.martgateway.applications.apiproxy;

import static org.mockito.ArgumentMatchers.any;

import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesRequestEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesResponseEntity;
import com.morningstar.martgateway.domains.apiproxy.service.DirectApiProxyService;
import org.junit.Before;
import org.junit.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class DirectApiGatewayTest {

    private DirectApiProxyService directApiProxyService;
    private DirectApiGateway directApiGateway;

    @Before
    public void setup() {
        directApiProxyService = mock(DirectApiProxyService.class);
        directApiGateway = new DirectApiGateway(directApiProxyService);
    }

    @Test
    public void asyncRetrieveSecuritiesInvalid() {
        when(directApiProxyService.getAllData(any())).thenReturn(Mono.empty());
        Mono<SecuritiesResponseEntity> result = directApiGateway.asyncRetrieveSecurities(new SecuritiesRequestEntity());
        StepVerifier.create(result)
                .expectSubscription()
                .verifyComplete();
    }
}
