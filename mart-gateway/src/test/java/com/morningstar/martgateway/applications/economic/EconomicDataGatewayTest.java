package com.morningstar.martgateway.applications.economic;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataRequest;
import com.morningstar.martgateway.domains.economic.entity.EconomicDataResult;
import com.morningstar.martgateway.domains.economic.entity.EconomicReferenceDataResponse;
import com.morningstar.martgateway.domains.economic.entity.TimeSeriesEconomicData;
import com.morningstar.martgateway.domains.economic.repository.EconomicRepo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class EconomicDataGatewayTest {

    @Mock
    private EconomicRepo economicRepo;

    @InjectMocks
    private EconomicDataGateway economicDataGateway;

    @Test
    void testRetrieve_withValidFilter() {

        EconomicDataRequest request = new EconomicDataRequest();
        request.setEconomicDataFilter("FRED");

        Map<String, Object> mockData = Map.of(
                "fred_series_id", "DEXSIUS",
                "economic_indicator_name", "Singapore Dollars to U.S. Dollar Spot Exchange Rate",
                "description", "FRED, Singapore Dollars to U.S. Dollar Spot Exchange Rate, Not Seasonally Adjusted, Singapore Dollars to One U.S. Dollar"
        );

        when(economicRepo.searchAPISql()).thenReturn(Mono.just(List.of(mockData)));

        Flux<EconomicDataResult> resultFlux = economicDataGateway.retrieve(request);

        StepVerifier.create(resultFlux)
                .assertNext(result -> {
                    assertNotNull(result);
                    assertEquals(Status.OK, result.getStatus());
                    assertNotNull(result.getIndicators());
                    assertEquals(1, result.getIndicators().size());

                    EconomicReferenceDataResponse response = result.getIndicators().get(0);
                    assertEquals("DEXSIUS", response.getId());
                    assertEquals("DEXSIUS", response.getSeriesId());
                    assertEquals("Singapore Dollars to U.S. Dollar Spot Exchange Rate", response.getMimSymbol());
                    assertEquals(
                            "FRED, Singapore Dollars to U.S. Dollar Spot Exchange Rate, Not Seasonally Adjusted, Singapore Dollars to One U.S. Dollar",
                            response.getDescription()
                    );
                })
                .verifyComplete();
    }

    @Test
    void testRetrieve_withEmptyFilter() {
        EconomicDataRequest request = new EconomicDataRequest();
        request.setEconomicDataFilter(null);

        Flux<EconomicDataResult> resultFlux = economicDataGateway.retrieve(request);

        StepVerifier.create(resultFlux)
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testRetrieve_MetaData() {

        EconomicDataRequest request = new EconomicDataRequest();
        request.setIndicatorId("DEXSIUS");

        Map<String, Object> mockData = Map.of(
                "fred_series_id", "DEXSIUS",
                "frequency", "Daily",
                "economic_indicator_name", "Singapore Dollars to U.S. Dollar Spot Exchange Rate"
        );

        when(economicRepo.metaDataAPISQL(request.getIndicatorId())).thenReturn(Mono.just(List.of(mockData)));

        Flux<EconomicDataResult> resultFlux = economicDataGateway.retrieve(request);

        StepVerifier.create(resultFlux)
                .assertNext(result -> {
                    assertNotNull(result);
                    assertEquals(Status.OK, result.getStatus());
                    assertNotNull(result.getIndicators());
                    assertEquals(1, result.getIndicators().size());

                    EconomicReferenceDataResponse response = result.getIndicators().get(0);
                    assertEquals("DEXSIUS", response.getSeriesId());
                    assertEquals("Daily", response.getFrequency());
                    assertEquals("Singapore Dollars to U.S. Dollar Spot Exchange Rate", response.getTitle()
                    );
                })
                .verifyComplete();
    }

    @Test
    void retrieveTimeSeriesFredEconomicDataTest() {

        List<Map<String, Object>> mockResult = new ArrayList<>();
        Map<String, Object> mockMap = new HashMap<>();
        mockMap.put("date", "2000-04-01");
        mockMap.put("value", "1.97");
        mockResult.add(mockMap);

        when(economicRepo.getTimeSeriesEconomicData(anyString(), anyString(), anyString()))
                .thenReturn(Mono.just((mockResult)));

        EconomicDataRequest request = EconomicDataRequest.builder()
                .fredSeriesId("DEXSIUS")
                .startDate("2000-01-01")
                .endDate("2024-01-01")
                .build();
        Flux<EconomicDataResult> resultFlux = economicDataGateway.retrieve(request);
        StepVerifier.create(resultFlux)
                .assertNext(result -> {
                    assertEquals(Status.OK, result.getStatus());
                    assertNotNull(result.getIndicators());

                    List<EconomicReferenceDataResponse> indicators = result.getIndicators();
                    assertEquals(1, indicators.size());
                    assertEquals("DEXSIUS", indicators.get(0).getId());

                    TimeSeriesEconomicData timeSeriesEconomicData = indicators.get(0).getTimeSeriesEconomicData();
                    assertNotNull(timeSeriesEconomicData);
                    assertNotNull(timeSeriesEconomicData.getDates());
                    assertNotNull(timeSeriesEconomicData.getValues());

                    List<String> dates = timeSeriesEconomicData.getDates();
                    List<String> values = timeSeriesEconomicData.getValues();
                    assertEquals(1, dates.size());
                    assertEquals(1, values.size());
                    assertEquals("2000-04-01", dates.get(0));
                    assertEquals("1.97", values.get(0));

                })
                .verifyComplete();
    }
}