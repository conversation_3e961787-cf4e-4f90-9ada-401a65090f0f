package com.morningstar.martgateway.applications.current;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martgateway.domains.current.CurrentManager;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import org.junit.Before;
import org.junit.Test;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CurrentGatewayTest {

    private CurrentManager currentManager;
    private CurrentGateway currentGateway;
    private IdMapUtil idMap;


    @Before
    public void setUp(){
        currentManager = mock(CurrentManager.class);
        idMap = mock(IdMapUtil.class);
        currentGateway = new CurrentGateway(currentManager);
    }

    @Test
    public void retrieveTest() throws NoSuchFieldException, IllegalAccessException {
        List<String> idList = Arrays.asList("0P00009T69");
        List<IdMapper> idMappers = Arrays.asList(new NonEmptyIdMapper("FOUSA00DOU", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00002QW5\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"FOUSA00DOU\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));
        MartRequest martRequest = MartRequest.builder().ids(idList).dps(Arrays.asList("81280","25200","23083")).idMappers(idMappers).currency("USD").build();

        Map<String,String> mapping = new HashMap<>();
        mapping.put("CU$$$$$USD","US Dollar");
        List<DataPoint> dps = Arrays.asList(DataPoint.builder().nid("25200").name("Currency").src("CDAPI").mappingSrc(DataPoint.builder().nid("81281").name("Currency Id").src("CDAPI").build()).mappingRef(Alias.builder().id("CurrencyIdNamewithCU").items(mapping).build()).build());
        DataPoint dp1 = DataPoint.builder().id("81280").nid("81280").src("CDAPI").name("Performance Currency Change").groupName("1dOperationGroup").group(true).subDataPoints(dps).build();
        DataPoint dp2 = DataPoint.builder().id("23083").nid("23083").src("CDAPI").name("Legal Name").groupName("1dOperationGroup").group(false).build();

        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("81280", dp1);
        dataPointMap.put("23083", dp2);
        DataPointRepository.setDataPointMap(dataPointMap);

        Set<String> subDataPointIdSet = new HashSet<>();
        subDataPointIdSet.add("25200");
        DataPointRepository.setSubDataPointIdSet(subDataPointIdSet);

        Map<String,String> values = new HashMap<>();
        values.put("25200","US Dollar");
        values.put("23083","test name");
        when(currentManager.retrieveCurrentResult(idList,Arrays.asList(dp2),true, idMappers)).thenReturn(Flux.just(new CurrentResult("0P00009T69",values)));
        when(currentManager.retrieveGroupResult(idList,subDataPointIdSet,Arrays.asList(dp1),true, idMappers)).thenReturn(Flux.just(new CurrentResult("0P00009T69",values)));

        Flux<Result> result = currentGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals(2,d.getValues().size());
                })
                .assertNext(d ->{
                    assertEquals("US Dollar",d.getValues().get("25200"));
                })
                .expectComplete()
                .verify();
        String expectResult1 = "/v1/security-data/investment-id/0P00009T69?dps=81280,25200,23083&format=json&currency=USD";
        assertEquals(expectResult1, martRequest.getRequestParam("/v1/security-data/investment-id/0P00009T69", "dps=81280,25200,23083&format=json&currency=USD"));

        String categoryCode = "$FOCA$UB$$";
        String peerGroupId="1";
        String regionId="1";
        String readCache="false";
        DataPoint dataPoint = DataPoint.builder().groupName("CategoryMonthEndTrailingPerformanceAverage").idLevel("CategoryId").nid("53118").id("53118").group(false).name("MTA_CategoryId").src("CDAPI").build();
        List<DataPoint> dataPoints = Arrays.asList(dataPoint);
        MartRequest martCategoryRequest = MartRequest.builder()
                .categoryCode(categoryCode)
                .peerGroupId(peerGroupId)
                .regionId(regionId)
                .dps(Arrays.asList("53118".split(",")))
                .readCache(readCache)
                .build();
        Map<String,String> categoryValue = new HashMap<>();
        categoryValue.put("53118","$FOCA$UB$$");
        dataPointMap.put("53118",dataPoint);
        when(currentManager.retrieveCategoryResult(martCategoryRequest.getPeerGroupId(),martCategoryRequest.getRegionId(),martCategoryRequest.getCategoryCode(),dataPoints,false)).thenReturn(Flux.just(new CurrentResult("$FOCA$UB$$_1_1",categoryValue)));
        Flux<Result> result1 = currentGateway.retrieve(martCategoryRequest);
        StepVerifier.create(result1)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals(1,d.getValues().size());
                    assertEquals("$FOCA$UB$$",d.getValues().get("53118"));
                })
                .expectComplete()
                .verify();
        String expectResult2 = "/v1/security-data/category-data?dps=53118&category-code=$FOCA$UB$$&peer-group-id=1&region-id=1&format=json&read-cache=false";
        assertEquals(expectResult2, martCategoryRequest.getRequestParam("/v1/security-data/category-data", "dps=53118&category-code=$FOCA$UB$$&peer-group-id=1&region-id=1&format=json&read-cache=false"));
    }
}
