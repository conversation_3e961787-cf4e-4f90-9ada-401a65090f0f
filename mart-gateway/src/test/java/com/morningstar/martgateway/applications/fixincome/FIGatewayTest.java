package com.morningstar.martgateway.applications.fixincome;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martgateway.domains.fi.service.FICaller;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import org.junit.Before;
import org.junit.Test;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

public class FIGatewayTest {
    private FIGateway fiGateway;
    private FICaller FICaller;
    private IdMapUtil idMap;

    @Before
    public void setUp(){
        FICaller = mock(FICaller.class);
        idMap = mock(IdMapUtil.class);
        fiGateway = new FIGateway(FICaller);
    }

    @Test
    public void retrieve() throws NoSuchFieldException, IllegalAccessException {
        MartRequest martRequest = MartRequest.builder().ids(Arrays.asList("F00000ORQ9")).dps(Arrays.asList("91001")).idMappers(new ArrayList<>()).readCache("").build();

        DataPoint dp1 = DataPoint.builder().id("91001").nid("91001").src("FIAPI").name("AircraftLoansLong").build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("91001", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);

        Map<String, String> values = new HashMap<>();
        values.put("91001","0.01406");
        when(FICaller.getFullDataByIdList(Arrays.asList("F00000ORQ9"),Arrays.asList(dp1), new ArrayList<>(), "")).thenReturn(Flux.just(new CurrentResult("F00000ORQ9",values)));

        Flux<Result> result  = fiGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals(1,d.getValues().size());
                })
                .verifyComplete();
    }
}