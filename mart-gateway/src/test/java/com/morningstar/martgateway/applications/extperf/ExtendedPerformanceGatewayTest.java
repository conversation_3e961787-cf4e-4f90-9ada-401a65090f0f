package com.morningstar.martgateway.applications.extperf;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.martgateway.applications.extendedperformance.ExtendedPerformanceGateway;
import com.morningstar.martgateway.domains.rdb.cache.TsDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.TsFrequencyDataLoader;
import com.morningstar.martgateway.domains.rdb.helper.RdbTsCacheDataHelper;
import com.morningstar.martgateway.domains.rdb.service.RdbTsService;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataAsyncRepo;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataRepo;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEventPublisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class ExtendedPerformanceGatewayTest {

    @Mock
    RdbDataRepo rdbDataRepo;

    @Mock
    private RdbTsCacheDataHelper rdbTsCacheDataHelper;
    private TsFrequencyDataLoader tsFrequencyDataLoader;
    @Mock
    private TsDataLoader tsDataLoader;
    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    ExtendedPerformanceGateway extPerfGateway;

    RdbDataAsyncRepo rdbDataAsyncRepo;

    @Before
    public void setUp() throws JsonMappingException {
        rdbDataAsyncRepo = spy(new RdbDataAsyncRepo(rdbDataRepo, 5, false));
        tsFrequencyDataLoader = new TsFrequencyDataLoader(rdbDataAsyncRepo);
        RdbTsService rdbTsService = new RdbTsService(rdbTsCacheDataHelper, tsFrequencyDataLoader, tsDataLoader, applicationEventPublisher, true);
        extPerfGateway = new ExtendedPerformanceGateway(rdbTsService);
    }

    @Test
    public void retrieveExtPerfTest() {
        MartRequest martRequest = mockMartRequest();
        martRequest.setIdMappers(mockIdMapper());
        DataPointRepository.setDataPointMap(mockDataPointRepository());

        DataPointRepository.setRdbGroupColDpsMap(mockRdbGroupColDpsMap());

        when(rdbTsCacheDataHelper.getCacheData(any(), any(), any(), any(), anyBoolean())).thenReturn(Mono.just(new HashMap<>()));
        doReturn(Mono.just(mockDbResult())).when(rdbDataAsyncRepo).executeTsSql(anyString(), anyString(), anyString(), anyString(), anyLong(), anyString(), anyList());
        Flux<Result> result = extPerfGateway.retrieve(martRequest);

        StepVerifier.create(result)
                .expectSubscription()
                .assertNext(d ->{
                    assertEquals("0P00009T69", d.getId());
                    assertEquals(1, d.getValues().size());
                    assertEquals(5, ((TimeSeriesResult)d).getValues().get("HP010").size());
                })
               .expectComplete()
               .verify();
    }

    @Test
    public void noExtPerfTest() {
        MartRequest martRequest = mockMartRequest();
        martRequest.setExtendedPerformance("");
        Flux<Result> result = extPerfGateway.retrieve(martRequest);
        verifyEmptyResult(result);
    }

    @Test
    public void noDpIdsTest() {

        MartRequest martRequest = mockMartRequest();
        martRequest.setDps(Collections.emptyList());
        Flux<Result> result = extPerfGateway.retrieve(martRequest);
        verifyEmptyResult(result);

        martRequest.setIds(Collections.emptyList());
        Flux<Result> result2 = extPerfGateway.retrieve(martRequest);
        StepVerifier.create(result2)
                .expectNextCount(0)
                .verifyComplete();

        martRequest = mockMartRequest();
        martRequest.setIds(Collections.emptyList());
        Flux<Result> result3 = extPerfGateway.retrieve(martRequest);
        StepVerifier.create(result3)
                .expectNextCount(0)
                .verifyComplete();
    }

    private void verifyEmptyResult(Flux<Result> result) {
        StepVerifier.create(result)
                .consumeNextWith(r -> {
                    assertEquals("0P00009T69", r.getId());
                    assertEquals(0, r.getValues().size());
                })
                .verifyComplete();
    }

    @Test
    public void noExtPerfDpTest() {
        MartRequest martRequest = mockMartRequest();
        martRequest.setDps(List.of("190252"));
        Flux<Result> result = extPerfGateway.retrieve(martRequest);
        StepVerifier.create(result)
                .consumeNextWith(r -> {
                    assertEquals("0P00009T69", r.getId());
                    assertEquals(0, r.getValues().size());
                })
                .verifyComplete();
    }

    private MartRequest mockMartRequest() {
        List<String> idList = Arrays.asList("0P00009T69");
        return MartRequest.builder().ids(idList).dps(Arrays.asList("HP010")).idMappers(new ArrayList<>()).startDate("2023-01-01").endDate("2023-12-31").extendedPerformance("1").readCache("false").build();
    }

    private List<IdMapper> mockIdMapper() {
        List<IdMapper> idMappers = new ArrayList<>();
        idMappers.add(new NonEmptyIdMapper("0P00009T69", "{\"CompanyId\":\"0C00001YSB\",\"FundId\":\"FSUSA001JH\",\"PerformanceId\":\"0P00009T69\",\"PolicyId\":null,\"SecId\":\"FOUSA00DOU\",\"ShareClassId\":\"E0ESP01AD4\",\"StrategyId\":\"STUSA05FF1\",\"MasterPortfolioId\":\"1990\",\"SubAccountId\":null,\"AggregateId\":\"AG0000001A\",\"MorningstarCategoryId\":\"$FOCA$MA$$\",\"GlobalCategoryId\":\"$GC$MODALL\"}"));

        return idMappers;
    }

    private Map<String, DataPoint> mockDataPointRepository() {
        RdbDataPoint extPerfRdb = RdbDataPoint.builder().id("HP010").nid("HP010").dataType("TimeSeries").src("RDB").columnPrefix("ReturnForMonth").frequency("m").groupName("SPExtendedReturnMonth").idLevel("SecId").storeProcedure("sp1").rdbCacheFlag("2").build();
        RdbDataPoint tsRdb = RdbDataPoint.builder().id("HP010").nid("HP010").dataType("LanguageCurrentData").src("RDB").columnPrefix("ReturnForMonth").frequency("m").name("Name").groupName("SPGrossReturnMonth").idLevel("SecId").storeProcedure("sp2").rdbCacheFlag("2").build();
        DataPoint dp = DataPoint.builder().id("HP010").nid("HP010").src("RDB").name("Return").tsRdb(tsRdb).extendedPerformanceRdb(extPerfRdb).build();
        Map<String, DataPoint> dataPointMap = new HashMap<>();
        dataPointMap.put("HP010", dp);

        RdbDataPoint rdbDp = RdbDataPoint.builder().id("190252").nid("190252").src("RDB").column("process_pillar_author").name("process_pillar_author").groupName("MorningstarMedalistRatings").idLevel("ShareClassId").storeProcedure("SELECT").rdbCacheFlag("3").build();
        DataPoint dp2 = DataPoint.builder().id("190252").nid("190252").src("RDB").name("process_pillar_author").groupName("MorningstarMedalistRatings").currentRdb(rdbDp).build();
        dataPointMap.put("190252", dp2);

        return dataPointMap;
    }

    private List<Map<String, Object>> mockDbResult() {
       List<Map<String, Object>> dbResult = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("UniqueId", "FOUSA00DOU");
        row1.put("Year", 2023);
        row1.put("ReturnForMonth1", "4.17971");
        row1.put("ReturnForMonth2", "1.10827");
        row1.put("ReturnForMonth3", "0.56471");
        row1.put("ReturnForMonth4", "-3.57503");
        row1.put("ReturnForMonth6", "6.44576");
        dbResult.add(row1);
        return dbResult;
    }

    private Map<String,Map<String, Pair<String, String>>> mockRdbGroupColDpsMap() {
        Map<String,Map<String, Pair<String, String>>> rdbGroupColDpsMap = new HashMap<>();
        Map<String, Pair<String, String>> colToDpsMap = new HashMap<>();
        colToDpsMap.put("ReturnForMonth;HP010", Pair.of("HP010", "DECIMAL"));
        rdbGroupColDpsMap.put("SPExtendedReturnMonth", colToDpsMap);
        return rdbGroupColDpsMap;
    }
}

