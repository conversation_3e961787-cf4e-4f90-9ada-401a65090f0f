package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DsDataPointLoaderTest {

    private DsDataPointLoader dsDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        dsDataPointLoader = new DsDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        when(datapointConfigFileService.getResourceAsString("config/ds_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/ds_datapoints.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }

    @Test
    public void testDataPoints() {

        dsDataPointLoader.loadDataPoints(context);

        { // no Subgroup
            DataPoint dataPoint = context.getDataPointById("90100");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), equalTo("CategoryESGCarbonScores"));
            assertThat(dataPoint.getIdLevel(), equalTo("MorningstarCategoryId"));

            assertThat(dataPoint.getDataServiceProperty(), notNullValue());

            assertThat(dataPoint.getDataServiceProperty().getDataGroup(), notNullValue());
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getBucketName(), equalTo("mstar-dwm-managedportfolio-esg-result"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getFrequency(), equalTo("q"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getLocation(), equalTo("category/{id}/{date}"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupName(), equalTo("CategoryESGCarbonScores"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getMulti(), equalTo("false"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupDpNid(), nullValue());

        }

        { // with Subgroup
            DataPoint dataPoint = context.getDataPointById("PMP04");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), equalTo("LookThroughPortfolioHolding"));
            assertThat(dataPoint.getIdLevel(), equalTo("MasterPortfolioId"));

            assertThat(dataPoint.getDataServiceProperty(), notNullValue());
            assertThat(dataPoint.getDataServiceProperty().getDataType(), equalTo("PortfolioHoldings"));
            assertThat(dataPoint.getDataServiceProperty().getAvroField(), equalTo("LookThroughHoldings"));

            assertThat(dataPoint.getDataServiceProperty().getDataGroup(), notNullValue());
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getBucketName(), equalTo("{mart_data} "));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getFrequency(), equalTo("q"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getLocation(), equalTo("Holding/PrivateModel/LookThrough/"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupName(), equalTo("LookThroughPortfolioHolding"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getMulti(), equalTo("false"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupDpNid(), nullValue());

            assertThat(dataPoint.getSubDataPoints(), notNullValue());
            assertThat(dataPoint.getSubDataPoints(), hasSize(7));
        }

        { // with groupDpNid
            DataPoint dataPoint = context.getDataPointById("90269");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), equalTo("NPXData"));
            assertThat(dataPoint.getIdLevel(), equalTo("FundId"));

            assertThat(dataPoint.getDataServiceProperty(), notNullValue());
            assertThat(dataPoint.getDataServiceProperty().getDataType(), equalTo("DateTime"));
            assertThat(dataPoint.getDataServiceProperty().getAvroField(), equalTo("reportDate"));

            assertThat(dataPoint.getDataServiceProperty().getDataGroup(), notNullValue());
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getBucketName(), equalTo("mstar-fundvote"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getFrequency(), equalTo("r"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getLocation(), equalTo("npx-data/{id}"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupName(), equalTo("NPXData"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getMulti(), equalTo("true"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupDpNid(), notNullValue());

            DataPoint groupDataPoint = context.getDataPointById(dataPoint.getDataServiceProperty().getDataGroup().getGroupDpNid());
            assertThat(groupDataPoint, notNullValue());
            assertThat(groupDataPoint.getSubDataPoints(), notNullValue());
            assertThat(groupDataPoint.getSubDataPoints(), hasSize(8));
        }

    }
}
