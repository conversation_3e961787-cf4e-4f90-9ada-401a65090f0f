package com.morningstar.martgateway.infrastructures.repo.investmentapiproxy;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.Test;
import org.springframework.web.util.DefaultUriBuilderFactory;

public class ProxyUriBuilderTest {

    @Test
    public void testGetUriBuilderUriFunction() {
        ProxyUriBuilder proxyUriBuilder = new ProxyUriBuilder("productId");
        assertEquals("http://localhost?UID=userId&PID=productId&RequestId=requestId", proxyUriBuilder.getUriBuilderURIFunction("userId", "requestId").apply(
                new DefaultUriBuilderFactory("http://localhost").builder()
        ).toString());
    }

    @Test
    public void testGetUriBuilderUriFunctionWithRequestId() {
        ProxyUriBuilder proxyUriBuilder = new ProxyUriBuilder("productId");
        assertEquals("http://localhost?UID=userId&PID=productId&RequestId=requestId", proxyUriBuilder.getUriBuilderURIFunction("userId", "requestId").apply(
                new DefaultUriBuilderFactory("http://localhost").builder()
        ).toString());
    }
}