package com.morningstar.martgateway.infrastructures.repo.investmentapiproxy.xml;

import com.morningstar.martgateway.domains.apiproxy.entity.DatapointValue;
import com.morningstar.martgateway.domains.apiproxy.entity.TimeSerialsValue;
import com.morningstar.martgateway.util.apiproxy.IOUtil;
import org.junit.Assert;
import org.junit.Test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

public class DynamicDataHandlerTest {

    @Test
    public void test() {
        InputStream xmlStream = getInputStream();
        Map<String, Map<String, DatapointValue>> handle = DynamicDataHandler.handle(IOUtil.wrapXmlRoot(xmlStream));

        Assert.assertEquals(handle.size(), 1);
        Assert.assertEquals(handle.get("FOUSA00EA6;FO").size(), 1);

        Map<String, DatapointValue> values = handle.get("FOUSA00EA6;FO");
        Assert.assertEquals(values.size(), 1);

        DatapointValue dpv = values.get("HP002");
        List<TimeSerialsValue> timeSerialsValues = (List<TimeSerialsValue>) dpv.getValue();
        Assert.assertEquals(timeSerialsValues.size(), 5);
    }

    private static InputStream getInputStream() {
        String sampleDirectResponse =
            "<res tot=\"1\" pt=\"2\">\n" +
            "    <flds>\n" +
            "        <f i=\"HP002\" start=\"01-01-2020\" />\n" +
            "    </flds>\n" +
            "    <dat>\n" +
            "        <r i=\"FOUSA00EA6;FO\">\n" +
            "            <c i=\"HP002\">\n" +
            "                <t d=\"0\" v=\"-0.0681010155\" />\n" +
            "                <t d=\"1\" v=\"0\" />\n" +
            "                <t d=\"2\" v=\"1.2265819411\" />\n" +
            "                <t d=\"3\" v=\"-1.5482972009\" />\n" +
            "                <t d=\"4\" v=\"-2.0512886925\" />\n" +
            "            </c>\n" +
            "        </r>\n" +
            "    </dat>\n" +
            "</res>";

        return new ByteArrayInputStream(sampleDirectResponse.getBytes());
    }
}

// TODO use this for Regular test?
//    String sampleDirectResponse = "<res tot=\"1\" pt=\"2\">\n" +
//            "    <flds>\n" +
//            "        <f i=\"OS001\" />\n" +
//            "        <f i=\"OS00F\" />\n" +
//            "        <f i=\"OS086\" />\n" +
//            "    </flds>\n" +
//            "    <dat>\n" +
//            "        <r i=\"FOUSA00EA6;FO\">\n" +
//            "            <c i=\"OS001\" v=\"OAKBX\" />\n" +
//            "            <c i=\"OS00F\" v=\"11-01-1995\" />\n" +
//            "        </r>\n" +
//            "    </dat>\n" +
//            "</res>";
