package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;

import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MappingDataPointLoaderTest {

    private MappingDataPointLoader mappingDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        mappingDataPointLoader = new MappingDataPointLoader();
        RdbDataPointLoader rdbDataPointLoader = new RdbDataPointLoader(datapointConfigFileService, documentLoader);

        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        when(datapointConfigFileService.getResourceAsString("config/datapoints.xml")).thenReturn(getMockDataPoint("dps-config/datapoints.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
        rdbDataPointLoader.loadDataPoints(context);
    }

    @Test
    public void testDataPoints() {

        { // Preload: GroupName and IdLevel should be null
            DataPoint dataPoint = context.getDataPointById("24934");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), nullValue());
            assertThat(dataPoint.getIdLevel(), nullValue());
        }

        mappingDataPointLoader.loadDataPoints(context);

        { // Post load should set the GroupName and IdLevel
            DataPoint dataPoint = context.getDataPointById("24934");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), notNullValue());
            assertThat(dataPoint.getIdLevel(), notNullValue());

        }
    }
}
