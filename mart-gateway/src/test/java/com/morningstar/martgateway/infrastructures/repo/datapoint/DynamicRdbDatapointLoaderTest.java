package com.morningstar.martgateway.infrastructures.repo.datapoint;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;

import java.io.IOException;
import java.util.List;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DynamicRdbDatapointLoaderTest {

    private DynamicRdbDatapointLoader dynamicRdbDatapointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);

        dynamicRdbDatapointLoader = new DynamicRdbDatapointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));


        when(datapointConfigFileService.getResourceAsString("config/rdb/dynamicrdb_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/dynamicrdb_datapoints.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }

    @Test
    public void testDataPointsWithParameter() {
        dynamicRdbDatapointLoader.loadDataPoints(context);

        { // Test: CurrentData
            DataPoint dataPoint = context.getDataPointById("EUVFZ");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("RDB"));

            RdbDataPoint currentRdb = dataPoint.getCurrentRdb();
            assertThat(currentRdb, notNullValue());
            assertThat(currentRdb.getDatabase(), equalTo("CurrentData"));
            assertThat(currentRdb.getGroupName(), equalTo("CDeu_taxonomy_objective"));
            assertThat(currentRdb.getIdLevel(), equalTo("CompanyId"));
            assertThat(currentRdb.getStoreProcedure(), equalTo("CurrentData.dbo.MDS_getEUTaxonomyObjective"));
            assertThat(currentRdb.getSpParameter(), nullValue());
            assertThat(currentRdb.getTables(), equalTo("CurrentData.dbo.eu_taxonomy_objective"));
            assertThat(currentRdb.getId(), equalTo("EUVFZ"));
            assertThat(currentRdb.getColumn(), equalTo("eligible_not_aligned_local_currency_capex"));
            assertThat(currentRdb.getDateColumn(), nullValue());
            assertThat(currentRdb.getExpireTime(), nullValue());
            assertThat(currentRdb.getRdbCacheFlag(), nullValue());
            assertThat(currentRdb.getGroupFrequency(), nullValue());
            assertThat(currentRdb.getFrequency(), nullValue());
            assertThat(currentRdb.isMultipleValues(), equalTo(false));
            assertThat(currentRdb.getColumnPrefix(), nullValue());
            assertThat(currentRdb.getDataType(), equalTo("ParameterCurrentData"));
            assertThat(currentRdb.getParamName(), equalTo("euTaxonomyObjective"));
        }
    }

    @Test
    public void testTsDataPointsWithParameter() {
        dynamicRdbDatapointLoader.loadDataPoints(context);

        {
            DataPoint dataPoint = context.getDataPointById("PU88U");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("RDB"));

            RdbDataPoint tsRdb = dataPoint.getTsRdb();
            assertThat(tsRdb, notNullValue());
            assertThat(tsRdb.getDatabase(), equalTo("TimeSeries"));
            assertThat(tsRdb.getGroupName(), equalTo("TSDistributionBreakdownSource"));
            assertThat(tsRdb.getIdLevel(), equalTo("PerformanceId"));
            assertThat(tsRdb.getStoreProcedure(), equalTo("MDS_getTimeSeries_distributionBreakdownSource"));
            assertThat(tsRdb.getSpParameter(), nullValue());
            assertThat(tsRdb.getTables(), equalTo("TimeSeries.dbo.CEDistribution"));
            assertThat(tsRdb.getId(), equalTo("PU88U"));
            assertThat(tsRdb.getColumn(), equalTo("ExcludingDate"));
            assertThat(tsRdb.getExpireTime(), nullValue());
            assertThat(tsRdb.getRdbCacheFlag(), nullValue());
            assertThat(tsRdb.getGroupFrequency(), nullValue());
            assertThat(tsRdb.getFrequency(), nullValue());
            assertThat(tsRdb.isMultipleValues(), equalTo(false));
            assertThat(tsRdb.getColumnPrefix(), nullValue());
            assertThat(tsRdb.getDataType(), equalTo("ParameterTimeSeries"));
            assertThat(tsRdb.getParamName(), equalTo("distributionType,specialDistribution"));
            assertThat(tsRdb.getDateColumn(), equalTo("PU88U"));
        }
    }

}
