package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.EquityDataPoint;
import com.morningstar.martequity.configuration.EquityDataPointLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.emptyString;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EquityDataPointLoaderTest {
    private EquityDataPointLoader equityDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);

        equityDataPointLoader = new EquityDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));

        when(datapointConfigFileService.getResourceAsString("config/equity_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/equity_datapoints.xml"));
        when(datapointConfigFileService.getResourceAsString("config/equity_datapoints_manual.xml")).thenReturn(getMockDataPoint("dps-config/equity_datapoints_manual.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }


    @Test
    public void testDataPoints() {
        equityDataPointLoader.loadDataPoints(context);

        { // Test: Subgroup
            DataPoint dataPoint = context.getDataPointById("EQDOA");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("Equity"));
            assertThat(dataPoint.getAggregationPipelineId(), nullValue());

            assertThat(dataPoint.getGroupName(), emptyString());
            assertThat(dataPoint.getName(), emptyString());
            assertThat(dataPoint.getDataType(), emptyString());
            assertThat(dataPoint.isGroup(), equalTo(true));
            assertThat(dataPoint.isMulti(), equalTo(true));
            assertThat(dataPoint.getSubDataPoints(), hasSize(18));
        }

        { // Test: EQMB2
            DataPoint dataPoint = context.getDataPointById("EQMB2");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("Equity"));
            assertThat(dataPoint.getAggregationPipelineId(), nullValue());

            assertThat(dataPoint.getGroupName(), emptyString());
            assertThat(dataPoint.getName(), emptyString());
            assertThat(dataPoint.getDataType(), emptyString());
            assertThat(dataPoint.isGroup(), equalTo(false));
            assertThat(dataPoint.isMulti(), equalTo(false));
            assertThat(dataPoint.getSubDataPoints(), nullValue());

            EquityDataPoint equityDatapoint = dataPoint.getEquityDatapoint();
            assertThat(equityDatapoint, notNullValue());

            assertThat(equityDatapoint.getMostRecentCollection(), notNullValue());
            assertThat(equityDatapoint.getMostRecentField(), equalTo("aggregationResidualRiskAndReturnSensitivity.periodEndDate"));
        }

        { // Test: EQFIT (isLatestData)
            DataPoint dataPoint = context.getDataPointById("EQFIT");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("Equity"));
            assertThat(dataPoint.getAggregationPipelineId(), nullValue());

            assertThat(dataPoint.getGroupName(), emptyString());
            assertThat(dataPoint.getName(), emptyString());
            assertThat(dataPoint.getDataType(), emptyString());
            assertThat(dataPoint.isGroup(), equalTo(false));
            assertThat(dataPoint.isMulti(), equalTo(false));
            assertThat(dataPoint.getSubDataPoints(), nullValue());

            EquityDataPoint equityDatapoint = dataPoint.getEquityDatapoint();
            assertThat(equityDatapoint, notNullValue());

            var mostRecentCollection = equityDatapoint.getMostRecentCollection();
            assertThat(mostRecentCollection, notNullValue());
            assertThat(mostRecentCollection.getName(), equalTo("financialStatementsReportAverageRatesPerSharePerformanceLatest"));
            assertThat(mostRecentCollection.getIdLevel(), equalTo("performanceId"));
            assertThat(mostRecentCollection.isApplicableForLatestData(), equalTo(true));
            assertThat(mostRecentCollection.getSortingField(), equalTo("averageRatesPerShare.reportPeriodEndDate"));

            assertThat(equityDatapoint.getMostRecentField(), equalTo("averageRatesPerShare.tangibleBookValuePerShare5YearAverage"));
            assertThat(equityDatapoint.getTimeSeriesField(), equalTo("averageRatesPerShare.tangibleBookValuePerShare5YearAverage"));
        }
    }

    @Test
    public void testDataPoints_ManualXmlOverride() {
        equityDataPointLoader.loadDataPoints(context);

        { // Test: dps in both xml
            DataPoint dataPoint = context.getDataPointById("EQF9D");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("Equity"));
            assertThat(dataPoint.getAggregationPipelineId(), nullValue());

            assertThat(dataPoint.getGroupName(), emptyString());
            assertThat(dataPoint.getName(), emptyString());
            assertThat(dataPoint.getDataType(), emptyString());
            assertThat(dataPoint.isGroup(), equalTo(false));
            assertThat(dataPoint.isMulti(), equalTo(false));
            assertThat(dataPoint.getSubDataPoints(), nullValue());

            EquityDataPoint equityDatapoint = dataPoint.getEquityDatapoint();
            assertThat(equityDatapoint, notNullValue());

            assertThat(equityDatapoint.getMostRecentCollection(), notNullValue());
            assertThat(equityDatapoint.getMostRecentField(), equalTo("companyLevelInsiderHolding.asOfDateTest"));

            assertThat(equityDatapoint.getTimeSeriesCollection(), notNullValue());
            assertThat(equityDatapoint.getTimeSeriesField(), equalTo("companyLevelInsiderHolding.asOfDateTest"));
        }

        { // Test: dps only in manual
            DataPoint dataPoint = context.getDataPointById("EQRYI");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("Equity"));
            assertThat(dataPoint.getAggregationPipelineId(), nullValue());

            assertThat(dataPoint.getGroupName(), emptyString());
            assertThat(dataPoint.getName(), emptyString());
            assertThat(dataPoint.getDataType(), emptyString());
            assertThat(dataPoint.isGroup(), equalTo(false));
            assertThat(dataPoint.isMulti(), equalTo(false));
            assertThat(dataPoint.getSubDataPoints(), nullValue());

            EquityDataPoint equityDatapoint = dataPoint.getEquityDatapoint();
            assertThat(equityDatapoint, notNullValue());

            assertThat(equityDatapoint.getMostRecentCollection(), notNullValue());
            assertThat(equityDatapoint.getMostRecentField(), equalTo("companyLevelInsiderHolding.amountOwned"));

            assertThat(equityDatapoint.getTimeSeriesCollection(), notNullValue());
            assertThat(equityDatapoint.getTimeSeriesField(), equalTo("companyLevelInsiderHolding.amountOwned"));
        }
        {
            DataPoint dataPoint = context.getDataPointById("testNullDp");
            assertThat(dataPoint, nullValue());
        }
    }
}
