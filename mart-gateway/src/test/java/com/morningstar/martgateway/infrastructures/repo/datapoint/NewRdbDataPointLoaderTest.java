package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class NewRdbDataPointLoaderTest {
    private NewRdbDataPointLoader newRdbDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        CalculationMetadataDataPointLoader calcMetadataDataPointLoader = new CalculationMetadataDataPointLoader(datapointConfigFileService, documentLoader);

        newRdbDataPointLoader = new NewRdbDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));

        when(datapointConfigFileService.getResourceAsString("config/calculations.xml")).thenReturn(getMockDataPoint("dps-config/calculations.xml"));

        when(datapointConfigFileService.getResourceAsString("config/rdb/rdb_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/rdb/rdb_datapoints.xml"));
        when(datapointConfigFileService.list("config/rdb/")).thenReturn(List.of("config/rdb/rdb_datapoints.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
        calcMetadataDataPointLoader.loadDataPoints(context);
    }


    @Test
    public void testDataPoints() {
        newRdbDataPointLoader.loadDataPoints(context);

        { // Test: CurrentData
            DataPoint dataPoint = context.getDataPointById("190236");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("RDB"));

            RdbDataPoint currentRdb = dataPoint.getCurrentRdb();
            assertThat(currentRdb, notNullValue());

            assertThat(currentRdb.getDatabase(), equalTo("CurrentData"));
            assertThat(currentRdb.getGroupName(), equalTo("MorningstarMedalistRatings"));
            assertThat(currentRdb.getIdLevel(), equalTo("ShareClassId"));
            assertThat(currentRdb.getStoreProcedure(), equalTo("CurrentData.dbo.MDS_getMedalistRating"));
            assertThat(currentRdb.getSpParameter(), nullValue());
            assertThat(currentRdb.getTables(), equalTo("medalist_live_rating_and_description"));
            assertThat(currentRdb.getId(), equalTo("190236"));

            assertThat(currentRdb.getColumn(), equalTo("overall_rating_publish_date_utc"));
            assertThat(currentRdb.getDateColumn(), nullValue());
            assertThat(currentRdb.getParseFunction(), equalTo("DATE"));
            assertThat(currentRdb.getExpireTime(), nullValue());
            assertThat(currentRdb.getRdbCacheFlag(), nullValue());
            assertThat(currentRdb.isMulti(), equalTo(false));
            assertThat(currentRdb.isGroup(), equalTo(false));
            assertThat(currentRdb.isCompress(), equalTo(false));
            assertThat(currentRdb.getGroupFrequency(), nullValue());
            assertThat(currentRdb.getFrequency(), nullValue());
            assertThat(currentRdb.isMultipleValues(), equalTo(false));
            assertThat(currentRdb.getColumnPrefix(), nullValue());
            assertThat(currentRdb.getDataType(), equalTo("CurrentData"));
            assertThat(currentRdb.getSubDataPoints(), nullValue());
        }

        { // Test: PostTaxCurrentData
            DataPoint dataPoint = context.getDataPointById("PM004");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("RDB"));

            RdbDataPoint currentRdb = dataPoint.getPostTaxCurrentRdb();
            assertThat(currentRdb, notNullValue());

            assertThat(currentRdb.getDatabase(), equalTo("CurrentData"));
            assertThat(currentRdb.getGroupName(), equalTo("PostTaxMonthEndPerformance"));
            assertThat(currentRdb.getIdLevel(), equalTo("SecId"));
            assertThat(currentRdb.getStoreProcedure(), equalTo("SELECT t0.Id as UniqueId, t1.MixTotalTrailing1MonthReturn, t1.MixTotalTrailing3MonthReturn FROM CurrentData.dbo.ParseSecIdListToTable('%s') t0 LEFT JOIN CurrentData.dbo.vw_MonthEndPerformance t1 wITH (NOLOCK) ON t0.Id = t1.SecId"));
            assertThat(currentRdb.getSpParameter(), nullValue());
            assertThat(currentRdb.getTables(), equalTo("CurrentData.dbo.MonthEndPerformance"));
            assertThat(currentRdb.getId(), equalTo("PM004"));

            assertThat(currentRdb.getColumn(), equalTo("MixTotalTrailing1MonthReturn"));
            assertThat(currentRdb.getDateColumn(), nullValue());
            assertThat(currentRdb.getParseFunction(), nullValue());
            assertThat(currentRdb.getExpireTime(), nullValue());
            assertThat(currentRdb.getRdbCacheFlag(), equalTo("2"));
            assertThat(currentRdb.isMulti(), equalTo(false));
            assertThat(currentRdb.isGroup(), equalTo(false));
            assertThat(currentRdb.isCompress(), equalTo(false));
            assertThat(currentRdb.getGroupFrequency(), nullValue());
            assertThat(currentRdb.getFrequency(), nullValue());
            assertThat(currentRdb.isMultipleValues(), equalTo(false));
            assertThat(currentRdb.getColumnPrefix(), nullValue());
            assertThat(currentRdb.getDataType(), equalTo("PostTaxCurrentData"));
            assertThat(currentRdb.getSubDataPoints(), nullValue());
        }


        { // Test: TimeSeries
            DataPoint dataPoint = context.getDataPointById("HP010");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), nullValue());

            RdbDataPoint currentRdb = dataPoint.getTsRdb();
            assertThat(currentRdb, notNullValue());

            assertThat(currentRdb.getDatabase(), equalTo("TimeSeries"));
            assertThat(currentRdb.getGroupName(), equalTo("SPGrossReturnMonth"));
            assertThat(currentRdb.getIdLevel(), equalTo("SecId"));
            assertThat(currentRdb.getStoreProcedure(), equalTo("TimeSeries.dbo.MDS_getBatchTotalReturnMonth"));
            assertThat(currentRdb.getSpParameter(), nullValue());
            assertThat(currentRdb.getTables(), equalTo("TimeSeries.dbo.GrossReturn,TimeSeries.dbo.TotalReturn"));
            assertThat(currentRdb.getId(), equalTo("HP010"));

            assertThat(currentRdb.getColumn(), nullValue());
            assertThat(currentRdb.getDateColumn(), nullValue());
            assertThat(currentRdb.getParseFunction(), equalTo("DECIMAL"));
            assertThat(currentRdb.getExpireTime(), equalTo("24"));
            assertThat(currentRdb.getRdbCacheFlag(), equalTo("2"));
            assertThat(currentRdb.isMulti(), equalTo(false));
            assertThat(currentRdb.isGroup(), equalTo(false));
            assertThat(currentRdb.isCompress(), equalTo(false));
            assertThat(currentRdb.getGroupFrequency(), nullValue());
            assertThat(currentRdb.getFrequency(), equalTo("m"));
            assertThat(currentRdb.isMultipleValues(), equalTo(false));
            assertThat(currentRdb.getColumnPrefix(), equalTo("ReturnForMonth"));
            assertThat(currentRdb.getDataType(), equalTo("TimeSeries"));
            assertThat(currentRdb.getSubDataPoints(), nullValue());
        }

        { // Test: PostTaxTimeSeries
            DataPoint dataPoint = context.getDataPointById("HP010");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), nullValue());

            RdbDataPoint currentRdb = dataPoint.getPostTaxTsRdb();
            assertThat(currentRdb, notNullValue());

            assertThat(currentRdb.getDatabase(), equalTo("TimeSeries"));
            assertThat(currentRdb.getGroupName(), equalTo("TaxAdjustedReturnMonth"));
            assertThat(currentRdb.getIdLevel(), equalTo("SecId"));
            assertThat(currentRdb.getStoreProcedure(), equalTo("TimeSeries.dbo.MDS_getBatchTaxAdjustedTotalReturnMonth"));
            assertThat(currentRdb.getSpParameter(), nullValue());
            assertThat(currentRdb.getTables(), equalTo("TimeSeries.dbo.TaxAdjustedTotalReturn"));
            assertThat(currentRdb.getId(), equalTo("HP010"));

            assertThat(currentRdb.getColumn(), nullValue());
            assertThat(currentRdb.getDateColumn(), nullValue());
            assertThat(currentRdb.getParseFunction(), equalTo("DECIMAL"));
            assertThat(currentRdb.getExpireTime(), equalTo("24"));
            assertThat(currentRdb.getRdbCacheFlag(), equalTo("2"));
            assertThat(currentRdb.isMulti(), equalTo(false));
            assertThat(currentRdb.isGroup(), equalTo(false));
            assertThat(currentRdb.isCompress(), equalTo(false));
            assertThat(currentRdb.getGroupFrequency(), nullValue());
            assertThat(currentRdb.getFrequency(), equalTo("m"));
            assertThat(currentRdb.isMultipleValues(), equalTo(false));
            assertThat(currentRdb.getColumnPrefix(), equalTo("ReturnForMonth"));
            assertThat(currentRdb.getDataType(), equalTo("PostTaxTimeSeries"));
            assertThat(currentRdb.getSubDataPoints(), nullValue());
        }

        { // Test: ExtendedPerformance
            DataPoint dataPoint = context.getDataPointById("HP010");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), nullValue());

            RdbDataPoint currentRdb = dataPoint.getExtendedPerformanceRdb();
            assertThat(currentRdb, notNullValue());

            assertThat(currentRdb.getDatabase(), equalTo("TimeSeries"));
            assertThat(currentRdb.getGroupName(), equalTo("SPExtendedReturnMonth"));
            assertThat(currentRdb.getIdLevel(), equalTo("SecId"));
            assertThat(currentRdb.getStoreProcedure(), equalTo("TimeSeries.dbo.MDS_getExtendedTotalReturnMonth"));
            assertThat(currentRdb.getSpParameter(), nullValue());
            assertThat(currentRdb.getTables(), equalTo("TimeSeries.dbo.ExtendedTotalReturn"));
            assertThat(currentRdb.getId(), equalTo("HP010"));

            assertThat(currentRdb.getColumn(), nullValue());
            assertThat(currentRdb.getDateColumn(), nullValue());
            assertThat(currentRdb.getParseFunction(), equalTo("DECIMAL"));
            assertThat(currentRdb.getExpireTime(), equalTo("24"));
            assertThat(currentRdb.getRdbCacheFlag(), equalTo("2"));
            assertThat(currentRdb.isMulti(), equalTo(false));
            assertThat(currentRdb.isGroup(), equalTo(false));
            assertThat(currentRdb.isCompress(), equalTo(false));
            assertThat(currentRdb.getGroupFrequency(), nullValue());
            assertThat(currentRdb.getFrequency(), equalTo("m"));
            assertThat(currentRdb.isMultipleValues(), equalTo(false));
            assertThat(currentRdb.getColumnPrefix(), equalTo("ReturnForMonth"));
            assertThat(currentRdb.getDataType(), equalTo("ExtendedPerformance"));
            assertThat(currentRdb.getSubDataPoints(), nullValue());
        }


    }
}
