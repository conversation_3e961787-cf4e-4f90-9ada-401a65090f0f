package com.morningstar.martgateway.infrastructures.repo.entitlement;

import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.DataPointFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.datapointfilter.IsinPrefixFilter;
import com.morningstar.dataac.martgateway.core.entitlement.config.EntitlementFilterLoaderContext;
import org.dom4j.Document;
import org.dom4j.Element;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.core.io.ResourceLoader;

import java.util.List;
import java.util.Set;

import static org.mockito.Mockito.*;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GridViewIsinFilterLoaderTest {
    @Mock
    private ResourceLoader resourceLoader;

    @Mock
    private DataPointFilterService dataPointFilterService;

    @Mock
    private Document document;

    @Mock
    private Element rootElement;

    @Mock
    private Element serviceElement;

    @Mock
    private Element filterElement;

    @Mock
    private Element dataPointsElement;

    @Mock
    private Element dataPointElement;

    private GridViewIsinFilterLoader gridViewIsinFilterLoader;

    @BeforeEach
    void setUp() {
        gridViewIsinFilterLoader = new GridViewIsinFilterLoader(resourceLoader, dataPointFilterService);
    }

    @Test
    void testProcessDocument() {
        // Arrange
        when(document.getRootElement()).thenReturn(rootElement);
        when(rootElement.elements("service")).thenReturn(List.of(serviceElement));
        when(serviceElement.elements("filter")).thenReturn(List.of(filterElement));
        when(filterElement.element("datapoints")).thenReturn(dataPointsElement);
        when(dataPointsElement.elements("datapoint")).thenReturn(List.of(dataPointElement));
        when(dataPointElement.attributeValue("id")).thenReturn("datapoint1");

        EntitlementFilterLoaderContext context = mock(EntitlementFilterLoaderContext.class);
        when(context.getRequiredPackageIds()).thenReturn(Set.of("package1"));
        when(context.getPrefixes()).thenReturn(Set.of("prefix1"));

        IsinPrefixFilter filter = mock(IsinPrefixFilter.class);
        // Mock the fromElement static method of IsinPrefixFilter
        try (MockedStatic<IsinPrefixFilter> mockedStatic = Mockito.mockStatic(IsinPrefixFilter.class)) {
            when(IsinPrefixFilter.fromElement(context.getRequiredPackageIds(), context.getPrefixes())).thenReturn(filter);

            // Act
            gridViewIsinFilterLoader.processDocument(context, document);

            // Assert
            verify(dataPointFilterService, times(1)).addFilter("datapoint1", filter);
        }
    }

}
