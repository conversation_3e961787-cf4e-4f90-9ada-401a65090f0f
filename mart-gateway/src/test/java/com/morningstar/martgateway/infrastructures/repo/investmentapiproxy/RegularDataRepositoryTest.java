package com.morningstar.martgateway.infrastructures.repo.investmentapiproxy;

import com.morningstar.martgateway.domains.apiproxy.entity.DataPointForColumnsResponse;
import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint;
import com.morningstar.martgateway.domains.apiproxy.entity.DoApiInvestment;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesRequestEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.StartEndDateEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.TimeSerialsValue;

import java.net.URI;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class RegularDataRepositoryTest {

    @Test
    public void getRegularInvestmentData() {
        SecuritiesRequestEntity entity = setupSecuritiesRequestEntityForCurrent();
        entity.setUserId("userId");
        List<DataPointForColumnsResponse> dpCols = setupDataPointForColumnsResponses();

        WebClient client = mock(WebClient.class);
        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        when(client.post()).thenReturn(requestBodyUriSpec);

        WebClient.RequestBodySpec uriSpec = mock(WebClient.RequestBodySpec.class);
        when(requestBodyUriSpec.uri((Function<UriBuilder, URI>) any())).thenReturn(uriSpec);

        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        when(uriSpec.contentType(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);

        WebClient.RequestHeadersSpec requestHeadersSpec1 = mock(WebClient.RequestHeadersSpec.class);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec1);

        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestHeadersSpec1.retrieve()).thenReturn(responseSpec);

        DataBuffer dataBuffer = mock(DataBuffer.class);
        when(dataBuffer.asInputStream()).thenReturn(getInputStream());
        when(responseSpec.bodyToMono(DataBuffer.class)).thenReturn(Mono.just(dataBuffer));

        ProxyUriBuilder proxyUriBuilder = mock(ProxyUriBuilder.class);
        when(proxyUriBuilder.getUriBuilderURIFunction(entity.getUserIdOrDefault(), entity.getJobId())).thenReturn(uriBuilder -> uriBuilder
                .queryParam("UID", entity.getUserIdOrDefault())
                .queryParam("PID", "pid")
                .build());
        RegularDataRepository regularDataRepository = new RegularDataRepository(client, proxyUriBuilder, 1, 2);
        Flux<DoApiInvestment> regularInvestmentData = regularDataRepository.getRegularInvestmentData(entity, setupDataPoints(), dpCols);

        StepVerifier.create(regularInvestmentData)
                .assertNext(d -> {
                    Assert.assertEquals(d.getId(), "FOUSA00KZH;FO");
                    Assert.assertEquals(d.getValues().size(), 3);
                    Assert.assertEquals(d.getValues().get(0).getValue(), "OAKLX");
                    Assert.assertEquals(d.getValues().get(0).getId(), "OS001");
                    Assert.assertEquals(d.getValues().get(1).getValue(), "11-01-1996");
                    Assert.assertEquals(d.getValues().get(1).getId(), "OS00F");
                    Assert.assertEquals(d.getValues().get(2).getId(), "HP002");
                    List<TimeSerialsValue> values = (List<TimeSerialsValue>) d.getValues().get(2).getValue();
                    Assert.assertEquals(2, values.size());
                    Assert.assertEquals("1.9299525125", values.get(0).getValue());
                    Assert.assertEquals("1.9986113136", values.get(1).getValue());
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void getRegularInvestmentDataError() {
        SecuritiesRequestEntity entity = setupSecuritiesRequestEntityForCurrent();
        entity.setUserId("userId");
        entity.setJobId("job123"); // Add job ID for logging
        List<DataPointForColumnsResponse> dpCols = setupDataPointForColumnsResponses();

        WebClient client = mock(WebClient.class);
        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        when(client.post()).thenReturn(requestBodyUriSpec);

        WebClient.RequestBodySpec uriSpec = mock(WebClient.RequestBodySpec.class);
        when(requestBodyUriSpec.uri((Function<UriBuilder, URI>) any())).thenReturn(uriSpec);

        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        when(uriSpec.contentType(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);

        WebClient.RequestHeadersSpec requestHeadersSpec1 = mock(WebClient.RequestHeadersSpec.class);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec1);

        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestHeadersSpec1.retrieve()).thenReturn(responseSpec);

        // Create a timeout exception to trigger retries
        TimeoutException timeoutException = new TimeoutException("Connection timed out");
        when(responseSpec.bodyToMono(DataBuffer.class)).thenReturn(Mono.error(timeoutException)); // Will trigger retry logic

        ProxyUriBuilder proxyUriBuilder = mock(ProxyUriBuilder.class);
        when(proxyUriBuilder.getUriBuilderURIFunction(entity.getUserIdOrDefault(), entity.getJobId())).thenReturn(uriBuilder -> uriBuilder
                .queryParam("UID", entity.getUserIdOrDefault())
                .queryParam("PID", "pid")
                .queryParam("RequestId", entity.getJobId())
                .build());

        RegularDataRepository regularDataRepository = new RegularDataRepository(client, proxyUriBuilder, 1, 2);
        Flux<DoApiInvestment> regularInvestmentData = regularDataRepository.getRegularInvestmentData(entity, setupDataPoints(), dpCols);

        StepVerifier.create(regularInvestmentData)
                .expectError(TimeoutException.class) // Expect the specific exception type
                .verify();
    }

    private static SecuritiesRequestEntity setupSecuritiesRequestEntityForCurrent() {
        SecuritiesRequestEntity entity = new SecuritiesRequestEntity();
        DoApiInvestment doApiInvestment = new DoApiInvestment();
        doApiInvestment.setId("FOUSA00KZH;FO");
        entity.setDoApiInvestments(List.of(doApiInvestment));

        Datapoint dp1 = new Datapoint();
        dp1.setId("OS001");
        dp1.setAlias("OS001");
        Datapoint dp2 = new Datapoint();
        dp2.setId("OS00F");
        dp2.setAlias("OS00F");
        Datapoint dp3 = new Datapoint();
        dp3.setId("HP002");
        dp3.setAlias("HP002");
        dp3.setStartDate("2012-01-01");
        dp3.setEndDate("2012-01-31");
        dp3.setSourceId("HP002");
        dp3.setFrequency("w");
        entity.setDatapoints(setupDataPoints());
        return entity;
    }

    private static List<Datapoint> setupDataPoints() {
        Datapoint dp1 = new Datapoint();
        dp1.setId("OS001");
        dp1.setAlias("OS001");
        Datapoint dp2 = new Datapoint();
        dp2.setId("OS00F");
        dp2.setAlias("OS00F");
        Datapoint dp3 = new Datapoint();
        dp3.setId("HP002");
        dp3.setAlias("HP002");
        dp3.setStartDate("2012-01-01");
        dp3.setEndDate("2012-01-31");
        dp3.setSourceId("HP002");
        dp3.setFrequency("w");
        return List.of(dp1, dp2, dp3);
    }

    private static SecuritiesRequestEntity setupSecuritiesRequestEntityForTs() {
        SecuritiesRequestEntity entity = new SecuritiesRequestEntity();
        DoApiInvestment doApiInvestment = new DoApiInvestment();
        doApiInvestment.setId("FOUSA00EA6;FO");
        entity.setDoApiInvestments(List.of(doApiInvestment));

        Datapoint dp1 = new Datapoint();
        dp1.setId("OS001");
        dp1.setAlias("OS001");
        Datapoint dp2 = new Datapoint();
        dp2.setId("OS00F");
        dp2.setAlias("OS00F");
        entity.setDatapoints(List.of(dp1, dp2));
        return entity;
    }

    private static List<DataPointForColumnsResponse> setupDataPointForColumnsResponses() {
        List<DataPointForColumnsResponse> dpCols = new ArrayList<>();

        DataPointForColumnsResponse dp1 = new DataPointForColumnsResponse();
        dp1.setDatapointId("OS001");
        dp1.setAlias("OS001");
        dp1.setDatePairs(null);
        dp1.setColumns(1);
        dpCols.add(dp1);

        DataPointForColumnsResponse dp2 = new DataPointForColumnsResponse();
        dp2.setDatapointId("OS00F");
        dp2.setAlias("OS00F");
        dp2.setDatePairs(null);
        dp2.setColumns(1);
        dpCols.add(dp2);

        DataPointForColumnsResponse dp3 = new DataPointForColumnsResponse();
        dp3.setDatapointId("HP002");
        dp3.setAlias("HP002");
        dp3.setColumns(2);
        StartEndDateEntity s1 = new StartEndDateEntity();
        s1.setStartDate("2022-01-01");
        s1.setStartDate("2022-01-07");
        StartEndDateEntity s2 = new StartEndDateEntity();
        s2.setStartDate("2022-01-08");
        s2.setStartDate("2022-01-14");
        dp3.setDatePairs(List.of(s1, s2));
        dpCols.add(dp3);

        return dpCols;
    }

    private InputStream getInputStream() {
        String sampleDirectResponse = "<res tot=\"1\" pt=\"2\">\n" +
                "    <flds>\n" +
                "        <f i=\"OS001\" />\n" +
                "        <f i=\"OS00F\" />\n" +
                "    </flds>\n" +
                "    <dat>\n" +
                "        <r i=\"FOUSA00KZH;FO\">\n" +
                "            <c i=\"OS001\" v=\"OAKLX\" />\n" +
                "            <c i=\"OS00F\" v=\"11-01-1996\" />\n" +
                "        </r>\n" +
                "    </dat>\n" +
                "</res>242\n" +
                "<res tot=\"1\" pt=\"2\">\n" +
                "    <flds>\n" +
                "        <f i=\"HP002\" start=\"01-01-2012\" />\n" +
                "    </flds>\n" +
                "    <dat>\n" +
                "        <r i=\"FOUSA00KZH;FO\">\n" +
                "            <c i=\"HP002\">\n" +
                "                <t d=\"0\" v=\"1.9299525125\" />\n" +
                "                <t d=\"1\" v=\"1.9986113136\" />\n" +
                "                <t d=\"2\" v=\"2.0969326008\" />\n" +
                "                <t d=\"3\" v=\"-0.1010033518\" />\n" +
                "            </c>\n" +
                "        </r>\n" +
                "    </dat>\n" +
                "</res>";

        return new ByteArrayInputStream(sampleDirectResponse.getBytes());
    }
}
