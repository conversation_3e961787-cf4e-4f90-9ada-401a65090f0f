package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GroupMappingLoaderTest {

    private GroupMappingLoader groupMappingLoader;

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @Mock
    private RedisTemplate<String, String> syncStorageDataTemplate;

    @Mock
    private ValueOperations opsForValue;


    @BeforeEach
    void setup() {
        groupMappingLoader = new GroupMappingLoader(syncStorageDataTemplate);

        String jsonBody = "{\"USCategoryGroup\":{\"0\":\"Money Market\"}}";
        when(syncStorageDataTemplate.opsForValue()).thenReturn(opsForValue);
        when(syncStorageDataTemplate.opsForValue().get(anyString())).thenReturn(jsonBody);
    }

    @Test
    public void testDataPoint() {
        DataPointLoaderContext context = new DataPointLoaderContext();
        groupMappingLoader.loadDataPoints(context);

        {
            Alias alias = context.getAliasById("USCategoryGroup");

            assertNotNull(alias);
            assertThat("Map has 1 items", alias.getItems().values(), hasSize(1));
        }

    }

}
