package com.morningstar.martgateway.infrastructures.repo.investmentapiproxy.xml;

import com.morningstar.martgateway.domains.apiproxy.entity.DatapointValue;
import com.morningstar.martgateway.util.apiproxy.IOUtil;
import org.junit.Assert;
import org.junit.Test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Map;

public class RegularDataHandlerTest
{
    @Test
    public void testHandler() {
        InputStream xmlStream = getInputStream();

        Map<String, Map<String, DatapointValue>> handle = RegularDataHandler.handle(IOUtil.wrapXmlRoot(xmlStream));
        Assert.assertEquals(handle.size(), 1);

        Map<String, DatapointValue> stringDatapointValueMap = handle.get("FOUSA00EA6;FO");
        Assert.assertEquals(stringDatapointValueMap.size(), 2);

        DatapointValue os001 = stringDatapointValueMap.get("OS001");
        Assert.assertEquals(os001.getValue(), "OAKBX");

        DatapointValue os00f = stringDatapointValueMap.get("OS00F");
        Assert.assertEquals(os00f.getValue(), "11-01-1995");
    }

    private static InputStream getInputStream() {
        String sampleDirectResponse = "<res tot=\"1\" pt=\"2\">\n" +
                "    <flds>\n" +
                "        <f i=\"OS001\" />\n" +
                "        <f i=\"OS00F\" />\n" +
                "    </flds>\n" +
                "    <dat>\n" +
                "        <r i=\"FOUSA00EA6;FO\">\n" +
                "            <c i=\"OS001\" v=\"OAKBX\" />\n" +
                "            <c i=\"OS00F\" v=\"11-01-1995\" />\n" +
                "        </r>\n" +
                "    </dat>\n" +
                "</res>";

        return new ByteArrayInputStream(sampleDirectResponse.getBytes());
    }
}
