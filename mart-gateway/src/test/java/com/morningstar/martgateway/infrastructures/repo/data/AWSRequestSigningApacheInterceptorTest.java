package com.morningstar.martgateway.infrastructures.repo.data;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.Signer;
import org.apache.http.*;
import org.apache.http.protocol.HttpContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AWSRequestSigningApacheInterceptorTest {
    private AWSRequestSigningApacheInterceptor awsRequestSigningApacheInterceptor;
    private AWSRequestSigningApacheInterceptor awsRequestSigningApacheInterceptorTwo;
    String service = "test";
    @Mock
    private Signer signer;
    @Mock
    private AWSCredentialsProvider awsCredentialsProvider;
    @Mock
    private HttpRequest request;
    @Mock
    private HttpEntityEnclosingRequest httpEntityEnclosingRequest;
    @Mock
    private RequestLine requestLine;
    @Mock
    private HttpContext context;
    @Mock
    private HttpEntity httpEntity;

    @Before
    public void setUp() {
        awsRequestSigningApacheInterceptor = new AWSRequestSigningApacheInterceptor(service,signer,awsCredentialsProvider);
        awsRequestSigningApacheInterceptorTwo = new AWSRequestSigningApacheInterceptor(service,signer,awsCredentialsProvider);
    }
    @Test
    public void shouldProcess() throws IOException, HttpException {
        when(request.getRequestLine()).thenReturn(requestLine);
        when(requestLine.getUri()).thenReturn("www.google.com");
        Header header = new Header() {
            @Override
            public String getName() {
                return "host123";
            }

            @Override
            public String getValue() {
                return "value123";
            }

            @Override
            public HeaderElement[] getElements() throws ParseException {
                return new HeaderElement[0];
            }
        };
        Header[] headers = new Header[]{header};
        when(request.getAllHeaders()).thenReturn(headers);
        awsRequestSigningApacheInterceptor.process(request,context);
    }

    @Test
    public void shouldProcessTwo() throws IOException, HttpException {
        when(httpEntityEnclosingRequest.getRequestLine()).thenReturn(requestLine);
        when(requestLine.getUri()).thenReturn("www.google.com");
        Header header = new Header() {
            @Override
            public String getName() {
                return "host123";
            }

            @Override
            public String getValue() {
                return "value123";
            }
            @Override
            public HeaderElement[] getElements() throws ParseException {
                return new HeaderElement[0];
            }
        };
        Header[] headers = new Header[]{header};
        when(httpEntityEnclosingRequest.getAllHeaders()).thenReturn(headers);
        when(httpEntityEnclosingRequest.getEntity()).thenReturn(httpEntity);
        awsRequestSigningApacheInterceptor.process(httpEntityEnclosingRequest,context);
    }

}
