package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TransferCalculationsDataPointLoaderTest {
    private TransferCalculationsDataPointLoader transferCalculationsDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        CalculationMetadataDataPointLoader calcMetadataDataPointLoader = new CalculationMetadataDataPointLoader(datapointConfigFileService, documentLoader);

        transferCalculationsDataPointLoader = new TransferCalculationsDataPointLoader();

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        when(datapointConfigFileService.getResourceAsString("config/calculations.xml")).thenReturn(getMockDataPoint("dps-config/calculations.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
        calcMetadataDataPointLoader.loadDataPoints(context);
    }


    @Test
    public void testTransferCalcToMappingSrc() {

        { // Pre-test: transferCalculationForMappingSrcDps
            DataPoint dataPoint = context.getDataPointById("7461");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getCalculation(), nullValue());
        }

        { // Pre-Test: transferCalculationForMultiUniverseDps
            {
                DataPoint multiUnivDp = context.getDataPointById("15928");
                assertThat(multiUnivDp, notNullValue());
                assertThat(multiUnivDp.getCalculation(), nullValue());
            }

            {
                DataPoint multiUnivDp = context.getDataPointById("ST168ST");
                assertThat(multiUnivDp, notNullValue());
                assertThat(multiUnivDp.getCalculation(), nullValue());
            }
        }
        transferCalculationsDataPointLoader.loadDataPoints(context);

        { // Test: transferCalculationForMappingSrcDps
            DataPoint dataPoint = context.getDataPointById("37062");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getMappingSrc(), notNullValue());
            assertThat(dataPoint.getCalculation(), notNullValue());


            DataPoint mappingSrcDp = dataPoint.getMappingSrc();
            assertThat(mappingSrcDp, notNullValue());

            assertThat(mappingSrcDp.getNid(), equalTo("7461"));
            assertThat(mappingSrcDp.getCalculation(), notNullValue());
            assertThat(mappingSrcDp.getCalculation(), notNullValue());
        }

        { // Test: transferCalculationForMultiUniverseDps
            DataPoint dataPoint = context.getDataPointById("ST168");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getMultiUniverseList(), notNullValue());
            assertThat(dataPoint.getCalculation(), notNullValue());

            {
                DataPoint multiUnivDp = context.getDataPointById("15928");
                assertThat(multiUnivDp, notNullValue());
                assertThat(multiUnivDp.getCalculation(), notNullValue());
            }

            {
                DataPoint multiUnivDp = context.getDataPointById("ST168ST");
                assertThat(multiUnivDp, notNullValue());
                assertThat(multiUnivDp.getCalculation(), notNullValue());
            }
        }
    }
}
