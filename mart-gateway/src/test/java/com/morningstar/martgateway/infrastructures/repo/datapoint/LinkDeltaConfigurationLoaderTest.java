package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.DeltaConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LinkDeltaConfigurationLoaderTest {
    private LinkDeltaConfigurationLoader linkDeltaConfigurationLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @BeforeEach
    public void setup() throws IOException {
        DocumentLoader documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        linkDeltaConfigurationLoader = new LinkDeltaConfigurationLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);

        when(datapointConfigFileService.getResourceAsString("config/rdb/rdb_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/rdb/rdb_datapoints.xml"));
        when(datapointConfigFileService.list("config/rdb/")).thenReturn(List.of("config/rdb/rdb_datapoints.xml"));

        NewRdbDataPointLoader newRdbDataPointLoader = new NewRdbDataPointLoader(datapointConfigFileService, documentLoader);
        newRdbDataPointLoader.loadDataPoints(context);

        when(datapointConfigFileService.getResourceAsString("config/delta/datasets/time_series_datasets.xml")).thenReturn(getMockDataPoint("dps-config/delta/datasets/time_series_datasets.xml"));
        when(datapointConfigFileService.list("config/delta/datasets/")).thenReturn(List.of("config/delta/datasets/time_series_datasets.xml"));

        DeltaDatasetLoader deltaDatasetLoader = new DeltaDatasetLoader(datapointConfigFileService, documentLoader);
        deltaDatasetLoader.loadDataPoints(context);

        when(datapointConfigFileService.getResourceAsString("config/delta/configurations/rdb_ts_delta_configurations.xml")).thenReturn(getMockDataPoint("dps-config/delta/configurations/rdb_ts_delta_configurations.xml"));
        when(datapointConfigFileService.list("config/delta/configurations/")).thenReturn(List.of("config/delta/configurations/rdb_ts_delta_configurations.xml"));
    }

    @Test
    public void loadDeltaDatasets() {
        linkDeltaConfigurationLoader.loadDataPoints(context);
        DataPoint dataPoint = context.getDataPointById("HS603");
        Assertions.assertNotNull(dataPoint);
        DeltaConfiguration deltaConfiguration = dataPoint.getTsRdb().getDeltaConfiguration();
        Assertions.assertNotNull(deltaConfiguration);
        Assertions.assertEquals(dataPoint.getTsRdb().getGroupName(), deltaConfiguration.getSrcGroup());
        Assertions.assertTrue(deltaConfiguration.getDatasets().stream().anyMatch(dataset -> "TimeSeries.dbo.vw_TSPrice".equals(dataset.getName())));
    }
}
