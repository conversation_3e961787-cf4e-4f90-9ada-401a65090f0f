package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.apache.commons.lang3.tuple.Pair;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasKey;

@ExtendWith(MockitoExtension.class)
public class AthenaDataPointLoaderTest {

    private AthenaDataPointLoader athenaDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        athenaDataPointLoader = new AthenaDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        when(datapointConfigFileService.getResourceAsString("config/athena_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/athena_datapoints.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }

    @Test
    public void testDataPoints() {

        athenaDataPointLoader.loadDataPoints(context);

        {
            DataPoint dataPoint = context.getDataPointById("94004");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getField(), equalTo("IndustryStandardSizeMonthEnd"));
            assertThat(dataPoint.isMulti(), equalTo(false));
            assertThat(dataPoint.getGroupName(), equalTo("IndustryStandardFactorProfiles"));
            assertThat(dataPoint.getIdLevel(), equalTo("MasterPortfolioId"));
        }

        { // Test: GlobalCategoryId/ProductInvolvementCategoryAverage
            String dataSet = "GlobalCategoryId/ProductInvolvementCategoryAverage";
            assertThat(DataPointRepository.getDataSetDateDataPointMap(), hasKey(dataSet));
            Pair<String, String> pair = DataPointRepository.getDataSetDateDataPointMap().get(dataSet);
            assertThat(pair.getLeft(), nullValue());
            assertThat(pair.getRight(), equalTo("sustainability_date"));
        }

        { // Test: MasterPortfolioId/ESGGenderDiversity
            String dataSet = "MasterPortfolioId/ESGGenderDiversity";
            assertThat(DataPointRepository.getDataSetDateDataPointMap(), hasKey(dataSet));
            Pair<String, String> pair = DataPointRepository.getDataSetDateDataPointMap().get(dataSet);
            assertThat(pair.getLeft(), equalTo("94301"));
            assertThat(pair.getRight(), equalTo("portfolio_date"));
        }
    }
}
