package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CalculationMetadataPointLoaderTest {

    private CalculationMetadataDataPointLoader calcMetadataDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        calcMetadataDataPointLoader = new CalculationMetadataDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        when(datapointConfigFileService.getResourceAsString("config/calculations.xml")).thenReturn(getMockDataPoint("dps-config/calculations.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }

    @Test
    public void testDataPoints() {

        calcMetadataDataPointLoader.loadDataPoints(context);

        {
            DataPoint dataPoint = context.getDataPointById("OS013");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getCalculation(), notNullValue());
            assertThat(dataPoint.getCalculation().getDp(), notNullValue());
            assertThat(dataPoint.getCalculation().getDp().getNid(), equalTo("100001"));

            assertThat(dataPoint.getCalculation().getEndDate(), notNullValue());
            assertThat(dataPoint.getCalculation().getCur(), notNullValue());
            assertThat(dataPoint.getCalculation().getCalSrc(), equalTo("CALCLIB"));
            assertThat(dataPoint.getCalculation().getFreq(), nullValue());
        }

        {
            DataPoint dataPoint = context.getDataPointById("RR002");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getCalculation(), notNullValue());
            assertThat(dataPoint.getCalculation().getDp(), notNullValue());
            assertThat(dataPoint.getCalculation().getDp().getNid(), equalTo("3"));

            assertThat(dataPoint.getCalculation().getAnn(), equalTo("true"));
            assertThat(dataPoint.getCalculation().getBmk(), notNullValue());
            assertThat(dataPoint.getCalculation().getCalSrc(), nullValue());
            assertThat(dataPoint.getCalculation().getCur(), notNullValue());
            assertThat(dataPoint.getCalculation().getEndDate(), notNullValue());
            assertThat(dataPoint.getCalculation().getFreq(), nullValue());
            assertThat(dataPoint.getCalculation().getRf(), notNullValue());
            assertThat(dataPoint.getCalculation().getSource(), notNullValue());
            assertThat(dataPoint.getCalculation().getTrailingPeriod(), equalTo("12"));

        }

        {
            DataPoint dataPoint = context.getDataPointById("RR152");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getCalculation(), notNullValue());
            assertThat(dataPoint.getCalculation().getDp(), notNullValue());
            assertThat(dataPoint.getCalculation().getDp().getNid(), equalTo("47"));

            assertThat(dataPoint.getCalculation().getAnn(), equalTo("false"));
            assertThat(dataPoint.getCalculation().getBmk(), notNullValue());
            assertThat(dataPoint.getCalculation().getCalSrc(), nullValue());
            assertThat(dataPoint.getCalculation().getCur(), notNullValue());
            assertThat(dataPoint.getCalculation().getEndDate(), notNullValue());
            assertThat(dataPoint.getCalculation().getFreq(), nullValue());
            assertThat(dataPoint.getCalculation().getRf(), nullValue());
            assertThat(dataPoint.getCalculation().getSource(), notNullValue());
            assertThat(dataPoint.getCalculation().getTrailingPeriod(), equalTo("12"));

        }
    }
}
