package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.FixedIncomeDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import com.morningstar.dataac.morningstar.data.fixedincome.config.IceFixedIncomeDataPointLoader;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IceFixedIncomeDataPointLoaderTest {
    private IceFixedIncomeDataPointLoader iceFixedIncomeDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);

        iceFixedIncomeDataPointLoader = new IceFixedIncomeDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        lenient().when(datapointConfigFileService.getResourceAsString("config/view/direct_datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/direct_datapoints_view.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml", "config/view/direct_datapoints_view.xml"));


        when(datapointConfigFileService.getResourceAsString("config/fixed_income_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/fixed_income_datapoints.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }


    @Test
    public void testDataPoints() {
        iceFixedIncomeDataPointLoader.loadDataPoints(context);

        { // Test: CurrentData
            DataPoint dataPoint = context.getDataPointById("BDL4U");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("ICEFI"));
            assertThat(dataPoint.getName(), equalTo("Price Date"));
            assertThat(dataPoint.getIdLevel(), equalTo("SecId"));

            FixedIncomeDataPoint currentFixedIncomeDataPoint = dataPoint.getCurrentFixedIncomeDataPoint();
            assertThat(currentFixedIncomeDataPoint, notNullValue());

            assertThat(currentFixedIncomeDataPoint.getDataType(), equalTo("CurrentData"));
            assertThat(currentFixedIncomeDataPoint.getColumn(), equalTo("price_date"));
            assertThat(currentFixedIncomeDataPoint.getSrc(), equalTo("ICEFI"));
            assertThat(currentFixedIncomeDataPoint.getDataBaseSchema(), equalTo("currentdata"));
            assertThat(currentFixedIncomeDataPoint.getTables(), equalTo("ice_instrument_prices"));
            assertThat(currentFixedIncomeDataPoint.getStoreProcedure(), equalTo("SELECT investment_id as id, price_date, evaluated_price_clean FROM currentdata.ice_instrument_prices WHERE investment_id IN (%s)"));
            assertThat(currentFixedIncomeDataPoint.getHoldingsStoreProcedure(), nullValue());
            assertThat(currentFixedIncomeDataPoint.getExpireTime(), equalTo("2"));
            assertThat(currentFixedIncomeDataPoint.getParseFunction(), nullValue());
            assertThat(currentFixedIncomeDataPoint.getName(), equalTo("Price Date"));
            assertThat(currentFixedIncomeDataPoint.getIdLevel(), equalTo("SecId"));
            assertThat(currentFixedIncomeDataPoint.getGroupName(), equalTo("FixedIncomePricesCurrent"));
            assertThat(currentFixedIncomeDataPoint.getLookback(), nullValue());
        }

        { // Test: TimeSeries
            DataPoint dataPoint = context.getDataPointById("BDL4U");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("ICEFI"));
            assertThat(dataPoint.getName(), equalTo("Price Date"));
            assertThat(dataPoint.getIdLevel(), equalTo("SecId"));

            FixedIncomeDataPoint timeSeries30daysLookBackDataPoint = dataPoint.getTsFixedIncomeDataPoint();
            assertThat(timeSeries30daysLookBackDataPoint, notNullValue());

            assertThat(timeSeries30daysLookBackDataPoint.getDataType(), equalTo("TimeSeries"));
            assertThat(timeSeries30daysLookBackDataPoint.getColumn(), equalTo("price_date"));
            assertThat(timeSeries30daysLookBackDataPoint.getSrc(), equalTo("ICEFI"));
            assertThat(timeSeries30daysLookBackDataPoint.getDataBaseSchema(), equalTo("timeseries"));
            assertThat(timeSeries30daysLookBackDataPoint.getTables(), equalTo("ice_instrument_prices"));
            assertThat(timeSeries30daysLookBackDataPoint.getStoreProcedure(), equalTo("SELECT investment_id as id, price_date, evaluated_price_clean FROM timeseries.ice_instrument_prices WHERE investment_id IN (%s) and price_date BETWEEN %s AND %s"));
            assertThat(timeSeries30daysLookBackDataPoint.getHoldingsStoreProcedure(), equalTo("SELECT i.investment_id AS id, a.price_date, a.evaluated_price_clean FROM UNNEST(array[%s]::text[]) AS i(investment_id) JOIN LATERAL ( SELECT price_date, evaluated_price_clean FROM timeseries.ice_instrument_prices a WHERE a.investment_id = i.investment_id AND a.price_date BETWEEN %s AND %s ORDER BY price_date desc limit 1 ) a ON TRUE;"));
            assertThat(timeSeries30daysLookBackDataPoint.getExpireTime(), equalTo("2"));
            assertThat(timeSeries30daysLookBackDataPoint.getParseFunction(), nullValue());
            assertThat(timeSeries30daysLookBackDataPoint.getName(), equalTo("Price Date"));
            assertThat(timeSeries30daysLookBackDataPoint.getIdLevel(), equalTo("SecId"));
            assertThat(timeSeries30daysLookBackDataPoint.getGroupName(), equalTo("FixedIncomePricesTimeSeries"));
            assertThat(timeSeries30daysLookBackDataPoint.getLookback(), equalTo("30"));
        }

        { // Test: TimeSeries with lookback
            DataPoint dataPoint = context.getDataPointById("ILVDQ");
            assertThat(dataPoint, notNullValue());

            assertThat(dataPoint.getSrc(), equalTo("ICEFI"));
            assertThat(dataPoint.getName(), equalTo("Current Coupon Rate"));
            assertThat(dataPoint.getIdLevel(), equalTo("SecId"));

            FixedIncomeDataPoint timeSeriesUnlimitedLookBackDataPoint = dataPoint.getTsFixedIncomeDataPoint();
            assertThat(timeSeriesUnlimitedLookBackDataPoint, notNullValue());

            assertThat(timeSeriesUnlimitedLookBackDataPoint.getDataType(), equalTo("TimeSeries"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getColumn(), equalTo("current_coupon_rate"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getSrc(), equalTo("ICEFI"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getDataBaseSchema(), equalTo("timeseries"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getTables(), equalTo("ice_coupon_schedule_standard"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getHoldingsStoreProcedure(), equalTo("SELECT i.investment_id AS id, a.coupon_as_of_date, a.current_coupon_rate, a.coupon_frequency FROM UNNEST(array[%s]::text[]) AS i(investment_id) JOIN LATERAL ( SELECT coupon_as_of_date, current_coupon_rate, coupon_frequency FROM timeseries.ice_coupon_schedule_standard a WHERE a.investment_id = i.investment_id AND a.coupon_as_of_date BETWEEN %s AND %s ORDER BY coupon_as_of_date desc limit 1 ) a ON TRUE;"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getExpireTime(), equalTo("2"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getParseFunction(), equalTo("DECIMAL"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getName(), equalTo("Current Coupon Rate"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getIdLevel(), equalTo("SecId"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getGroupName(), equalTo("FixedIncomeCouponScheduleStandardTimeSeries"));
            assertThat(timeSeriesUnlimitedLookBackDataPoint.getLookback(), nullValue());
        }
    }
}
