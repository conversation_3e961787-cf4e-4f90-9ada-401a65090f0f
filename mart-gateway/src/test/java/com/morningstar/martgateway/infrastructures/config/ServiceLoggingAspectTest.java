package com.morningstar.martgateway.infrastructures.config;

import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ServiceLoggingAspectTest {

    @Mock
    private ProceedingJoinPoint joinPoint;

    private ServiceLoggingAspect serviceLoggingAspect;

    @BeforeEach
    public void setUp() {
        serviceLoggingAspect = new ServiceLoggingAspect();
        MDC.clear();
    }

    @Test
    public void testLogRetrieveTimeSeriesData() throws Throwable {

        TSRequest tsRequest = TSRequest.builder()
                .dataId("8202")
                .secIds("F0000001V5")
                .build();

        Object[] args = new Object[]{tsRequest};
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn("Result");
        MDC.put(LogAttribute.PRODUCT_ID.getDisplayName(), "Direct");

        Object result = serviceLoggingAspect.logRetrieveTimeSeriesData(joinPoint);

        assertEquals("Result", result);
        verify(joinPoint, times(1)).proceed();
    }

    @Test
    public void testLogRetrieveCalc() throws Throwable {
        CalcRequest calcRequest = CalcRequest.builder()
                .calcDps(List.of("13457"))
                .build();

        Object[] args = new Object[]{calcRequest};
        when(joinPoint.getArgs()).thenReturn(args);
        when(joinPoint.proceed()).thenReturn("Result");
        MDC.put(LogAttribute.PRODUCT_ID.getDisplayName(), "Direct");

        Object result = serviceLoggingAspect.logRetrieveCalc(joinPoint);

        assertEquals("Result", result);
        verify(joinPoint, times(1)).proceed();
    }
}
