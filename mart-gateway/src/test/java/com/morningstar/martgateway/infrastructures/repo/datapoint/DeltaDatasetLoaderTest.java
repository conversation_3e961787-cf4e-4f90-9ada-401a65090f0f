package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.Dataset;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DeltaDatasetLoaderTest {
    private DeltaDatasetLoader deltaDatasetLoader;
    private final DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @BeforeEach
    public void setup() throws IOException {
        DocumentLoader documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        deltaDatasetLoader = new DeltaDatasetLoader(datapointConfigFileService, documentLoader);
        when(datapointConfigFileService.getResourceAsString("config/delta/datasets/time_series_datasets.xml")).thenReturn(getMockDataPoint("dps-config/delta/datasets/time_series_datasets.xml"));
        when(datapointConfigFileService.list("config/delta/datasets/")).thenReturn(List.of("config/delta/datasets/time_series_datasets.xml"));
    }

    @Test
    public void loadDeltaDatasets() {
        deltaDatasetLoader.loadDataPoints(context);
        Map<String, Dataset> deltaDatasetMap = context.getDeltaDatasetMap();
        Assertions.assertFalse(deltaDatasetMap.isEmpty());
        Assertions.assertTrue(deltaDatasetMap.containsKey("TimeSeries.dbo.vw_TSPrice"));
        Assertions.assertEquals("PerformanceId", deltaDatasetMap.get("TimeSeries.dbo.vw_TSPrice").getIdLevel());
    }
}
