package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DataPointGroupLocationLoaderTest {
    private DataPointGroupLocationLoader dataPointGroupLocationLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);

        dataPointGroupLocationLoader = new DataPointGroupLocationLoader(datapointConfigFileService, documentLoader);

        when(datapointConfigFileService.getResourceAsString("config/data-group.xml")).thenReturn(getMockDataPoint("dps-config/data-group.xml"));
    }


    @Test
    public void testDataPoints() {
        dataPointGroupLocationLoader.loadDataPoints(context);

        { // Test:
            assertThat(context.getGroupLocationMap(), notNullValue());
            assertThat(context.getGroupLocationMap(), hasEntry("MasterPortfolioId/EquityPortfolio", "cache"));
            assertThat(context.getGroupLocationMap(), hasEntry("MasterPortfolioId/IndustryStandardFactorProfiles", "s3"));
            assertThat(context.getGroupLocationMap(), hasEntry("SecId/MasterHeader", "search"));
        }

    }
}
