package com.morningstar.martgateway.infrastructures.repo.investmentapiproxy;

import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint;
import com.morningstar.martgateway.domains.apiproxy.entity.DatapointValue;
import com.morningstar.martgateway.domains.apiproxy.entity.DoApiInvestment;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesRequestEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.TimeSerialsValue;
import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import java.net.URI;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class
DynamicDataRepositoryTest {

    @Test
    public void getDynamicData() {
        SecuritiesRequestEntity entity = createEntity();

        WebClient client = mock(WebClient.class);
        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        when(client.post()).thenReturn(requestBodyUriSpec);

        WebClient.RequestBodySpec uriSpec = mock(WebClient.RequestBodySpec.class);
        when(requestBodyUriSpec.uri((Function<UriBuilder, URI>) any())).thenReturn(uriSpec);

        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        when(uriSpec.contentType(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);

        WebClient.RequestHeadersSpec requestHeadersSpec1 = mock(WebClient.RequestHeadersSpec.class);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec1);

        WebClient.RequestBodySpec requestBodySpec2 = mock(WebClient.RequestBodySpec.class);
        when(requestHeadersSpec1.header(anyString(), anyString())).thenReturn(requestBodySpec2);

        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestBodySpec2.retrieve()).thenReturn(responseSpec);

        DataBuffer dataBuffer = mock(DataBuffer.class);
        when(dataBuffer.asInputStream()).thenReturn(getInputStream1()).thenReturn(getInputStream2());
        when(responseSpec.bodyToMono(DataBuffer.class))
                .thenReturn(Mono.just(dataBuffer))
                .thenReturn(Mono.just(dataBuffer));

        ProxyUriBuilder proxyUriBuilder = mock(ProxyUriBuilder.class);
        when(proxyUriBuilder.getUriBuilderURIFunction(entity.getUserIdOrDefault(), entity.getJobId())).thenReturn(uriBuilder -> uriBuilder
                .queryParam("UID", entity.getUserIdOrDefault())
                .queryParam("PID", "pid")
                .queryParam("RequestId", entity.getJobId())
                .build());
        DynamicDataRepository dynamicDataRepository = new DynamicDataRepository(client, proxyUriBuilder, 1, 1, 1);
        Mono<Map<String, Map<String, DatapointValue>>> dynamicData = dynamicDataRepository.getDynamicData(entity);

        StepVerifier.create(dynamicData)
                .assertNext(d -> {
                    Assert.assertEquals(d.size(), 2);
                    Assert.assertEquals(d.get("FOUSA00EA6;FO").size(), 2);
                    List<TimeSerialsValue> values1 = (List<TimeSerialsValue>) d.get("FOUSA00EA6;FO").get("dp1").getValue();
                    Assert.assertEquals(values1.size(), 5);
                    List<TimeSerialsValue> values2 = (List<TimeSerialsValue>) d.get("FOUSA00EA6;FO").get("dp2").getValue();
                    Assert.assertEquals(values2.size(), 2);

                    Assert.assertEquals(d.get("inv1").size(), 1);
                    values1 = (List<TimeSerialsValue>) d.get("inv1").get("dp1").getValue();
                    Assert.assertEquals(values1.size(), 3);
                    values2 = (List<TimeSerialsValue>) d.get("inv1").get("dp2");
                    Assert.assertNull(values2);
                })
                .expectComplete()
                .verify();
    }

    @Test
    public void getDynamicDataError() {
        SecuritiesRequestEntity entity = createEntity();

        WebClient client = mock(WebClient.class);
        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        when(client.post()).thenReturn(requestBodyUriSpec);

        WebClient.RequestBodySpec uriSpec = mock(WebClient.RequestBodySpec.class);
        when(requestBodyUriSpec.uri((Function<UriBuilder, URI>) any())).thenReturn(uriSpec);

        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        when(uriSpec.contentType(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);

        WebClient.RequestHeadersSpec requestHeadersSpec1 = mock(WebClient.RequestHeadersSpec.class);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec1);

        WebClient.RequestBodySpec requestBodySpec2 = mock(WebClient.RequestBodySpec.class);
        when(requestHeadersSpec1.header(anyString(), anyString())).thenReturn(requestBodySpec2);

        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestBodySpec2.retrieve()).thenReturn(responseSpec);

        // Create a timeout exception to trigger retries
        TimeoutException timeoutException = new TimeoutException("Connection timed out");
        when(responseSpec.bodyToMono(DataBuffer.class)).thenReturn(Mono.error(timeoutException)); // Will trigger retry logic

        ProxyUriBuilder proxyUriBuilder = mock(ProxyUriBuilder.class);
        when(proxyUriBuilder.getUriBuilderURIFunction(entity.getUserIdOrDefault(), entity.getJobId())).thenReturn(uriBuilder -> uriBuilder
                .queryParam("UID", entity.getUserIdOrDefault())
                .queryParam("PID", "pid")
                .queryParam("RequestId", entity.getJobId())
                .build());

        DynamicDataRepository dynamicDataRepository = new DynamicDataRepository(client, proxyUriBuilder, 1, 1, 2);
        Mono<Map<String, Map<String, DatapointValue>>> dynamicData = dynamicDataRepository.getDynamicData(entity);

        StepVerifier.create(dynamicData)
                .expectError(TimeoutException.class)
                .verify();
    }

    @Test
    public void getDynamicDataErrorIOException() {
        SecuritiesRequestEntity entity = createEntity();

        WebClient client = mock(WebClient.class);
        WebClient.RequestBodyUriSpec requestBodyUriSpec = mock(WebClient.RequestBodyUriSpec.class);
        when(client.post()).thenReturn(requestBodyUriSpec);

        WebClient.RequestBodySpec uriSpec = mock(WebClient.RequestBodySpec.class);
        when(requestBodyUriSpec.uri((Function<UriBuilder, URI>) any())).thenReturn(uriSpec);

        WebClient.RequestBodySpec requestBodySpec = mock(WebClient.RequestBodySpec.class);
        when(uriSpec.contentType(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);
        when(requestBodySpec.accept(MediaType.APPLICATION_XML)).thenReturn(requestBodySpec);

        WebClient.RequestHeadersSpec requestHeadersSpec1 = mock(WebClient.RequestHeadersSpec.class);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersSpec1);

        WebClient.RequestBodySpec requestBodySpec2 = mock(WebClient.RequestBodySpec.class);
        when(requestHeadersSpec1.header(anyString(), anyString())).thenReturn(requestBodySpec2);

        WebClient.ResponseSpec responseSpec = mock(WebClient.ResponseSpec.class);
        when(requestBodySpec2.retrieve()).thenReturn(responseSpec);

        DataBuffer dataBuffer = mock(DataBuffer.class);
        when(dataBuffer.asInputStream()).thenReturn(getInputError());
        when(responseSpec.bodyToMono(DataBuffer.class)).thenReturn(Mono.just(dataBuffer));

        ProxyUriBuilder proxyUriBuilder = mock(ProxyUriBuilder.class);
        when(proxyUriBuilder.getUriBuilderURIFunction(entity.getUserIdOrDefault(), entity.getJobId())).thenReturn(uriBuilder -> uriBuilder
                .queryParam("UID", entity.getUserIdOrDefault())
                .queryParam("PID", "pid")
                .queryParam("RequestId", entity.getJobId())
                .build());
        DynamicDataRepository dynamicDataRepository = new DynamicDataRepository(client, proxyUriBuilder, 1,1,2);
        Mono<Map<String, Map<String, DatapointValue>>> dynamicData = dynamicDataRepository.getDynamicData(entity);

        StepVerifier.create(dynamicData)
                .expectError(MartException.class)
                .verify();
    }

    private SecuritiesRequestEntity createEntity() {
        SecuritiesRequestEntity entity = new SecuritiesRequestEntity();
        entity.setUserId("userId");

        // DPs
        Datapoint dp1 = new Datapoint();
        dp1.setAlias("dp1");
        dp1.setId("dp1");
        Datapoint dp2 = new Datapoint();
        dp1.setAlias("dp2");
        dp1.setId("dp2");
        entity.setDatapoints(List.of(dp1, dp2));
        Datapoint dp3 = new Datapoint();
        dp1.setAlias("dp3");
        dp1.setId("dp3"); // not present in response
        entity.setDatapoints(List.of(dp1, dp2, dp3));

        // Investments
        DoApiInvestment investment1 = new DoApiInvestment();
        investment1.setId("FOUSA00EA6;FO");
        DoApiInvestment investment2 = new DoApiInvestment();
        investment2.setId("inv2");
        DoApiInvestment investment3 = new DoApiInvestment();
        investment2.setId("inv3"); // not present in response
        entity.setDoApiInvestments(List.of(investment1, investment2, investment3));

        entity.setDirectIdBatchSize(100);
        entity.setDirectDpsBatchSize(1);

        return entity;
    }

    // Both investments have data for dp1
    private InputStream getInputStream1() {
        String sampleDirectResponse =
                "<res tot=\"1\" pt=\"2\">\n" +
                        "    <flds>\n" +
                        "        <f i=\"dp1\" start=\"01-01-2020\" />\n" +
                        "    </flds>\n" +
                        "    <dat>\n" +
                        "        <r i=\"FOUSA00EA6;FO\">\n" +
                        "            <c i=\"dp1\">\n" +
                        "                <t d=\"0\" v=\"-0.0681010155\" />\n" +
                        "                <t d=\"1\" v=\"0\" />\n" +
                        "                <t d=\"2\" v=\"1.2265819411\" />\n" +
                        "                <t d=\"3\" v=\"-1.5482972009\" />\n" +
                        "                <t d=\"4\" v=\"-2.0512886925\" />\n" +
                        "            </c>\n" +
                        "        </r>\n" +
                        "    </dat>\n" +
                        "    <dat>\n" +
                        "        <r i=\"inv1\">\n" +
                        "            <c i=\"dp1\">\n" +
                        "                <t d=\"0\" v=\"-1\" />\n" +
                        "                <t d=\"1\" v=\"0\" />\n" +
                        "                <t d=\"2\" v=\"1\" />\n" +
                        "            </c>\n" +
                        "        </r>\n" +
                        "    </dat>\n" +
                        "</res>";

        return new ByteArrayInputStream(sampleDirectResponse.getBytes());
    }

    // Only FOUSA00EA6 has data for dp2
    private InputStream getInputStream2() {
        String sampleDirectResponse =
                "<res tot=\"1\" pt=\"2\">\n" +
                        "    <flds>\n" +
                        "        <f i=\"dp2\" start=\"01-01-2024\" />\n" +
                        "    </flds>\n" +
                        "    <dat>\n" +
                        "        <r i=\"FOUSA00EA6;FO\">\n" +
                        "            <c i=\"dp2\">\n" +
                        "                <t d=\"0\" v=\"First\" />\n" +
                        "                <t d=\"1\" v=\"Second\" />\n" +
                        "            </c>\n" +
                        "        </r>\n" +
                        "    </dat>\n" +
                        "</res>";

        return new ByteArrayInputStream(sampleDirectResponse.getBytes());
    }

    private InputStream getInputError() {
        String sampleDirectResponse =
                "<res tot=\"1\" pt=\"2\">\n" +
                        "    <flds>\n" +
                        "        <f i=\"dp2\" start=\"01-01-2024\" />\n" +
                        "    </flds>\n" +
                        "    <dat>\n" +
                        "        <r i=\"FOUSA00EA6;FO\">\n" +
                        "            <c i=\"dp2\">\n";

        return new ByteArrayInputStream(sampleDirectResponse.getBytes());
    }
}
