package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LakeHouseDataPointLoaderTest {

    private LakeHouseDataPointLoader lhDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        lhDataPointLoader = new LakeHouseDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        when(datapointConfigFileService.getResourceAsString("config/lh_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/lh_datapoints.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }

    @Test
    public void testDataPoints() {

        lhDataPointLoader.loadDataPoints(context);

        {
            DataPoint dataPoint = context.getDataPointById("93955");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), equalTo("SustainableGeneralAttributes"));
            assertThat(dataPoint.getIdLevel(), equalTo("FundId"));
            assertThat(dataPoint.getParseFunction(), nullValue());

            assertThat(dataPoint.getSubDataPoints(), nullValue());
        }

        {
            DataPoint dataPoint = context.getDataPointById("118001");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), equalTo("MorningstarSectorCurrent"));
            assertThat(dataPoint.getIdLevel(), equalTo("MasterPortfolioId"));
            assertThat(dataPoint.getParseFunction(), equalTo("DOUBLE"));

            assertThat(dataPoint.getLakeHouseProperty(), notNullValue());
            assertThat(dataPoint.getLakeHouseProperty().getDatabaseTable(), equalTo("incubating__client_account_#client#_portfolio__#env#.private_morningstar_sector_for_current_data,client_account_#client#__portfolio_analytics__#env#.morningstar_sector_latest"));
            assertThat(dataPoint.getLakeHouseProperty().getIdColumn(), equalTo("master_portfolio_id"));
            assertThat(dataPoint.getLakeHouseProperty().getDateColumn(), equalTo("portfolio_date"));

            assertThat(dataPoint.getLakeHouseField(), equalTo("primary_sector_breakdown_agency_mortgage_backed_percentage_long"));

            assertThat(dataPoint.getSubDataPoints(), nullValue());
        }

        {
            DataPoint dataPoint = context.getDataPointById("72681");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), equalTo("InvestmentAccessPointAdministrationFeeTier"));
            assertThat(dataPoint.getIdLevel(), equalTo("PlatformId"));
            assertThat(dataPoint.getParseFunction(), nullValue());

            assertThat(dataPoint.getLakeHouseProperty(), nullValue());

            assertThat(dataPoint.getSubDataPoints(), notNullValue());
            assertThat(dataPoint.getSubDataPoints(), hasSize(6));
        }
    }
}
