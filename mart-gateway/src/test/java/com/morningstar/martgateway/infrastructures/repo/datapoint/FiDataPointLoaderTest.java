package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.common.DataPointViewLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import org.dom4j.io.SAXReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.List;

import static com.morningstar.dataac.martgateway.test.datapointloader.util.DataPointTestUtils.getMockDataPoint;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FiDataPointLoaderTest {

    private FiDataPointLoader fiDataPointLoader;
    private DataPointLoaderContext context = new DataPointLoaderContext();

    @Mock
    private DatapointConfigFileService datapointConfigFileService;

    @InjectMocks
    private DocumentLoader documentLoader;

    @BeforeEach
    void setup() throws IOException {
        documentLoader = new DocumentLoader(new SAXReader(), datapointConfigFileService);
        fiDataPointLoader = new FiDataPointLoader(datapointConfigFileService, documentLoader);

        lenient().when(datapointConfigFileService.getResourceAsString("config/view/datapoints_view.xml")).thenReturn(getMockDataPoint("dps-config/view/datapoints_view.xml"));
        when(datapointConfigFileService.getResourceAsString("config/fi_datapoints.xml")).thenReturn(getMockDataPoint("dps-config/fi_datapoints.xml"));
        when(datapointConfigFileService.list("config/view/")).thenReturn(List.of("config/view/datapoints_view.xml"));

        DataPointViewLoader dataPointViewLoader = new DataPointViewLoader(datapointConfigFileService, documentLoader);
        dataPointViewLoader.loadDataPoints(context);
    }

    @Test
    public void testDataPoints() {

        fiDataPointLoader.loadDataPoints(context);

        {
            DataPoint dataPoint = context.getDataPointById("93953");
            assertThat(dataPoint, notNullValue());
            assertThat(dataPoint.getGroupName(), equalTo("FixedIncomeCalculated"));
            assertThat(dataPoint.getIdLevel(), equalTo("MasterPortfolioId"));

            assertThat(dataPoint.getDataServiceProperty(), notNullValue());
            assertThat(dataPoint.getDataServiceProperty().getDataType(), equalTo("string"));
            assertThat(dataPoint.getDataServiceProperty().getAvroField(), equalTo("portfolioSummary/portfolioDate"));

            assertThat(dataPoint.getDataServiceProperty().getDataGroup(), notNullValue());
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getBucketName(), equalTo("mstar-dwm-managedportfolio-fixedincome-result-verified"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getFrequency(), equalTo("r"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getLocation(), equalTo("portfolio/{encoded_id}/{date}"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupName(), equalTo("FixedIncomeCalculated"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getMulti(), equalTo("false"));
            assertThat(dataPoint.getDataServiceProperty().getDataGroup().getGroupDpNid(), nullValue());

        }
    }
}
