package com.morningstar.martgateway.infrastructures.reactive;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.CONTEXT_MAP;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.REQUEST_ID_HEADER;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.slf4j.MDC;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.context.Context;


public class ReactiveMDCTest {

	@Test
	public void testWithMDC_setsAndClearsMDC() {
		// Arrange: Create a test Mono and define a context map with a requestId
		String requestId = "test-request-id";
		Map<String, String> contextMap = new HashMap<>();
		contextMap.put(REQUEST_ID_HEADER, requestId);

		Mono<String> testMono = Mono.defer(() -> {
			// Inside the Mono, check if MDC is set correctly
			Map<String, String> actual = MDC.getCopyOfContextMap();
			assertNotNull(actual);
			assertEquals(requestId, actual.get(LogAttribute.REQUEST_ID.getDisplayName()));
			return Mono.just("Success");
		});

		// Act: Apply the withMDC logic and pass a Reactor context with the context map
		Mono<String> resultMono = ReactiveMDC.withMDC(testMono)
				.contextWrite(Context.of(CONTEXT_MAP, contextMap));

		// Assert: Verify the behavior using StepVerifier
		StepVerifier.create(resultMono)
				.expectNext("Success")
				.verifyComplete();

		// After completion, ensure MDC is cleared
		assertNull(MDC.get("requestId"));
	}

	@Test
	public void testWithMDC_noContextMap() {
		// Arrange: Create a test Mono
		Mono<String> testMono = Mono.defer(() -> {
			// Inside the Mono, check that MDC is not set
			assertNull(MDC.get(LogAttribute.REQUEST_ID.getDisplayName()));
			return Mono.just("No Request Id");
		});

		// Act: Apply the withMDC logic without a context map in the context
		Mono<String> resultMono = ReactiveMDC.withMDC(testMono);

		// Assert: Verify the behavior using StepVerifier
		StepVerifier.create(resultMono)
				.expectNext("No Request Id")
				.verifyComplete();

		// Ensure MDC is cleared
		assertNull(MDC.get(LogAttribute.REQUEST_ID.getDisplayName()));
	}

	@Test
	public void testWithMDC_contextMapWithoutRequestId() {
		// Arrange: Create a test Mono with a context map that doesn't contain the requestId
		Map<String, String> contextMap = new HashMap<>();
		Mono<String> testMono = Mono.defer(() -> {
			// Inside the Mono, check that MDC is not set
			assertNull(MDC.get(LogAttribute.REQUEST_ID.getDisplayName()));
			return Mono.just("No Request Id");
		});

		// Act: Apply the withMDC logic and pass a Reactor context with the incomplete context map
		Mono<String> resultMono = ReactiveMDC.withMDC(testMono)
				.contextWrite(Context.of(CONTEXT_MAP, contextMap));

		// Assert: Verify the behavior using StepVerifier
		StepVerifier.create(resultMono)
				.expectNext("No Request Id")
				.verifyComplete();

		// Ensure MDC is cleared
		assertNull(MDC.get(LogAttribute.REQUEST_ID.getDisplayName()));
	}
}
