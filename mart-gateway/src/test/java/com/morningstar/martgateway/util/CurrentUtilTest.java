package com.morningstar.martgateway.util;

import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

public class CurrentUtilTest {

    @Test
    public void getReadCacheTest(){
        boolean readCache = CurrentUtil.getReadCache("false");
        assertFalse(readCache);
    }

    @Test
    public void extractSubGroupTest() {
        String id = "F00000Y4CJ";
        List<CurrentResult> currentResult = CurrentUtil.extractSubGroup(id, new JSONArray(), new ArrayList<>(), Instant.now());
        assertEquals(0, currentResult.size());

        JSONObject json = new JSONObject();
        json.put("48050","USA");
        json.put("48048","270 Park Avenue");
        json.put("48049","New York, NY 10017");
        json.put("48046","10017-2070");
        json.put("48047","JPMorgan");
        json.put("48045","United States");
        json.put("48042","JPMorgan");
        json.put("48043","New York");
        json.put("48041","0C00001YRR");
        json.put("48051","1");
        JSONArray dpArray = new JSONArray();
        dpArray.put(json);
        DataPoint dp1 = DataPoint.builder().id("BrokerID").nid("48041").src("CDAPI").name("BrokerID").build();
        DataPoint dp2 = DataPoint.builder().id("BrokerName").nid("48042").src("CDAPI").name("BrokerName").build();

        DataPoint dp4 = DataPoint.builder().id("BrokerTest").nid("48051").src("CDAPI").name("BrokerYesNo").build();
        Map<String, String> items = new HashMap<>();
        items.put("1","Yes");
        items.put("0","No");
        DataPoint dp3 = DataPoint.builder().id("BrokerYesNo").nid("48052").src("CDAPI").name("BrokerYesNo").mappingSrc(dp4).mappingRef(Alias.builder().id("YesNo").items(items).build()).build();
        List<CurrentResult> currentResults = CurrentUtil.extractSubGroup(id, dpArray, Arrays.asList(dp1,dp2,dp3), Instant.now());
        assertEquals("0C00001YRR", currentResults.get(0).getValues().get("48041"));
        assertEquals("JPMorgan", currentResults.get(0).getValues().get("48042"));
        assertEquals("Yes", currentResults.get(0).getValues().get("48052"));
    }

    @Test
    public void extractSubsetIsNull() throws NoSuchFieldException, IllegalAccessException {
        String id = "F00000Y4CJ";
        JSONObject json = new JSONObject();
        json.put("48050","USA");
        json.put("48048","270 Park Avenue");
        json.put("48049","New York, NY 10017");
        json.put("48046","10017-2070");
        json.put("48047","JPMorgan");
        json.put("48045","United States");
        json.put("48042","JPMorgan");
        json.put("48043","New York");
        json.put("48041","0C00001YRR");
        json.put("48051","1");
        JSONArray dpArray = new JSONArray();
        dpArray.put(json);

        DataPoint dp1 = DataPoint.builder().id("BrokerID").nid("48041").src("CDAPI").name("BrokerID").build();
        Map<String, DataPoint> dataPointMap = new HashMap();
        dataPointMap.put("48041", dp1);
        DataPointRepository.setDataPointMap(dataPointMap);


        List<CurrentResult> currentResults = CurrentUtil.extractSubGroup(id, dpArray, new ArrayList<>(), Instant.now());
        assertEquals("0C00001YRR", currentResults.get(0).getValues().get("48041"));
    }

    @Test
    public void getObfuscationKeyTest(){
        String key = CurrentUtil.getObfuscationKey("10001");
        assertEquals("Jb2-10001", key);
    }
}
