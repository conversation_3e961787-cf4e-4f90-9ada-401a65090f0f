{"type": "record", "name": "Portfolio", "namespace": "com.morningstar.data.domain.managed", "doc": "Portfolio details", "fields": [{"name": "RecordAction", "type": {"type": "enum", "name": "Action", "namespace": "com.morningstar.data.domain.common", "doc": "An enum that define the possible actions that is related with a given message schema. NOTE! Make sure to add new entries at the end", "symbols": ["UPDATE", "DELETE", "ADD", "UPSERT"]}}, {"name": "PortfolioSummary", "type": ["null", {"type": "record", "name": "PortfolioSummary", "doc": "XOI PortfolioSummary", "fields": [{"name": "portfolioDate", "type": {"type": "string", "avro.java.string": "String"}, "doc": "yyyy-MM-dd"}]}], "default": null}, {"name": "MasterPortfolioId", "type": "long"}, {"name": "Name", "type": {"type": "string", "avro.java.string": "String"}}, {"name": "PortfolioHoldings", "type": ["null", {"type": "array", "items": {"type": "record", "name": "PortfolioHolding", "doc": "Holdings details of a portfolio", "fields": [{"name": "AccruedInterest", "type": ["null", "double"], "default": null}, {"name": "AltMinTaxEligible", "type": ["null", "boolean"], "default": null}, {"name": "CUSIP", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "CommoditySectorId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "CompanyId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null, "alias": "Company"}, {"name": "CompanyName", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "ContractEffectiveDate", "type": ["null", {"type": "string", "avro.java.string": "String"}], "doc": "yyyy-MM-dd", "default": null}, {"name": "CostBasis", "type": ["null", "double"], "default": null}, {"name": "CountryId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null, "alias": "Country"}, {"name": "CountryName", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "Coupon", "type": ["null", "double"], "default": null}, {"name": "CreditQuality", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "CurrencyId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "doc": "char3", "default": null, "alias": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "DetailHoldingTypeId", "type": {"type": "string", "avro.java.string": "String"}, "default": ""}, {"name": "Duration", "type": ["null", "double"], "default": null}, {"name": "EconomicExposure", "type": ["null", "long"], "default": null}, {"name": "ExternalId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "ExternalName", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "FirstAccrualDate", "type": ["null", {"type": "string", "avro.java.string": "String"}], "doc": "yyyy-MM-dd", "default": null}, {"name": "FirstBoughtDate", "type": ["null", {"type": "string", "avro.java.string": "String"}], "doc": "yyyy-MM-dd", "default": null}, {"name": "GICSIndustryId", "type": ["null", "int"], "default": null}, {"name": "GlobalIndustryId", "type": ["null", "int"], "default": null}, {"name": "GlobalSector", "type": ["null", "int"], "default": null}, {"name": "HoldingDetailId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null, "alias": "Id"}, {"name": "ISIN", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "ISOExchangeId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "ImpliedVolatility", "type": ["null", "double"], "default": null}, {"name": "IndianCreditQualityClassification", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "IndianIndustryClassification", "type": ["null", "int"], "default": null}, {"name": "IndianIndustryClassificationName", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "IndustryId", "type": ["null", "int"], "default": null}, {"name": "JointlyOwnedShares", "type": ["null", "long"], "default": null, "alias": "NumberOfJointlyOwnedShare"}, {"name": "LessThan92DaysBond", "type": ["null", "boolean"], "default": null}, {"name": "LessThanOneYearBond", "type": ["null", "boolean"], "default": null}, {"name": "LocalCurrencyCode", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "LocalMarketValue", "type": ["null", "long"], "default": null}, {"name": "LocalName", "type": ["null", {"type": "map", "values": {"type": "string", "avro.java.string": "String"}, "avro.java.string": "String"}], "default": null}, {"name": "LocalSecurityType", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "MarketValue", "type": ["null", "long"], "default": null}, {"name": "MaturityDate", "type": ["null", {"type": "string", "avro.java.string": "String"}], "doc": "yyyy-MM-dd", "default": null}, {"name": "MexicanEmisora", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "MexicanSerie", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "MexicanTipoValor", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "OSICode", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "OriginalMarketValue", "type": ["null", "long"], "default": null}, {"name": "OriginalMarketValueInDouble", "type": ["null", "double"], "default": null}, {"name": "OriginalNumberOfShareInDouble", "type": ["null", "double"], "default": null}, {"name": "OriginalShares", "type": ["null", "long"], "default": null, "alias": "OriginalNumberOfShare"}, {"name": "PayCurrencyId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "PaymentType", "type": ["null", "int"], "default": null}, {"name": "PerformanceId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "RTExchangeId", "type": ["null", "int"], "default": null}, {"name": "RTRoot", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "RTSecurityType", "type": ["null", "int"], "default": null}, {"name": "RTSymbol", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "ReceiveCurrencyId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "Rule144AEligible", "type": ["null", "boolean"], "default": null}, {"name": "SEDOL", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "SecondarySectorId", "type": ["null", "int"], "default": null}, {"name": "Sector", "type": ["null", "int"], "default": null}, {"name": "SectorName", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "SecurityName", "type": {"type": "string", "avro.java.string": "String"}, "default": ""}, {"name": "Sequence", "type": ["null", "int"], "default": null}, {"name": "ShareChange", "type": ["null", "long"], "default": null}, {"name": "SharePercentage", "type": ["null", "double"], "default": null}, {"name": "Shares", "type": ["null", "long"], "default": null, "alias": "NumberOfShare"}, {"name": "StorageId", "type": ["null", "int"], "default": null}, {"name": "StrikePrice", "type": ["null", "double"], "default": null}, {"name": "SurveyedWeighting", "type": ["null", "double"], "default": null}, {"name": "Ticker", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "UnderlyingPerformanceId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "UnderlyingSecId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "UnderlyingSecurityName", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "Weight", "type": ["null", "double"], "default": null, "alias": "Weighting"}, {"name": "ZAFAssetType", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "ZAFBondIssuerClass", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "RecordAction", "type": "com.morningstar.data.domain.common.Action"}, {"name": "PortfolioCurrency", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "PortfolioDate", "type": {"type": "string", "avro.java.string": "String"}, "doc": "yyyy-MM-dd", "default": ""}, {"name": "SequenceId", "type": "int", "default": 0}, {"name": "StableValueDetail", "type": ["null", {"type": "record", "name": "PortfolioHoldingStableValueDetail", "doc": "StableValueDetail for holdings", "fields": [{"name": "contractDuration", "type": ["null", "double"], "default": null}, {"name": "contractType", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "purchaseDate", "type": ["null", {"type": "string", "avro.java.string": "String"}], "doc": "yyyy-MM-dd", "default": null}, {"name": "syntheticType", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}]}], "default": null}, {"name": "TopHoldingSequenceId", "type": ["null", "int"], "default": null}, {"name": "RTConsolidatedId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}]}}], "default": null}, {"name": "CurrencyId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}, {"name": "providerId", "type": ["null", "long"], "default": null}, {"name": "id", "type": ["null", "long"], "default": null}, {"name": "sequenceId", "type": ["null", {"type": "string", "avro.java.string": "String"}], "default": null}]}