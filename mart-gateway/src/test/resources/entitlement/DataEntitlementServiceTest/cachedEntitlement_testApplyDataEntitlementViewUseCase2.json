{"updatedOn": "2024-01-01 01:01:01", "morningstarFullAccessUseCase": ["view", "export"], "cellMatch": [{"packageId": "1", "useCase": "feed", "thirdParty": false, "universeCriteria": [{"dataPointId": "dp1", "operator": "eq", "values": ["value1"]}]}, {"packageId": "2", "useCase": "feed", "thirdParty": false, "universeCriteria": [{"dataPointId": "dp2", "operator": "eq", "values": ["value2"]}]}, {"packageId": "3", "useCase": "feed", "thirdParty": false, "universeCriteria": [{"dataPointId": "dp3", "operator": "contains", "values": ["value1", "value2"]}]}, {"packageId": "4", "useCase": "feed", "thirdParty": false, "universeCriteria": [{"dataPointId": "dp1", "operator": "eq", "values": ["value1"]}, {"dataPointId": "dp2", "operator": "eq", "values": ["value2"]}]}], "rowFilter": [{"useCase": "view", "matchCriteria": [], "universeCriteria": []}]}