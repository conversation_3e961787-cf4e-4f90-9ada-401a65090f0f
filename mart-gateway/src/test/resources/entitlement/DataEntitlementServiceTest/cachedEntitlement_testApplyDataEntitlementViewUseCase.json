{"updatedOn": "2024-01-01 01:01:01", "morningstarFullAccessUseCase": ["view", "export"], "cellMatch": [{"packageId": "1", "useCase": "view", "thirdParty": true, "universeCriteria": [{"dataPointId": "dp1", "operator": "eq", "values": ["value1"]}]}, {"packageId": "2", "useCase": "view", "thirdParty": true, "universeCriteria": [{"dataPointId": "dp2", "operator": "neq", "values": ["value2"]}]}, {"packageId": "3", "useCase": "view", "thirdParty": true, "universeCriteria": [{"dataPointId": "dp3", "operator": "contains", "values": ["value1", "value2"]}]}, {"packageId": "4", "useCase": "view", "thirdParty": true, "universeCriteria": [{"dataPointId": "dp1", "operator": "eq", "values": ["value1"]}, {"dataPointId": "dp2", "operator": "eq", "values": ["value2"]}]}], "rowFilter": [{"useCase": "view", "matchCriteria": [], "universeCriteria": []}]}