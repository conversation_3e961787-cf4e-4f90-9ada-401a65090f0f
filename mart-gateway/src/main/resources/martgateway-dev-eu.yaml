# connection config for mongodb cloud
mongodb:
  uri: mongodb+srv://DOWS-MGSVC-NON-PROD-READ:<EMAIL>/?readPreference=secondaryPreferred&maxPoolSize=250&minPoolSize=0&connectTimeoutMS=5000
  maximumPoolSize: 5

martgateway:
  aws:
    region: eu-west-1
    s3:
      region: eu-west-1
      prefix: dev-
      bucket: mart-data-dev-eu
      xoiConfig: config/xoi_datapoints.xml
      rdbConfig: config/datapoints.xml
      athenaConfig: config/athena_datapoints.xml
      lakehouseConfig: config/lh_datapoints.xml
      viewConfig: config/datapoints_view.xml
      dataPointWhiteListConfig: config/datapoint_whitelist.txt
      codeMappingsConfig: config/code_mappings.xml
      fixedIncomeConfig: config/fixed_income_datapoints.xml
      # a product id list to be loaded in memory
      productIdsRegistration: product_ids_registration.txt
    sqs:
      accountId: ************
      rdb: rdb-sqs-dev

  tscache:
    server: http://172.28.41.200/fcgi-bin/tscs.cgi

  calculation:
    customCalculationUrl: "http://customcalcapi-stg.date7ebe.easn.morningstar.com"
    customCalculationEndpoint: /v1/calculator/calculate
    customCalculationTimeout: 60
    baseUrl: http://platform-calc-stg.morningstar.com
    userAgent: Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.84 Safari/537.36
    uri: /v1/dcs/calculation?service_type=49006&action=1&priority=0&retryable=1&mem_level=1&max_wait_time=200000&max_exec_time=200000&input_format=1&format=2&operation_type=0
    timeout: 5
    useNewCCS: true

  redshift:
    dbUrl: *****************************************************************************************
    user: cz_a_************
    password: /LAKEHOUSE/ADMIN_PASSWORD

  redislock:
    rdb:
      useLock: false

  martapi: false

  #  If this value is empty (default), program will load datapoints XML files from S3.
  #  If this value is not empty, program will load XML files from local to reduce debuging time. Set environment variable with your local directory such as localDpsXmlPath = ../mart-loader-aws/
  localDpsXmlPath:

  delta:
    esChangeLogUrl: mart-changelog-search-dev-us-east-1.date7ebe.easn.morningstar.com:80

  direct:
    standardUrl: http://direct-serversite-stg.date7ebe.easn.morningstar.com/researchdatacenter/researchdatacenter.aspx?retXML=yes&ProductCode=DIRECT&lang=ENU&DOVersion=AWD3.20&PCODE=DIRECT
    pid: 2262e3b5-8f49-49c2-9a39-5099fc3b1435
    investmentsPerCall: 25
    datapointsPerCall: 5
    dynamicClientTimeout: 300
    regularClientTimeout: 50
    regularRetries: 2
    regularBackOffSeconds: 10
    dynamicRetries: 2
    dynamicBackOffSeconds: 10

  localCache:
    count: 2000000
    enableCache: false
    queueSize: 10
    s3File:
      idMapper: activeSecIds
      deltaId: deltaSecIds

  uim:
    url: https://www.us-stg-api.morningstar.com/token/oauth
    username: <EMAIL>
    password: /MART/ENTITLEMENT_ADMIN_PASSWORD
