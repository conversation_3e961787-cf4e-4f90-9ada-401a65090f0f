package com.morningstar.martgateway.applications.calculation;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.martgateway.domains.calc.CustomCalculationService;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.calclib.CalculationLibService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

@Component
public class CustomCalculationServiceGateway implements Gateway<Result, MartRequest> {
	private final CustomCalculationService customCalculationService;
	private final CalculationLibService calculationLibService;

	@Autowired
	public CustomCalculationServiceGateway(CustomCalculationService customCalculationService,
			CalculationLibService calculationLibService) {
		this.customCalculationService = customCalculationService;
		this.calculationLibService = calculationLibService;
	}

	/**
	 * @param martRequest
	 * @return
	 */
	@Override
	public Flux<Result> retrieve(MartRequest martRequest) {

		boolean hasNoEquityDatapoint = martRequest.getDps().stream()
				.map(DataPointRepository::getByNid)
				.noneMatch(dp -> dp.getEquityDatapoint() != null);

		if(hasNoEquityDatapoint) {
			CalcRequest calcApiRequest = CalcRequest.buildApiRequest(martRequest);
			CalcRequest calcLibRequest = CalcRequest.buildLibRequest(martRequest);
			Flux<Result> calculationAPIResult = customCalculationService.retrieveCalc(calcApiRequest);
			Flux<Result> calculationLibResult = calculationLibService.retrieveCalcLib(calcLibRequest);
			return Flux.merge(calculationAPIResult, calculationLibResult)
					.subscribeOn(SchedulerConfiguration.getScheduler());
		}
		return Flux.empty();
	}

	@Override
	public boolean handleRequest(MartRequest request) {
		return request.isUseNewCCS();
	}
}
