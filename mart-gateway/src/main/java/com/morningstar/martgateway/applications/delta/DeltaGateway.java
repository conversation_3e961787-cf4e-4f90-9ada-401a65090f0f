package com.morningstar.martgateway.applications.delta;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.Dataset;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.martgateway.domains.delta.DeltaValueService;
import com.morningstar.martgateway.domains.delta.MostRecentDeltaDetectionService;
import com.morningstar.martgateway.domains.delta.TimeSeriesDeltaDetectionService;
import com.morningstar.martgateway.domains.delta.entity.DataPointDeltaDetection;
import com.morningstar.martgateway.domains.delta.entity.DeltaDataPoint;
import com.morningstar.martgateway.domains.delta.entity.DeltaDetection;
import com.morningstar.martgateway.domains.delta.entity.DeltaRange;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DeltaGateway implements Gateway<Result, InvestmentApiRequest> {

    private final MostRecentDeltaDetectionService mostRecentDeltaDetectionService;
    private final TimeSeriesDeltaDetectionService timeSeriesDeltaDetectionService;
    private final DeltaValueService deltaValueService;

    private record DataPointLists(List<DeltaDataPoint> mostRecentDataPoints, List<DeltaDataPoint> timeSeriesDataPoints) {}

    public DeltaGateway(MostRecentDeltaDetectionService mostRecentDeltaDetectionService, TimeSeriesDeltaDetectionService timeSeriesDeltaDetectionService, DeltaValueService deltaValueService) {
        this.mostRecentDeltaDetectionService = mostRecentDeltaDetectionService;
        this.timeSeriesDeltaDetectionService = timeSeriesDeltaDetectionService;
        this.deltaValueService = deltaValueService;
    }

    @Override
    public Flux<Result> retrieve(InvestmentApiRequest request) {
        if (request.getDeltaStartTime() == null) {
            return Flux.empty();
        }

        DeltaRange deltaRange = new DeltaRange(request.getDeltaStartTime().toEpochMilli(), Instant.now().toEpochMilli());
        List<IdMapper> idMappers = request.getIdMappers();

        DataPointLists filteredDataPoints = filterDataPoints(request.getDataPoints());

        List<DeltaDataPoint> mostRecentDataPoints = filteredDataPoints.mostRecentDataPoints();
        List<DeltaDataPoint> timeSeriesDataPoints = filteredDataPoints.timeSeriesDataPoints();

        Map<String, Map<String, Set<String>>> idMapsByLevel = buildIdMapByLevel(mostRecentDataPoints, timeSeriesDataPoints, idMappers);
        Flux<DataPointDeltaDetection> mostRecentDpDetections = mostRecentDeltaDetectionService.getDelta(mostRecentDataPoints, idMapsByLevel, deltaRange);
        Flux<DataPointDeltaDetection> timeSeriesDpDetections = timeSeriesDeltaDetectionService.getDelta(timeSeriesDataPoints, idMapsByLevel, deltaRange);

        if (request.isDeltaDetection()) {
            Flux<Result> mostRecentResults = mostRecentDpDetections.groupBy(DataPointDeltaDetection::getDeltaDataPoint, DataPointDeltaDetection::getDeltaDetection).flatMap(groupedDelta -> buildMostRecentResults(groupedDelta.key(), groupedDelta.map(Function.identity())));
            Flux<Result> timeSeriesResults = timeSeriesDpDetections.groupBy(DataPointDeltaDetection::getDeltaDataPoint, DataPointDeltaDetection::getDeltaDetection).flatMap(groupedDelta -> buildTimeSeriesResults(groupedDelta.key(), groupedDelta.map(Function.identity())));
            return Flux.merge(mostRecentResults, timeSeriesResults);
        }

        return deltaValueService.getDeltaValues(request, mostRecentDpDetections, timeSeriesDpDetections);
    }

    private DataPointLists filterDataPoints(List<GridviewDataPoint> gridviewDataPoints) {
        List<DeltaDataPoint> mostRecentDataPoints = new ArrayList<>();
        List<DeltaDataPoint> timeSeriesDataPoints = new ArrayList<>();
        for (GridviewDataPoint gridviewDataPoint : gridviewDataPoints) {
            DataPoint dataPoint = DataPointRepository.getByNid(gridviewDataPoint.getDataPointId());
            if (!DeltaDataPoint.isDeltaCapable(gridviewDataPoint, dataPoint)) {
                continue;
            }
            if (gridviewDataPoint.isTimeSeries()) {
                timeSeriesDataPoints.add(new DeltaDataPoint(gridviewDataPoint, dataPoint));
            } else {
                mostRecentDataPoints.add(new DeltaDataPoint(gridviewDataPoint, dataPoint));
            }
        }
        return new DataPointLists(mostRecentDataPoints, timeSeriesDataPoints);
    }

    private Map<String, Map<String, Set<String>>> buildIdMapByLevel(List<DeltaDataPoint> mostRecentDataPoints, List<DeltaDataPoint> timeSeriesDataPoints, List<IdMapper> idMappers) {
        Map<String, Map<String, Set<String>>> idMapsByLevel = new HashMap<>();
        Stream.of(mostRecentDataPoints, timeSeriesDataPoints)
                .flatMap(Collection::stream)
                .flatMap(dp -> dp.getDeltaConfiguration().getDatasets().stream().map(Dataset::getIdLevel))
                .forEach(idLevel -> idMapsByLevel.computeIfAbsent(idLevel, absentIdLevel -> getIdMap(idMappers, absentIdLevel)));
        return idMapsByLevel;
    }

    private Map<String, Set<String>> getIdMap(List<IdMapper> idMapperList, String idLevel) {
        Map<String, Set<String>> idMap = new HashMap<>();
        idMapperList.forEach(idMapper -> {
            String matchedId = idMapper.getId(idLevel);
            if (StringUtils.isNotEmpty(matchedId)) {
                Set<String> argsList = idMap.computeIfAbsent(matchedId, k -> new HashSet<>());
                argsList.add(idMapper.getInvestmentId());
            }
        });
        return idMap;
    }

    private Flux<Result> buildMostRecentResults(DeltaDataPoint deltaDataPoint, Flux<DeltaDetection> mostRecentDeltaFlux) {
        return mostRecentDeltaFlux
                .collectList()
                .map(deltaDetections -> {
                    Set<String> deltaDetectionInvestments = deltaDetections.stream().map(DeltaDetection::getInvestmentId).collect(Collectors.toSet());
                    return deltaDetectionInvestments.stream().map(investmentId -> buildMostRecentResult(deltaDataPoint, investmentId));
                })
                .flatMapMany(Flux::fromStream);
    }

    private CurrentResult buildMostRecentResult(DeltaDataPoint deltaDataPoint, String investmentId) {
        Map<String, String> values = new HashMap<>();
        values.put(labelDataPoint(deltaDataPoint), null);
        return new CurrentResult(investmentId, values);
    }

    private Flux<Result> buildTimeSeriesResults(DeltaDataPoint deltaDataPoint, Flux<DeltaDetection> deltaDetectionFlux) {
        return deltaDetectionFlux
                .collectList()
                .map(deltaDetections -> buildTimeSeriesResults(deltaDataPoint, deltaDetections))
                .flatMapMany(Flux::fromStream);
    }

    private Stream<TimeSeriesResult> buildTimeSeriesResults(DeltaDataPoint deltaDataPoint, List<DeltaDetection> deltaDetections) {
        Map<String, Set<String>> deltaDetectionDateById = deltaDetections.stream().collect(Collectors.groupingBy(DeltaDetection::getInvestmentId, Collectors.mapping(DeltaDetection::getDetectionDate, Collectors.toSet())));
        return deltaDetectionDateById.entrySet().stream().map(entry -> buildTimeSeriesResult(deltaDataPoint, entry.getKey(), entry.getValue()));
    }

    private TimeSeriesResult buildTimeSeriesResult(DeltaDataPoint deltaDataPoint, String investmentId, Set<String> dates) {
        Map<String, List<V>> values = buildTimeSeriesResultValues(deltaDataPoint, dates);
        return new TimeSeriesResult(investmentId, values);
    }

    private Map<String, List<V>> buildTimeSeriesResultValues(DeltaDataPoint deltaDataPoint, Set<String> deltaDetectionDates) {
        List<V> valueList = deltaDetectionDates.stream()
                .map(date -> new V(date, null))
                .toList();
        Map<String, List<V>> values = new HashMap<>();
        values.put(labelDataPoint(deltaDataPoint), valueList);
        return values;
    }

    private String labelDataPoint(DeltaDataPoint deltaDataPoint) {
        return StringUtils.isEmpty(deltaDataPoint.getGridviewDataPoint().getAlias()) ? deltaDataPoint.getDataPoint().getNid() : deltaDataPoint.getGridviewDataPoint().getAlias();
    }
}
