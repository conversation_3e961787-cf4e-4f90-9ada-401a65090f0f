package com.morningstar.martgateway.applications.calculation;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.martgateway.domains.calc.CalculationService;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.calclib.CalculationLibService;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 1.if current data, get bmk value if bmk exist in configurationnn
 * 2.if time serials data, convert start date
 * 3.build calculation service request
 * 4.format calculation result
 */
@Component
public class CalcServiceGateway implements Gateway<Result, MartRequest> {

    private final CalculationService calculationService;
    private final CalculationLibService calculationLibService;

    private static final String UNKNOWN_SECURITY_TYPE = "UNKNOWN";

    @Autowired
    public CalcServiceGateway(CalculationService calculationService,
                              CalculationLibService calculationLibService) {
        this.calculationService = calculationService;
        this.calculationLibService = calculationLibService;
    }

    @Override
    public Flux<Result> retrieve(MartRequest martRequest) {

        // this is skipping equity DPs to proceed to EquityGateway for equity DPs defined in calculations.xml
        boolean hasNoEquityDatapoint = martRequest.getDps().stream()
                .map(DataPointRepository::getByNid)
                .noneMatch(dp -> dp.getEquityDatapoint() != null);

        if(hasNoEquityDatapoint) {
            CalcRequest calcApiRequest = CalcRequest.buildApiRequest(martRequest);
            CalcRequest calcLibRequest = CalcRequest.buildLibRequest(martRequest);
            Flux<Result> calculationAPIResult = calculationService.retrieveCalc(calcApiRequest);
            Flux<Result> calculationLibResult = Flux.empty();
            for (CalcRequest calcRequestForSecurityType : splitBySecurityType(calcLibRequest)) {
                calculationLibResult = Flux.merge(calculationLibService.retrieveCalcLib(calcRequestForSecurityType), calculationLibResult);
            }
            return Flux.merge(calculationAPIResult, calculationLibResult)
                    .subscribeOn(SchedulerConfiguration.getScheduler());
        }
        return Flux.empty();
    }

    @Override
    public boolean handleRequest(MartRequest request) {
        return !request.isUseNewCCS();
    }

    private List<CalcRequest> splitBySecurityType(CalcRequest calcRequest) {
        if (CollectionUtils.isEmpty(calcRequest.getIdMappers())) {
            return Collections.singletonList(calcRequest);
        }
        Map<String, CalcRequest> idsBySecurityType = new HashMap<>();
        Map<String, Optional<IdMapper>> idMap = buildIdMap(calcRequest);
        for (Map.Entry<String, Optional<IdMapper>> idEntry : idMap.entrySet()) {
            Optional<IdMapper> idMapperOpt = idEntry.getValue();
            if (idMapperOpt.isPresent()) {
                IdMapper idMapper = idMapperOpt.get();
                String securityType = extractSecurityType(idMapper);
                CalcRequest calcRequestForSecurityType = idsBySecurityType.computeIfAbsent(securityType, k -> buildRequestWithoutIds(calcRequest));
                calcRequestForSecurityType.getIdList().add(idEntry.getKey());
                calcRequestForSecurityType.getIdMappers().add(idMapper);
                continue;
            }
            CalcRequest calcRequestForSecurityType = idsBySecurityType.computeIfAbsent(UNKNOWN_SECURITY_TYPE, k -> buildRequestWithoutIds(calcRequest));
            calcRequestForSecurityType.getIdList().add(idEntry.getKey());
        }
        return new ArrayList<>(idsBySecurityType.values());
    }

    private String extractSecurityType(IdMapper idMapper) {
        String securityType;
        if (idMapper.isPrivateModel()) {
            securityType = "PrivateModel";
        } else {
            securityType = idMapper.getSecurityType();
        }
        return securityType;
    }

    private Map<String, Optional<IdMapper>> buildIdMap(CalcRequest calcRequest) {
        Map<String, Optional<IdMapper>> idMap = calcRequest.getIdMappers().stream().collect(Collectors.toMap(
                IdMapper::getInvestmentId, Optional::of, (oldMapper, newMapper) -> oldMapper, HashMap::new)
        );
        calcRequest.getIdList().stream().filter(id -> !idMap.containsKey(id))
                .forEach(id -> idMap.put(id, Optional.empty()));
        return idMap;
    }

    private CalcRequest buildRequestWithoutIds(CalcRequest calcRequest) {
        CalcRequest request = calcRequest.shallowCopy();
        request.setIdList(new ArrayList<>());
        request.setIdMappers(new ArrayList<>());
        return request;
    }

}
