package com.morningstar.martgateway.domains.rdb.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.GroupDataTS;
import com.morningstar.dataac.martgateway.core.common.entity.result.TSDatapointValueGroup;
import com.morningstar.dataac.martgateway.core.common.entity.result.TSGroupResultValue;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeseriesGroupData;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeseriesGroupResult;
import com.morningstar.martgateway.domains.rdb.cache.TSGroupTransformer;
import com.morningstar.martgateway.domains.rdb.cache.entity.Filters;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsData;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsDataList;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.morningstar.martgateway.util.FrequencyDateFormatUtil;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.MapUtils;

@UtilityClass
@SuppressWarnings("java:S1118")
public class TSGroupTransformers {

    public static final TSGroupTransformer<String, Collection<TsDataList>, TimeseriesGroupResult> transformToTimeSeriesGroupResult =
            (sdt, edt, dataList, key, dpsToFreqMap,id) -> {

                List<TSDatapointValueGroup> datapointGroups = dataList.stream()
                        .filter(tsDataList -> MapUtils.isNotEmpty(dpsToFreqMap))
                        .map(tsDataList -> {
                            String datapointId = tsDataList.getDpId();
                            LocalDate startDate = FrequencyDateFormatUtil.getFrequencyStartDate(sdt, dpsToFreqMap.get(datapointId));
                            LocalDate endDate = FrequencyDateFormatUtil.getFrequencyEndDate(edt, dpsToFreqMap.get(datapointId));

                            // Grouping TS data with Date first
                            Map<LocalDate, List<TsData>> groupedByDate = tsDataList.getValues().stream()
                                    .filter(Filters.dateRange(startDate, endDate))
                                    .filter(data -> data.getValue() != null && !data.getValue().isBlank())
                                    .collect(Collectors.groupingBy(tsData -> LocalDate.ofEpochDay(tsData.getDateEpoc())));

                            List<TimeseriesGroupData> timeSeriesDataList = groupedByDate.entrySet().stream()
                                    .map(entry -> {
                                        String date = entry.getKey().toString();
                                        List<GroupDataTS> groupDataList = entry.getValue().stream()
                                                .map(tsData -> {
                                                    List<TSGroupResultValue> values = List.of(
                                                            new TSGroupResultValue(datapointId, tsData.getValue())
                                                    );
                                                    return new GroupDataTS(values);
                                                })
                                                .filter(group -> group.getValues() != null && !group.getValues().isEmpty())
                                                .collect(Collectors.toList());

                                        return groupDataList.isEmpty() ? null : new TimeseriesGroupData(date, groupDataList);
                                    })
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());

                            return timeSeriesDataList.isEmpty() ? null : new TSDatapointValueGroup(datapointId, timeSeriesDataList);
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                Map<String, List<TSDatapointValueGroup>> valuesMap = Map.of(id, datapointGroups);

                return new TimeseriesGroupResult(key, valuesMap);
            };


}
