package com.morningstar.martgateway.domains.calc;
import static com.morningstar.dataac.martgateway.core.common.util.DateUtil.getDateOfQuarterBasic;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.ValueAtRisk;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.calc.model.CalculationFrequency;
import com.morningstar.martgateway.domains.calc.model.CalculationWindowType;
import com.morningstar.martgateway.domains.calc.model.Compounding;
import com.morningstar.martgateway.domains.calc.model.CustomCalcDataPointRequest;
import com.morningstar.martgateway.domains.calc.model.CustomCalcExcludeFromSource;
import com.morningstar.martgateway.domains.calc.model.CustomCalcRequestNew;
import com.morningstar.martgateway.domains.calc.model.ValueAtRiskSetting;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import com.morningstar.martgateway.util.FrequencyDateFormatUtil;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.Logger;
import reactor.util.Loggers;

public class CustomCalculationRequestBuilder {

	private static final Logger LOGGER = Loggers.getLogger(CustomCalculationRequestBuilder.class);
	public static final String INVESTMENT_API_PRODUCT_ID = "<investment_api_product_id>";
	public static final String START_DATE_KEY = "START_DATE";
	public static final String END_DATE_KEY = "END_DATE";

	public static final String ANNUAL_DAYS = "365.25";

	private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	private CustomCalculationRequestBuilder(){}

	public static CustomCalcRequestNew initNewCalcRequestDto(List<String> ids, String userId, String requestId) {
		return CustomCalcRequestNew.builder().uid(userId).investmentIds(ids).pid(INVESTMENT_API_PRODUCT_ID).datapoints(new ArrayList<>()).requestId(requestId).build();
	}

	// Assuming Data, CalcSetting, and Calculation are defined as previously discussed
	public static CustomCalcDataPointRequest createCurrentDataPointRequest(String id, DataPoint dpObj, String bmkSecId, String rfSecId, CalcRequest calcRequest, Map<String, String> dateResultsMap) {

		Calculation calculation = dpObj.getCalculation();
		String ann = calculation.getAnn() == null ? calcRequest.getAnnualized() : calculation.getAnn();
		String dpId = dpObj.getNid();

		// Create an instance of Data
		CustomCalcDataPointRequest calcDpRequest = new CustomCalcDataPointRequest();
		calcDpRequest.setId(dpId);
		calcDpRequest.setSourceId(calculation.getSource().getNid());
		calcDpRequest.setCurrency(calcRequest.getCurrency()	);
		calcDpRequest.setFrequency((calculation.getFreq() == null) ? null : CalculationFrequency.fromValue(Integer.valueOf(calculation.getFreq())));
		calcDpRequest.setStartDate(dateResultsMap.get(START_DATE_KEY));
		calcDpRequest.setEndDate(dateResultsMap.get(END_DATE_KEY));
		calcDpRequest.setPreEuroCurrency(StringUtils.isNotEmpty(calcRequest.getPreCurrency()) ? calcRequest.getPreCurrency() : "DEM");

		// Set calculation settings
		calcDpRequest.setCalcId(calculation.getDp().getNid());
		String requireFullHistory = calculation.getRequireFullHistory();
		calcDpRequest.setIsRequireFullHistory(StringUtils.isEmpty(requireFullHistory) || Boolean.TRUE.toString().equalsIgnoreCase(requireFullHistory));
		initBenchmark(calculation, calcDpRequest, bmkSecId, rfSecId);
		calcDpRequest.setIsAnnualize(Boolean.parseBoolean(ann));

		String annualDays = StringUtils.isNotEmpty(calcRequest.getAnnualDays()) ? calcRequest.getAnnualDays() : ANNUAL_DAYS;
		calcDpRequest.setDailyAnnualMultiplier(Double.valueOf(annualDays));

		calcDpRequest.setIsSkipHoliday(Boolean.valueOf(calcRequest.getSkipHoliday()));
		calcDpRequest.setWindowType(StringUtils.isNotEmpty(calcRequest.getWindowType()) ? CalculationWindowType.fromValue(Integer.valueOf(calcRequest.getWindowType())) : CalculationWindowType.SINGLE);
		calcDpRequest.setIsExtendedPerformance(Boolean.valueOf(calcRequest.getExtendedPerformance()));
		calcDpRequest.setCompoundingMethod(calcRequest.getCompoundingMethod());
		calcDpRequest.setBaseCurrencyId(calculation.getCur() != null ? calculation.getCur().getNid() : "");
		calcDpRequest.setIsApplyIsraelsenModification(calcRequest.getIsApplyIsraelsenModification());

		return calcDpRequest;
	}

	private static void initBenchmark(Calculation calculation, CustomCalcDataPointRequest calcDpRequest, String bmkSecId, String rfSecId) {
		// Only set bmkId and rfid if applicable
		if (calculation.getRf() != null || calculation.getBmk() != null) {
			if (StringUtils.isEmpty(bmkSecId) && StringUtils.isEmpty(rfSecId)) {
				return; // Return null or handle as necessary if both IDs are empty
			}
			if (!StringUtils.isEmpty(bmkSecId)) {
				calcDpRequest.setBenchmarkId(bmkSecId);
			}
			if (!StringUtils.isEmpty(rfSecId)) {
				calcDpRequest.setRiskFreeProxyId(rfSecId);
			}
		}
	}

	public static Mono<Result> mergeResultsAsSingleValue(Flux<Result> currentResults) {
		return currentResults
				.filter(result -> result.getValues() != null && !result.getValues().isEmpty())
				.groupBy(Result::getId)
				.flatMap(groupedFlux -> groupedFlux.collectList()
						.flatMapMany(Flux::fromIterable)
						.reduce(CustomCalculationRequestBuilder::mergeResults)).singleOrEmpty();
	}

	private static Result mergeResults(Result origin, Result append) {
		origin.getValues().putAll(append.getValues());
		return origin;
	}

	public static CustomCalcDataPointRequest createTimeSeriesDataPointRequestDto(DataPoint dataPoint, String startDate, String endDate, CalcRequest calcRequest){
		Calculation calculation = dataPoint.getCalculation();
		// Create an instance of Data
		CustomCalcDataPointRequest data = new CustomCalcDataPointRequest();
		data.setId(dataPoint.getNid());
		data.setSourceId(calculation.getSource().getNid());
		data.setCurrency(calcRequest.getCurrency());
		data.setFrequency((calculation.getFreq() == null) ? null : CalculationFrequency.fromValue(Integer.valueOf(calculation.getFreq())));
		data.setStartDate(startDate);
		data.setEndDate(getAdjustedEndDate(data.getFrequency().getShortName(), endDate));
		data.setPreEuroCurrency(StringUtils.isNotEmpty(calcRequest.getPreCurrency()) ? calcRequest.getPreCurrency() : "DEM");
		String requireFullHistory = calculation.getRequireFullHistory();
		data.setIsRequireFullHistory(StringUtils.isEmpty(requireFullHistory) || Boolean.TRUE.toString().equalsIgnoreCase(requireFullHistory));
		String calcBmk = calculation.getBmk() != null ? calculation.getBmk().getNid() : "";
		data.setBenchmarkId(StringUtils.isNotEmpty(calcRequest.getBenchmark()) ? calcRequest.getBenchmark() : calcBmk);
		String calcRf = calculation.getRf() != null ? calculation.getRf().getNid() : "";
		data.setRiskFreeProxyId(StringUtils.isNotEmpty(calcRequest.getRiskFree()) ? calcRequest.getRiskFree() : calcRf);
		data.setIsSkipHoliday(Boolean.valueOf(calcRequest.getSkipHoliday()));
		data.setWindowType(StringUtils.isNotEmpty(calcRequest.getWindowType()) ? CalculationWindowType.fromValue(Integer.valueOf(calcRequest.getWindowType())) : CalculationWindowType.SINGLE);
		data.setWindowSize((StringUtils.isNotEmpty(calcRequest.getWindowSize())) ? Integer.parseInt(calcRequest.getWindowSize()) : null);
		data.setIsExtendedPerformance(Boolean.valueOf(calcRequest.getExtendedPerformance()));
		data.setCompoundingMethod(calcRequest.getCompoundingMethod());

		// Set calculation settings
		data.setCalcId(calculation.getDp().getNid());
		data.setIsAnnualize(Boolean.parseBoolean(calculation.getAnn() == null ? calcRequest.getAnnualized() : calculation.getAnn()));
		String annualDays = StringUtils.isNotEmpty(calcRequest.getAnnualDays()) ? calcRequest.getAnnualDays() : ANNUAL_DAYS;
		data.setDailyAnnualMultiplier(Double.valueOf(annualDays));
		data.setIsApplyIsraelsenModification(calcRequest.getIsApplyIsraelsenModification());

		return data;
	}

	public static boolean areBothNonEmptyDates(String startDate, String endDate) {
		if (StringUtils.isEmpty(startDate)) {
			LOGGER.warn("Missing start date");
			return false;
		}
		if (StringUtils.isEmpty(endDate)) {
			LOGGER.warn("Missing end date");
			return false;
		}
		return true;
	}

	// freq: [4: Yearly; 3: Quarterly; 2: Monthly; 1: Weekly; 0: Daily]
	public static String getDateByFreq(String startDate, String freq) {
		if (StringUtils.isEmpty(freq)) {
			return startDate;
		}
		Date date = null;
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		try {
			date = formatter.parse(startDate);
		} catch (ParseException e) {
			LOGGER.error("invalid start date format: {}", startDate);
			return null;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		if ("4".equals(freq)) {
			calendar.set(Calendar.DAY_OF_YEAR, 1);
		} else if ("3".equals(freq)) {
			int quarter = calendar.get(Calendar.MONTH) / 3;
			calendar.set(Calendar.DAY_OF_MONTH, 1);
			switch (quarter) {
				case 3:
					calendar.set(Calendar.MONTH, Calendar.OCTOBER);
					break;
				case 2:
					calendar.set(Calendar.MONTH, Calendar.JULY);
					break;
				case 1:
					calendar.set(Calendar.MONTH, Calendar.APRIL);
					break;
				case 0:
				default:
					calendar.set(Calendar.MONTH, Calendar.JANUARY);
					break;
			}
		} else if ("2".equals(freq)) {
			calendar.set(Calendar.DAY_OF_MONTH, 1);
		} else if ("1".equals(freq)) {
			calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
		}

		return formatter.format(calendar.getTime());
	}


	public static Map<String, String> buildDateResults(Result result, String endDateDpsNid, Integer trailingPeriodVal, String frequency) {
		Map<String, String> values = new HashMap<>();

		Map<String, String> resultValues = (Map<String, String>) result.getValues();
		String endDateString = resultValues.get(endDateDpsNid);
		if(endDateString == null){
			return values;
		}
		LocalDate endDate = LocalDate.parse(endDateString, formatter);
		values.put(CustomCalculationRequestBuilder.END_DATE_KEY, endDateString);
		if(trailingPeriodVal == -1){
			values.put(CustomCalculationRequestBuilder.START_DATE_KEY, "-1");
		}
		else if(trailingPeriodVal > 0){ //past X <freq>  - 0 = daily, 1 = weekly, 2 = monthly, 3 = quarterly, 4 = yearly
			Integer frequencyNum = (frequency == null) ? null : Integer.valueOf(frequency);
			LocalDate startDate = getStartDateBySubtractingFreqInterval(endDate, trailingPeriodVal, frequencyNum);
			values.put(CustomCalculationRequestBuilder.START_DATE_KEY, startDate.format(formatter));
		}
		else if(trailingPeriodVal == 0){ // year-to-date
			LocalDate startDate = endDate.with(TemporalAdjusters.firstDayOfYear());
			values.put(CustomCalculationRequestBuilder.START_DATE_KEY, startDate.format(formatter));
		}
		else if(trailingPeriodVal == -2){ // month-to-date
			LocalDate startDate = endDate.with(TemporalAdjusters.firstDayOfMonth());
			values.put(CustomCalculationRequestBuilder.START_DATE_KEY, startDate.format(formatter));
		}
		else if(trailingPeriodVal == -3){ // quarter-to-date
			LocalDate startDate = getDateOfQuarterBasic(endDate, true);
			values.put(CustomCalculationRequestBuilder.START_DATE_KEY, startDate.format(formatter));
		}
		return values;
	}

	private static LocalDate getStartDateBySubtractingFreqInterval(LocalDate date, Integer intervalNum, Integer freq){
		CalculationFrequency input = CalculationFrequency.fromValue(freq);
		switch (input){
			case DAILY:
				return date.minusDays(intervalNum);
			case WEEKLY:
				return date.minusWeeks(intervalNum).with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
			case MONTHLY:
				return date.minusMonths(intervalNum - 1L).with(TemporalAdjusters.firstDayOfMonth());
			case QUARTERLY:
				LocalDate prevQuarter = date.minusMonths(intervalNum * 3L);
				prevQuarter = DateUtil.getDateOfQuarterBasic(prevQuarter, false);
				return prevQuarter.plusDays(1L);
			case YEARLY:
				return date.minusYears(intervalNum).with(TemporalAdjusters.firstDayOfYear());
			default:
				throw new IllegalArgumentException("Unknown frequency: " + freq);
		}
	}


	public static CustomCalcDataPointRequest convertToCalcDpRequest(GridviewDataPoint dataPoint){
		CustomCalcDataPointRequest.CustomCalcDataPointRequestBuilder customCalcDatapointBuilder = CustomCalcDataPointRequest.builder()
				.sourceId(dataPoint.getSourceId())
				.id(StringUtils.isNotEmpty(dataPoint.getAlias()) ? dataPoint.getAlias() : dataPoint.getDataPointId())
				.calcId(dataPoint.getDataPointId())
				.currency(dataPoint.getCurrency())
				.startDate(getDateByFreq(dataPoint.getStartDate(), StringUtils.isNotEmpty(dataPoint.getFrequency()) ? String.valueOf(CalculationFrequency.fromShortNameToValue(dataPoint.getFrequency())) : null))
				.endDate(getAdjustedEndDate(dataPoint.getFrequency(), dataPoint.getEndDate()))
				.preEuroCurrency(dataPoint.getPreCurrency())
				.isRequireFullHistory(StringUtils.isEmpty(dataPoint.getRequireContinueData()) || Boolean.TRUE.toString().equalsIgnoreCase(dataPoint.getRequireContinueData()))
				.benchmarkId(dataPoint.getBenchmark())
				.riskFreeProxyId(dataPoint.getRiskFree())
				.isAnnualize(Boolean.parseBoolean(dataPoint.getRiskFree()))
				.dailyAnnualMultiplier(Double.valueOf(StringUtils.isNotEmpty(dataPoint.getAnnualDays()) ? dataPoint.getAnnualDays() : ANNUAL_DAYS))
				.isSkipHoliday(Boolean.parseBoolean(dataPoint.getSkipHoliday()))
				.windowType(StringUtils.isNotEmpty(dataPoint.getWindowType()) ? CalculationWindowType.fromValue(Integer.parseInt(dataPoint.getWindowType())) : CalculationWindowType.SINGLE)
				.windowSize(StringUtils.isNotEmpty(dataPoint.getWindowSize()) ? Integer.parseInt(dataPoint.getWindowSize()) : null)
				.stepSize((StringUtils.isNotEmpty(dataPoint.getStepSize())) ? Integer.parseInt(dataPoint.getStepSize()) : null)
				.frequency((dataPoint.getFrequency() == null) ? null : CalculationFrequency.fromShortName(dataPoint.getFrequency()))
				.daysOfAnnuyear((StringUtils.isNotEmpty(dataPoint.getAnnualDays())) ? Double.parseDouble(dataPoint.getAnnualDays()) : null)
				.isExtendedPerformance(Boolean.valueOf(dataPoint.getExtendedPerformance()))
				.compoundingMethod(StringUtils.isNotEmpty(dataPoint.getCompounding()) ? Compounding.fromValue(Integer.parseInt(dataPoint.getCompounding())).getShortName() : null)
				.isApplyIsraelsenModification(dataPoint.getIsApplyIsraelsenModification())
				.rollingTimePeriod(StringUtils.isNotEmpty(dataPoint.getRollingTimePeriod()) ? Integer.parseInt(dataPoint.getRollingTimePeriod()) : null)
				.valueAtRiskSetting(getValueAtRiskSetting(dataPoint.getValueAtRisk()))
				.excludeFromSource(getExcludeFromSource(dataPoint.getCalcBestReturnNum(), dataPoint.getCalcWorstReturnNum()));
		//these settings are removed in new interface STAR-2184
		//.taxAdjust()

		return customCalcDatapointBuilder.build();
	}

	private static ValueAtRiskSetting getValueAtRiskSetting(ValueAtRisk valueAtRisk) {
		if(valueAtRisk == null) {
			return null;
		}

		return ValueAtRiskSetting.builder().confidenceValue(valueAtRisk.getConfidenceValue() != null ? 1.0 - valueAtRisk.getConfidenceValue() : 0.0)
				.type(valueAtRisk.getType()).fitType(valueAtRisk.getFitType()).scaleType(valueAtRisk.getScaleType() != null ? valueAtRisk.getScaleType().getValue() : 0)
				.timeHorizon(valueAtRisk.getTimeHorizon()).initialAmount(valueAtRisk.getInitialAmount()).build();
	}

	private static CustomCalcExcludeFromSource getExcludeFromSource(String calcBestReturnNum, String calcWorstReturnNum) {
		if(StringUtils.isEmpty(calcBestReturnNum) || StringUtils.isEmpty(calcWorstReturnNum)) {
			return null;
		}
		return CustomCalcExcludeFromSource.builder().bestNum(Integer.valueOf(calcBestReturnNum)).worstNum(Integer.valueOf(calcWorstReturnNum)).build();
	}

	private static String getAdjustedEndDate(String frequency, String endDate) {
		if(StringUtils.isEmpty(endDate)) {
			return endDate;
		}

		if(CalculationFrequency.WEEKLY.shortName().equals(frequency)) {
			return DateFormatUtil.format(DateFormatUtil.parseLocalDate(endDate).with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)));
		}

		return DateFormatUtil.format(FrequencyDateFormatUtil.getFrequencyEndDate(DateFormatUtil.parseLocalDate(endDate), frequency));
	}
}
