package com.morningstar.martgateway.domains.calclib.entity;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Builder
@Data
public class CalcMetaData {

    private Map<String, DataPoint> baseCurrencyMap;
    private Map<String, DataPoint> endDateDpMap;
    private Map<String, String> secIdCurrencyMap;
    private Map<String, List<CurrentResult>> currencyChangeMap;
    private Map<String, String> inceptionMap;

}
