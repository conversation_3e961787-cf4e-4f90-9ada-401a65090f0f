package com.morningstar.martgateway.domains.eod.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.morningstar.dataac.martgateway.core.common.util.MDCInheritedThreadPoolExecutor;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import javax.sql.DataSource;
import java.util.concurrent.ExecutorService;

/**
 * This configuration is responsible for defining EOD datasource, sql session and connection.
 */
@Configuration
@MapperScan(basePackages = "com.morningstar.martgateway.domains.eod.repository",
        sqlSessionFactoryRef = "eodSqlSessionFactory")
public class EODDataSourceConfiguration {

    @ConfigurationProperties(prefix = "martgateway.eod")
    @Bean(name = "eodDataSource")
    public DataSource eodDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = "eodSqlSessionFactory")
    public SqlSessionFactory eodSqlSessionFactory(@Qualifier("eodDataSource") DataSource eodDataSource)
            throws Exception {
        final SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(eodDataSource);
        return sqlSessionFactoryBean.getObject();
    }
    
    @Bean(name = "eodScheduler")
    public Scheduler eodScheduler(@Value("${martgateway.eod.maximumPoolSize}") int maximumPoolSize) {
        int minPoolSize = Math.min(20, maximumPoolSize);
        ExecutorService executorService= new MDCInheritedThreadPoolExecutor(minPoolSize, maximumPoolSize,
                "EOD-ThreadPool-");
        return Schedulers.fromExecutor(executorService);
    }

    @Bean
    @ConditionalOnMissingBean(MongoClient.class)
    public MongoClient eodMongoClient(@Value("${mongodb.uri}") String uri) {
        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(uri))
                .build();

        return MongoClients.create(settings);
    }

    @Bean(name = "eodReferenceScheduler")
    public Scheduler eodReferenceScheduler(@Value("${mongodb.maximumPoolSize}") int maximumPoolSize) {
        int minPoolSize = Math.min(20, maximumPoolSize);
        ExecutorService executorService= new MDCInheritedThreadPoolExecutor(minPoolSize, maximumPoolSize,
                "EODReference-ThreadPool-");
        return Schedulers.fromExecutor(executorService);
    }
}
