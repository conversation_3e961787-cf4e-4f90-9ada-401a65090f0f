package com.morningstar.martgateway.domains.apiproxy.entity;

import com.morningstar.martgateway.domains.apiproxy.entity.enums.WindowTypeEnum;
import com.morningstar.martgateway.domains.apiproxy.exceptions.DPSException;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import com.morningstar.martgateway.util.apiproxy.PairToEntityUtil;
import com.morningstar.martgateway.util.apiproxy.timeperiod.CalculationWindowType;
import com.morningstar.martgateway.util.apiproxy.timeperiod.TimeSerialsCalculator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;

import java.util.*;

@Slf4j
public class DataPointTypeCustomCalc implements IDataPointType {

    @Override
    public DataPointForColumnsResponse generateTimePeriodsByDPType(String dataPointType, DataPointForColumns dataPoint) {
        DataPointForColumnsResponse response = DataPointForColumnsResponse.builder()
                .datapointId(dataPoint.getDatapointId())
                .alias(dataPoint.getAlias())
                .build();
        // special set
        if (dataPoint.isCustomCalc() && dataPoint.getWindowType().equalsIgnoreCase("-1")) {
            dataPoint.setWindowType("1");
        }

        if (TimeSerialsCalculator.checkIllegalParams(dataPoint.getWindowType()+"", dataPoint.getWindowSize(), dataPoint.getStepSize())) {
            throw new DPSException(HttpStatus.INTERNAL_SERVER_ERROR, String.format("windowType =%s windowSize =%s or stepSize =%s is illegal", dataPoint.getWindowType(), dataPoint.getWindowSize(), dataPoint.getStepSize()));
        }
		if (!StringUtils.equals(WindowTypeEnum.SINGLEDATAPOINT.getId(), dataPoint.getWindowType())
				&& !TimeSerialsCalculator.checkIllegalDates(DateUtil.asParse(dataPoint.getStartDate()),
						DateUtil.asParse(dataPoint.getEndDate()), dataPoint.getStepSize(), dataPoint.getFrequency())) {
            throw new DPSException(HttpStatus.INTERNAL_SERVER_ERROR, "Moving step value is greater than the available date range. " + String.format("frequency =%s startDate =%s or endDate =%s or stepSize =%s is illegal", dataPoint.getFrequency(), dataPoint.getStartDate(), dataPoint.getEndDate(), dataPoint.getStepSize()));
        }

        return getDataPointForColumnsResponse(dataPoint, response);
    }

    private DataPointForColumnsResponse getDataPointForColumnsResponse(DataPointForColumns dataPoint, DataPointForColumnsResponse response) {
        Pair<String, String> resDates = TimeSerialsCalculator.initialStartDateAndEndDate(dataPoint);

        Date standardStartDate = Optional.ofNullable(resDates.getLeft()).map(DateUtil::tryParse)
                .map(it -> FrequencyType.getActualFirstDateByFrequency(it, dataPoint.getFrequency(), true)).orElse(null);
        Date standardEndDate = Optional.ofNullable(resDates.getRight()).map(DateUtil::tryParse)
                .map(it -> FrequencyType.getActualFirstDateByFrequency(it, dataPoint.getFrequency(), false)).orElse(null);
        if (standardStartDate == null || standardEndDate == null || dataPoint.getFrequency() == null) {
            response.setColumns(0);
            response.setDatePairs(new ArrayList<>());
            return response;
        }

        if (standardStartDate.compareTo(standardEndDate) <= 0) {
            List<Pair<String, String>> datePairs = CalculationWindowType.generatePeriods(standardStartDate, standardEndDate, dataPoint.getFrequency(), dataPoint.getStepSize(), dataPoint.getWindowSize(),
                    dataPoint.getWindowType() + "");
            response.setColumns(datePairs.size());
            response.setDatePairs(PairToEntityUtil.pair2StartEndDate(datePairs));
        } else {
            response.setColumns(1);
            response.setDatePairs(PairToEntityUtil.pair2StartEndDate(Collections.singletonList(Pair.of(dataPoint.getStartDate(), dataPoint.getEndDate()))));
        }
        return response;
    }

}
