package com.morningstar.martgateway.domains.rdb.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.domains.rdb.cache.Transformer;
import com.morningstar.martgateway.domains.rdb.cache.entity.Filters;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsDataList;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.morningstar.martgateway.util.FrequencyDateFormatUtil;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

@UtilityClass
@SuppressWarnings("java:S1118")
public class Transformers {

    public static final Transformer<String, Collection<TsDataList>, TimeSeriesResult> transformToTimeSeriesResult = (sdt, edt, dataList, key, dpsToFreqMap) -> {

        Map<String, Map<String, String>> primaries = dataList.stream()
                .filter(d -> StringUtils.isNotEmpty(d.getPrimaries()))
                .collect(Collectors.toMap(TsDataList::getDpId, TsDataList::getPrimariesAsMap, (firstDpInstance, secondDpInstance) -> firstDpInstance));

        Map<String, List<V>> values = dataList.stream()
                .filter(tsDataList -> MapUtils.isNotEmpty(dpsToFreqMap) && dpsToFreqMap.containsKey(tsDataList.getDpId()))
                .collect(Collectors.toMap(
                        TsDataList::getDpId,
                        tsDataList -> {
                            LocalDate startDate = FrequencyDateFormatUtil.getFrequencyStartDate(sdt, dpsToFreqMap.get(tsDataList.getDpId()));
                            LocalDate endDate = FrequencyDateFormatUtil.getFrequencyEndDate(edt, dpsToFreqMap.get(tsDataList.getDpId()));

                            return tsDataList.getValues().stream()
                                    .filter(Filters.dateRange(startDate, endDate))
                                    .map(p -> new V(LocalDate.ofEpochDay(p.getDateEpoc()).toString(), p.getValue()))
                                    .collect(Collectors.toList());
                        },
                        (existing, replacement) -> { existing.addAll(replacement); return existing; }
                ));

        values.values().removeIf(List::isEmpty);

        return primaries.isEmpty() ? new TimeSeriesResult(key, values): new TimeSeriesResult(key, values, primaries);
    };
}
