package com.morningstar.martgateway.domains.current;

import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.martgateway.domains.core.entity.MasterHeader;
import com.morningstar.martgateway.infrastructures.repo.data.S3DataRepo;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.martgateway.util.CurrentUtil;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import org.json.JSONObject;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public class CategoryIdLevelDataService {

    private S3DataRepo s3DataRepo;
    private RedisTemplate<String, String> syncDataStorageTemplate;
    private RedisTemplate<String, String> syncDataCacheTemplate;
    private static final String CATEGORY_ID = "CategoryId";
    private static final String AVAILABLE_ID_SET_KEY_PREFIX = "available_id_";

    public CategoryIdLevelDataService(S3DataRepo s3DataRepo,
            RedisTemplate<String, String> syncDataStorageTemplate,
            RedisTemplate<String, String> syncDataCacheTemplate) {
        this.s3DataRepo = s3DataRepo;
        this.syncDataStorageTemplate = syncDataStorageTemplate;
        this.syncDataCacheTemplate = syncDataCacheTemplate;
    }

    protected Flux<MasterHeader> getFullDataByCategoryId(String groupName,
            String peerGroupId,
            String regionId,
            String categoryCode,
            boolean readCache) {
        List<String> keys = StringUtils.isEmpty(categoryCode) ? getKeysByRegionAndPeerGroup(regionId + "_" + peerGroupId, groupName) : Arrays.asList(categoryCode + "_" + regionId + "_" + peerGroupId);
        return Flux.fromIterable(keys)
                .publishOn(SchedulerConfiguration.getScheduler())
                .map(key -> "DP_" + CATEGORY_ID + "/" + groupName + "/" + key)
                .flatMap(s3Key -> retrieveCategoryDataContent(s3Key, readCache));
    }

    private List<String> getKeysByRegionAndPeerGroup(String id, String groupName) {
        List<String> result = new ArrayList<>();
        Set<String> availableKeys = syncDataStorageTemplate.opsForSet().members(AVAILABLE_ID_SET_KEY_PREFIX + groupName + "_" + id);
        if (!CollectionUtils.isEmpty(availableKeys)) {
            result.addAll(availableKeys);
        }
        return result;
    }

    private Mono<? extends MasterHeader> retrieveCategoryDataContent(String keyName, boolean readCache) {
        String dataKeyPrefix = keyName.substring(3, keyName.lastIndexOf('/'));
        String categoryId = keyName.substring(keyName.lastIndexOf('/') + 1);
        if (DataPointRepository.getDataSource(dataKeyPrefix).equals("s3")) {
            if (!readCache || syncDataCacheTemplate.opsForValue().get(keyName) == null) {
                return retrieveS3Object(keyName, categoryId);
            } else {
                JSONObject object = JsonUtils.retrieveStoredContent(syncDataCacheTemplate.opsForValue().get(keyName));
                return Mono.just(new MasterHeader(categoryId, object));
            }
        } else {
            String result = syncDataStorageTemplate.opsForValue().get(keyName);
            JSONObject object = !StringUtils.isEmpty(result) ? JsonUtils.retrieveStoredContent(result) : new JSONObject();
            return Mono.just(new MasterHeader(categoryId, object));
        }
    }

    private Mono<? extends MasterHeader> retrieveS3Object(String s3Key, String categoryId) {
        return s3DataRepo.call(s3Key.substring(3))
                .doOnNext(value -> syncDataCacheTemplate.opsForValue().set(s3Key, value,  CurrentUtil.getRandomExpireTime()))
                .map(value -> new MasterHeader(categoryId, JsonUtils.retrieveStoredContent(value)));
    }
}
