package com.morningstar.martgateway.domains.eod.service;

import com.morningstar.martgateway.domains.eod.datapoint.EODDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import reactor.core.publisher.Flux;

import java.util.List;

public interface EODDataService {

    Flux<Result> getData(MartRequest martRequest, List<EODDataPoint> dataPoints);
}
