package com.morningstar.martgateway.domains.apiproxy.entity;

import com.morningstar.martgateway.domains.apiproxy.entity.enums.CalculationId;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.CalculationWindow2;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.DcsFrequency;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.StringConst;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import com.morningstar.martgateway.util.apiproxy.StringUtil;
import com.morningstar.martgateway.util.apiproxy.timeperiod.TimeSerialsCalculator;
import com.morningstar.martgateway.util.apiproxy.XmlUtil;
import java.util.ArrayList;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SecuritiesRequestEntity {
    public static final String START = "start";

    protected String userId;
    protected String jobId;
    protected String productId;
    protected List<Datapoint> datapoints;
    protected List<DoApiInvestment> doApiInvestments = new ArrayList<>();
    private int directDpsBatchSize;
    private int directIdBatchSize;

    public List<DataPointForColumnsResponse> calcDatapointColumns() {
        return TimeSerialsCalculator.calcTotalColumns(DataPointForColumns.from(checkWindowType()));
    }

    public SecuritiesRequestEntity copyWithSubInvestmentListAndDatapoints(List<DoApiInvestment> subInvestments, List<Datapoint> datapoints) {
        SecuritiesRequestEntity copy = this.toBuilder().doApiInvestments(subInvestments).datapoints(datapoints).build();
        return copy;
    }

    private List<Datapoint> checkWindowType() {
        return datapoints.stream().map(dp -> {
            if (dp.getWindowType() == null && dp.isDynamicDataPoint()) {
                if (dp.getId().equals(StringConst.RETURN_CALENDAR_WEEKLY)) {
                    dp.setWindowType(CalculationWindow2.ROLLINGWINDOW.getCode());
                    dp.setWindowSize(7);
                    dp.setStepSize(7);
                } else {
                    dp.setWindowType(CalculationWindow2.SINGLEDATAPOINT.getCode());
                }
            }
            return dp;
        }).collect(Collectors.toList());
    }

    public String getUserIdOrDefault() {
        return StringUtils.defaultIfBlank(userId, "");
    }

    public String generateRequestXml(String rootXml, boolean isForPermission) {
        Document doc = XmlUtil.loadXML(rootXml);
        Element root = doc.getRootElement();
        generateFldsNode(root, isForPermission);
        generateDatNode(root);
        return doc.asXML();
    }

    public String generateRequestXml(String startXml, String endXml, boolean isForPermission) {
        StringBuilder root = new StringBuilder();
        generateFldsNodeString(root, isForPermission);
        generateDatNodeStr(root);
        return startXml + root.toString() + endXml;
    }

    public String generateRequestXml(String startXml, String endXml, String datString, boolean isForPermission) {
        StringBuilder root = new StringBuilder();
        generateFldsNodeString(root, isForPermission);
        generateDatNodeStr(root, datString);
        return startXml + root.toString() + endXml;
    }

    public String generateDCSRequestXml() {
        Element doc = getDCSXML();
        getDatXML(doc);
        getFldsXML(doc);
        return doc.getDocument().getRootElement().asXML();
    }

    private Element generateDatNode(Element root) {
        Element datElement = root.addElement("dat");
        for (DoApiInvestment s : doApiInvestments) {
            Element r = datElement.addElement("r");
            r.addAttribute("i", s.getId());
        }
        return datElement;
    }

    private StringBuilder generateDatNodeStr(StringBuilder xml) {
        xml.append(" <dat>");
        for (DoApiInvestment s : doApiInvestments) {
            xml.append(" <r");
            addAttributeIf(xml, "i", s.getId(), true);
            xml.append(" />");
        }
        xml.append(" </dat>");
        return xml;
    }

    private StringBuilder generateDatNodeStr(StringBuilder xml, String datString) {
        xml.append(" " + datString);
        for (DoApiInvestment s : doApiInvestments) {
            xml.append(" <r");
            addAttributeIf(xml, "i", s.getId(), true);
            xml.append(" />");
        }
        xml.append(" </dat>");
        return xml;
    }

    private Element generateFldsNode(Element root, boolean isForPermission) {
        Element fldsElement = root.addElement("flds");
        for (Datapoint f : datapoints) {
            Element fe;
            if (isForPermission) {
                if (f.isRegularDataPoint()) {
                    fe = fldsElement.addElement("f");
                    fe.addAttribute("i", f.getId());
                }
                continue;
            }
            fe = fldsElement.addElement("f");
            addAttributeIf(fe, "i", f.getAlias(), !StringUtil.isNullOrEmpty(f.getAlias()));
            String sid = getSid(f);

            addAttributeIf(fe, "sid", getSid(f), StringUtils.isNotBlank(sid));
            addAttributeIf(fe, "i", f.getAlias(), StringUtil.isNullOrEmpty(f.getAlias()));
            addAttributeIf(fe, "end", f.getEndDate(), !StringUtil.isNullOrEmpty(f.getEndDate()));
            addAttributeIf(fe, "freq", f.getFrequency(), !StringUtil.isNullOrEmpty(f.getFrequency()));
            setParams(f, fe);
            setSpecialCasesParams(f, fe);
        }
        return fldsElement;
    }

    private StringBuilder generateFldsNodeString(StringBuilder xml, boolean isForPermission) {
        xml.append(" <flds>");
        for (Datapoint f : datapoints) {
            xml.append(" <f");
            if (isForPermission) {
                if (f.isRegularDataPoint()) {
                    addAttributeIf(xml, "i", f.getId(), true);
                }
                xml.append("/>");
                continue;
            }


            addAttributeIf(xml, "i", f.getAlias(), !StringUtil.isNullOrEmpty(f.getAlias()));
            String sid = getSid(f);

            addAttributeIf(xml, "sid", getSid(f), StringUtils.isNotBlank(sid));
            addAttributeIf(xml, "i", f.getAlias(), StringUtil.isNullOrEmpty(f.getAlias()));
            addAttributeIf(xml, "end", f.getEndDate(), !StringUtil.isNullOrEmpty(f.getEndDate()));
            addAttributeIf(xml, "freq", f.getFrequency(), !StringUtil.isNullOrEmpty(f.getFrequency()));
            setParams(f, xml);
            setSpecialCasesParams(f, xml);
            xml.append("/>");
        }
        xml.append(" </flds>");
        return xml;
    }

    private void setParams(Datapoint f, Element fe) {
        if (CalculationId.AVERAGEROLLINGPERIODRETURN.equals(f.getId())) {
            addAttributeIf(fe, START, getAverageRollingPeriodReturnStartDate(f),
                    !StringUtil.isNullOrEmpty(f.getStartDate()) && !StringUtil.isNullOrEmpty(f.getRollingTimePeriod()));
        } else {
            addAttributeIf(fe, START, f.getStartDate(), !StringUtil.isNullOrEmpty(f.getStartDate()));
        }

        addAttributeIf(fe, "aggregationtype", f.getAggregationType(), !StringUtil.isNullOrEmpty(f.getAggregationType()));
        addAttributeIf(fe, "aggregationweight", f.getAggregationWeight(), !StringUtil.isNullOrEmpty(f.getAggregationWeight()));
        addAttributeIf(fe, "bmid", f.getBenchmark(), !StringUtil.isNullOrEmpty(f.getBenchmark()));
        addAttributeIf(fe, "rfid", f.getRiskfree(), !StringUtil.isNullOrEmpty(f.getRiskfree()));
        addAttributeIf(fe, "cur", f.getCurrency(), !StringUtil.isNullOrEmpty(f.getCurrency()));

        if(f.getWindowType() != null){
            fe.addAttribute("wintype", f.getWindowType().toString());
        }
        if(f.getWindowSize() != null){
            fe.addAttribute("winsz", f.getWindowSize().toString());
        }
        if(f.getStepSize() != null){
            fe.addAttribute("mvstep", f.getStepSize().toString());
        }
        addAttributeIf(fe, "annu", Boolean.TRUE.equals(f.getAnnualized()) ? "1" : "0", f.getAnnualized() != null);
        addAttributeIf(fe, "daysofannuyear", Optional.ofNullable(f.getAnnualDays()).map(Object::toString).orElse(null), f.getAnnualDays() != null);
        addAttributeIf(fe, "compoundingmethod", f.getCompounding(), !StringUtil.isNullOrEmpty(f.getCompounding()));
    }

    private void setParams(Datapoint f, StringBuilder fe) {
        if (CalculationId.AVERAGEROLLINGPERIODRETURN.equals(f.getId())) {
            addAttributeIf(fe, START, getAverageRollingPeriodReturnStartDate(f),
                    !StringUtil.isNullOrEmpty(f.getStartDate()) && !StringUtil.isNullOrEmpty(f.getRollingTimePeriod()));
        } else {
            addAttributeIf(fe, START, f.getStartDate(), !StringUtil.isNullOrEmpty(f.getStartDate()));
        }

        addAttributeIf(fe, "aggregationtype", f.getAggregationType(), !StringUtil.isNullOrEmpty(f.getAggregationType()));
        addAttributeIf(fe, "aggregationweight", f.getAggregationWeight(), !StringUtil.isNullOrEmpty(f.getAggregationWeight()));
        addAttributeIf(fe, "bmid", f.getBenchmark(), !StringUtil.isNullOrEmpty(f.getBenchmark()));
        addAttributeIf(fe, "rfid", f.getRiskfree(), !StringUtil.isNullOrEmpty(f.getRiskfree()));
        addAttributeIf(fe, "cur", f.getCurrency(), !StringUtil.isNullOrEmpty(f.getCurrency()));

        if(f.getWindowType() != null){
            addAttributeIf(fe, "wintype", f.getWindowType().toString(), true);
        }
        if(f.getWindowSize() != null){
            addAttributeIf(fe, "winsz", f.getWindowSize().toString(), true);
        }
        if(f.getStepSize() != null){
            addAttributeIf(fe, "mvstep", f.getStepSize().toString(), true);
        }

        addAttributeIf(fe, "annu", Boolean.TRUE.equals(f.getAnnualized()) ? "1" : "0", f.getAnnualized() != null);
        addAttributeIf(fe, "daysofannuyear", Optional.ofNullable(f.getAnnualDays()).map(Object::toString).orElse(null), f.getAnnualDays() != null);
        addAttributeIf(fe, "compoundingmethod", f.getCompounding(), !StringUtil.isNullOrEmpty(f.getCompounding()));
    }


    private String getAverageRollingPeriodReturnStartDate(Datapoint f) {
        // only for data point id 1114
        Date startDate = DateUtil.tryParse(f.getStartDate());
        Date newStartDate= DateUtil.addMonths(startDate, Integer.parseInt(f.getRollingTimePeriod()) * -1 + 1);
        return DateUtil.formatToGeneralDate(newStartDate);
    }

    private void setSpecialCasesParams(Datapoint f, Element fe) {
        addAttributeIf(fe, "BestReturnNum", f.getCalcBestReturnNum(), !StringUtil.isNullOrEmpty(f.getCalcBestReturnNum()));
        addAttributeIf(fe, "WorstReturnNum", f.getCalcWorstReturnNum(),
                !StringUtil.isNullOrEmpty(f.getCalcWorstReturnNum()));
        addAttributeIf(fe, "uncomp", Boolean.TRUE.equals(f.getRequireContinueData()) ? "0" : "1", f.getRequireContinueData() != null);
        addAttributeIf(fe, "preeurocastcur", f.getPreeurocur(), !StringUtil.isNullOrEmpty(f.getPreeurocur()));
        addAttributeIf(fe, "calcid", f.getId(), !StringUtil.isNullOrEmpty(f.getId()) && StringUtil.isInteger(f.getId()));
        addAttributeIf(fe, "roltimeperiod", f.getRollingTimePeriod(), !StringUtil.isNullOrEmpty(f.getRollingTimePeriod()));
        addAttributeIf(fe, "addtionalsrc", f.getCalcAdditionalSource(), f.getCalcAdditionalSource() != null);
        addAttributeIf(fe, "sid2", f.getCalcS2(), f.getCalcS2() != null);
        addAttributeIf(fe, "mnav", f.getCalcMnav(), f.getCalcMnav() != null);
        addAttributeIf(fe, "IsApplyIsraelsenModification", "1", f.getIsApplyIsraelsenModification() != null && f.getIsApplyIsraelsenModification());
    }

    private void setSpecialCasesParams(Datapoint f, StringBuilder fe) {
        addAttributeIf(fe, "BestReturnNum", f.getCalcBestReturnNum(), !StringUtil.isNullOrEmpty(f.getCalcBestReturnNum()));
        addAttributeIf(fe, "WorstReturnNum", f.getCalcWorstReturnNum(),
                !StringUtil.isNullOrEmpty(f.getCalcWorstReturnNum()));
        addAttributeIf(fe, "uncomp", Boolean.TRUE.equals(f.getRequireContinueData()) ? "0" : "1", f.getRequireContinueData() != null);
        addAttributeIf(fe, "preeurocastcur", f.getPreeurocur(), !StringUtil.isNullOrEmpty(f.getPreeurocur()));
        addAttributeIf(fe, "calcid", f.getId(), !StringUtil.isNullOrEmpty(f.getId()) && StringUtil.isInteger(f.getId()));
        addAttributeIf(fe, "roltimeperiod", f.getRollingTimePeriod(), !StringUtil.isNullOrEmpty(f.getRollingTimePeriod()));
        addAttributeIf(fe, "addtionalsrc", f.getCalcAdditionalSource(), f.getCalcAdditionalSource() != null);
        addAttributeIf(fe, "sid2", f.getCalcS2(), f.getCalcS2() != null);
        addAttributeIf(fe, "mnav", f.getCalcMnav(), f.getCalcMnav() != null);
        addAttributeIf(fe, "IsApplyIsraelsenModification", "1", f.getIsApplyIsraelsenModification() != null && f.getIsApplyIsraelsenModification());
    }

    private void addAttributeIf(Element fe, String attName, String attValue, boolean addAttr) {
        if (addAttr && !StringUtil.isNullOrEmpty(attValue)) {
            fe.addAttribute(attName, attValue);
        }
    }

    private void addAttributeIf(StringBuilder xml, String attName, String attValue, boolean addAttr) {
        if (addAttr && !StringUtil.isNullOrEmpty(attValue)) {
            xml.append(" ").append(attName).append("=\"").append(attValue).append("\"");
        }
    }

    private String getSid(Datapoint dp) {
        String sid = null;
        if (StringUtils.isNotBlank(dp.getCalcS1())) {
            sid = dp.getCalcS1();
        } else if (StringUtils.isNotBlank(dp.getSourceId())) {
            sid = dp.getSourceId();
        } else {
            sid = dp.getId();
        }
        return sid;
    }

    private Element getDCSXML() {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement("root");
        Element calc = root.addElement("customcalculation");
        return calc.addElement("customcalculationrequest");
    }

    private Element getDatXML(Element reqnode) {
        Element ls = reqnode.addElement("security-list");
        if (CollectionUtils.isNotEmpty(doApiInvestments)) {
            for (DoApiInvestment e : doApiInvestments) {
                Node node = ls.addElement("secId");
                XmlUtil.setAttribute(node, "v", e.getId());
            }
        }
        return reqnode;
    }

    private Element getFldsXML(Element doc) {
        if (CollectionUtils.isNotEmpty(datapoints)) {
            for (Datapoint e : datapoints) {
                addDataPointNodeToDoc(doc, e);
            }
        }
        return doc;
    }

    /**
     * create xml node for datapoint and return such format:
     * <datapoint id="3" freq="2" startDate="1" endDate="PM001" dateType="1"
     * bmkSecId="" rfSecId="" srcId="HP010" skip_holiday="false" bAnnualize=
     * "false" require_full_history="false" bEpSetting="true" cur="" preeurocur=
     * "" dDailyAnnualMultiplier="363" calcId="42" nWindowType="1" nWindowSize=
     * "" nStepSize=""> </datapoint>
     *
     * wiki
     * https://mswiki.morningstar.com/pages/viewpage.action?spaceKey=PFS&title=Custom+Calculation+Service
     * freq : the frequency for the source data. 0 daily 1 weekly 2 monthly 3
     * quarterly 4 annually
     *
     * @param doc
     * @param dp
     */
    private static void addDataPointNodeToDoc(Element doc, Datapoint dp) {
        Element dpNode = doc.addElement("datapoint");
        setAttrIfValueNotNull(dpNode, "id", dp.getAlias());

        setAttrIfValueNotNull(dpNode, "freq", Optional.ofNullable(DcsFrequency.parseByName(dp.getFrequency()))
                .map(DcsFrequency::getCode).orElse(null));

        setAttrIfValueNotNull(dpNode, "startDate", dp.getStartDate());

        setAttrIfValueNotNull(dpNode, "endDate", dp.getEndDate());
        setAttrIfValueNotNull(dpNode, "dateType", dp.getDateType());
        setAttrIfValueNotNull(dpNode, "bmkSecId", dp.getBenchmark());

        setAttrIfValueNotNull(dpNode, "rfSecId", dp.getRiskfree());
        setAttrIfValueNotNull(dpNode, "srcId", dp.getSourceId());
        setAttrIfValueNotNull(dpNode, "skip_holiday", dp.getSkipHoliday());

        setAttrIfValueNotNull(dpNode, "bAnnualize", dp.getAnnualized());
        setAttrIfValueNotNull(dpNode, "require_full_history", dp.getRequireContinueData());

        setAttrIfValueNotNull(dpNode, "bEpSetting", dp.getEpSetting());
        String cur = dp.getCurrency();
        if ("BASE".equalsIgnoreCase(cur)) {
            cur = "";
        }
        setAttrIfValueNotNull(dpNode, "cur", cur);
        setAttrIfValueNotNull(dpNode, "preeurocur", dp.getPreeurocur());

        setAttrIfValueNotNull(dpNode, "dDailyAnnualMultiplier", dp.getAnnualDays());
        setAttrIfValueNotNull(dpNode, "calcId", dp.getCalcId());

        setAttrIfValueNotNull(dpNode, "nWindowType", dp.getWindowType());
        setAttrIfValueNotNull(dpNode, "nWindowSize", dp.getWindowSize());
        setAttrIfValueNotNull(dpNode, "nStepSize", dp.getStepSize());
        addVarsettingToDp(dpNode, dp);
    }

    private static void addVarsettingToDp(Element dpNode, Datapoint dp) {
        DataPointVarSetting setting = dp.getVarSetting();
        if (setting != null) {
            Element varsettingNode = dpNode.addElement("varsetting");
            setAttrIfValueNotNull(varsettingNode, "dsType", setting.getDsType().ordinal());
            setAttrIfValueNotNull(varsettingNode, "scaletype", setting.getScaleType().ordinal());
            setAttrIfValueNotNull(varsettingNode, "cfValue", setting.getCsValue());
            setAttrIfValueNotNull(varsettingNode, "initialAmmout", setting.getInitialAmount());
            setAttrIfValueNotNull(varsettingNode, "timeHorizon", setting.getTimeHorizon());
            setAttrIfValueNotNull(varsettingNode, "fitType", setting.getFitType());
        }

    }

    private static void setAttrIfValueNotNull(Element node, String name, Object value) {
        if (value != null) {
            XmlUtil.setAttribute(node, name, value.toString());
        }
    }

}