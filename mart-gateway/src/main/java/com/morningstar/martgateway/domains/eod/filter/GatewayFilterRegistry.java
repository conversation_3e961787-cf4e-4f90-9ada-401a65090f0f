package com.morningstar.martgateway.domains.eod.filter;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martgateway.infrastructures.repo.datapoint.filter.AbstractFluxResultFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * data point filter registry maintain maps of datapoint filters.
 * when a new datapoint filter is added to the registry, the filter
 * is initiated and referenced. There are following different filters:
 * 1) datapoint result filter. Each datapoint could have multiple filters
 * 2) datapoint request filter. One group of data points share the same filter
 */
@Slf4j
public class GatewayFilterRegistry {

    // result filter apply to single datapoint, multiple filter supported
    private final Map<String, List<AbstractFluxResultFilter>> resultFiltersMap;

    private final ApplicationContext applicationContext;

    public GatewayFilterRegistry(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        this.resultFiltersMap = new HashMap<>();
    }

    public void register(DataPoint dataPoint) {
        registerResultFilter(dataPoint);
    }

    private void registerResultFilter(DataPoint dataPoint) {
        List<String> filters = dataPoint.getFilter();
        if (filters == null || filters.isEmpty()) {
            return;
        }

        List<AbstractFluxResultFilter> filterList = new ArrayList<>();
        for (String filterName : filters) {
            try {
                // a filter bean with the filter name much be available in context
                var filterBean = applicationContext.getBean(filterName, AbstractFluxResultFilter.class);
                filterBean.setDataPoint(dataPoint);
                filterList.add(filterBean);
                log.debug("registered result filter {}", filterName);
            } catch (Exception e) {
                log.warn("a result filter bean {} is not able to be registered", filterName);
            }
        }
        resultFiltersMap.put(dataPoint.getNid(), filterList);
    }

    public boolean hasResultFilter(String datapointId) {
        return resultFiltersMap.containsKey(datapointId);
    }

    public List<AbstractFluxResultFilter> getResultFilters(String datapointId) {
        return resultFiltersMap.get(datapointId);
    }
}
