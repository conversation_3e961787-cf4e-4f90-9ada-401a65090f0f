package com.morningstar.martgateway.domains.calc;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesRequestEntity;
import com.morningstar.martgateway.domains.calc.model.CalcResponse;
import com.morningstar.martgateway.domains.calc.model.PostBodyBuilder;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.calculationlib.util.CalculationUtil;
import com.morningstar.martgateway.util.CurrentUtil;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.reactivestreams.Publisher;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.Logger;
import reactor.util.Loggers;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;

@Component
@Slf4j
public class CalculationService {

    private static final Logger LOGGER = Loggers.getLogger(CalculationService.class);

    private CurrentDataRetrievalService currentDataRetrievalService;
    private CalcAPICaller calcAPICaller;


    public CalculationService(CurrentDataRetrievalService currentDataRetrievalService,
                                CalcAPICaller calcAPICaller) {
        this.currentDataRetrievalService = currentDataRetrievalService;
        this.calcAPICaller = calcAPICaller;
    }

    /**
     * @param entity
     * @return
     */
    public Flux<Result> retrieveCalc(CalcRequest entity) {
        List<String> allDps = entity.getCalcDps();
        List<String> idList = entity.getIdList();
        if (allDps.isEmpty()) {
            return Flux.empty();
        }
        LogEntry.info(new LogEntity(EVENT_TYPE, "Calculation"), new LogEntity(EVENT_DESCRIPTION, "Calling calc old Api"));
        List<DataPoint> allDataPoints = allDps.stream()
                .map(DataPointRepository::getByNid)
                .collect(Collectors.toList());

        if(calcAPICaller.readTimeout > 20) {
            //feed builder
            return getResultByIdList(entity, allDataPoints, idList, entity.getIdMappers());
        } else {
            //mart API
            return getResultConcurrently(entity, allDataPoints, idList, entity.getIdMappers());
        }
    }

    /**
     *  call DCS with all ids in single request
     *
     * @param entity
     * @param allDps
     * @param idList
     * @return
     */
    private Flux<Result> getResultByIdList(CalcRequest entity, List<DataPoint> allDps, List<String> idList, List<IdMapper> idMappers) {
        Mono<String> bodyMono = Flux.fromIterable(allDps)
                .flatMap(dataPoint -> buildQueryWithIdList(entity, idList, dataPoint, idMappers))
                .subscribeOn(Schedulers.parallel())
                .reduce(new StringBuilder(), (accu, next) -> accu.append(next))
                .map(StringBuilder::toString)
                .flatMap(dataPoint -> PostBodyBuilder.buildBody(entity.getIdList(), dataPoint));

        List<Datapoint> listOfDps = entity.getCalcDps().stream().map(dpId -> Datapoint.builder().id(dpId).build()).toList();
        SecuritiesRequestEntity securitiesRequestEntity = SecuritiesRequestEntity.builder()
                .jobId(entity.getRequestId())
                .userId(entity.getUserId())
                .datapoints(listOfDps)
                .build();
        Mono<Document> docMono = calcAPICaller.post(bodyMono, securitiesRequestEntity)
                .onErrorReturn(RuntimeException.class, "")
                .filter(str -> !StringUtils.isEmpty(str))
                .map(str -> CalculationService.parseDocument.apply(str));
        return CalcResponse.parseDocument(docMono);
    }

    /**
     *  call DCS concurrently with one id in one request, combine them after all responses return
     *
     * @param entity
     * @param allDps
     * @param idList
     * @return
     */
    private Flux<Result> getResultConcurrently(CalcRequest entity, List<DataPoint> allDps, List<String> idList, List<IdMapper> idMappers) {
        List<Datapoint> listOfDps = entity.getCalcDps().stream().map(dpId -> Datapoint.builder().id(dpId).build()).toList();
        SecuritiesRequestEntity securitiesRequestEntity = SecuritiesRequestEntity.builder()
                .jobId(entity.getRequestId())
                .userId(entity.getUserId())
                .datapoints(listOfDps)
                .build();

        return Flux.fromIterable(idList).map(id ->
            Flux.fromIterable(allDps)
                    .flatMap(dataPoint -> buildQueryWithId(entity, id, dataPoint, idMappers))
                    .subscribeOn(Schedulers.parallel())
                    .reduce(new StringBuilder(), (accu, next) -> accu.append(next))
                    .map(StringBuilder::toString)
                    .flatMap(dataPoint -> PostBodyBuilder.buildBody(Arrays.asList(id), dataPoint))
        ).map(bodyMono ->
            calcAPICaller.post(bodyMono, securitiesRequestEntity)
                    .onErrorReturn(RuntimeException.class, "")
                    .filter(str -> !StringUtils.isEmpty(str))
                    .map(str -> CalculationService.parseDocument.apply(str))
        ).flatMap(CalcResponse::parseDocument);
    }

    private Publisher<? extends String> handleCurrentDpWithIdList(CalcRequest entity, List<String> idList, DataPoint dataPoint, List<IdMapper> idMappers) {
        Calculation calculation = dataPoint.getCalculation();
        DataPoint bmk = calculation.getBmk();
        DataPoint rf = calculation.getRf();
        List<DataPoint> bmk_rf = new ArrayList<>();
        if (rf != null) {
            bmk_rf.add(rf);
        }
        if (bmk != null) {
            bmk_rf.add(bmk);
        }
        //if contains bmk or rfid
        if (!bmk_rf.isEmpty()) {
            Flux<Result> currentResults = currentDataRetrievalService.getCurrentData(idList, bmk_rf, CurrentUtil.getReadCache(entity.getReadCache()), idMappers);
            return CalculationUtil.mergeResultsWithSameIds(currentResults)
                    .map(currentResult -> {
                        String secId = currentResult.getId();
                        Map<String, String> values = currentResult.getValues();
                        String bmkSecId = "";
                        String rfSecId = "";
                        if (bmk != null) {
                            bmkSecId = values.getOrDefault(bmk.getNid(), "");
                        }
                        if (rf != null) {
                            rfSecId = values.getOrDefault(rf.getNid(), "");
                        }
                        return PostBodyBuilder.createCDDps(secId, dataPoint, bmkSecId, rfSecId,
                                entity.getCurrency(), entity.getAnnualized(), entity.getAdjustment());
                    })
                    .filter(str -> !StringUtils.isEmpty(str))
                    .reduce(new StringBuilder(), (accu, next) -> accu.append(next))
                    .map(StringBuilder::toString);
        } else {
            String temp = PostBodyBuilder.createCDDps("", dataPoint, "", "",
                    entity.getCurrency(), entity.getAnnualized(), entity.getAdjustment());
            return Mono.just(temp);
        }
    }

    private Publisher<? extends String> buildQueryWithId(CalcRequest entity, String id, DataPoint dataPoint, List<IdMapper> idMappers) {
        //Current Data
        if (dataPoint.isCurrentDataPoint() || dataPoint.isRdbDataPoint()) {
            return handleCurrentDpWithIdList(entity, Collections.singletonList(id), dataPoint, idMappers);
        } else if (dataPoint.isTimeSerialPoint()) {
            return handleTSDp(entity, dataPoint);
        } else {
            //invalid source type
            LOGGER.warn("invalid Data Point Src: {}", String.join(",", dataPoint.getSrc()));
            return Mono.empty();
        }
    }

    Publisher<? extends String> buildQueryWithIdList(CalcRequest entity, List<String> idList, DataPoint dataPoint, List<IdMapper> idMappers) {
        //Current Data
        if (dataPoint.isCurrentDataPoint() || dataPoint.isRdbDataPoint()) {
            return handleCurrentDpWithIdList(entity, idList, dataPoint, idMappers);
        } else if (dataPoint.isTimeSerialPoint()) {
            return handleTSDp(entity, dataPoint);
        } else {
            //invalid source type
            LOGGER.warn("invalid Data Point Src: {}", String.join(",", dataPoint.getSrc()));
            return Mono.empty();
        }
    }

    public Boolean verifyDate(String startDate, String endDate) {
        Boolean flag = true;
        if (StringUtils.isEmpty(startDate)) {
            LOGGER.warn("Missing start date");
            flag = false;
        }
        if (StringUtils.isEmpty(endDate)) {
            LOGGER.warn("Missing end date");
            flag = false;
        }
        return flag;
    }

    // freq: [4: Yearly; 3: Quarterly; 2: Monthly; 1: Weekly; 0: Daily]
    public String getDateByFreq(String startDate, String freq) {
        if (StringUtils.isEmpty(freq)) {
            return startDate;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = formatter.parse(startDate);
        } catch (ParseException e) {
            LOGGER.error("invalid start date format: {}", startDate);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if ("4".equals(freq)) {
            calendar.set(Calendar.DAY_OF_YEAR, 1);
        } else if ("3".equals(freq)) {
            int quarter = calendar.get(Calendar.MONTH) / 3;
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            switch (quarter) {
                case 3:
                    calendar.set(Calendar.MONTH, Calendar.OCTOBER);
                    break;
                case 2:
                    calendar.set(Calendar.MONTH, Calendar.JULY);
                    break;
                case 1:
                    calendar.set(Calendar.MONTH, Calendar.APRIL);
                    break;
                case 0:
                default:
                    calendar.set(Calendar.MONTH, Calendar.JANUARY);
                    break;
            }
        } else if ("2".equals(freq)) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
        } else if ("1".equals(freq)) {
            calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        }

        return formatter.format(calendar.getTime());
    }

    private Publisher<? extends String> handleTSDp(CalcRequest entity, DataPoint dataPoint) {
        Calculation calculation = dataPoint.getCalculation();
        String startDate = entity.getStartDate();
        String endDate = entity.getEndDate();

        if (verifyDate(startDate, endDate)) {
            // convert start date
            String realStartDate = getDateByFreq(entity.getStartDate(), calculation.getFreq());
            String temp = PostBodyBuilder.createTSDps(dataPoint, entity.getCurrency(),
                    realStartDate, endDate, entity.getAnnualized(), entity.getAdjustment());
            return Mono.just(temp);
        } else {
            return Mono.empty();
        }
    }

    private static Function<String, Document> parseDocument =
            (String str) -> {
                Document doc = null;
                try {
                    doc = DocumentHelper.parseText(str);
                } catch (DocumentException error) {
                    LOGGER.warn("Error occurred in parsing XML Text from calculation", error);
                }
                return doc;
            };
}
