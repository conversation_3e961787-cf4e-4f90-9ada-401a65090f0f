package com.morningstar.martgateway.domains.eod.helper;

import org.springframework.util.StringUtils;

import java.util.List;

public class SimpleQueryBuilder {

    protected SimpleQueryBuilder() {
        //void
    }

    /**
     * Build string like 'a','b','c' from a list of id
     *
     * @param idList list of id
     * @return string
     */
    public static String buildIdsJoin(List<String> idList) {
        return "'" + String.join("','", idList) + "'";
    }

    /**
     * Build string for column with name and alias.
     * If alias is not null, column name is "column as alias"
     *
     * @param column name of column
     * @param alias alias of column
     * @return string
     */
    public static String buildColumnName(String column, String alias) {
        return (alias == null || alias.isBlank()) ? column : String.format( "%s as %s", column, alias);
    }

    /**
     * Build string for field with name and alias.
     * If alias is not null, column name is "alias: '$field'"
     *
     * @param field name of field
     * @param alias alias of field
     * @return string
     */
    public static String buildProjection(String field, String alias) {
        return StringUtils.hasText(alias) ? String.format( "%s: '$%s'", alias, field) : String.format( "%s: 1", field);
    }

    /**
     * Build string for list of column. Say col1,col2,col3
     *
     * @param columnList list of column
     * @return string
     */
    public static String buildColumnList(List<String> columnList) {
        return String.join(",", columnList);
    }
}
