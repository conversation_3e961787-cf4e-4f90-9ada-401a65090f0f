package com.morningstar.martgateway.domains.calc;

import static com.morningstar.martgateway.domains.calc.CustomCalculationRequestBuilder.convertToCalcDpRequest;
import static com.morningstar.martgateway.domains.calc.CustomCalculationRequestBuilder.getDateByFreq;
import static com.morningstar.martgateway.domains.calc.CustomCalculationRequestBuilder.areBothNonEmptyDates;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.calc.model.CustomCalcDataPointRequest;
import com.morningstar.martgateway.domains.calc.model.CustomCalcRequestNew;
import com.morningstar.martgateway.domains.calc.model.CustomCalcResponseNew;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.martgateway.util.CurrentUtil;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.Logger;
import reactor.util.Loggers;

@Slf4j
public class CustomCalculationService {

	private static final Logger LOGGER = Loggers.getLogger(CustomCalculationService.class);
	private final CurrentDataRetrievalService currentDataRetrievalService;
	private final CustomCalculationAPICaller calcAPICaller;
	private static final ObjectMapper objectMapper;

	static {
		objectMapper = new ObjectMapper();
		objectMapper.setNodeFactory(JsonNodeFactory.withExactBigDecimals(true));
	}


	public CustomCalculationService(CurrentDataRetrievalService currentDataRetrievalService,
									CustomCalculationAPICaller calcAPICaller) {
		this.currentDataRetrievalService = currentDataRetrievalService;
		this.calcAPICaller = calcAPICaller;
	}

	public Flux<Result> retrieveCalc(CalcRequest calcRequest) {
		List<String> calcDps = Optional.ofNullable(calcRequest.getCalcDps()).orElse(new ArrayList<>());
		Set<String> allDps = new HashSet<>(calcDps);
		if(CollectionUtils.isNotEmpty(calcRequest.getCustomCalcDps())) {
			allDps.addAll(calcRequest.getCustomCalcDps().stream().map(GridviewDataPoint::getDataPointId).toList());
		}

		if (allDps.isEmpty()) {
			return Flux.empty();
		}

		LogEntry.info(new LogEntity(EVENT_TYPE, "Calculation"), new LogEntity(EVENT_DESCRIPTION, "Calling calc new CCS Api"));
		List<DataPoint> allDataPoints = allDps.stream()
				.map(DataPointRepository::getByNid)
				.collect(Collectors.toList());

		return getResultByIdList(calcRequest, allDataPoints);
	}

	protected Mono<String> convertRequestToJson(CustomCalcRequestNew req) {
		return Mono.fromCallable(() -> {
			try {
				String requestBody = objectMapper.writeValueAsString(req);
				log.info("CCS payload, request_id=\"{}\", request_body=\"{}\"", req.getRequestId(), requestBody);
				return requestBody;
			} catch (JsonProcessingException e) {
				// Handle the exception, maybe log it or rethrow it as a RuntimeException
				throw new RuntimeException("Error converting DTO to JSON", e);
			}
		});
	}

	private Flux<Result> getResultByIdList(CalcRequest calcRequest, List<DataPoint> allDataPoints) {

		Flux<CustomCalcRequestNew> newCalcRequestFlux = buildQueryWithIdList(calcRequest, allDataPoints);
		return newCalcRequestFlux
				.flatMap(requestDto ->
						Mono.just(requestDto)
								.zipWith(
										Mono.defer(() -> Mono.just(convertRequestToJson(requestDto)))
												.flatMap(calcAPICaller::post)
												.map(CustomCalculationService.parseDocument::apply),
										(dto, responseDto) -> Map.entry(dto, responseDto)
								)
				)
				.flatMap(entry -> {
					CustomCalcRequestNew request = entry.getKey();
					CustomCalcResponseNew responseDto = entry.getValue();
					return CustomCalcResponseParser.parseDocument(responseDto, request, calcRequest.getIdList());
				});
	}

	protected Mono<CustomCalcDataPointRequest> handleCurrentDpWithIdList(CalcRequest calcRequest, String id, DataPoint dataPoint, List<IdMapper> idMappers, Mono<Map<String, Result>> calculationDateResults) {
		Calculation calculation = dataPoint.getCalculation();
		DataPoint bmk = calculation.getBmk();
		DataPoint rf = calculation.getRf();
		List<DataPoint> bmk_rf = new ArrayList<>();
		if (rf != null) {
			bmk_rf.add(rf);
		}
		if (bmk != null) {
			bmk_rf.add(bmk);
		}
		Mono<Map<String, String>> startAndEndDatesMap = getStartAndEndDatesFromCalculationDates(id, calculation.getTrailingPeriod(), calculation.getEndDate().getNid(), calculation.getFreq(), calculationDateResults);

		CalcRequest dpCalcRequest = getCalcRequestByDp(calcRequest, dataPoint);

		//if contains bmk or rfid
		if (!bmk_rf.isEmpty()) {
			Flux<Result> currentResults = currentDataRetrievalService.getCurrentData(List.of(id), bmk_rf, CurrentUtil.getReadCache(calcRequest.getReadCache()), idMappers);
			return CustomCalculationRequestBuilder.mergeResultsAsSingleValue(currentResults)
					.flatMap(currentResult -> {
						String secId = currentResult.getId();
						Map<String, String> values = currentResult.getValues();
						String bmkSecId = values.getOrDefault(bmk != null ? bmk.getNid() : "", "");
						String rfSecId = values.getOrDefault(rf != null ? rf.getNid() : "", "");

						return startAndEndDatesMap
								.defaultIfEmpty(Collections.emptyMap())
								.flatMap(dateResultsMap -> {
									CustomCalcDataPointRequest newCalcDataPointRequestDto = CustomCalculationRequestBuilder.createCurrentDataPointRequest(secId, dataPoint, bmkSecId, rfSecId,
											dpCalcRequest, dateResultsMap);
									return Mono.just(newCalcDataPointRequestDto);
								});
					});
		} else {
			return startAndEndDatesMap
					.defaultIfEmpty(Collections.emptyMap())
					.flatMap(dateResultsMap -> {
						CustomCalcDataPointRequest newCalcDataPointRequestDto = CustomCalculationRequestBuilder.createCurrentDataPointRequest("", dataPoint, "", "",
								dpCalcRequest, dateResultsMap);
						return Mono.just(newCalcDataPointRequestDto);
					});
		}
	}

	private CalcRequest getCalcRequestByDp(CalcRequest calcRequest, DataPoint dataPoint) {

		if(CollectionUtils.isNotEmpty(calcRequest.getCustomCalcDps())) {
			Optional<GridviewDataPoint> gdp = calcRequest.getCustomCalcDps().stream().filter(d -> d.getDataPointId().equals(dataPoint.getNid())).findFirst();
			if(gdp.isPresent()) {
				GridviewDataPoint dp = gdp.get();
				return CalcRequest.builder().annualized(dp.getAnnualized()).currency(dp.getCurrency()).preCurrency(dp.getPreCurrency()).annualDays(dp.getAnnualDays()).
						skipHoliday(dp.getSkipHoliday()).windowType(dp.getWindowType()).extendedPerformance(dp.getExtendedPerformance()).compoundingMethod(dp.getCompounding()).isApplyIsraelsenModification(dp.getIsApplyIsraelsenModification()).build();
			}
		}

		return calcRequest;
	}

	protected Flux<CustomCalcRequestNew> buildQueryWithIdList(CalcRequest calcRequest, List<DataPoint> allDataPoints) {
		List<DataPoint> currencyCalcDps = allDataPoints.stream().filter(dataPoint -> dataPoint.isCurrentDataPoint() || dataPoint.isRdbDataPoint() || dataPoint.isTimeSerialPoint()).collect(Collectors.toList());
		List<GridviewDataPoint> calcDps = calcRequest.getCustomCalcDps() != null ? calcRequest.getCustomCalcDps().stream().filter(ccdp -> !(currencyCalcDps.stream().anyMatch(dp -> dp.getNid().equals(ccdp.getDataPointId())))).toList() : Collections.emptyList();
		return Flux.merge(buildCalcQuery(calcRequest, calcDps), buildCurrencyCalcQuery(calcRequest, currencyCalcDps));
	}

	protected Flux<CustomCalcRequestNew> buildCalcQuery(CalcRequest calcRequest, List<GridviewDataPoint> calcDps) {

		if(CollectionUtils.isEmpty(calcDps)) {
			return Flux.empty();
		}

		List<List<String>> batchIds = ListUtils.partition(calcRequest.getIdList(), 1000);
		return Flux.fromIterable(batchIds)
				.flatMap(ids -> buildCustomCalcQuery(calcRequest, calcDps, ids));
	}

	protected Flux<CustomCalcRequestNew> buildCurrencyCalcQuery(CalcRequest calcRequest, List<DataPoint> currencyCalcDps) {

		if(CollectionUtils.isEmpty(currencyCalcDps)) {
			return Flux.empty();
		}

		Mono<Map<String, Result>> calculationDateResults = getSecIdToDateResultMap(calcRequest, currencyCalcDps);
		return Flux.fromIterable(calcRequest.getIdList())
				.flatMap(id -> buildCurrencyQuery(calcRequest, currencyCalcDps, List.of(id), calculationDateResults));
	}

	protected Flux<CustomCalcRequestNew> buildCustomCalcQuery(CalcRequest calcRequest, List<GridviewDataPoint> calcDps, List<String> ids) {
		List<List<GridviewDataPoint>> batchDps = ListUtils.partition(calcDps, 1000);
		return Flux.fromIterable(batchDps).flatMap(dps -> {
			CustomCalcRequestNew customCalcRequest = CustomCalculationRequestBuilder.initNewCalcRequestDto(ids, calcRequest.getUserId(), calcRequest.getRequestId());
			List<CustomCalcDataPointRequest> requestDataPoints = new ArrayList<>();
			List<CustomCalcDataPointRequest> calcApiDps = dps.stream().map(dp -> convertToCalcDpRequest(dp)).collect(Collectors.toList());
			requestDataPoints.addAll(calcApiDps);
			customCalcRequest.setDatapoints(requestDataPoints);
			return Flux.just(customCalcRequest);
		});
	}

	protected Flux<CustomCalcRequestNew> buildCurrencyQuery(CalcRequest calcRequest, List<DataPoint> currencyCalcDps, List<String> ids, Mono<Map<String, Result>> calculationDateResults) {
		return Flux.fromIterable(ids).flatMap(id -> {
			CustomCalcRequestNew newCalcRequest = CustomCalculationRequestBuilder.initNewCalcRequestDto(ids, calcRequest.getUserId(), calcRequest.getRequestId());
			return Flux.fromIterable(currencyCalcDps)
					.flatMap(dataPoint -> {
						if (dataPoint.isCurrentDataPoint() || dataPoint.isRdbDataPoint()) {
							return handleCurrentDpWithIdList(calcRequest, id, dataPoint, calcRequest.getIdMappers(), calculationDateResults)
									.filter(Objects::nonNull).doOnNext(newCalcRequest.getDatapoints()::add);
						} else if (dataPoint.isTimeSerialPoint()) {
							return handleTSDp(calcRequest, dataPoint)
									.filter(Objects::nonNull).doOnNext(newCalcRequest.getDatapoints()::add);
						} else {
							// Log invalid source types, and continue
							LOGGER.warn("invalid Data Point Src: {}", String.join(",", dataPoint.getSrc()));
							return Mono.empty(); // Return an empty Mono for invalid data points
						}
					})
					.then(Mono.just(newCalcRequest));
		});
	}

	protected Mono<CustomCalcDataPointRequest> handleTSDp(CalcRequest calcRequest, DataPoint dataPoint) {
		Calculation calculation = dataPoint.getCalculation();
		String startDate = calcRequest.getStartDate();
		String endDate = calcRequest.getEndDate();

		if (areBothNonEmptyDates(startDate, endDate)) {
			// convert start date
			String realStartDate = getDateByFreq(calcRequest.getStartDate(), calculation.getFreq());

			CalcRequest dpCalcRequest = getCalcRequestByDp(calcRequest, dataPoint);

			CustomCalcDataPointRequest temp = CustomCalculationRequestBuilder.createTimeSeriesDataPointRequestDto(dataPoint,
					realStartDate, endDate, dpCalcRequest);

			return Mono.just(temp);
		} else {
			return Mono.empty();
		}
	}

	protected static Function<String, CustomCalcResponseNew> parseDocument =
			(String str) -> {
				try {
					return objectMapper.readValue(str, CustomCalcResponseNew.class);
				} catch (JsonProcessingException e) {
					throw new RuntimeException(e);
				}
			};


	protected Flux<Result> getDatesFromOtherGateways(List<String> idList, List<DataPoint> dataPoints, Boolean readCache, List<IdMapper> idMappers) {
		if (CollectionUtils.isEmpty(idList) || CollectionUtils.isEmpty(dataPoints)) {
			return Flux.empty();
		}
		Set<String> datapointSet = new HashSet<>();

		for (DataPoint dataPoint : dataPoints) {
			if (dataPoint.getCalculation() != null && dataPoint.getCalculation().getEndDate() != null) {
				datapointSet.add(dataPoint.getCalculation().getEndDate().getNid());
			}
		}

		return currentDataRetrievalService.getCurrentDataByDpsString(idList, new ArrayList<>(datapointSet), readCache, idMappers);
	}

	private Mono<Map<String, Result>> getSecIdToDateResultMap(CalcRequest calcRequest, List<DataPoint> allDataPoints) {
		Flux<Result> gatewayResultsFlux = getDatesFromOtherGateways(calcRequest.getIdList(), allDataPoints, CurrentUtil.getReadCache(calcRequest.getReadCache()), calcRequest.getIdMappers()).cache(Duration.ofMinutes(2));
		return gatewayResultsFlux.collectMap(Result::getId);
	}

	protected Mono<Map<String, String>> getStartAndEndDatesFromCalculationDates(String id, String trailingPeriod, String endDateDpsNid, String frequency, Mono<Map<String, Result>> calculationDateResults) {
		if (StringUtils.isEmpty(id) || endDateDpsNid == null || trailingPeriod == null) {
			return Mono.empty();
		}
		// Convert trailingPeriod to Integer for further processing
		Integer trailingPeriodVal = Integer.parseInt(trailingPeriod);
		return calculationDateResults
				.cache(Duration.ofMinutes(2))
				.defaultIfEmpty(Collections.emptyMap())
				.flatMap(dateResultsMap -> {
					Result result = dateResultsMap.get(id); // Retrieve Result for current id
					if (result != null) {
						// Call buildDateResults and accumulate into the resultMap
						return Mono.just(CustomCalculationRequestBuilder.buildDateResults(result, endDateDpsNid, trailingPeriodVal, frequency));
					} else {
						return Mono.empty();
					}
				});
	}
}
