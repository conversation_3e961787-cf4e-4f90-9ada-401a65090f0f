package com.morningstar.martgateway.domains.eod.filter;


import com.morningstar.entitlement.eod.data.entity.Investment;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.morningstar.martgateway.domains.eod.util.MartRequestConstants.ENTITLED_INVESTMENTS;

@Slf4j
@NoArgsConstructor
public class EntitlementEODErrorHandler {

    public Flux<Result> apply(MartRequest martRequest, Flux<Result> resultFlux, List<String> dpIds) {
        log.debug("Apply entitlementEODErrorHandler");

        List<String> requestIds = martRequest.getIds();

        Collection<Investment> entitledInvestments = (Collection<Investment>)martRequest.getAttribute(ENTITLED_INVESTMENTS);
        // There is no entitled ID mappers, all IDs are unentitled
        if (CollectionUtils.isEmpty(entitledInvestments)) {
            return addUnentitledErrors(requestIds, resultFlux, dpIds);
        }

        // All request ids are entitled, return the result as is
        if (requestIds.size() == entitledInvestments.size()) {
            return resultFlux;
        }

        // Identify unentitled IDs and add errors for them
        Set<String> entitledIdSet = entitledInvestments.stream().map(Investment::getPerformanceId).collect(Collectors.toSet());
        List<String> unentitledIds = requestIds.stream().filter(id -> !entitledIdSet.contains(id)).toList();

        return addUnentitledErrors(unentitledIds, resultFlux, dpIds);
    }

    private Flux<Result> addUnentitledErrors(List<String> unentitledIds, Flux<Result> resultFlux,
                                             List<String> dataPointIds) {
        Flux<Result> unentitledFlux = Flux.fromIterable(unentitledIds.stream()
                .flatMap(id -> createErrorResult(id, dataPointIds).stream()).toList());
        return Flux.merge(resultFlux, unentitledFlux);
    }

    private List<ErrorResult> createErrorResult(String unentitledId, List<String> dataPointIds) {
        return dataPointIds.stream().map(datapointId -> new ErrorResult(unentitledId, datapointId,
                Status.NO_ENTITLEMENT_INFO.getCode())).toList();
    }
}
