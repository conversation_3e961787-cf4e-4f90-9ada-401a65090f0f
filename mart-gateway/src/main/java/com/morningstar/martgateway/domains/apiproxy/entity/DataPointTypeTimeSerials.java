package com.morningstar.martgateway.domains.apiproxy.entity;

import com.morningstar.martgateway.util.apiproxy.PairToEntityUtil;
import com.morningstar.martgateway.util.apiproxy.timeperiod.TimeSerialsCalculator;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public class DataPointTypeTimeSerials implements IDataPointType {
    @Override
    public DataPointForColumnsResponse generateTimePeriodsByDPType(String dataPointType, DataPointForColumns dataPoint) {
        DataPointForColumnsResponse response = DataPointForColumnsResponse.builder()
                .datapointId(dataPoint.getDatapointId())
                .alias(dataPoint.getAlias())
                .build();
        List<Pair<String, String>> datePairs = new ArrayList<>();
        Pair<String, String> resDates = TimeSerialsCalculator.initialStartDateAndEndDate(dataPoint);

        Date standardStartDate = Optional.ofNullable(resDates.getLeft()).map(DateUtil::tryParse)
                .map(it -> FrequencyType.getActualFirstDateByFrequency(it, dataPoint.getFrequency(), true)).orElse(null);
        Date standardEndDate = Optional.ofNullable(resDates.getRight()).map(DateUtil::tryParse)
                .map(it -> FrequencyType.getActualFirstDateByFrequency(it, dataPoint.getFrequency(), false)).orElse(null);
        return getDataPointForColumnsResponse(dataPoint, response, datePairs, standardStartDate, standardEndDate);
    }

    private DataPointForColumnsResponse getDataPointForColumnsResponse(DataPointForColumns dataPoint, DataPointForColumnsResponse response, List<Pair<String, String>> datePairs, Date standardStartDate, Date standardEndDate) {
        if (standardStartDate == null || standardEndDate == null || dataPoint.getFrequency() == null) {
            response.setColumns(datePairs.size());
            response.setDatePairs(PairToEntityUtil.pair2StartEndDate(datePairs));
            return response;
        }
        LocalDate realEnd = DateUtil.asLocalDate(standardEndDate);
        LocalDate realStart = DateUtil.asLocalDate(standardStartDate);
        if (realStart.compareTo(realEnd) <= 0) {
            LocalDate subStartDate = realStart;
            LocalDate subEndDate = FrequencyType.getActualFirstDateByFrequency(realStart, dataPoint.getFrequency(), false);
            int columnSize = 0;
            List<StartEndDateEntity> se = new ArrayList<>();
            while (subEndDate.compareTo(realEnd) <= 0) {
                columnSize++;
                StartEndDateEntity startEndDateEntity = new StartEndDateEntity();
                startEndDateEntity.setStartDate(subStartDate.toString());
                startEndDateEntity.setEndDate(subEndDate.toString());
                se.add(startEndDateEntity);
                subEndDate = nextEndDate(subEndDate, dataPoint.getFrequency());
                subStartDate = getFirstDate(subEndDate, dataPoint.getFrequency());
            }
            response.setColumns(columnSize);
            response.setDatePairs(se);
        } else {
            response.setColumns(1);
            datePairs.clear();
            datePairs.add(Pair.of(dataPoint.getStartDate(), dataPoint.getEndDate()));
            response.setDatePairs(PairToEntityUtil.pair2StartEndDate(datePairs));
        }
        return response;
    }

    private static LocalDate getFirstDate(LocalDate date, String frequency) {
        return FrequencyType.getActualFirstDateByFrequency(date, frequency, true);
    }

    private static LocalDate nextEndDate(LocalDate date, String frequency) {
        LocalDate localDate = FrequencyType.nextEndDate(date, frequency);
        return FrequencyType.getActualFirstDateByFrequency(localDate, frequency, false);
    }
}