package com.morningstar.martgateway.domains.apiproxy.entity;

import com.morningstar.dataac.martgateway.core.common.util.DateUtil;

import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.Date;
import java.util.Locale;

public class FrequencyTypeWeekly implements IFrequencyType {

    @Override
    public Date getActualFirstDateByFrequency(Date date, String frequency, boolean isFirstOrLast) {
        return isFirstOrLast ? DateUtil.getFirstDateOfWeek(date) : DateUtil.getLastDateOfWeek(date);
    }

    @Override
    public Date nextEndDate(Date date, String frequency) {
        LocalDate localDate = DateUtil.asLocalDate(date);
        localDate = localDate.plusWeeks(1);
        return DateUtil.asDate(localDate);
    }

    @Override
    public LocalDate getActualFirstDateByFrequency(LocalDate date, String frequency, boolean isFirstOrLast) {
        return isFirstOrLast ? date.with(WeekFields.of(Locale.US).dayOfWeek(), 1L) : date.with(WeekFields.of(Locale.US).dayOfWeek(), 7L);
    }

    @Override
    public LocalDate nextEndDate(LocalDate date, String frequency) {
        return date.plusWeeks(1);
    }

    @Override
    public boolean checkLegalDate(Integer stepSize, String frequency, long y, long m, long w, long ds) {
        return w - stepSize >= 0;
    }

    @Override
    public LocalDate getEndDateByWindowSize(LocalDate startDate, long windowSize) {
        return startDate.plusWeeks(windowSize).plusDays(-1);
    }

    @Override
    public LocalDate getStartDateByWindowSize(LocalDate endDate, long windowSize) {
        return endDate.plusWeeks(windowSize * -1).plusDays(1);
    }

    @Override
    public LocalDate getRollingWindowNextStartDate(LocalDate startDate, long stepSize) {
        return startDate.plusWeeks(stepSize);
    }

    @Override
    public LocalDate getForwardExtendingNextEndDate(LocalDate endDate, long stepSize) {
        return endDate.plusWeeks(stepSize);
    }

    @Override
    public LocalDate getBackwardExtendingPreviousStartDate(LocalDate startDate, long stepSize) {
        return startDate.plusWeeks(stepSize * -1);
    }

}
