package com.morningstar.martgateway.domains.delta;


import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.martgateway.domains.delta.entity.DataPointDeltaDetection;
import com.morningstar.martgateway.domains.delta.entity.DeltaDataPoint;
import com.morningstar.martgateway.domains.delta.entity.DeltaDetection;
import com.morningstar.martgateway.domains.delta.entity.DeltaRange;
import com.morningstar.martgateway.domains.delta.entity.DeltaSearchParameters;
import com.morningstar.martgateway.domains.delta.entity.TimeSeriesDelta;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_MESSAGE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EXCEPTION_TYPE;

public abstract class DeltaDetectionService {

    private static final String DELTA_INDEX = "timeseriesdelta";

    private final ReactiveElasticsearchClient esChangeLogClient;

    public DeltaDetectionService(ReactiveElasticsearchClient esChangeLogClient) {
        this.esChangeLogClient = esChangeLogClient;
    }

    protected abstract void addSearchParameters(DeltaDataPoint deltaDataPoint, Map<String, Map<String, Set<String>>> idMapsByLevel, Map<DeltaSearchParameters, List<DeltaDataPoint>> deltaDataPointsByParameters);

    protected abstract boolean isDeltaParameterMatch(DeltaSearchParameters deltaSearchParameters, TimeSeriesDelta delta);

    protected abstract QueryBuilder buildQuery(List<DeltaSearchParameters> deltaSearchParameterList, long deltaStartTime, long deltaEndTime);

    public Flux<DataPointDeltaDetection> getDelta(List<DeltaDataPoint> timeSeriesDataPoints, Map<String, Map<String, Set<String>>> idMapsByLevel, DeltaRange deltaRange) {
        Map<DeltaSearchParameters, List<DeltaDataPoint>> deltaDataPointsByParameters = getDataPointsBySearchParameters(
                timeSeriesDataPoints,
                idMapsByLevel
        );
        return Flux.fromIterable(deltaDataPointsByParameters.keySet())
                .buffer(200)
                .flatMap(searchParameterList -> getChangeLog(searchParameterList, deltaRange))
                .flatMap(delta -> fetchDetectionsForDataPoint(deltaDataPointsByParameters, idMapsByLevel, delta))
                .distinct();
    }

    private Map<DeltaSearchParameters, List<DeltaDataPoint>> getDataPointsBySearchParameters(
            List<DeltaDataPoint> dataPoints,
            Map<String, Map<String, Set<String>>> idMapsByLevel
    ) {
        Map<DeltaSearchParameters, List<DeltaDataPoint>> deltaDataPointsByParameters = new HashMap<>();
        dataPoints.forEach(deltaDataPoint -> addSearchParameters(deltaDataPoint, idMapsByLevel, deltaDataPointsByParameters));
        return deltaDataPointsByParameters;
    }

    private Flux<DataPointDeltaDetection> fetchDetectionsForDataPoint(Map<DeltaSearchParameters, List<DeltaDataPoint>> deltaDataPointsByParameters, Map<String, Map<String, Set<String>>> idMapsByLevel, TimeSeriesDelta delta) {
        Flux<DataPointDeltaDetection> dataPointWithDeltaFlux = Flux.empty();
        for (Map.Entry<DeltaSearchParameters, List<DeltaDataPoint>> entry : deltaDataPointsByParameters.entrySet()) {
            DeltaSearchParameters deltaSearchParameters = entry.getKey();
            List<DeltaDataPoint> deltaDataPoints = entry.getValue();
            if (isDeltaParameterMatch(deltaSearchParameters, delta)) {
                dataPointWithDeltaFlux = Flux.merge(dataPointWithDeltaFlux, Flux.fromStream(buildDataPointDeltas(deltaDataPoints, deltaSearchParameters, idMapsByLevel, delta)));
            }
        }
        return dataPointWithDeltaFlux;
    }

    private Stream<DataPointDeltaDetection> buildDataPointDeltas(List<DeltaDataPoint> deltaDataPoints, DeltaSearchParameters deltaSearchParameters, Map<String, Map<String, Set<String>>> idMapsByLevel, TimeSeriesDelta delta) {
        return deltaDataPoints.stream()
                .flatMap(dp -> getInvestmentsFromDelta(delta, idMapsByLevel, deltaSearchParameters)
                        .stream()
                        .map(investmentId -> new DataPointDeltaDetection(dp, new DeltaDetection(investmentId, toInvestmentApiDate(delta.getAsOfDate()))))
                );
    }

    private Set<String> getInvestmentsFromDelta(TimeSeriesDelta delta, Map<String, Map<String, Set<String>>> idMapsByLevel, DeltaSearchParameters deltaSearchParameters) {
        String idLevel = deltaSearchParameters.getIdLevel();
        String instrumentId = delta.getInstrumentId();
        if (!idMapsByLevel.containsKey(idLevel) || !idMapsByLevel.get(idLevel).containsKey(instrumentId)) {
            return Collections.emptySet();
        }
        return idMapsByLevel.get(deltaSearchParameters.getIdLevel()).get(delta.getInstrumentId());
    }

    private String toInvestmentApiDate(LocalDate date) {
        if (date == null) {
            return "";
        }
        return DateFormatUtil.format(date);
    }

    private Flux<TimeSeriesDelta> getChangeLog(List<DeltaSearchParameters> searchParameterList, DeltaRange deltaRange) {
        QueryBuilder query = buildQuery(searchParameterList, deltaRange.getStartEpochTime(), deltaRange.getEndEpochTime());
        SearchRequest searchRequest = buildSearchRequest(query);
        return esChangeLogClient.scroll(searchRequest)
                .onErrorResume(error -> {
                    LogEntry.error(error, new LogEntity(EVENT_DESCRIPTION, "Error occurred while scrolling through ES for delta"),
                            new LogEntity(EXCEPTION_TYPE, error.getClass()),
                            new LogEntity(EXCEPTION_MESSAGE, error.getMessage()));
                    return Flux.empty();
                })
                .flatMap(this::transformToDelta);
    }

    private SearchRequest buildSearchRequest(QueryBuilder query) {
        SearchRequest searchRequest = new SearchRequest(DELTA_INDEX);
        searchRequest.source(new SearchSourceBuilder()
                .query(query)
                .size(1000)
                .sort("_doc")
                .timeout(TimeValue.timeValueSeconds(30))
        );
        searchRequest.scroll(TimeValue.timeValueMillis(200));
        return searchRequest;
    }

    private Mono<TimeSeriesDelta> transformToDelta(SearchHit hit) {
        String source = hit.getSourceAsString();
        TimeSeriesDelta delta = null;
        try {
            delta = JsonUtils.fromJsonString(source, TimeSeriesDelta.class);
        } catch (IllegalStateException e) {
            LogEntry.error(e, new LogEntity(EVENT_DESCRIPTION, "Unable to parse delta ES document"),
                    new LogEntity(EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(EXCEPTION_MESSAGE, e.getMessage()));
        }
        if (delta == null) {
            return Mono.empty();
        }
        delta.setId(hit.getId());
        return Mono.just(delta);
    }
}
