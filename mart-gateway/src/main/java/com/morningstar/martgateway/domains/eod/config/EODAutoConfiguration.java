package com.morningstar.martgateway.domains.eod.config;

import com.mongodb.client.MongoClient;
import com.morningstar.entitlement.eod.data.entity.entitlementrequest.EntitlementRequest;
import com.morningstar.entitlement.eod.data.entity.entitlementresponse.EntitlementResponse;
import com.morningstar.entitlement.eod.data.interfaces.EntitlementEODDataService;
import com.morningstar.martgateway.applications.eod.EODGateway;
import com.morningstar.martgateway.domains.eod.filter.EmbargoTimeFilter;
import com.morningstar.martgateway.domains.eod.filter.EntitlementEODErrorHandler;
import com.morningstar.martgateway.domains.eod.filter.EntitlementEODHandler;
import com.morningstar.martgateway.domains.eod.filter.GatewayFilterChain;
import com.morningstar.martgateway.domains.eod.filter.GatewayFilterRegistry;
import com.morningstar.martgateway.domains.eod.repository.EODAsyncRepo;
import com.morningstar.martgateway.domains.eod.repository.EODMongodbRepo;
import com.morningstar.martgateway.domains.eod.repository.EODMongodbAsyncRepo;
import com.morningstar.martgateway.domains.eod.repository.EODRepo;
import com.morningstar.martgateway.domains.eod.service.EODDataService;
import com.morningstar.martgateway.domains.eod.service.EODDataServiceImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;
import reactor.core.scheduler.Scheduler;

@Lazy
@Configuration(proxyBeanMethods = false)
@EnableAspectJAutoProxy
@Import(EODDataSourceConfiguration.class)
public class EODAutoConfiguration {

    @Bean("eodAsyncRepo")
    @ConditionalOnMissingBean(EODAsyncRepo.class)
    public EODAsyncRepo eodAsyncRepo(@Qualifier("eodScheduler") Scheduler scheduler, EODRepo eodRepo) {
        return new EODAsyncRepo(scheduler, eodRepo);
    }

    @Bean("eodMongoRepo")
    @ConditionalOnMissingBean(EODMongodbRepo.class)
    public EODMongodbRepo eodMongodbRepo(MongoClient mongoClient) {
        return new EODMongodbRepo(mongoClient);
    }

    @Bean("eodReferenceAsyncRepo")
    @ConditionalOnMissingBean(EODMongodbAsyncRepo.class)
    public EODMongodbAsyncRepo eodReferenceAsyncRepo(@Qualifier("eodReferenceScheduler") Scheduler scheduler,
                                                     @Qualifier("eodMongoRepo") EODMongodbRepo eodMongoRepo) {
        return new EODMongodbAsyncRepo(scheduler, eodMongoRepo);
    }

    @Bean("gatewayFilterRegistry")
    @ConditionalOnMissingBean(GatewayFilterRegistry.class)
    public GatewayFilterRegistry gatewayFilterRegistry(ApplicationContext applicationContext) {
        return new GatewayFilterRegistry(applicationContext);
    }

    @Bean("gatewayFilterChain")
    @ConditionalOnMissingBean(GatewayFilterChain.class)
    public GatewayFilterChain gatewayFilterChain(@Qualifier("gatewayFilterRegistry") GatewayFilterRegistry gatewayFilterRegistry) {
        return new GatewayFilterChain(gatewayFilterRegistry);
    }

    @Bean("eodDataService")
    @ConditionalOnMissingBean(EODDataService.class)
    public EODDataService eodDataService(EODAsyncRepo eodAsyncRepo, EODMongodbAsyncRepo eodMongodbAsyncRepo) {
        return new EODDataServiceImpl(eodAsyncRepo, eodMongodbAsyncRepo);
    }

    @Bean("eodGateway")
    @ConditionalOnMissingBean(EODGateway.class)
    @DependsOn({"eodDataService", "entitlementEODHandler"})
    public EODGateway eodGateway(EODDataService eodDataService, EntitlementEODHandler entitlementEODHandler,
                                 EntitlementEODErrorHandler entitlementEODErrorHandler) {
        return new EODGateway(eodDataService, entitlementEODHandler, entitlementEODErrorHandler);
    }

    /** Filters for gateway filter chain **/
    @Bean("entitlementEODHandler")
    @ConditionalOnMissingBean(EntitlementEODHandler.class)
    public EntitlementEODHandler entitlementEODHandler(EntitlementEODDataService<EntitlementResponse, EntitlementRequest> entitlementEODDataService) {
        return new EntitlementEODHandler(entitlementEODDataService);
    }

    @Bean("entitlementEODErrorHandlingFilter")
    @ConditionalOnMissingBean(EntitlementEODErrorHandler.class)
    public EntitlementEODErrorHandler entitlementEODErrorHandlingFilter() {
        return new EntitlementEODErrorHandler();
    }

    @Bean("embargoTimeFilter")
    @ConditionalOnMissingBean(EmbargoTimeFilter.class)
    public EmbargoTimeFilter embargoTimeFilter() {
        return new EmbargoTimeFilter();
    }

    /** Logging aspects **/
    @Bean
    @ConditionalOnMissingBean(EODLoggingAspect.class)
    public EODLoggingAspect eodLoggingAspect() {
        return new EODLoggingAspect();
    }
}
