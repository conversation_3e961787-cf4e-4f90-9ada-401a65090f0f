package com.morningstar.martgateway.domains.apiproxy.entity;

import com.morningstar.dataac.martgateway.core.common.util.DateUtil;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Date;

public class FrequencyTypeQuarterly implements IFrequencyType{

    @Override
    public Date getActualFirstDateByFrequency(Date date, String frequency, boolean isFirstOrLast) {
        return isFirstOrLast ? DateUtil.getFirstDateOfQuarter(date) : DateUtil.getLastDateOfQuarter(date);
    }

    @Override
    public Date nextEndDate(Date date, String frequency) {
        LocalDate localDate = DateUtil.asLocalDate(date);
        localDate = localDate.plusMonths(3);
        return DateUtil.asDate(localDate);
    }

    @Override
    public LocalDate getActualFirstDateByFrequency(LocalDate date, String frequency, boolean isFirstOrLast) {
        return DateUtil.getDateOfQuarterBasic(date, isFirstOrLast);
    }

    @Override
    public LocalDate nextEndDate(LocalDate date, String frequency) {
        return date.plusMonths(3);
    }

    @Override
    public boolean checkLegalDate(Integer stepSize, String frequency, long y, long m, long w, long ds) {
        long months = (y * 12 + m);
        long quaters = (months % 3 == 0) ? months / 3 : months + 1;
        return quaters - stepSize >= 0;
    }

    @Override
    public LocalDate getEndDateByWindowSize(LocalDate startDate, long windowSize) {
        return YearMonth.of(startDate.getYear(), startDate.getMonth())
                .plusMonths(windowSize * 3).plusMonths(-1).atEndOfMonth();
    }

    @Override
    public LocalDate getStartDateByWindowSize(LocalDate endDate, long windowSize) {
        return YearMonth.of(endDate.getYear(), endDate.getMonth())
                .plusMonths(windowSize * -3).plusMonths(1).atDay(1);
    }

    @Override
    public LocalDate getRollingWindowNextStartDate(LocalDate startDate, long stepSize) {
        return YearMonth.of(startDate.getYear(), startDate.getMonth())
                .plusMonths(stepSize * 3).atDay(1);
    }

    @Override
    public LocalDate getForwardExtendingNextEndDate(LocalDate endDate, long stepSize) {
        return YearMonth.of(endDate.getYear(), endDate.getMonth())
                .plusMonths(stepSize * 3).atEndOfMonth();
    }

    @Override
    public LocalDate getBackwardExtendingPreviousStartDate(LocalDate startDate, long stepSize) {
        return YearMonth.of(startDate.getYear(), startDate.getMonth())
                .plusMonths(stepSize * -3).atDay(1);
    }
}