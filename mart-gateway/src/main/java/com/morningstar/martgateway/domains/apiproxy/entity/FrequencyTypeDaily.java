package com.morningstar.martgateway.domains.apiproxy.entity;

import com.morningstar.dataac.martgateway.core.common.util.DateUtil;

import java.time.LocalDate;
import java.util.Date;

public class FrequencyTypeDaily implements  IFrequencyType {

    @Override
    public Date getActualFirstDateByFrequency(Date date, String frequency, boolean isFirstOrLast) {
        return date;
    }

    @Override
    public Date nextEndDate(Date date, String frequency) {
        LocalDate localDate = DateUtil.asLocalDate(date);
        localDate = localDate.plusDays(1);
        return DateUtil.asDate(localDate);
    }

    @Override
    public LocalDate getActualFirstDateByFrequency(LocalDate date, String frequency, boolean isFirstOrLast) {
        return date;
    }

    @Override
    public LocalDate nextEndDate(LocalDate date, String frequency) {
        return date.plusDays(1);
    }

    @Override
    public boolean checkLegalDate(Integer stepSize, String frequency, long y, long m, long w, long ds) {
        return ds - stepSize >= 0;
    }

    @Override
    public LocalDate getEndDateByWindowSize(LocalDate startDate, long windowSize) {
        return startDate.plusDays(windowSize - 1);
    }

    @Override
    public LocalDate getStartDateByWindowSize(LocalDate endDate, long windowSize) {
        return endDate.plusDays(windowSize * -1).plusDays(1);
    }

    @Override
    public LocalDate getRollingWindowNextStartDate(LocalDate startDate, long stepSize) {
        return startDate.plusDays(stepSize);
    }

    @Override
    public LocalDate getForwardExtendingNextEndDate(LocalDate endDate, long stepSize) {
        return endDate.plusDays(stepSize);
    }

    @Override
    public LocalDate getBackwardExtendingPreviousStartDate(LocalDate startDate, long stepSize) {
        return startDate.plusDays(stepSize * -1);
    }

}
