package com.morningstar.martgateway.domains.dataservice.customcalc.helpers;

import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.morningstar.martgateway.domains.dataservice.customcalc.model.CustomCalcConfig;
import com.morningstar.martgateway.domains.dataservice.customcalc.model.DataPointCalcInfo;
import com.morningstar.martgateway.domains.dataservice.customcalc.model.LevelInfo;
import com.morningstar.martgateway.domains.dataservice.customcalc.model.RescaleInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public class CustomConfigHelper {

    private static final int MAX_LEVEL = 5;
    private static CustomCalcConfig customCalcConfig;
    private static Map<String, Set<String>> dataPointDependencyMap;

    static {
        try {
            Reader reader = new InputStreamReader(new ClassPathResource("customCalcConfig.json").getInputStream());
            customCalcConfig = JsonUtils.fromJsonString(FileCopyUtils.copyToString(reader), CustomCalcConfig.class);
            dataPointDependencyMap = buildDependencyMap();
        } catch (IOException e) {
            customCalcConfig = new CustomCalcConfig()
                    .setCustomCalcDataPoints(new HashMap<>())
                    .setCustomRescaled(new HashMap<>())
                    .setPathToBreakdownValues(new HashMap<>());
        }
    }

    private CustomConfigHelper() {}

    private static Map<String, Set<String>> buildDependencyMap() {
        Map<String, Set<String>> dependencyMap = new HashMap<>();
        for(String nid : getAllCustomCalcDataPoints()) {
            Set<String> rawDataPoints = dataPointerResolver(nid, dependencyMap, 0);
            dependencyMap.put(nid, rawDataPoints);
        }
        return dependencyMap;
    }

    public static Set<String> dataPointerResolver(String dataPointNid, Map<String, Set<String>> dependencyMap, int tries) {
        if (tries >= MAX_LEVEL) {
            throw new MartException("Exceeded Max Nesting for custom calc data points");
        }

        if (dependencyMap.containsKey(dataPointNid)) {
            return dependencyMap.get(dataPointNid);
        }
        Set<String> reqDataPoints = getDataPointConfig(dataPointNid).getReqDataPoints();
        if (!CollectionUtils.isEmpty(getDataPointConfig(dataPointNid).getReqDataPointsLH())) {
            reqDataPoints.addAll(getDataPointConfig(dataPointNid).getReqDataPointsLH());
        }
        Set<String> resolvedDataPoints = new HashSet<>(reqDataPoints);
        Set<String> customCalcDataPoints = new HashSet<>(reqDataPoints);
        Set<String> allCustomCalcDps = getAllCustomCalcDataPoints();
        customCalcDataPoints.retainAll(allCustomCalcDps);
        resolvedDataPoints.removeAll(allCustomCalcDps);

        if(customCalcDataPoints.isEmpty()) {
            return resolvedDataPoints;
        }

        for(String customDataPointNid : customCalcDataPoints) {
            Set<String> rawDataPoints = dataPointerResolver(customDataPointNid, dependencyMap, tries++);
            resolvedDataPoints.addAll(rawDataPoints);
            dependencyMap.put(customDataPointNid, rawDataPoints);
        }

        return resolvedDataPoints;
    }

    public static Set<String> getRawForCustomDataPoint(String nid) {
        return dataPointDependencyMap.get(nid);
    }

    public static Optional<RescaleInfo> getRescaleInfo(DataPoint dataPoint) {
        if (CustomCalcHelper.isRescaled(dataPoint)) {
            return customCalcConfig.getCustomRescaled().entrySet().stream().filter(rescaleTemplate -> dataPoint.getName().contains(rescaleTemplate.getKey())).map(Map.Entry::getValue).findFirst();
        }
        return Optional.empty();
    }

    public static Optional<LevelInfo> getLevelInfo(DataPoint dataPoint, RescaleInfo rescaleInfo) {
        return rescaleInfo.getLevels().stream().filter(level -> level.getDataPointsInLevel().contains(dataPoint.getNid())).findFirst();
    }

    public static boolean isCustomRescaled(DataPoint dataPoint) {
        return CustomCalcHelper.isDerived(dataPoint) && CustomConfigHelper.getRescaleInfo(dataPoint).isPresent();
    }

    public static boolean isCustomCalcDataPoint(String dataPointNid) {
        return customCalcConfig.getCustomCalcDataPoints().containsKey(dataPointNid);
    }

    public static boolean isBreakdownDataPoint(String nid) {
        return customCalcConfig.getPathToBreakdownValues().containsKey(nid);
    }

    public static DataPointCalcInfo getDataPointConfig(String nid) {
        return customCalcConfig.getCustomCalcDataPoints().get(nid);
    }

    public static Set<String> getAllBreakdowns() {
        return customCalcConfig.getPathToBreakdownValues().keySet();
    }

    public static String getBreakdownPathForNid(String nid){
        return customCalcConfig.getPathToBreakdownValues().get(nid);
    }

    public static Set<String> getAllCustomCalcDataPoints() {
        return customCalcConfig.getCustomCalcDataPoints().keySet();
    }

    public static Set<String> getSumBreakdownValuesForNid(String nid) {
        return customCalcConfig.getCustomCalcDataPoints().get(nid).getSumBreakdownValues();
    }
}
