package com.morningstar.martgateway.domains.delta.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDate;

@Document(indexName = "timeseriesdelta", createIndex = false)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TimeSeriesDelta {

    @Id
    private String id;
    private String instrumentId;
    private String tsType;
    private String dataset;
    private String dataGroup;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyyMMdd")
    @Field(type = FieldType.Date, format = DateFormat.date, pattern = "yyyyMMdd")
    private LocalDate asOfDate;
    private long updateOn;
    private long producerTimeStamp;

}
