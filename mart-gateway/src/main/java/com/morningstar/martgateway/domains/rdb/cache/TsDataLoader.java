package com.morningstar.martgateway.domains.rdb.cache;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataAsyncRepo;
import com.morningstar.martgateway.domains.core.entity.DateRange;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsData;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsDataList;
import com.morningstar.martgateway.domains.rdb.helper.DbHelper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import com.morningstar.martgateway.util.TransformResultUtil;

import java.util.*;
import java.util.Map.Entry;

import com.morningstar.martgateway.util.parsefunction.ParseUtil;
import io.netty.util.internal.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.UNIQUE_ID;

@Slf4j
@RequiredArgsConstructor
public class TsDataLoader extends AbstractDataLoader<String, Collection<TsDataList>>  {

    private static DateTimeFormatter yearMonthFormatter = DateTimeFormatter.ofPattern("yyyy-MM", Locale.US);

    private final RdbDataAsyncRepo rdbDataAsyncRepo;

    @Override
    public Mono<Map<String, Collection<TsDataList>>> queryForData(RdbDataPoint rdbDataPoint, Collection<String> ids, DateRange dateRange, List<String> parameters) {
        String idsAsString = String.join(",", ids);

        log.debug("Get data from rdb sp={}, ids={}", rdbDataPoint.getStoreProcedure(), idsAsString);

        Map<String, Pair<String, String>> colToDpMap = getRdbDpsMap(rdbDataPoint);

        Optional<String> dateColumn = colToDpMap.entrySet().stream().filter(e -> e.getValue().getLeft().equals(rdbDataPoint.getDateColumn())).map(Entry::getKey).findAny();
        if(dateColumn.isEmpty()) {
            log.warn("DateColumn is missing in timeseries data point configuration, group={}", rdbDataPoint .getGroupName());
            return Mono.empty();
        }

        return DbHelper.getRdbData(rdbDataPoint, ids, rdbDataAsyncRepo, dateRange, System.currentTimeMillis(), parameters).flatMap(data -> {
            Map<String, Map<String, TsDataList>> dataById = new HashMap<>();
            boolean hasParams = StringUtils.isNotEmpty(rdbDataPoint.getParamName());

            data.stream()
                    .filter(MapUtils::isNotEmpty)
                    .forEach(map -> {
                        String uniqueId = String.valueOf(map.get(UNIQUE_ID));
                        Map<String, TsDataList> tsDataByDpYearMonth = dataById.computeIfAbsent(uniqueId, i -> new HashMap<>());

                        if (null == map.get(dateColumn.get())) return;
                        LocalDate date = DateFormatUtil.parseLocalDate(String.valueOf(map.get(dateColumn.get())).substring(0, 10));

                        List<String> paramNames = null;
                        if (hasParams)
                            paramNames = Arrays.asList(StringUtils.split(rdbDataPoint.getParamName(), StringUtil.COMMA));
                        String hashKeySuffix = parameterHashKeys(paramNames, map);
                        Map<String, String> primaries = getRdbDataPrimaries(paramNames, map);

                        map.entrySet().stream()
                                .filter(e -> colToDpMap.containsKey(e.getKey()))
                                .forEach(e -> {
                                    String dpId = colToDpMap.get(e.getKey()).getKey();
                                    String hashKey = dpId + ":" + date.format(yearMonthFormatter) + hashKeySuffix;
                                    TsDataList tsDataList = tsDataByDpYearMonth.computeIfAbsent(hashKey, t -> {
                                        TsDataList.TsDataListBuilder builder = TsDataList.<TsData>builder()
                                                    .investmentId(uniqueId)
                                                    .dpId(dpId)
                                                    .values(new ArrayList<>());
                                        if (hasParams)
                                            builder.primaryHash(hashKeySuffix);
                                        return builder.build();
                                    });
                                    if (StringUtils.isEmpty(tsDataList.getPrimaries()) && MapUtils.isNotEmpty(primaries)) {
                                        tsDataList.addPrimariesFromMap(primaries);
                                    }
                                    TsData tsData = TsData.builder()
                                            .dateEpoc(date.toEpochDay())
                                            .value(getFormattedValue(e.getValue(), colToDpMap.get(e.getKey()).getValue()))
                                            .build();
                                    tsDataList.addValueToList(tsData);
                                });
                    });

            return Mono.just(dataById.entrySet().stream().collect(Collectors.toMap(Entry::getKey, b -> b.getValue().values())));
        });
    }

    Map<String, Pair<String, String>> getRdbDpsMap(RdbDataPoint rdbDataPoint) {

        String matchingKey = "";
        Map<String, Pair<String, String>> getRdbDpsMapByGroupName=DataPointRepository.getRdbDpsMapByGroupName(rdbDataPoint.getGroupName());
        Optional<String> dateKey = getRdbDpsMapByGroupName.keySet()
                .stream()
                .filter(key -> key.toLowerCase().contains("date"))
                .findFirst();
        if (dateKey.isPresent()) {
            matchingKey = dateKey.get();
        }
        if (rdbDataPoint.isMulti() && CollectionUtils.isNotEmpty(rdbDataPoint.getSubDataPoints())) {
            Map<String, Pair<String, String>> map = rdbDataPoint.getSubDataPoints()
                    .stream()
                    .collect(Collectors.toMap(
                            DataPoint::getColumn,
                            b -> Pair.of(b.getNid(), null)
                    ));

            map.put(matchingKey, Pair.of(rdbDataPoint.getDateColumn(), null));

            return map;
        }

        return DataPointRepository.getRdbDpsMapByGroupName(rdbDataPoint.getGroupName());
    }

    private String parameterHashKeys(List<String> parameters, Map<String, Object> resultValues) {

        if (CollectionUtils.isEmpty(parameters)) {
            return StringUtils.EMPTY;
        }
        return parameters.stream()
                .map(p -> (String) ParseUtil.parseValue(resultValues.getOrDefault(p, StringUtils.EMPTY), "STRING"))
                .collect(Collectors.joining("_"));
    }

    private Map<String, String> getRdbDataPrimaries(List<String> parameters, Map<String, Object> resultValues) {

        if (CollectionUtils.isEmpty(parameters)) {
            return Collections.emptyMap();
        }

        return parameters.stream()
                .collect(Collectors.toMap(p -> p, p -> (String) ParseUtil.parseValue(resultValues.getOrDefault(p, StringUtils.EMPTY), "STRING")));
    }

    protected String getFormattedValue(Object value, String parseFunction) {
        return TransformResultUtil.formatNumericalValue(value);
    }
}
