package com.morningstar.martgateway.domains.rdb.helper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.martgateway.util.CurrentUtil;

import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.core.common.util.Lz4Util;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
public class RdbCurrentCacheDataHelper {
    private static final String ID = "id";
    private final RedisReactiveRepo appCacheRedisClient;
    private final Semaphore semaphore;

    public RdbCurrentCacheDataHelper(RedisReactiveRepo appCacheRedisClient, Semaphore semaphore) {
        this.appCacheRedisClient = appCacheRedisClient;
        this.semaphore = semaphore;
    }

    public Flux<Map<String, Object>> getCachedData(Set<String> nidSet, Set<String> idList, List<DataPoint> dataPoints, RdbDataPoint rdbDataPoint, boolean useCache) {
        List<String> cacheKeys = getCacheKeys(rdbDataPoint, idList);
        if (useCache && !"3".equals(rdbDataPoint.getRdbCacheFlag())) {
            return getCachedDataList(cacheKeys, nidSet, dataPoints);
        } else {
            return Flux.empty();
        }
    }

    public List<String> getCacheKeys( RdbDataPoint rdbDataPoint, Set<String> idList) {
        String keyPrefix = String.format("rdb:DP_%s/%s:", rdbDataPoint.getIdLevel(), rdbDataPoint.getGroupName());
        return idList.stream().map(id -> keyPrefix + id).collect(Collectors.toList());
    }


    public Flux<Map<String, Object>> getCachedDataList(List<String> cacheKeys, Set<String> nidSet, List<DataPoint> dataPoints) {

        return getCurrentCacheData(cacheKeys, nidSet, dataPoints)
                .filter(Objects::nonNull)
                .map(json -> {
                    String content = JsonUtils.retrieveContent(json);
                    Map<String, Object> objMap = JsonUtils.fromJsonString(content, new TypeReference<>() {
                    });
                    objMap.entrySet().removeIf(entry -> !nidSet.contains(entry.getKey()));
                    return objMap;
                });
    }

    private Flux<String>  getCurrentCacheData(List<String> cacheKeys, Set<String> dpSet, List<DataPoint> dataPoints) {
        Flux<Map<String, Object>> cacheDataFlux = appCacheRedisClient.multiGetHash(cacheKeys, new ArrayList<>(dpSet));

        cacheDataFlux = processCompressedDataPoints(dataPoints, cacheDataFlux);
        cacheDataFlux = processSubDataPoints(dataPoints, cacheDataFlux);

        return cacheDataFlux.map(JsonUtils::toJsonString);
    }

    private Flux<Map<String, Object>> processCompressedDataPoints(List<DataPoint> dataPoints, Flux<Map<String, Object>> cacheDataFlux) {
        Set<String> compressDps = getCompressDpsSet(dataPoints);
        // Process compressed data points (Reactive)
        if (CollectionUtils.isNotEmpty(compressDps)) {
            cacheDataFlux = cacheDataFlux.flatMap(r -> Flux.fromIterable(compressDps)
                    .flatMap(cdp -> {
                        Object data = r.get(cdp);
                        if (data != null) {
                            String val = Lz4Util.decompress(String.valueOf(data));
                            r.put(cdp, val);
                        }
                        return Mono.empty();
                    })
                    .then(Mono.just(r))
            );
        }
        return cacheDataFlux;
    }

    private Flux<Map<String, Object>> processSubDataPoints(List<DataPoint> dataPoints, Flux<Map<String, Object>> cacheDataFlux) {
        Set<String> subDps = CurrentUtil.getSubDataPointsSet(dataPoints);
        subDps.addAll(CurrentUtil.getMultiValueDataPointsSet(dataPoints));

        // Process sub data points (Reactive)
        if (CollectionUtils.isNotEmpty(subDps)) {
            cacheDataFlux = cacheDataFlux.flatMap(e -> Flux.fromIterable(subDps)
                    .flatMap(dp -> processSubDataPointsDataType(e, dp))
                    .then(Mono.just(e))
            );
        }
        return cacheDataFlux;
    }

    private Mono<Void> processSubDataPointsDataType(Map<String, Object> dataMap, String dp) {
        Object objVal = dataMap.get(dp);
        if (objVal != null) {
            String val = String.valueOf(objVal);
            if (val.startsWith("[")) {
                Object res = JsonUtils.fromJsonString(val, List.class);
                dataMap.put(dp, res);
            } else {
                Map<String, Object> data = JsonUtils.fromJsonString(val, Map.class);
                Object res = (data != null) ? List.of(data) : null;
                dataMap.put(dp, res);
            }
        }
        return Mono.empty();
    }

    private static Set<String> getCompressDpsSet(List<DataPoint> dataPoints) {
        return dataPoints.stream().filter(DataPoint::isCompress).map(DataPoint::getNid).collect(Collectors.toSet());
    }

    public void cacheData(List<Map<String, Object>> result, List<DataPoint> dataPoints, RdbDataPoint rdbDataPoint) {
        if (!"3".equals(rdbDataPoint.getRdbCacheFlag()) && semaphore.tryAcquire())  {
            cacheCurrentData(result, dataPoints, rdbDataPoint).doFinally(i -> semaphore.release()).subscribe();
        } else {
            log.debug("semaphore is not available when caching rdb current data. available permits: {}", semaphore.availablePermits());
        }
    }

    private Flux<Object> cacheCurrentData(List<Map<String, Object>> dataList, List<DataPoint> dataPoints, RdbDataPoint rdbDataPoint) {

        boolean isMultipleValue = dataPoints.stream().anyMatch(dp -> dp.getCurrentRdb().isMultipleValues());
        if(isMultipleValue) {
            return cacheMultiValueData(dataList, dataPoints, rdbDataPoint);
        }

        // Convert the dataList into a reactive Flux and process it
        return Flux.fromIterable(dataList)
                .flatMap(map -> {
                    // Convert the current cache data for each map
                    Pair<String, Map<String, String>> cacheData = convertCurrentCacheData(map, dataPoints, rdbDataPoint);
                    return Mono.just(cacheData);
                })
                .collectMap(Pair::getLeft, Pair::getRight)  // Collect into a Map<String, Map<String, Object>>
                .flatMapMany(dataMap -> appCacheRedisClient.multiSetHash(dataMap, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint))
                );
    }

    private Flux<Object> cacheMultiValueData(List<Map<String, Object>> dataList, List<DataPoint> dataPoints, RdbDataPoint rdbDataPoint) {
        return Flux.fromIterable(dataList).filter(map -> map.containsKey(ID))
                .groupBy((map) -> map.get(ID)).
                flatMap((groupedFlux) -> groupedFlux.collectList().flatMap((data) -> {
                    Pair<String, Map<String, String>> cacheData = convertMultiValueCurrentCacheData(data, dataPoints, rdbDataPoint);
                    return Mono.just(cacheData);
                }))
                .collectMap(Pair::getLeft, Pair::getRight)  // Collect into a Map<String, Map<String, Object>>
                .flatMapMany(dataMap -> appCacheRedisClient.multiSetHash(dataMap, RdbDataPointHelper.getCacheExpireTime(rdbDataPoint)));
    }

    Pair<String, Map<String, String>> convertCurrentCacheData(Map<String, Object> data, List<DataPoint> dataPoints, RdbDataPoint rdbDataPoint) {
        Set<String> subDps = CurrentUtil.getSubDataPointsSet(dataPoints);
        Set<String> compressDps = getCompressDpsSet(dataPoints);

        Map<String, String> cacheData = data.entrySet().stream().collect(Collectors.toMap(Entry::getKey,
                v -> {
                    Object objVal = v.getValue();
                    String newValue;
                    if (subDps.contains(v.getKey()) && objVal != null) {
                        String json = JsonUtils.toJsonString(objVal);
                        newValue = (json.length() > 2) ? json : "{}";
                    }
                    else{
                        newValue =  String.valueOf(objVal);
                    }

                    if (compressDps.contains(v.getKey())) {
                        newValue = Lz4Util.compress(newValue);
                    }
                    return newValue;
                }
        ));
        return Pair.of(getCacheKey(rdbDataPoint.getIdLevel(), rdbDataPoint.getGroupName(), String.valueOf(cacheData.get(ID))), cacheData);
    }


    Pair<String, Map<String, String>> convertMultiValueCurrentCacheData(List<Map<String, Object>> data, List<DataPoint> dataPoints, RdbDataPoint rdbDataPoint) {

        Map<String, String> cacheData = new HashMap<>();

        // Find the requested multi value datapoint
        Optional<DataPoint> multiValueDp = dataPoints.stream().filter(dp -> dp.isMultipleValueDataPoint(DataPoint::getCurrentRdb) && data.stream().anyMatch(m -> m.containsKey(dp.getNid()))).findFirst();

        String id = String.valueOf(data.get(0).get(ID));
        Map<String, List<Map<String, String>>> combinedData = new HashMap<>();
        if(multiValueDp.isPresent()) {
            data.forEach(singleData -> singleData.forEach((dpId, value) -> {
                if  (!dpId.equals("id")) {
                    List<Map<String, String>> values = combinedData.getOrDefault(dpId, new ArrayList<>());
                    Map<String, String> map = new HashMap<>();
                    map.put(dpId, value.toString());
                    values.add(map);
                    combinedData.put(dpId, values);
                }
            }));
            combinedData.forEach((key, value) -> cacheData.put(key, JsonUtils.toJsonString(value)));
            cacheData.put(ID, id);
        } else {
            log.debug("Could not find multi-value datapoint while writing to cache");
        }
        return Pair.of(getCacheKey(rdbDataPoint.getIdLevel(), rdbDataPoint.getGroupName(), id), cacheData);
    }

    private String getCacheKey(String idLevel, String groupName, String id) {
        return String.format("rdb:DP_%s/%s:%s", idLevel, groupName, id);
    }
}
