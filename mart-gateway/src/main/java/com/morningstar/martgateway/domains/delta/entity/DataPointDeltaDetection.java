package com.morningstar.martgateway.domains.delta.entity;

import lombok.Getter;

import java.util.Objects;

@Getter
public final class DataPointDeltaDetection {
    private final DeltaDataPoint deltaDataPoint;
    private final DeltaDetection deltaDetection;

    public DataPointDeltaDetection(DeltaDataPoint deltaDataPoint, DeltaDetection deltaDetection) {
        this.deltaDataPoint = deltaDataPoint;
        this.deltaDetection = deltaDetection;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DataPointDeltaDetection that)) return false;
        return Objects.equals(deltaDataPoint, that.deltaDataPoint) && Objects.equals(deltaDetection, that.deltaDetection);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deltaDataPoint, deltaDetection);
    }
}
