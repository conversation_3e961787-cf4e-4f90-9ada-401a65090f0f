package com.morningstar.martgateway.domains.apiproxy.entity;

import com.morningstar.dataac.martgateway.core.common.util.DateUtil;

import java.time.LocalDate;
import java.time.Year;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

public class FrequencyTypeYearly  implements IFrequencyType{

    @Override
    public Date getActualFirstDateByFrequency(Date date, String frequency, boolean isFirstOrLast) {
        return isFirstOrLast ? DateUtil.getFirstDateOfYear(date) : DateUtil.getLastDayOfYear(date);
    }

    @Override
    public Date nextEndDate(Date date, String frequency) {
        LocalDate localDate = DateUtil.asLocalDate(date);
        localDate = localDate.plusYears(1);
        return DateUtil.asDate(localDate);
    }

    @Override
    public LocalDate getActualFirstDateByFrequency(LocalDate date, String frequency, boolean isFirstOrLast) {
        return isFirstOrLast ? date.with(TemporalAdjusters.firstDayOfYear()) : date.with(TemporalAdjusters.lastDayOfYear());
    }

    @Override
    public LocalDate nextEndDate(LocalDate date, String frequency) {
        return date.plusYears(1);
    }

    @Override
    public boolean checkLegalDate(Integer stepSize, String frequency, long y, long m, long w, long ds) {
        return y - stepSize >= 0;
    }

    @Override
    public LocalDate getEndDateByWindowSize(LocalDate startDate, long windowSize) {
        return Year.of(startDate.getYear()).plusYears(windowSize - 1).atMonth(12).atEndOfMonth();
    }

    @Override
    public LocalDate getStartDateByWindowSize(LocalDate endDate, long windowSize) {
        return Year.of(endDate.getYear()).plusYears(-1 * (windowSize - 1)).atMonth(1).atDay(1);
    }

    @Override
    public LocalDate getRollingWindowNextStartDate(LocalDate startDate, long stepSize) {
        return Year.of(startDate.getYear()).plusYears(stepSize).atMonth(1).atDay(1);
    }

    @Override
    public LocalDate getForwardExtendingNextEndDate(LocalDate endDate, long stepSize) {
        return Year.of(endDate.getYear()).plusYears(stepSize).atMonth(12).atEndOfMonth();
    }

    @Override
    public LocalDate getBackwardExtendingPreviousStartDate(LocalDate startDate, long stepSize) {
        return Year.of(startDate.getYear()).plusYears(stepSize * -1).atDay(1);
    }

}