package com.morningstar.martgateway.domains.tscacheproxy.service;

import com.google.protobuf.InvalidProtocolBufferException;
import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import java.nio.ByteBuffer;
import java.util.concurrent.TimeUnit;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.tcp.TcpClient;

public class TsCacheProxyService {

    private final WebClient tsClient;
    private static final int MAX_WAIT = 5;

    public TsCacheProxyService(String tsCache) {
        ExchangeStrategies exchangeStrategies = ExchangeStrategies.builder()
                .codecs(configure -> configure.defaultCodecs().maxInMemorySize(-1)).build();
        TcpClient timeoutClient = TcpClient.create()
                .option(ChannelOption.SO_KEEPALIVE, Boolean.TRUE)
                .option(ChannelOption.TCP_NODELAY, Boolean.TRUE)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, MAX_WAIT * 1000)//Connect Timeout
                .doOnConnected(
                        c -> c.addHandlerLast(new ReadTimeoutHandler(50, TimeUnit.SECONDS))
                                .addHandlerLast(new WriteTimeoutHandler(MAX_WAIT, TimeUnit.SECONDS)));
        this.tsClient = WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(HttpClient.from(timeoutClient)))
                .exchangeStrategies(exchangeStrategies)
                .baseUrl(tsCache)
                .build();
    }

    public TsCacheProxyService(WebClient tsClient) {
        this.tsClient = tsClient;
    }

    public Flux<TSResponse> getTsCacheData(TSRequest tsRequest) {
        int originalFormat = tsRequest.getFormat();
        tsRequest.setFormat(0);
        Mono<TSResponse> response = tsClient.get()
                .uri(tsRequest.getParameters())
                .retrieve()
                .onStatus(HttpStatus::is4xxClientError, clientResponse -> Mono.empty())
                .onStatus(HttpStatus::is5xxServerError, clientResponse -> Mono.empty())
                .toEntity(ByteBuffer.class)
                .map(tsCacheBinaryResponse -> TSResponse.fromProtobuf(parseTimeSeriesDataProtobuf(tsCacheBinaryResponse.getBody())));
        tsRequest.setFormat(originalFormat);
        return Flux.from(response);
    }

    private TimeSeriesDatas parseTimeSeriesDataProtobuf(ByteBuffer protobufMsg) {
        try {
            return TimeSeriesDatas.parseFrom(protobufMsg);
        } catch (InvalidProtocolBufferException e) {
            throw new MartException("Protobuf parse failure", e);
        }
    }

    public Mono<ResponseEntity<String>> getTsCacheDataPostResponse(String tsRequestBody) {
        return tsClient.post()
                .bodyValue(tsRequestBody)
                .retrieve()
                .onStatus(HttpStatus::is4xxClientError, clientResponse -> Mono.empty())
                .onStatus(HttpStatus::is5xxServerError, clientResponse -> Mono.empty())
                .toEntity(String.class);
    }
}
