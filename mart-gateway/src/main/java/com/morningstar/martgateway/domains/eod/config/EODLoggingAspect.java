package com.morningstar.martgateway.domains.eod.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.util.List;

@Slf4j
@Aspect
public class EODLoggingAspect {

    @Around("execution(* com.morningstar.martgateway.domains.eod.repository.EODRepo.executeSQL(..))")
    public Object postgresPerformanceLogger(ProceedingJoinPoint joinPoint) throws Throwable {
        return performanceLogger(joinPoint, "Postgres");
    }

    @Around("execution(* com.morningstar.martgateway.domains.eod.repository.EODMongodbRepo.findAll(..))")
    public Object mongodbPerformanceLogger(ProceedingJoinPoint joinPoint) throws Throwable {
        return performanceLogger(joinPoint, "Mongodb");
    }

    private Object performanceLogger(ProceedingJoinPoint joinPoint, String datasource) throws Throwable {
        long start = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long timeSpent = System.currentTimeMillis() - start;

        if (result instanceof List) {
            int total = ((List<?>) result).size();
            String msg = String.format("retrieve eod data from %s : total_number_of_records=%s time_spent=%sms", datasource, total, timeSpent);
            if (timeSpent > 100) {
                log.info(msg);
            } else {
                log.debug(msg);
            }
        }

        return result;
    }

    @Around("execution(* com.morningstar.entitlement.eod.data.interfaces.EntitlementEODDataService.retrieveUserInvestments(..))")
    public Object eodEntitlePerformanceLogger(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long timeSpent = System.currentTimeMillis() - start;
        String msg = String.format("event=timer func=EntitlementEODDataService.retrieveUserInvestments() time=%sms", timeSpent);
        if (timeSpent > 100) {
            // only log info level in production when time spent is larger than 100ms
            log.info(msg);
        } else {
            log.debug(msg);
        }

        return result;
    }
}
