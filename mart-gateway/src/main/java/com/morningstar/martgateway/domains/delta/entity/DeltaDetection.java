package com.morningstar.martgateway.domains.delta.entity;

import lombok.Getter;

import java.util.Objects;

@Getter
public class DeltaDetection {

    private final String investmentId;
    private final String detectionDate;

    public DeltaDetection(String investmentId, String detectionDate) {
        this.investmentId = investmentId;
        this.detectionDate = detectionDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DeltaDetection that)) return false;
        return Objects.equals(investmentId, that.investmentId) && Objects.equals(detectionDate, that.detectionDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(investmentId, detectionDate);
    }
}
