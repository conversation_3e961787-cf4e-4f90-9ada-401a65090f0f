package com.morningstar.martgateway.domains.calclib;

import com.morningstar.dataac.martgateway.core.calculationlib.CalculationLibConversionService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.calc.Calculation;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.applications.rdb.RdbGateway;
import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.calculationlib.entity.CalcMetaData;
import com.morningstar.martgateway.domains.current.CurrentManager;
import com.morningstar.martgateway.domains.rdb.service.RdbTsService;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.service.TScacheService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import com.morningstar.dataac.martgateway.core.calculationlib.util.CalculationUtil;
import com.morningstar.martgateway.util.CurrentUtil;
import com.morningstar.martgateway.util.DataPointUtils;
import com.morningstar.martgateway.util.MultiUniverseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper.CURCHANGE;
import static com.morningstar.dataac.martgateway.core.calculationlib.conversion.CurrencyConversionHelper.PERFORMANCEDATE;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute.EVENT_TYPE;

@Slf4j
public class CalculationLibService {

    private final CurrentManager currentManager;
    private final TScacheService tscacheService;
    private final CalculationLibConversionService calculationLibConversionService;
    private final RdbGateway rdbGateway;

    private final RdbTsService rdbTsService;

    public CalculationLibService(CurrentManager currentManager, TScacheService tscacheService,
                                 CalculationLibConversionService calculationLibConversionService, RdbGateway rdbGateway,
                                 RdbTsService rdbTsService) {
        this.currentManager = currentManager;
        this.tscacheService = tscacheService;
        this.calculationLibConversionService = calculationLibConversionService;
        this.rdbGateway = rdbGateway;
        this.rdbTsService = rdbTsService;
    }

    public Flux<Result> retrieveCalcLib(CalcRequest request) {
        List<String> requestCalcDps = request.getCalcDps();
        if (requestCalcDps.isEmpty()) {
            return Flux.empty();
        }
        LogEntry.info(new LogEntity(EVENT_TYPE, "Calculation"), new LogEntity(EVENT_DESCRIPTION, "Calling calc Lib"));
        CalcMetaData calcMetaData = buildMetaDataPointInfo(request);

        Set<DataPoint> currentDpSet = requestCalcDps.stream().map(DataPointRepository::getByNid)
                .filter(d -> d.isCurrentDataPoint() || (d.isRdbDataPoint() && !request.isTimeseriesRequest()))
                .collect(Collectors.toSet());
        Set<DataPoint> tsDpSet = requestCalcDps.stream().map(DataPointRepository::getByNid)
                .filter(d -> d.isTimeSerialPoint() || (d.isRdbDataPoint() && request.isTimeseriesRequest()))
                .collect(Collectors.toSet());

        Flux<Result> currentResultFlux = getCurrentData(request, currentDpSet, tsDpSet, calcMetaData)
                .map(result -> extractMap(result, calcMetaData));
        Flux<Result> timeSeriesResultFlux = getTimeSeriesData(request, tsDpSet);

        return CalculationUtil.mergeResultsWithSameIds(Flux.merge(currentResultFlux, timeSeriesResultFlux))
                .map(result -> convertResultsByType(result, request, currentDpSet, tsDpSet, calcMetaData))
                .map(result -> removeReferenceDatapoints(result, request))
                .flatMap(this::divideResults)
                .filter(result -> !result.getValues().isEmpty());
    }

    /**
     * perform currency conversion for current result, group result and time series result
     */
    private Result convertResultsByType(Result result, CalcRequest request, Set<DataPoint> currentDpSet,
                                        Set<DataPoint> tsDpSet, CalcMetaData calcMetaData) {
        //NOTICE: Refactor this with pattern matching in JDK21
        if (result instanceof CurrentResult currentResult) {
            return calculationLibConversionService.convertCurrent(currentResult, currentDpSet,
                    request.getCurrency(), calcMetaData);
        }
        if (result instanceof GroupResult groupResult) {
            return calculationLibConversionService.convertGroup(groupResult, currentDpSet,
                    request.getCurrency(), calcMetaData);
        }
        if (result instanceof TimeSeriesResult timeSeriesResult) {
            return calculationLibConversionService.convertTS(timeSeriesResult, tsDpSet, request.getCurrency(), request.getPreCurrency(), calcMetaData);
        }
        return result;
    }

    private CalcMetaData buildMetaDataPointInfo(CalcRequest request) {
        return CalcMetaData.builder()
                .baseCurrencyMap(getBaseCurrencyDps(request))
                .endDateDpMap(getEndDateDps(request))
                .secIdCurrencyMap(new HashMap<>())
                .currencyChangeMap(new HashMap<>())
                .inceptionMap(new HashMap<>())
                .build();
    }

    private Map<String, DataPoint> getEndDateDps(CalcRequest request) {
        if (CollectionUtils.isEmpty(request.getIdMappers())) {
            return request.getCalcDps().stream()
                    .map(DataPointRepository::getByNid)
                    .filter(dp -> dp.getCalculation().getEndDate() != null)
                    .collect(Collectors.toMap(
                            DataPoint::getNid,
                            dp -> dp.getCalculation().getEndDate(),
                            (oldDp, newDp) -> oldDp)
                    );
        }
        return getEndDateDpsByUniverse(request);
    }

    private Map<String, DataPoint> getEndDateDpsByUniverse(CalcRequest request) {
        Map<String, DataPoint> endDateDpMap = new HashMap<>();
        for (String dp : request.getCalcDps()) {
            DataPoint dataPoint = DataPointRepository.getByNid(dp);
            getEndDateDpByUniverse(dataPoint, request.getIdMappers().get(0))
                    .ifPresent(endDateDp -> endDateDpMap.put(dp, endDateDp));
        }
        return endDateDpMap;
    }

    private Optional<DataPoint> getEndDateDpByUniverse(DataPoint dataPoint, IdMapper idMapper) {
        Optional<DataPoint> endDateDpOpt = Optional.ofNullable(dataPoint.getCalculation().getEndDate());
        return endDateDpOpt.flatMap(endDateDp -> MultiUniverseUtil.getDataPointForApplicableMultipleUniverse(endDateDp, idMapper, false))
                .or(() -> endDateDpOpt);
    }

    private Map<String, DataPoint> getBaseCurrencyDps(CalcRequest request) {
        DataPoint defaultBaseCurrency = DataPointRepository.getByNid(CurrencyConversionHelper.BASECUR);
        if (CollectionUtils.isEmpty(request.getIdMappers())) {
            return request.getCalcDps().stream()
                    .map(DataPointRepository::getByNid)
                    .filter(dp -> dp.getCalculation() != null)
                    .collect(Collectors.toMap(DataPoint::getNid,
                            dp -> Optional.ofNullable(dp.getCalculation().getCur()).orElse(defaultBaseCurrency),
                            (oldDp,newDp) -> oldDp)
                    );
        }
        return getBaseCurrencyDpsByUniverse(request);
    }

    private Map<String, DataPoint> getBaseCurrencyDpsByUniverse(CalcRequest request) {
        DataPoint defaultBaseCurrency = DataPointRepository.getByNid(CurrencyConversionHelper.BASECUR);
        Map<String, DataPoint> baseCurrencyMap = new HashMap<>();
        for (String dp : request.getCalcDps()) {
            DataPoint dataPoint = DataPointRepository.getByNid(dp);
            Optional<DataPoint> currencyDpOpt = Optional.ofNullable(dataPoint.getCalculation()).map(Calculation::getCur);
            DataPoint baseCurrencyDp = defaultBaseCurrency;
            if (currencyDpOpt.isPresent()) {
                DataPoint currencyDp = currencyDpOpt.get();
                baseCurrencyDp = getBaseCurrencyDpByUniverse(request, currencyDp, defaultBaseCurrency);
            }
            baseCurrencyMap.put(dp, baseCurrencyDp);
        }
        return baseCurrencyMap;
    }

    private DataPoint getBaseCurrencyDpByUniverse(CalcRequest request, DataPoint currencyDp, DataPoint defaultBaseCurrency) {
        if (CollectionUtils.isEmpty(currencyDp.getMultiUniverseList())) {
            return currencyDp;
        }
        return MultiUniverseUtil.getDataPointForApplicableMultipleUniverse(currencyDp, request.getIdMappers().get(0), false)
                    .orElse(defaultBaseCurrency);
    }

    private Flux<Result> getCurrentData(CalcRequest request,
                                        Set<DataPoint> currentDpSet,
                                        Set<DataPoint> tsDpSet,
                                        CalcMetaData calcMetaData) {
        List<IdMapper> idMappers = request.getIdMappers();
        Set<DataPoint> currentDpsToDownload = new HashSet<>();
        //get history currency for ts datapoints
        if (!tsDpSet.isEmpty()) {
            currentDpsToDownload.add(DataPointRepository.getByNid(CURCHANGE));
        }
        //get inception date for since inception datapoints
        boolean hasSinceInception = currentDpSet.stream().anyMatch(dataPoint ->
                dataPoint.getCalculation().getTrailingPeriod() != null && dataPoint.getCalculation().getTrailingPeriod().equals("-1"));
        if (hasSinceInception) {
            currentDpsToDownload.add(DataPointRepository.getByNid(PERFORMANCEDATE));
        }
        //get base currency for all datapoints
        calcMetaData.getBaseCurrencyMap().forEach((nid, currencyDp) -> currentDpsToDownload.add(currencyDp));
        //get raw data for all requested current datapoints
        currentDpsToDownload.addAll(currentDpSet);
        //get enddate datapoint for current datapoints
        currentDpSet.forEach(dp -> currentDpsToDownload.add(calcMetaData.getEndDateDpMap().getOrDefault(dp.getNid(), null)));
        currentDpSet.forEach(dataPoint -> {
            currentDpsToDownload.add(dataPoint.getCalculation().getPenceTraded());
            currentDpsToDownload.add(dataPoint.getCalculation().getCcy());
        });

        currentDpsToDownload.remove(null);
        List<DataPoint> dataPoints = currentDpsToDownload.stream().filter(d -> !d.isRdbDataPoint()).collect(Collectors.toList());

        Set<String> subDatapoints = DataPointUtils.getSubDataPoints(dataPoints);
        List<DataPoint> rdbDataPoints = DataPointUtils.getRdbCalcDataPoints(new ArrayList<>(currentDpsToDownload), subDatapoints);
        List<DataPoint> rdbGroupDataPoints = DataPointUtils.getRdbGroupedCalcDataPoints(new ArrayList<>(currentDpsToDownload), subDatapoints);

        List<DataPoint> nonGroupDataPoints = dataPoints.stream().filter(dataPoint -> !dataPoint.isGroup())
                .collect(Collectors.toList());
        List<DataPoint> groupDataPoints = dataPoints.stream().filter(DataPoint::isGroup).collect(Collectors.toList());

        MartRequest martRequest = MartRequest.builder().readCache(request.getReadCache()).ids(request.getIdList()).currency(request.getCurrency()).idMappers(idMappers).build();
        return Flux.merge(currentManager.retrieveCurrentResult(request.getIdList(), nonGroupDataPoints, true, idMappers),
                currentManager.retrieveGroupResult(request.getIdList(), new HashSet<>(), groupDataPoints, true, idMappers),
                rdbGateway.retrieve(martRequest, rdbDataPoints, rdbGroupDataPoints));
    }

    private Flux<Result> divideResults(Result mergedResult) {
        Map<String, String> currentResultMap = new HashMap<>();
        Map<String, List<V>> timeSeriesResultMap = new HashMap<>();
        mergedResult.getValues().forEach((key, value) -> {
            String nid = String.valueOf(key);
            if (DataPointRepository.getByNid(nid).isTimeSerialPoint() && value instanceof List list) {
                timeSeriesResultMap.put(nid, list);
            } else {
                currentResultMap.put(nid, String.valueOf(value));
            }
        });
        return Flux.just(new CurrentResult(mergedResult.getId(), currentResultMap),
                new TimeSeriesResult(mergedResult.getId(), timeSeriesResultMap));
    }

    private Flux<Result> getTimeSeriesData(CalcRequest calcRequest,
                                           Set<DataPoint> tsDpSet) {
        List<IdMapper> idMappers = calcRequest.getIdMappers();
        List<String> tsDpList = new ArrayList<>();
        //add endDate dpId
        tsDpSet.stream().filter(d -> !d.isRdbDataPoint()).toList().forEach(dataPoint -> {
            DataPoint endDate = dataPoint.getCalculation().getEndDate();
            if (endDate != null) {
                tsDpList.add(endDate.getId());
            }
            tsDpList.add(dataPoint.getId());
        });
        List<DataPoint> tsRdbDpList = tsDpSet.stream().filter(DataPoint::isRdbDataPoint).collect(Collectors.toList());

        TSRequest request = TSRequest.builder()
                .endDate(calcRequest.getEndDate())
                .format(1)
                .startDate(calcRequest.getStartDate())
                .adjustment(calcRequest.getAdjustment())
                .secIds(calcRequest.getIdList()
                        .stream()
                        .filter(id -> StringUtils.hasText(id) && !"null".equalsIgnoreCase(id))
                        .collect(Collectors.joining(",")))
                .dataId(String.join(",", tsDpList))
                .build();

        MartRequest martRequest = MartRequest.builder().startDate(calcRequest.getStartDate()).endDate(calcRequest.getEndDate()).readCache(calcRequest.getReadCache()).ids(calcRequest.getIdList()).idMappers(idMappers).build();
        return Flux.merge(tscacheService.retrieveTimeSeriesData(request), rdbTsService.loadBatchedData(martRequest, tsRdbDpList, CurrentUtil.getReadCache(calcRequest.getReadCache())));
    }

    private Result extractMap(Result result, CalcMetaData calcMetaData) {

        Map<String, Object> dpMap = result.getValues();
        String secId = result.getId();
        for (Map.Entry<String, Object> dp : dpMap.entrySet()) {
            String dpKey = dp.getKey();
            Object dpValue = dp.getValue();

            if (dpValue == null) {
                continue;
            }

            extractMapFromBaseCurrency(secId, dpKey, dpValue, calcMetaData);

            if (dpKey.equals(CURCHANGE) && dpValue instanceof List results) {
                calcMetaData.getCurrencyChangeMap().put(secId + CURCHANGE, results);
            }

            if (dpKey.equals(PERFORMANCEDATE)) {
                calcMetaData.getInceptionMap().put(secId + PERFORMANCEDATE, dpValue.toString());
            }
        }

        return result;
    }

    private void extractMapFromBaseCurrency(String secId, String dpKey, Object dpValue, CalcMetaData calcMetaData) {
        for (Map.Entry<String, DataPoint> entry : calcMetaData.getBaseCurrencyMap().entrySet()) {
            String key = secId + entry.getKey();
            DataPoint baseCurr = entry.getValue();
            if (dpKey.equals(baseCurr.getNid()) && StringUtils.hasText(dpValue.toString())) {
                calcMetaData.getSecIdCurrencyMap().put(key, dpValue.toString().trim());
            }
            if (dpKey.equals(CURCHANGE) && dpValue instanceof List results) {
                calcMetaData.getCurrencyChangeMap().put(key, results);
            }
        }
    }

    private Result removeReferenceDatapoints(Result result, CalcRequest request) {
        List<String> referenceIdList = new ArrayList<>();
        Map<String, Object> resultMap = result.getValues();
        for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
            String dataPointId = entry.getKey();
            if (!request.getCalcDps().contains(dataPointId)) {
                referenceIdList.add(dataPointId);
            }
        }
        referenceIdList.forEach(resultMap::remove);
        return result;
    }

}
