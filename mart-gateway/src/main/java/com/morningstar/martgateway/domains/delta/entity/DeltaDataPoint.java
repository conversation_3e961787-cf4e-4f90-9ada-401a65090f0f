package com.morningstar.martgateway.domains.delta.entity;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.DeltaConfiguration;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import lombok.Getter;

import java.util.Objects;

@Getter
public class DeltaDataPoint {

    private final GridviewDataPoint gridviewDataPoint;
    private final DataPoint dataPoint;
    private final DeltaConfiguration deltaConfiguration;

    public DeltaDataPoint(GridviewDataPoint gridviewDataPoint, DataPoint dataPoint) {
        this.gridviewDataPoint = gridviewDataPoint;
        this.dataPoint = dataPoint;
        this.deltaConfiguration = selectDeltaConfiguration(gridviewDataPoint, dataPoint);
    }

    public static boolean isDeltaCapable(GridviewDataPoint gridviewDataPoint, DataPoint dataPoint) {
        return isValidMostRecentRdbDp(gridviewDataPoint, dataPoint) ||
                isValidTimeSeriesRdbDp(gridviewDataPoint, dataPoint);
    }

    private DeltaConfiguration selectDeltaConfiguration(GridviewDataPoint gridviewDataPoint, DataPoint dataPoint) {
        DeltaConfiguration configuration = null;
        if (isValidMostRecentRdbDp(gridviewDataPoint, dataPoint)) {
            configuration = dataPoint.getCurrentRdb().getDeltaConfiguration();
        }
        if (isValidTimeSeriesRdbDp(gridviewDataPoint, dataPoint)) {
            configuration = dataPoint.getTsRdb().getDeltaConfiguration();
        }
        return configuration;
    }

    private static boolean isValidMostRecentRdbDp(GridviewDataPoint gridviewDataPoint, DataPoint dataPoint) {
        return !gridviewDataPoint.isTimeSeries() && dataPoint.isRdbDataPoint() && dataPoint.getCurrentRdb() != null && dataPoint.getCurrentRdb().getDeltaConfiguration() != null;
    }

    private static boolean isValidTimeSeriesRdbDp(GridviewDataPoint gridviewDataPoint, DataPoint dataPoint) {
        return gridviewDataPoint.isTimeSeries() && dataPoint.isRdbDataPoint() && dataPoint.getTsRdb() != null && dataPoint.getTsRdb().getDeltaConfiguration() != null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DeltaDataPoint that)) return false;
        return Objects.equals(gridviewDataPoint, that.gridviewDataPoint);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(gridviewDataPoint);
    }
}
