package com.morningstar.martgateway.domains.current;

import com.morningstar.dataac.martgateway.core.common.util.JsonUtils;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;
import com.morningstar.martgateway.domains.core.entity.MasterHeader;
import com.morningstar.martgateway.infrastructures.repo.data.S3DataRepo;
import com.morningstar.martgateway.util.CurrentUtil;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.data.redis.core.RedisTemplate;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public class CurrentService {

    private final S3DataRepo s3DataRepo;
    private final RedisTemplate<String, String> syncDataStorageTemplate;
    private final RedisTemplate<String, String> syncDataCacheTemplate;

    public CurrentService(S3DataRepo s3DataRepo,
            RedisTemplate<String, String> syncDataStorageTemplate,
            RedisTemplate<String, String> syncDataCacheTemplate) {
        this.s3DataRepo = s3DataRepo;
        this.syncDataStorageTemplate = syncDataStorageTemplate;
        this.syncDataCacheTemplate = syncDataCacheTemplate;
    }

    /**
     * @param groupName data group name which the retrieved data point is belong to
     * @param idList sec id list of data to be retrieved
     * @param dps data point list
     * @param readCache read cache or not for data stored in s3; used in testing
     * @return data of result packaged in flow
     */
    protected Flux<MasterHeader> getFullDataByIdList(String groupName, List<String> idList, List<DataPoint> dps, boolean readCache, List<IdMapper> idMappers) {
        Flux<MasterHeader> fullData = Flux.fromIterable(idMappers)
                .flatMap(mapper -> getFullDataById(groupName, mapper, dps, readCache));
        return fullData.map(masterHeader -> CurrentUtil.suppressLegacyAttributes(masterHeader, dps));
    }

    private Flux<MasterHeader> getFullDataById(String groupName, IdMapper idMapper, List<DataPoint> dps, boolean readCache) {
        Map<String, List<DataPoint>> groupedDataPoint = dps.stream()
                .collect(Collectors.groupingBy(DataPoint::getIdLevel));
        return Flux.fromIterable(groupedDataPoint.keySet())
                .publishOn(SchedulerConfiguration.getScheduler())
                .map(idLevel -> CurrentUtil.getStorageKey(idLevel, idMapper, groupName))
                .flatMap(s3Key -> {
                    if (s3Key.endsWith("/")) {
                        return Mono.just(new MasterHeader(idMapper.getInvestmentId(), new JSONObject()));
                    } else {
                        return retrieveDataContent(s3Key, readCache, idMapper.getInvestmentId());
                    }
                });
    }

    private Mono<? extends MasterHeader> retrieveDataContent(String keyName, boolean readCache, String investmentId) {
        String dataKeyPrefix = keyName.substring(0, keyName.lastIndexOf('/'));
        if (DataPointRepository.getDataSource(dataKeyPrefix).equals("s3")) {
            if (!readCache) {
                return retrieveS3Object(keyName, investmentId);
            } else {
                String value = syncDataCacheTemplate.opsForValue().get("DP_" + keyName);
                if (value == null) {
                    return retrieveS3Object(keyName, investmentId);
                } else {
                    JSONObject object = JsonUtils.retrieveStoredContent(value);
                    return Mono.just(new MasterHeader(investmentId, object));
                }
            }
        } else {
            String result = syncDataStorageTemplate.opsForValue().get("DP_" + keyName);
            JSONObject object;
            if (!StringUtils.isEmpty(result)) {
                object = JsonUtils.retrieveStoredContent(result);
            } else {
                object = new JSONObject();
            }
            return Mono.just(new MasterHeader(investmentId, object));
        }
    }

    private Mono<? extends MasterHeader> retrieveS3Object(String s3Key, String investmentId) {
        return s3DataRepo.call(s3Key)
                .doOnNext(value -> syncDataCacheTemplate.opsForValue().set("DP_" + s3Key, value, CurrentUtil.getRandomExpireTime()))
                .map(value -> new MasterHeader(investmentId, JsonUtils.retrieveStoredContent(value)));
    }
}