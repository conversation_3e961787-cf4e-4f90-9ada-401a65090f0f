package com.morningstar.martgateway.domains.core.entity;

import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import lombok.Data;


@Data
public class DateRange {
    private String startDate;
    private String endDate;
    private LocalDate localStartDate;
    private LocalDate localEndDate;
    private DateTimeFormatter formatter;

    public DateRange(String startDate, String endDate, String dateFormat) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.formatter = DateTimeFormatter.ofPattern(dateFormat);
        localStartDate = DateFormatUtil.toLocalDateFromStringYYYYMMDD(startDate, formatter);
        localEndDate = DateFormatUtil.toLocalDateFromStringYYYYMMDD(endDate, formatter);
    }
}


