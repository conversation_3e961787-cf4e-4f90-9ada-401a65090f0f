package com.morningstar.martgateway.domains.delta.entity;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.SearchMapping;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Objects;
import java.util.Set;

@Getter
@Builder
public class DeltaSearchParameters {

    private final String idLevel;
    private final Set<String> instrumentIds;
    private final Set<String> datasets;
    private final SearchMapping searchMapping;
    private final LocalDate startDate;
    private final LocalDate endDate;

    public boolean isTimeSeries() {
        return startDate != null && endDate != null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DeltaSearchParameters that)) return false;
        return Objects.equals(idLevel, that.idLevel) && Objects.equals(instrumentIds, that.instrumentIds) && Objects.equals(datasets, that.datasets) && Objects.equals(searchMapping, that.searchMapping) && Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(idLevel, instrumentIds, datasets, searchMapping, startDate, endDate);
    }
}
