package com.morningstar.martgateway.domains.rdb.helper;

import com.google.common.base.Strings;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.function.Function;

/**
 * Helper class to determine whether a DataPoint contains Current or Ts RdbData.
 */
@Slf4j
public class RdbDataPointHelper {

    private static final String ILLEGAL_CACHE_FLAG = "3";
    private static final long CACHE_EXPIRE = 4L;

    private RdbDataPointHelper() {
    }

    /**
     * Prevent MartAPI uses data which configured with flag 3. Only Feedbuilder can use it.
     */
    public static boolean filterMartApiData(DataPoint dataPoint, boolean hasDataRange, boolean isMartApi) {
        if (!isMartApi) return false;
        DataPointType dataPointType = determineRdbDataPointType(dataPoint, hasDataRange);
        if (dataPointType.equals(DataPointType.TS)) {
            return ILLEGAL_CACHE_FLAG.equals(dataPoint.getTsRdb().getRdbCacheFlag());
        }
        if (dataPointType.equals(DataPointType.CURRENT)) {
            return ILLEGAL_CACHE_FLAG.equals(dataPoint.getCurrentRdb().getRdbCacheFlag());
        }
        return true;
    }

    public static RdbDataPoint selectRdbDataPoint(DataPoint dataPoint, boolean hasDataRange, Function<DataPoint, RdbDataPoint> rdbDpSelector) {
        DataPointType dataPointType = determineRdbDataPointType(dataPoint, hasDataRange);
        if (dataPointType.equals(DataPointType.TS) || dataPointType.equals(DataPointType.CURRENT)) return rdbDpSelector.apply(dataPoint);
        throw new MartException(String.format(
                "DataPoint [%s][%s] contains neither Current or TS data!",
                Strings.isNullOrEmpty(dataPoint.getId()) ? "UNDEFINED" : dataPoint.getName(),
                Strings.isNullOrEmpty(dataPoint.getName()) ? "UNNAMED" : dataPoint.getName())
        );
    }

    public static boolean isTimeSeries(DataPoint dataPoint, boolean hasDataRange) {
        DataPointType dataPointType = determineRdbDataPointType(dataPoint, hasDataRange);
        return dataPointType.equals(DataPointType.TS);
    }

    public static boolean containsTimeSeriesDataPoint(List<DataPoint> datapoints, boolean hasDateRange) {
        return datapoints.stream().anyMatch(dp -> isTimeSeries(dp, hasDateRange));
    }

    private static DataPointType determineRdbDataPointType(DataPoint dataPoint, boolean hasDataRange) {
        if (hasCurrentAndTsData(dataPoint)) {
            if (hasDataRange) {
                return DataPointType.TS;
            } else {
                return DataPointType.CURRENT;
            }
        }
        if (hasOnlyCurrentData(dataPoint)) return DataPointType.CURRENT;
        if (hasOnlyTsDataAndDateRange(dataPoint, hasDataRange)) return DataPointType.TS;
        return DataPointType.NONE;
    }

    private static boolean hasCurrentAndTsData(DataPoint dataPoint) {
        return hasCurrentData(dataPoint) && hasTsData(dataPoint);
    }

    private static boolean hasOnlyTsDataAndDateRange(DataPoint dataPoint, boolean isTimeSeries) {
        return hasOnlyTsData(dataPoint) && isTimeSeries;
    }

    private static boolean hasOnlyCurrentData(DataPoint dataPoint) {
        return hasCurrentData(dataPoint) && !hasTsData(dataPoint);
    }

    private static boolean hasOnlyTsData(DataPoint dataPoint) {
        return !hasCurrentData(dataPoint) && hasTsData(dataPoint);
    }

    private static boolean hasCurrentData(DataPoint dataPoint) {
        return dataPoint.getCurrentRdb() != null;
    }

    private static boolean hasTsData(DataPoint dataPoint) {
        return dataPoint.getTsRdb() != null;
    }

    private enum DataPointType {
        CURRENT,
        TS,
        NONE
    }

    public static long getCacheExpireTime(RdbDataPoint rdbDataPoint) {

        /* Comment for now until logic finalized 07-15-2024
        if(StringUtils.isNotEmpty(rdbDataPoint.getGroupFrequency())) {
            if(FREQUENCY_DAILY.equals(rdbDataPoint.getGroupFrequency())) {
                return 24L;
            }
            if(FREQUENCY_WEEKLY.equals(rdbDataPoint.getGroupFrequency())) {
                return 7L * 24L;
            }
            if(FREQUENCY_MONTHLY.equals(rdbDataPoint.getGroupFrequency())) {
                return 30L * 24L;
            }
            if(FREQUENCY_QUARTERLY.equals(rdbDataPoint.getGroupFrequency())) {
                return 3L * 30L * 24L;
            }
            if(FREQUENCY_ANNUALLY.equals(rdbDataPoint.getGroupFrequency())) {
                return 365 * 24L;
            }
        }
         */

        if (StringUtils.isNotEmpty(rdbDataPoint.getExpireTime())) {
           return NumberUtils.toLong(rdbDataPoint.getExpireTime());
        }

        return CACHE_EXPIRE;
    }
}
