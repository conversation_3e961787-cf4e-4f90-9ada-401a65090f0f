package com.morningstar.martgateway.domains.rdb.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TSDatapointValueGroup;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeseriesGroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.domains.core.entity.DateRange;
import com.morningstar.martgateway.domains.rdb.cache.TsDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.TsFrequencyDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.entity.TsDataList;
import com.morningstar.martgateway.domains.rdb.cache.event.DataLoadedFromSourceEvent;
import com.morningstar.martgateway.domains.rdb.helper.RdbParameterHelper;
import com.morningstar.martgateway.domains.rdb.helper.RdbTsCacheDataHelper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Map.Entry;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.core.common.util.DateFormatUtil.parseLocalDate;
import static java.util.Objects.requireNonNullElse;


@RequiredArgsConstructor
@Slf4j
public class RdbTsService {
    private final RdbTsCacheDataHelper rdbTsCacheDataHelper;

    private final TsFrequencyDataLoader tsFrequencyDataLoader;

    private final TsDataLoader tsDataLoader;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final boolean isMartApi;

    private static final String ID = "id";
    private static final String DATE = "date";
    private static final int MIN_YEAR = 1900;

    public Flux<Result> loadBatchGroupData(MartRequest martRequest, List<DataPoint> dataPointList, boolean useCache) {
        return loadBatchGroupData(martRequest, dataPointList, useCache, DataPoint::getTsRdb);
    }

    public Flux<Result> loadBatchGroupData(MartRequest martRequest, List<DataPoint> dataPointList, boolean useCache,
                                           Function<DataPoint, RdbDataPoint> rdbDpSelector) {
        if (StringUtils.isEmpty(martRequest.getStartDate()) || StringUtils.isEmpty(martRequest.getEndDate())) {
            return createEmptyTimeSeriesResults(martRequest);
        }

        overwriteDateRange(martRequest);
        DateRange dateRange = new DateRange(martRequest.getStartDate(), martRequest.getEndDate(), DateFormatUtil.YYYY_MM_DD);

        Map<String, List<DataPoint>> groupedDataPoints = getGroupedDataPoints(dataPointList, true, false, rdbDpSelector);

        if (MapUtils.isEmpty(groupedDataPoints)) {
            return createEmptyTimeSeriesResults(martRequest);
        }

        return loadBatchGroupData(martRequest.getIdMappers(), groupedDataPoints, dateRange, rdbDpSelector, useCache, martRequest.getAllParameters());
    }

    Flux<Result> loadBatchGroupData(List<IdMapper> idMappers, Map<String, List<DataPoint>> groupedDataPoints,
                                    DateRange dateRange, Function<DataPoint, RdbDataPoint> rdbDpSelector,
                                    boolean useCache, Map<String, List<String>> parameters) {

        return Flux.fromIterable(idMappers).buffer(100)
                .flatMap(idMapperList -> getGroupData(idMapperList, groupedDataPoints, dateRange, useCache, rdbDpSelector, parameters));
    }

    private Flux<Result> getGroupData(List<IdMapper> idMapperList, Map<String, List<DataPoint>> groupedDataPoints,
                                      DateRange dateRange, boolean useCache,
                                      Function<DataPoint, RdbDataPoint> rdbDpSelector, Map<String, List<String>> parameters) {

        return Flux.fromIterable(idMapperList).buffer(100).
                flatMap(idMapperListData -> getData(idMapperList, groupedDataPoints, dateRange, useCache, rdbDpSelector,true, parameters));
    }

    public Flux<Result> loadBatchedData(MartRequest martRequest, List<DataPoint> dataPointList, boolean useCache) {
        return loadBatchedData(martRequest, dataPointList, useCache, DataPoint::getTsRdb);
    }

    public Flux<Result> loadBatchedData(MartRequest martRequest, List<DataPoint> dataPointList, boolean useCache, Function<DataPoint, RdbDataPoint> rdbDpSelector) {

        if(StringUtils.isEmpty(martRequest.getStartDate()) || StringUtils.isEmpty(martRequest.getEndDate())){
            return createEmptyTimeSeriesResults(martRequest);
        }

        overwriteDateRange(martRequest);
        DateRange dateRange = new DateRange(martRequest.getStartDate(), martRequest.getEndDate(), DateFormatUtil.YYYY_MM_DD);

        if (martRequest.isPostTax()) {
            return handlePostTax(martRequest, useCache, dateRange, dataPointList);
        }

        Map<String, List<DataPoint>> groupedDataPoints = getGroupedDataPoints(dataPointList, true, false, rdbDpSelector);

        if(MapUtils.isEmpty(groupedDataPoints)) {
            return createEmptyTimeSeriesResults(martRequest);
        }
        return loadBatchedData(martRequest.getIdMappers(), groupedDataPoints, dateRange, rdbDpSelector, useCache, martRequest.getAllParameters());
    }

    private void overwriteDateRange(MartRequest martRequest){

        LocalDate startDate = parseLocalDate(martRequest.getStartDate());
        if(LocalDate.of(1900, 1, 1).isAfter(startDate))
            martRequest.setStartDate("1900-01-01");

        LocalDate maximumDate = LocalDate.now().plusYears(5);
        LocalDate endDate = parseLocalDate(martRequest.getEndDate());
        if(maximumDate.isBefore(endDate)){
            martRequest.setEndDate(maximumDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
    }

    public Flux<Result> createEmptyTimeSeriesResults(MartRequest martRequest){
        if(martRequest.getIds() != null) {
            return Flux.fromIterable(martRequest.getIds()).map(id -> new TimeSeriesResult(id, new HashMap<>()));
        }
        return Flux.empty();
    }

    private Flux<Result> handlePostTax(MartRequest martRequest, boolean useCache, DateRange dateRange, List<DataPoint> dataPointList) {
        Map<Boolean, List<IdMapper>> postTaxIdMappers = martRequest.getIdMappers().stream().collect(Collectors.partitioningBy(idMapper -> "1".equals(idMapper.getPostTaxForMixSetting())));

        //grouping of datapoint should be based on IDs if they have PostTax setting or not.
        Map<String, List<DataPoint>> postTaxGroupedDataPoints = getGroupedDataPoints(dataPointList, true, true, DataPoint::getTsRdb);
        Flux<Result> tsDataWithPostTax;
        if(MapUtils.isEmpty(postTaxGroupedDataPoints)) {
            tsDataWithPostTax = createEmptyTimeSeriesResults(martRequest);
        }
        else{
            tsDataWithPostTax = loadBatchedData(postTaxIdMappers.get(true), postTaxGroupedDataPoints, dateRange, this::getPostTaxTimeSeriesRdbDp, useCache, martRequest.getAllParameters());
        }

        Map<String, List<DataPoint>> nonPostTaxGroupedDataPoints = getGroupedDataPoints(dataPointList, true, false, DataPoint::getTsRdb);
        Flux<Result> dataWithoutPostTax;
        if(MapUtils.isEmpty(nonPostTaxGroupedDataPoints)) {
            dataWithoutPostTax = createEmptyTimeSeriesResults(martRequest);
        }
        else{
            dataWithoutPostTax = loadBatchedData(postTaxIdMappers.get(false), nonPostTaxGroupedDataPoints, dateRange, DataPoint::getTsRdb, useCache, martRequest.getAllParameters());
        }
        return Flux.merge(dataWithoutPostTax, tsDataWithPostTax);
    }

    private RdbDataPoint getPostTaxTimeSeriesRdbDp(DataPoint dataPoint) {
        RdbDataPoint postTaxRdb = dataPoint.getPostTaxTsRdb();
        if (postTaxRdb != null) {
            return postTaxRdb;
        }
        return dataPoint.getTsRdb();
    }

    Flux<Result> loadBatchedData(List<IdMapper> idMappers, Map<String, List<DataPoint>> groupedDataPoints, DateRange dateRange, Function<DataPoint, RdbDataPoint> rdbDpSelector, boolean useCache, Map<String, List<String>> parameters) {

        return Flux.fromIterable(idMappers).buffer(100).
                flatMap(idMapperList -> getData(idMapperList, groupedDataPoints, dateRange, useCache, rdbDpSelector,false, parameters));
    }

    private Flux<Result> getData(List<IdMapper> idMapperList, Map<String, List<DataPoint>> groupedDataPoints, DateRange dateRange, boolean useCache, Function<DataPoint, RdbDataPoint> rdbDataPointSelector,boolean subGroup, Map<String, List<String>> parameters) {
        return Flux.fromIterable(groupedDataPoints.entrySet()).flatMap(dataPoints -> {
            DataPoint dataPoint = dataPoints.getValue().get(0);
            RdbDataPoint rdbDataPoint = rdbDataPointSelector.apply(dataPoint);
            Map<String, Set<String>> idMap = getIdMap(idMapperList, rdbDataPoint.getIdLevel());

            String rdbCacheFlag = rdbDataPoint.getRdbCacheFlag();

            if (StringUtils.isEmpty(rdbCacheFlag)) {
                rdbCacheFlag = Objects.isNull(dateRange) ? "1": "2";
                rdbDataPoint.setRdbCacheFlag(rdbCacheFlag);
            }

            Mono<Map<String, Collection<TsDataList>>> cacheData = rdbTsCacheDataHelper.getCacheData(dataPoints.getValue(), rdbDataPoint, idMap.keySet(), dateRange, useCache);

            return cacheData.flatMapMany(cd -> combineCacheDataWithRdbData(dateRange, useCache, dataPoints, rdbDataPoint, idMap, cd,subGroup, parameters));
        });
    }

    private Flux<Result> combineCacheDataWithRdbData(DateRange dateRange, boolean useCache, Entry<String, List<DataPoint>> dataPoints, RdbDataPoint rdbDataPoint, Map<String, Set<String>> idMap, Map<String, Collection<TsDataList>> cd, boolean subGroup, Map<String, List<String>> parameters) {
        Set<String> uniqueIds = idMap.keySet();
        Map<String, String> freqMap = dataPoints.getValue().stream().collect(Collectors.toMap(a -> a.getNid(), b -> requireNonNullElse(b.getTsRdb().getFrequency(), "")));

        if ("1".equals(rdbDataPoint.getRdbCacheFlag()) && useCache) {
            return convertCacheDataToResult(cd, idMap, dateRange, freqMap);
        }
        else {
            Flux<Result> rdbResult = getRDBData(rdbDataPoint, idMap, cd, uniqueIds, freqMap, dateRange, subGroup, parameters);
            Flux<Result> cacheResult = convertCacheDataToResult(cd, idMap, dateRange, freqMap);
            if(subGroup)
            {
                return mergeRDBandCacheGroupData(rdbResult, cacheResult, rdbDataPoint);
            } else return mergeRDBandCacheData(rdbResult, cacheResult, rdbDataPoint);
        }
    }

    private Flux<Result> getRDBData(RdbDataPoint rdbDataPoint, Map<String, Set<String>> idMap, Map<String, Collection<TsDataList>> cd, Set<String> uniqueIds, Map<String, String> datapointsInRequest, DateRange dateRange, boolean subGroup, Map<String, List<String>> parameters) {
        Map<String, Set<String>> idYearMap = createYearIdMap(cd.keySet());
        Set<String> cacheIds = new HashSet<>(uniqueIds);
        DateRange rdbDateRange = getDateRangeForRDBQuery(dateRange.getLocalStartDate().getYear(), dateRange.getLocalEndDate().getYear(), uniqueIds, idYearMap, cacheIds);
        Flux<Result> rdbData = Flux.empty();
        if (cacheIds.size() != uniqueIds.size()) {
            Map<String, Set<String>> noCacheIdMap = getNoCacheIdMap(new ArrayList<>(cacheIds), idMap);
            rdbData = getRDBDataResults(rdbDataPoint, idMap, noCacheIdMap, rdbDateRange, datapointsInRequest, dateRange, subGroup, parameters);
        }
        return rdbData;
    }

    protected Flux<Result> getRDBDataResults(RdbDataPoint rdbDataPoint, Map<String, Set<String>> idMap, Map<String, Set<String>> noCacheIdMap, DateRange rdbDateRange, Map<String, String> datapointsInRequest, DateRange dateRange, boolean subGroup, Map<String, List<String>> parameters) {
        if(subGroup){
            return getRDBData(rdbDataPoint, noCacheIdMap, rdbDateRange, parameters).flatMapMany(m -> Flux.fromIterable(m.entrySet()).flatMap(e -> Flux.fromIterable(
                    idMap.get(e.getKey())).map(key -> TSGroupTransformers.transformToTimeSeriesGroupResult.transform(dateRange.getLocalStartDate(), dateRange.getLocalEndDate(), e.getValue(), key, datapointsInRequest,rdbDataPoint.getId()))));
        }
        return getRDBData(rdbDataPoint, noCacheIdMap, rdbDateRange, parameters).flatMapMany(m -> Flux.fromIterable(m.entrySet()).flatMap(e -> Flux.fromIterable(
                idMap.get(e.getKey())).map(key -> Transformers.transformToTimeSeriesResult.transform(dateRange.getLocalStartDate(), dateRange.getLocalEndDate(), e.getValue(), key, datapointsInRequest))));
    }

    /*
      Returns a DateRange object to be passed into the RDB database query.

       For 'Monthly' frequency, the year for the date range will be in five-year date spans as we
       store increments of five years of data into our cache for monthly frequencies. Also, the
       latestYear is configured to be four years after the earliestYear because we want to ensure
       the dateRange is within the five-year range and not go into the next five-year dataset
       (i.e. 2016-01-01 - 2020-12-31)

       In the event where all years for an id are present in the cache, the DateRange object
       will have a start and end year of 1900 to indicate its values won't be used.
   */
    private DateRange getDateRangeForRDBQuery(int startDateYear, int endDateYear, Set<String> uniqueIds, Map<String, Set<String>> idYearMap, Set<String> cacheIds) {
        int earliestYear = getStartDateYearForMonthlyFrequency(endDateYear);
        startDateYear = getStartDateYearForMonthlyFrequency(startDateYear);
        int latestYear = startDateYear + 4;
        int increment = 5;
        int cacheIdsSize = cacheIds.size();

        for (int year = startDateYear; year <= endDateYear; year += increment) {
            for (String id : uniqueIds) {
                boolean idNotInMapForYear = CollectionUtils.isEmpty(idYearMap.get(String.valueOf(year))) || !idYearMap.get(String.valueOf(year)).contains(id);
                if (idNotInMapForYear) {
                    cacheIds.remove(id);
                    if (earliestYear > year) {
                        earliestYear = year;
                    }
                    if (latestYear < year) {
                        latestYear = year + 4;
                    }
                }
            }
        }
        if (cacheIdsSize == cacheIds.size()) {
            earliestYear = 1900;
            latestYear = 1900;
        }
        return new DateRange(Math.max(earliestYear, MIN_YEAR) + "-01-01", Math.max(latestYear, MIN_YEAR) + "-12-31", "yyyy-MM-dd");
    }

    /*
      Returns the nearest monthly year from the start date year.

      This is calculated by retrieving the year aligned with the 5th digit year (ending in 1 or 6) from the
      year specified in the key. This is because for monthly frequency, we store data in 5-year datasets.
      If the year from the key is 2014, this function will return 2011 for example.
   */
    private int getStartDateYearForMonthlyFrequency(int startYear) {
        int lastDigit = startYear % 10;
        if (lastDigit >= 1 && lastDigit <= 5) {
            return startYear - lastDigit + 1;
        } else if (lastDigit >= 6) {
            return startYear - (lastDigit - 6);
        } else {
            return startYear - 4;
        }
    }

    protected Flux<Result> mergeRDBandCacheData(Flux<Result> rdbData, Flux<Result> cacheData, RdbDataPoint rdbDataPoint) {
        return Flux.merge(rdbData, cacheData)
                .groupBy(Result::getId)
                .flatMap(groupedFlux -> groupedFlux.collectList()
                        .flatMapMany(resultList -> {
                            Map<String, List<V>> mergedMap = new HashMap<>();
                            for (Result result : resultList) {
                                TimeSeriesResult timeSeriesResult = (TimeSeriesResult) result;
                                for (Map.Entry<String, List<V>> entry : timeSeriesResult.getValues().entrySet()) {
                                    String key = entry.getKey();
                                    List<V> values = entry.getValue();
                                    mergedMap.computeIfAbsent(key, k -> new ArrayList<>()).addAll(values);
                                }
                            }
                            if (!rdbDataPoint.isMultipleValues()) {
                                removeDuplicatesFromMergedResult(mergedMap);
                            }
                            TimeSeriesResult timeSeriesResult = new TimeSeriesResult(resultList.get(0).getId(), mergedMap);
                            if (rdbDataPoint.isMultipleValues()) {
                                timeSeriesResult.setMultipleValues(true);
                            }
                            return Flux.just(timeSeriesResult);
                        })
                );
    }

    private Flux<Result> mergeRDBandCacheGroupData(Flux<Result> rdbData, Flux<Result> cacheData, RdbDataPoint rdbDataPoint) {
        return Flux.merge(rdbData, cacheData)
                .groupBy(Result::getId)
                .flatMap(groupedFlux -> groupedFlux.collectList()
                        .flatMapMany(results -> {
                            Map<String, List<TSDatapointValueGroup>> mergedMap = mergeGroupResults(results);

                            if (!rdbDataPoint.isMultipleValues()) {
                                removeDuplicatesFromMergedResultTSGroup(mergedMap);
                            }

                            TimeseriesGroupResult finalResult = new TimeseriesGroupResult(results.get(0).getId(), mergedMap);
                            finalResult.setDatapointId(rdbDataPoint.getId());
                            finalResult.setMultipleValues(rdbDataPoint.isMultipleValues());

                            return Flux.just(finalResult);
                        })
                );
    }

    private Map<String, List<TSDatapointValueGroup>> mergeGroupResults(List<Result> results) {
        Map<String, List<TSDatapointValueGroup>> mergedMap = new HashMap<>();

        results.stream()
                .filter(result -> result instanceof TimeseriesGroupResult)
                .map(result -> (TimeseriesGroupResult) result)
                .flatMap(groupResult -> groupResult.getValues().entrySet().stream())
                .forEach(entry ->
                        mergedMap.computeIfAbsent(entry.getKey(), k -> new ArrayList<>()).addAll(entry.getValue())
                );

        return mergedMap;
    }

    private void removeDuplicatesFromMergedResult(Map<String, List<V>> mergedMap) {
        for (Entry<String, List<V>> entry: mergedMap.entrySet()) {
            List<V> vList = new ArrayList<>();
            Set<String> dateSet = new HashSet<>();
            for (V v : entry.getValue()) {
                if (!dateSet.contains(v.getI())) {
                    dateSet.add(v.getI());
                    vList.add(v);
                }
            }
            vList.sort(Comparator.comparing(V::getI));
            mergedMap.put(entry.getKey(), vList);
        }
    }

    private void removeDuplicatesFromMergedResultTSGroup(Map<String, List<TSDatapointValueGroup>> mergedMap) {
        for (Entry<String, List<TSDatapointValueGroup>> entry: mergedMap.entrySet()) {
            List<TSDatapointValueGroup> vList = new ArrayList<>();
            Set<String> dateSet = new HashSet<>();
            for (TSDatapointValueGroup v : entry.getValue()) {
                if (!dateSet.contains(v.getDatapointId())) {
                    dateSet.add(v.getDatapointId());
                    vList.add(v);
                }
            }
            vList.sort(Comparator.comparing(TSDatapointValueGroup::getDatapointId));
            mergedMap.put(entry.getKey(), vList);
        }
    }

    protected Mono<Map<String, Collection<TsDataList>>> getRDBData(RdbDataPoint rdbDataPoint, Map<String, Set<String>> noCacheIdMap, DateRange dateRange, Map<String, List<String>> parameters) {

        return loadData(rdbDataPoint, noCacheIdMap, dateRange, parameters)
                .doOnSuccess(d -> {
                    log.debug("Data Loader Success!");
                    log.debug("Fire Data Loader Success!");
                    applicationEventPublisher.publishEvent(new DataLoadedFromSourceEvent(d, rdbDataPoint, dateRange, noCacheIdMap.keySet()));
                });
    }

    Mono<Map<String, Collection<TsDataList>>> loadData(RdbDataPoint rdbDataPoint, Map<String, Set<String>> noCacheIdMap, DateRange dateRange, Map<String, List<String>> parameters) {
        List<String> inputParameters = RdbParameterHelper.getRequestParameters(parameters, rdbDataPoint.getParamName());
        return rdbDataPoint.isValidFrequency() && StringUtils.isNotEmpty(rdbDataPoint.getColumnPrefix()) ? tsFrequencyDataLoader.load(rdbDataPoint, noCacheIdMap.keySet(), dateRange, inputParameters) : tsDataLoader.load(rdbDataPoint, noCacheIdMap.keySet(), dateRange, inputParameters);
    }

    private Map<String, Set<String>> getNoCacheIdMap(List<String> hasCacheIdList, Map<String, Set<String>> idMap) {
        Map<String, Set<String>> noCacheIdMap = new HashMap<>(idMap);

        if (CollectionUtils.isNotEmpty(hasCacheIdList)) {
            hasCacheIdList.forEach(noCacheIdMap.keySet()::remove);
        }

        return noCacheIdMap;
    }

    private Flux<Result> convertCacheDataToResult(Map<String, Collection<TsDataList>> cachedData, Map<String, Set<String>> idMap, DateRange dateRange, Map<String, String> datapointsInRequest) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = DateFormatUtil.toLocalDateFromStringYYYYMMDD(dateRange.getStartDate(), formatter);
        LocalDate endDate = DateFormatUtil.toLocalDateFromStringYYYYMMDD(dateRange.getEndDate(), formatter);

        return Flux.fromIterable(cachedData.entrySet()).flatMap(e ->
                Flux.fromIterable(idMap.get(e.getKey().split(":")[0]))
                        .map(key -> Transformers.transformToTimeSeriesResult.transform(startDate, endDate, e.getValue(), key, datapointsInRequest)));
    }

    private Map<String, Set<String>> getIdMap(List<IdMapper> idMapperList, String idLevel) {
        Map<String, Set<String>> idMap = new HashMap<>();
        idMapperList.forEach(idMapper -> {
            String matchedId = idMapper.getId(idLevel);

            if (StringUtils.isNotEmpty(matchedId)) {
                Set<String> argsList = idMap.computeIfAbsent(matchedId, k -> new HashSet<>());
                argsList.add(idMapper.getInvestmentId());
            }
        });
        return idMap;
    }

    private Map<String, List<DataPoint>> getGroupedDataPoints(List<DataPoint> dps, boolean isTimeSeries, boolean isPostTax, Function<DataPoint, RdbDataPoint> rdbDpSelector) {
        //filter condition will still just check that datapoints has dp.getTsRdb() even though isPostTax is set to true. This is because we still want to query normal tsRdbDataPoint if postTaxTsRdbDataPoint is not present
        //this means there is a limitation that a datapoint should always have a TimeSeries implementation first before it can have a PostTaxTimeSeries implementation.
        return dps.stream().filter(dp -> !dp.isMappingDataPoint() && dp.getTsRdb() != null && !filterMartApiData(dp, isTimeSeries))
                .collect(Collectors.groupingBy(datapoint -> {
                    RdbDataPoint rdbDataPoint = null;
                    if(isPostTax){
                        rdbDataPoint = getPostTaxTimeSeriesRdbDp(datapoint);
                    }
                    if(rdbDataPoint == null){
                        rdbDataPoint = rdbDpSelector.apply(datapoint);
                    }
                    return rdbDataPoint.getGroupName();
                }));
    }

    private boolean filterMartApiData(DataPoint dataPoint, boolean isTimeSeries) {
        if (isMartApi && isTimeSeries) {
            return "3".equals(dataPoint.getTsRdb().getRdbCacheFlag());
        } else if (isMartApi){
            return "3".equals(dataPoint.getCurrentRdb().getRdbCacheFlag());
        } else {
            return false;
        }
    }

    private Map<String, Set<String>> createYearIdMap(Set<String> idsWithYear) {
        return idsWithYear.stream()
                .map(s -> s.split(":"))
                .collect(Collectors.groupingBy(
                        arr -> arr[1],
                        Collectors.mapping(arr -> arr[0], Collectors.toSet())
                ));
    }
}
