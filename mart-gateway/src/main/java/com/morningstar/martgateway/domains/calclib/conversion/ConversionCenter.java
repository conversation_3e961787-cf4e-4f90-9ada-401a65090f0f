package com.morningstar.martgateway.domains.calclib.conversion;

import com.morningstar.calculationlibrary.CalcLibraryDefine;
import com.morningstar.calculationlibrary.CalcLibraryDefine.ErrorCode;
import com.morningstar.calculationlibrary.CurrencyConversionJNI;
import com.morningstar.martgateway.domains.calclib.util.CurrencyIdUtil;
import com.morningstar.martgateway.domains.calclib.util.PreEuropeExchangeUtil;
import com.morningstar.martgateway.util.DateFormatUtil;
import com.morningstar.oneteam.calculation.common.DateUtil;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ConversionCenter {
    private static CurrencyConversionJNI currencyConversionJNI = new CurrencyConversionJNI();
    private static final String CURR_PREFIX ="CU$$$$$";
    private String start = "1900-01-01";
    private Date startDate = DateFormatUtil.parseDate(start);
    private int index = (int) (LocalDate.parse("1999-01-01").toEpochDay() - LocalDate.parse("1900-01-01").toEpochDay());
    private ConcurrentHashMap<String, Long> currencyUpdateTime = new ConcurrentHashMap<>();
    private ExchangeRateLoader exchangeRateLoader;
    private ConcurrentHashMap<String, Integer> currencyStartDateMap = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, Integer> currencyEndDateMap = new ConcurrentHashMap<>();

    public ConversionCenter(ExchangeRateLoader exchangeRateLoader) {
        this.exchangeRateLoader = exchangeRateLoader;
    }

    public void setExchangeRate(String currency, double[] exchangeRate, Date startDate) {
        CalcLibraryDefine.ContinuousDoubleTS cTs = new CalcLibraryDefine.ContinuousDoubleTS(startDate, exchangeRate);
        currencyConversionJNI.SetExchangeRate(currency, cTs);
        boolean beginFlag = false;
        for (int i = 0; i < exchangeRate.length; i++) {
            double value = exchangeRate[i];
            if (!beginFlag && !Double.isInfinite(value) && !Double.isNaN(value)) {
                currencyStartDateMap.put(currency, i + 2);
                beginFlag = true;
            }
            if(beginFlag && (Double.isNaN(value) || i == exchangeRate.length - 1)){
                currencyEndDateMap.put(currency, i + 2);
                break;
            }
        }
    }

    public CalcLibraryDefine.ErrorCode convertCurrency(CalcLibraryDefine.DoubleTS[] priceSeries, String[] historyCurrency, int[] historyCurrencyDate, String target,
                                                              List<CalcLibraryDefine.DoubleTS> convertedValue) {
        checkCurrencyExchangeRate(target);
        for (int i = 0; i< historyCurrency.length; i++) {
            checkCurrencyExchangeRate(historyCurrency[i]);
        }
        return currencyConversionJNI.ConvertCurrency(priceSeries, historyCurrency, historyCurrencyDate, target, convertedValue);

    }

    public CalcLibraryDefine.ErrorCode convertReturn(CalcLibraryDefine.DoubleTS[] priceSerice, String freq, String[] historyCurrency, int[] historyCurrencyDate, String target,
                                                            List<CalcLibraryDefine.DoubleTS> convertedValue) {
        checkCurrencyExchangeRate(target);
        for (int i = 0; i< historyCurrency.length; i++) {
            checkCurrencyExchangeRate(historyCurrency[i]);
        }
        CalcLibraryDefine.Frequency frequence = CalcLibraryDefine.Frequency.valueOf(Integer.parseInt(freq));
        return currencyConversionJNI.RescaleCurrencyRatio(priceSerice, frequence, historyCurrency, historyCurrencyDate, target, convertedValue);
    }

    public CalcLibraryDefine.ErrorCode convertReturn(Date startDate, Date endDate, double value, String[] historyCurrency, int[] historyCurrencyDate,
                                                            String target, List<Double> convertedValue) {
        checkCurrencyExchangeRate(target);
        int startDateInteger = DateUtil.convertDate(startDate);
        int endDateInteger = DateUtil.convertDate(endDate);
        for (int i = 0; i< historyCurrency.length; i++) {
            checkCurrencyExchangeRate(historyCurrency[i]);
            startDateInteger = checkExchangeRateStartDate(historyCurrency[i], startDateInteger);
        }
        if(currencyStartDateMap.getOrDefault(target, 0) > startDateInteger ||
                currencyEndDateMap.getOrDefault(target, 0) <= endDateInteger){
            return ErrorCode.NoError;
        }
        return currencyConversionJNI.RescaleCurrencyRatio(startDateInteger, endDateInteger, value, historyCurrency,
                historyCurrencyDate, target, convertedValue);
    }

    public CalcLibraryDefine.ErrorCode convertExchangeRates(double[] rates, String base, String target, Date date, List<Double> convertedValue) {
        checkCurrencyExchangeRate(target);
        checkCurrencyExchangeRate(base);
        return currencyConversionJNI.ConvertExchangeRateSeries(rates, base, target, DateUtil.convertDate(date), convertedValue);
    }

    private void updateExchangeRate(String currency) {
        if (CurrencyIdUtil.isExchangeId(CURR_PREFIX + currency)) {
            String currencyId = CURR_PREFIX + currency;
            double[] exchangeRateList = exchangeRateLoader
                    .getExchangeRates(currencyId, start, LocalDate.now().toString());
            setExchangeRate(currency, exchangeRateList, startDate);
        } else {
            String precur = currency.substring(0, 3);
            if (PreEuropeExchangeUtil.isPreEurope(precur)) {
                double[] exchangeRateList = exchangeRateLoader
                        .getExchangeRates(CURR_PREFIX + precur, start, LocalDate.now().toString());
                double[] eurExchangeRateList = exchangeRateLoader
                        .getExchangeRates("CU$$$$$EUR", start, LocalDate.now().toString());
                double rate = PreEuropeExchangeUtil.getRate(currency);
                double[] newData = new double[eurExchangeRateList.length];
                for (int i = 0; i < index; i++) {
                    newData[i] = exchangeRateList[i] / rate;
                }
                System.arraycopy(eurExchangeRateList, index, newData, index, eurExchangeRateList.length - index);
                setExchangeRate(currency, newData, startDate);
            }
        }
        currencyUpdateTime.put(currency, Instant.now().getEpochSecond());
    }

    private void checkCurrencyExchangeRate(String currency) {
        //update specific currency exchange rate full history hourly
        if (!currencyUpdateTime.containsKey(currency)) {
            updateExchangeRate(currency);
        }
        if (currencyUpdateTime.containsKey(currency) &&
                Instant.now().getEpochSecond() - currencyUpdateTime.get(currency) > 600) {
            updateExchangeRate(currency);
        }
    }

    /**
     *  use the start date of exchange rate history to replace real performance start date
     *  this is to fix NaN issue when performance start date earlier than exchange rate start date
     * @param currency
     * @param date
     * @return date
     */
    private int checkExchangeRateStartDate(String currency, int date) {
        int currencyStartDate = currencyStartDateMap.getOrDefault(currency, 0);
        return Math.max(currencyStartDate, date);

    }
}
