package com.morningstar.martgateway.util;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.MultiUniverse;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.morningstar.dataac.martgateway.core.entitlement.entity.DataPackageItem;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageRefInfo;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementCacheService;
import org.apache.commons.collections4.CollectionUtils;

public class MultiUniverseUtil {
    private MultiUniverseUtil() {
    }

    public static Map<String, List<String>> getMultiUniverseSrcIdParentIdMap(List<DataPoint> dataPoints) {
        Map<String, List<String>> srcIdParentIdMap = new HashMap<>();
        for (DataPoint dataPoint : dataPoints) {
            if (CollectionUtils.isNotEmpty(dataPoint.getMultiUniverseList())) {
                for (MultiUniverse universe : dataPoint.getMultiUniverseList()) {
                    srcIdParentIdMap.computeIfAbsent(universe.getSrcid(), k -> new ArrayList<>())
                            .add(dataPoint.getNid());
                }
            }
        }
        return srcIdParentIdMap;
    }

    public static Optional<DataPoint> getDataPointForApplicableMultipleUniverse(DataPoint dataPoint, IdMapper investmentId, boolean isTimeSeriesRequest) {
        return applicableMultipleUniverse(dataPoint, investmentId, isTimeSeriesRequest)
                .map(DataPointRepository::getByNid);
    }

    private static Optional<String> applicableMultipleUniverse(DataPoint dataPoint, IdMapper investmentId, boolean isTimeSeriesRequest) {
        if (CollectionUtils.isEmpty(dataPoint.getMultiUniverseList())) {
            return Optional.empty();
        }
        Optional<String> applicableMultipleUniverseDp = dataPoint.getMultiUniverseList().stream()
                .filter(multiUniverse ->
                        multiUniverse.getType().equals(investmentId.getId(multiUniverse.getAttribute())) ||
                                ("PrivateModel".equalsIgnoreCase(multiUniverse.getAttribute()) && investmentId.isPrivateModel()))
                .map(MultiUniverse::getSrcid)
                .findFirst();

        if(applicableMultipleUniverseDp.isEmpty()){
            applicableMultipleUniverseDp = dataPoint.getMultiUniverseList().stream()
                    .filter(multiUniverse ->
                            "default".equalsIgnoreCase(multiUniverse.getType()))
                    .map(MultiUniverse::getSrcid)
                    .findFirst();
        }

        return applicableMultipleUniverseDp.isPresent() && isConfiguredForDataType(applicableMultipleUniverseDp.get(), isTimeSeriesRequest) ? applicableMultipleUniverseDp : Optional.empty();
    }

    private static boolean isConfiguredForDataType(String applicableMultipleUniverseDp, boolean isTimeSeriesRequest) {
        DataPoint dp = DataPointRepository.getByNid(applicableMultipleUniverseDp);
        if (dp.getSrc() != null && dp.getSrc().equalsIgnoreCase("EQUITY")) return true;
        return (isTimeSeriesRequest && dp.getTsRdb() != null) || (!isTimeSeriesRequest && dp.getCurrentRdb() != null) || "CDAPI".equals(dp.getSrc());
    }

    public static List<MartRequest> getMultiUniverseMartRequestList(MartRequest martRequest, List<DataPoint> dataPoints,
                                                                    EntitlementCacheService entitlementCacheService) {
        if (CollectionUtils.isEmpty(martRequest.getIdMappers())) {
            return List.of(martRequest);
        }
        Map<String, Set<String>> dataPointIdSetMap = getDataRetrievalPlan(dataPoints, martRequest);
        Map<Set<String>, Set<String>> groupedDpMap = new HashMap<>();
        boolean hasBypassPkg = false;
        for (Entry<String, Set<String>> entry : dataPointIdSetMap.entrySet()) {
            String dpId = entry.getKey();
            Set<String> idSet = entry.getValue();
            Set<String> dps = groupedDpMap.computeIfAbsent(idSet, k -> new HashSet<>());
            dps.add(dpId);
            groupedDpMap.put(idSet, dps);
            if (!hasBypassPkg) {
                hasBypassPkg = hasBypassPkg(dpId, entitlementCacheService);
            }
        }

        List<MartRequest> martRequestList = new ArrayList<>();
        for (Entry<Set<String>, Set<String>> entry : groupedDpMap.entrySet()) {
            List<IdMapper> investmentPortion = martRequest.getIdMappers().stream().filter(id -> entry.getKey().contains(id.getInvestmentId()))
                    .collect(Collectors.toList());
            MartRequest currMartRequest = martRequest.shallowCopy();
            currMartRequest.setDps(new ArrayList<>(entry.getValue()));
            // if any datapoint belongs to bypass pkg, keep the ids as is since idMapper is bypassed
            if(!hasBypassPkg) {
                currMartRequest.setIds(new ArrayList<>(entry.getKey()));
            }
            currMartRequest.setIdMappers(investmentPortion);
            martRequestList.add(currMartRequest);
        }
        return martRequestList;
    }

    /**
     * This method is to check if the datapoint in the request belongs to CPQ package which bypass universe
     *
     * @param dpId
     * @param entitlementCacheService
     * @return boolean
     */
    private static boolean hasBypassPkg(String dpId, EntitlementCacheService entitlementCacheService){
        DataPackageItem packageItem = entitlementCacheService.getDataPackageItem(dpId);
        if (packageItem != null) {
            PackageRefInfo packageRefInfo = entitlementCacheService.getPckgRefMap().get(packageItem.getPkg());
            return packageRefInfo != null && packageRefInfo.isBypassUniverseCheck();
        }
        return false;

    }

    private static Map<String, Set<String>> getDataRetrievalPlan(List<DataPoint> dataPoints, MartRequest martRequest) {
        Map<String, Set<String>> dataPointIdSetMap = new HashMap<>();
        for (DataPoint dataPoint : dataPoints) {
            for (IdMapper investmentId : martRequest.getIdMappers()) {
                String resultingDataPoint = applicableMultipleUniverse(dataPoint, investmentId, martRequest.isTimeSeriesRequest()).orElse(dataPoint.getNid());
                dataPointIdSetMap.computeIfAbsent(resultingDataPoint, k -> new HashSet<>()).add(investmentId.getInvestmentId());
            }
        }
        return dataPointIdSetMap;
    }

    public static Map<String, List<String>> retrieveMultipleUniverseContext(MartRequest martRequest, List<DataPoint> rootDataPoints) {
        Map<String, List<String>> context = new HashMap<>();
        for (DataPoint dataPoint : rootDataPoints) {
            for (IdMapper investmentId : martRequest.getIdMappers()) {
                Optional<String> applicableSrcId = applicableMultipleUniverse(dataPoint, investmentId, martRequest.isTimeSeriesRequest());
                applicableSrcId.ifPresent(id -> context.computeIfAbsent(investmentId.getInvestmentId(), k -> new ArrayList<>()).add(id));
            }
        }
        return context;
    }

    public static Result fillMultipleUniverseContents(Result result, Map<String, List<String>> multiUniverseSrcIdParentIdMap, Map<String, List<String>> investmentIdSrcIdMap, List<String> rootDataPoints) {
        if (!investmentIdSrcIdMap.containsKey(result.getId())) {
            return result;
        }

        for (String srcDataPointId : investmentIdSrcIdMap.get(result.getId())) {
            if (rootDataPoints.contains(srcDataPointId) && multiUniverseSrcIdParentIdMap.containsKey(srcDataPointId)) {
                multiUniverseSrcIdParentIdMap.get(srcDataPointId)
                        .forEach(parentId -> result.duplicateContent(srcDataPointId, parentId));
            } else {
                List<String> parentIdsForSrcId = multiUniverseSrcIdParentIdMap.getOrDefault(srcDataPointId, Collections.emptyList());
                duplicateAndRenameSrcDataPoint(srcDataPointId, parentIdsForSrcId, result);
            }
        }
        return result;
    }

    private static void duplicateAndRenameSrcDataPoint(String srcDataPointId, List<String> parentIdsForSrcId, Result result) {
        if (parentIdsForSrcId.isEmpty()) {
            return;
        }

        for (int i = 1; i < parentIdsForSrcId.size(); i++) {
            result.duplicateContent(srcDataPointId, parentIdsForSrcId.get(i));
        }

        result.renameDataPoint(srcDataPointId, parentIdsForSrcId.get(0));
    }

}
