package com.morningstar.martgateway.util.apiproxy.timeperiod;

import com.morningstar.martgateway.domains.apiproxy.entity.FrequencyType;
import com.morningstar.martgateway.domains.apiproxy.entity.IFrequencyType;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class BackwardExtendingWindow implements IWindowType {

    @Override
    public List<Pair<String, String>> generatePeriods(Date startDate, Date endDate, String frequency, Integer stepSize, Integer windowSize) {
        IFrequencyType frequencyType = FrequencyType.getFrequencyType(frequency).orElseThrow(() -> new IllegalArgumentException("Invalid frequency: " + frequency));
        LocalDate subEndDate = DateUtil.asLocalDate(endDate);
        LocalDate subStartDate = frequencyType.getStartDateByWindowSize(subEndDate, stepSize);
        LocalDate startDateLocalDate = DateUtil.asLocalDate(startDate);
        List<Pair<String, String>> datePairs = new ArrayList<>();
        while (startDateLocalDate.compareTo(subStartDate) <= 0) {
            datePairs.add(Pair.of(DateUtil.formatToGeneralDate(subStartDate), DateUtil.formatToGeneralDate(subEndDate)));
            subStartDate = frequencyType.getBackwardExtendingPreviousStartDate(subStartDate, stepSize);
        }

        Collections.reverse(datePairs);
        return datePairs;
    }

}
