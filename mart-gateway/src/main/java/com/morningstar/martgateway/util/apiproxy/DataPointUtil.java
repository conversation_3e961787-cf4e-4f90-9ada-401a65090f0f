package com.morningstar.martgateway.util.apiproxy;

import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointDataType;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointMetaInfo;
import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint;
import com.morningstar.martgateway.domains.apiproxy.entity.DoApiInvestment;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointForColumns;
import com.morningstar.martgateway.domains.apiproxy.entity.DatapointValue;
import com.morningstar.dataac.martgateway.core.common.util.DateConst;
import io.netty.util.internal.StringUtil;
import io.vavr.CheckedFunction0;
import io.vavr.control.Option;
import io.vavr.control.Try;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class DataPointUtil {
	private DataPointUtil() {
	}

	/**
	 * convert datapoint value to corresponding type, and if value is date, convert to yyyy-MM-dd format
	 * if value is not string, then return directly
	 * @param doApiInvestments
	 * @param aliasDpMetaMap with type of DataPointDataType
	 * @return
	 */
	public static void convertInvestmentDataPointValue(List<DoApiInvestment> doApiInvestments, Map<String, DataPointMetaInfo> aliasDpMetaMap) {
		for(DoApiInvestment doApiInvestment : doApiInvestments) {
			List<DatapointValue> values = Option.of(doApiInvestment.getValues()).getOrElse(new ArrayList<DatapointValue>());
			for(DatapointValue value : values) {
				Object newValue = convertValueAndFormat(value.getValue(), Optional.ofNullable(aliasDpMetaMap.get(value.getAlias())).orElse(new DataPointMetaInfo()));
				value.setValue(newValue);
			}
		}
	}

	public static Object convertValueAndFormat(Object value, DataPointMetaInfo metaInfo) {
		String dataType = Optional.ofNullable(metaInfo).map(DataPointMetaInfo::getType).orElse(null);
		if (value instanceof List) {
			return ((List<Object>) value).stream().map(it -> {
				if (it instanceof Map) {
					Map<String, Object> subItem = (Map<String, Object>) it;
					subItem.put("value", convertDataPointValueAndFormat(subItem.get("value"), dataType));
					return subItem;
				} else {
					return convertDataPointValueAndFormat(it, dataType);
				}
			}).collect(Collectors.toList());
		} else {
			return convertDataPointValueAndFormat(value, dataType);
		}
	}
	
	/**
	 * convert datapoint value to corresponding type, and if value is date, conver to yyyy-MM-dd format
	 * if value is not string, then return directly
	 * @param value
	 * @param dataType with type of DataPointDataType
	 * @return
	 */
	public static Object convertDataPointValueAndFormat(Object value, String dataType) {
		if(!(value instanceof String)) {
			return value;
		}
	  
		Object result = value;
		String v = (String)value;
		if(DataPointDataType.isNumberType(dataType)) {
			result = parseNumber(v);
		} else if(DataPointDataType.isDateType(dataType)) {
			String[] sourcePatterns = new String[] {"MM/dd/yyyy", "MM-dd-yyyy", DateConst.DEFAULT_DATE_PATTERN, "yyyy/MM/dd","dd/MM/yyyy", "dd-MM-yyyy"};
			result = getValue(() -> DateUtil.transformDateFormat(v, sourcePatterns, DateConst.DEFAULT_DATE_PATTERN), v);
		}
		return result;
	}
	
	
	private static Object parseNumber(String v) {
		Object result = null;
		if(StringUtils.isBlank(v)) {
			result = null;
		}
		else if(NumberUtils.isDigits(StringUtils.removeStart(v, "-"))) {//handle negative number
			result = getValue(() -> Long.parseLong(v), v) ;
		}else {
			result = getValue(() -> BigDecimal.valueOf(Double.valueOf(v)), v);
		}
		return result;
	}
	
	
	@SuppressWarnings("unchecked")
	private static Object getValue(@SuppressWarnings("rawtypes") CheckedFunction0 supplier, Object defaultValue) {
		return Try.of(supplier).getOrElse(defaultValue);
	}
	
	
	
	public static void setIdToDataPointValueFromDps(DatapointValue v, List<Datapoint> dataPoints) {
		if(dataPoints == null) {
			return;
		}
		Optional<Datapoint> dpOpt = dataPoints.stream().filter(dp -> StringUtils.equals(dp.getAlias(), v.getAlias())).findFirst();
		if(dpOpt.isPresent()) {
			v.setId(dpOpt.get().getId());
		}
	}
	
	public static Datapoint getDataPointByAlias(List<Datapoint> flds, String alias) {
		return flds.stream()
				.filter(d -> !StringUtil.isNullOrEmpty(d.getAlias()) && d.getAlias().equalsIgnoreCase(alias)).findAny()
				.orElse(null);
	}

	 
	public static void addInvalidDataPointValue(List<DatapointValue> values, List<Datapoint> datapoints) {
		datapoints.forEach(dataPoint -> {
			String datapointId = dataPoint.getId();
			String datapointAlias = dataPoint.getAlias();

			DatapointValue validDatapointValue = values.stream().filter(value -> datapointAlias.equalsIgnoreCase(value.getAlias())).findAny().orElse(null);
			if (validDatapointValue == null) {
				DatapointValue nullValue = new DatapointValue();
				nullValue.setId(datapointId);
				nullValue.setAlias(datapointAlias);
				values.add(nullValue);
			}
		});
	}

	public static boolean isCalculationDatapoint(String dataPoinId, boolean isTsdp) {
		return StringUtils.isNumeric(dataPoinId) && isTsdp;
	}

	public static void setCustomCalcTsDpValue(DataPointForColumns dataPoint) {
		boolean isTsdp = dataPoint.isTsdp() || StringUtils.isNotBlank(dataPoint.getStartDate()) && StringUtils.isNotBlank(dataPoint.getEndDate());
		dataPoint.setTsdp(isTsdp);
		// true: dynamic datapoint, false: single datapoint
		dataPoint.setCustomCalc(isCalculationDatapoint(dataPoint.getDatapointId(), isTsdp));
	}
}
