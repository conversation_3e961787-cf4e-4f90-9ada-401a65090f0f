package com.morningstar.martgateway.util.apiproxy.timeperiod;

import com.morningstar.dataac.martgateway.core.common.util.DateConst;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SingleDataPointWindow implements IWindowType {

    @Override
    public List<Pair<String, String>> generatePeriods(Date startDate, Date endDate, String frequency, Integer stepSize, Integer windowSize) {
        List<Pair<String, String>> datePairs = new ArrayList<>();
        datePairs.add(Pair.of(DateUtil.format(startDate, DateConst.DEFAULT_DATE_PATTERN),
                DateUtil.format(endDate, DateConst.DEFAULT_DATE_PATTERN)));
        return datePairs;
    }
}
