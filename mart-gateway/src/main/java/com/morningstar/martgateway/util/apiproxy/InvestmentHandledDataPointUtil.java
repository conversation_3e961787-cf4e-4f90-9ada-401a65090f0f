package com.morningstar.martgateway.util.apiproxy;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import java.util.Arrays;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODMDB;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.RDB;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODPG;

public class InvestmentHandledDataPointUtil {

    public boolean isInvestmentApiHandledDataPoint(GridviewDataPoint requestedDataPoint) {
        if (DataPointSourceUtil.isCustomDatapoint(requestedDataPoint.getDataPointId())) {
            return true;
        }

        DataPoint analysisDataPoint = DataPointRepository.getByNid(requestedDataPoint.getDataPointId());
        if (StringUtils.isEmpty(analysisDataPoint.getNid())) {
            return false;
        }

        if (analysisDataPoint.isMappingDataPoint() || CollectionUtils.isNotEmpty(analysisDataPoint.getMultiUniverseList()) || analysisDataPoint.isCalcApi(requestedDataPoint.getCurrency())) {
            return true;
        }

        String src = Optional.ofNullable(analysisDataPoint.getSrc()).orElse("");

        if (EODPG.equals(src) || EODMDB.equals(src)) {
            return true;
        }

        if (Arrays.asList(src.split(",")).contains(RDB)) {
            if (isTimeSeries(requestedDataPoint)) {
                return analysisDataPoint.getTsRdb() != null;
            } else {
                return analysisDataPoint.getCurrentRdb() != null || analysisDataPoint.getLanguageCurrentRdb() != null;
            }
        }

        return StringUtils.isEmpty(requestedDataPoint.getSourceId());
    }

    private boolean isTimeSeries(GridviewDataPoint requestedDataPoint) {
        return !StringUtils.isEmpty(requestedDataPoint.getStartDate()) &&
                !StringUtils.isEmpty(requestedDataPoint.getEndDate());
    }
}
