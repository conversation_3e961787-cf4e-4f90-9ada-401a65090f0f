package com.morningstar.martgateway.util.apiproxy.timeperiod;

import com.morningstar.martgateway.domains.apiproxy.entity.enums.DataPointTypeEnum;
import com.morningstar.dataac.martgateway.core.common.util.DateConst;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.DpSettingFrequency;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.FloatTypeEnum;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.HttpStatus;
import com.morningstar.martgateway.domains.apiproxy.entity.enums.WindowTypeEnum;
import com.morningstar.martgateway.domains.apiproxy.exceptions.DOAPIException;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointType;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointForColumns;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointForColumnsResponse;
import com.morningstar.martgateway.domains.apiproxy.entity.FrequencyType;
import com.morningstar.martgateway.util.apiproxy.DataPointUtil;
import com.morningstar.martgateway.util.apiproxy.DateFloatUtil;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.morningstar.dataac.martgateway.core.common.util.DateUtil.asLocalDate;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TimeSerialsCalculator {

    // 1. calculation columns
    public static List<DataPointForColumnsResponse> calcTotalColumns(List<DataPointForColumns> dataPointList) {
        // check startDate endDate without single dataPoint
        checkStartDateEndDate(dataPointList);
        List<DataPointForColumnsResponse> columnList = new ArrayList<>();
        for (DataPointForColumns dataPoint : dataPointList) {
            columnList.add(calcTotalColumnsBySingle(dataPoint));
        }
        return columnList;
    }

    // 2. calculate single dataPoint
    public static DataPointForColumnsResponse calcTotalColumnsBySingle(DataPointForColumns dataPoint) {
        // 1. dynamic 2. ts 3 single
        return DataPointType.generateTimePeriodsByDPType(checkDataPointType(dataPoint).getCode(), dataPoint);
    }

    private static DataPointTypeEnum checkDataPointType(DataPointForColumns dataPoint) {
		DataPointUtil.setCustomCalcTsDpValue(dataPoint);
        if(!dataPoint.isCustomCalc() && !dataPoint.isTsdp())
            return DataPointTypeEnum.SINGLE_DATAPOINT;
        else if(dataPoint.isCustomCalc())
            return DataPointTypeEnum.CUSTOMCALC_DATAPOINT;
        else{
            return DataPointTypeEnum.TIMESERIALS_DATAPOINT;
        }
    }

    private static void checkStartDateEndDate(List<DataPointForColumns> dataPointList) {
        if (null == dataPointList) {
        	throw new DOAPIException("Input Parameter Entity is empty", null, HttpStatus.STATUS_400);
        }
        dataPointList.forEach(dataPoint -> {
            if(StringUtils.isEmpty(dataPoint.getDatapointId())){
            	throw new DOAPIException("datapointId is empty", null, HttpStatus.STATUS_400);
            }
            if(StringUtils.isEmpty(dataPoint.getAlias())){
            	throw new DOAPIException("alias is empty", null, HttpStatus.STATUS_400);
            }
			DataPointUtil.setCustomCalcTsDpValue(dataPoint);
            if (dataPoint.isCustomCalc() || dataPoint.isTsdp()) {
                checkConditions(dataPoint);
            }
        });
    }

    private static void checkConditions(DataPointForColumns dataPoint) {
        Date standardStartDate = Optional.ofNullable(dataPoint.getStartDate()).map(DateUtil::tryParse).orElse(null);
        Date standardEndDate = Optional.ofNullable(dataPoint.getEndDate()).map(DateUtil::tryParse).orElse(null);
        if (standardStartDate == null) {
        	throw new DOAPIException("start date is empty", null, HttpStatus.STATUS_400);
        }
        if (standardEndDate == null) {
        	throw new DOAPIException("end date is empty", null, HttpStatus.STATUS_400);
        }
        if(DpSettingFrequency.WEEKLY.getShortName().equals(dataPoint.getFrequency())
                && StringUtils.isEmpty(Optional.ofNullable(dataPoint.getSourceId()).orElse(""))) {
        	throw new DOAPIException("sourceId is empty", null, HttpStatus.STATUS_400);
        }
        if(dataPoint.isCustomCalc()
                && WindowTypeEnum.ROLLING.getId().equals(dataPoint.getWindowType())
                &&(dataPoint.getWindowSize() == null || dataPoint.getStepSize() == null)){
        	throw new DOAPIException("window size or step size is empty", null, HttpStatus.STATUS_400);
        }
        if (standardStartDate.compareTo(standardEndDate) > 0) {
            dataPoint.setWindowType("1");
        }
    }

    private static boolean needFloatCalc(DataPointForColumns dataPoint) {
        return ((dataPoint.getFloatStart() != null) || (dataPoint.getFloatEnd() != null));
    }

    public static Pair<String, String> initialStartDateAndEndDate(DataPointForColumns dataPoint) {
        Pair<String,String> pair = null;
        if (needFloatCalc(dataPoint)) {
            LocalDate[] localDates = DateFloatUtil.calculateDate(dataPoint.getSourceId(),
                    asLocalDate(DateUtil.asParse(dataPoint.getStartDate(),
                            DateConst.DEFAULT_DATE_PATTERN)),
                    asLocalDate(DateUtil.asParse(dataPoint.getEndDate(),
                            DateConst.DEFAULT_DATE_PATTERN)),
                    asLocalDate(DateUtil.asParse(dataPoint.getBenchmarkDate() == null ? DateUtil.formatToGeneralDate(new Date()) :
                                    DateUtil.format(DateUtil.asParse(dataPoint.getBenchmarkDate(),DateConst.DEFAULT_DATE_PATTERN),DateConst.DEFAULT_DATE_PATTERN),
                            DateConst.DEFAULT_DATE_PATTERN)),
                    FloatTypeEnum.parse(dataPoint.getFloatStart()),
                    FloatTypeEnum.parse(dataPoint.getFloatEnd()),
                    dataPoint.getStartDelay(),
                    dataPoint.getEndDelay());
            pair = Pair.of(DateUtil.formatToGeneralDate(localDates[0]), DateUtil.formatToGeneralDate(localDates[1]));
        } else {
            pair = Pair.of(dataPoint.getStartDate(), dataPoint.getEndDate());
        }
        return pair;
    }

    public static boolean checkIllegalParams(String windowType, Integer windowSize, Integer stepSize) {
        if (windowType == null || windowType.equals("null")) {
            return true;
        }
        if (WindowTypeEnum.ROLLING.getId().equalsIgnoreCase(windowType)) {
            return (windowSize == null || stepSize == null);
        } else if (WindowTypeEnum.FORWDEXTD.getId().equalsIgnoreCase(windowType)
                || WindowTypeEnum.BACKWARDEXTD.getId().equalsIgnoreCase(windowType)) {
            return (stepSize == null);
        }
        return false;
    }

    public static boolean checkIllegalDates(Date startDate, Date endDate, Integer stepSize, String frequency) {
        if (startDate == null || endDate == null) return false;
        stepSize = (stepSize == null) ? 0 : stepSize;
        if (startDate.compareTo(endDate) > 0) {
            return false;
        }
        LocalDate startLocalDate = asLocalDate(startDate);
        LocalDate endLocalDate = asLocalDate(endDate);
        long y = startLocalDate.until(endLocalDate, ChronoUnit.YEARS) + 1;
        long m = startLocalDate.until(endLocalDate, ChronoUnit.MONTHS) + 1;
        long w = startLocalDate.until(endLocalDate, ChronoUnit.WEEKS) + 1;
        long ds = startLocalDate.until(endLocalDate, ChronoUnit.DAYS) + 1;
        return FrequencyType.checkLegalDate(stepSize, frequency, y, m, w, ds);
    }

}

