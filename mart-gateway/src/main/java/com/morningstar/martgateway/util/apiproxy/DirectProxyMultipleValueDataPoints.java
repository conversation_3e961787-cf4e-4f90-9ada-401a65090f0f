package com.morningstar.martgateway.util.apiproxy;

import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

@Slf4j
public class DirectProxyMultipleValueDataPoints {

    private static final Set<String> dataPoints = fetchDataPoints();

    private static Set<String> fetchDataPoints() {
        Set<String> dps = new HashSet<>();
        try (Reader reader = new InputStreamReader(new ClassPathResource("directProxyMultipleValueDataPoints.txt").getInputStream());
                BufferedReader br = new BufferedReader(reader)) {
            String line;
            while ((line = br.readLine()) != null) {
                dps.add(line.trim());
            }
        } catch (IOException e) {
            throw new MartException("Unable to read directProxyMultipleValueDataPoints.txt");
        }
        return dps;
    }

    public static boolean contains(String dataPoint) {
        return dataPoints.contains(dataPoint);
    }
}
