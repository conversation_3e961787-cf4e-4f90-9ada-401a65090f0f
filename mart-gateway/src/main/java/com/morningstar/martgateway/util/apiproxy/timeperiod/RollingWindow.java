package com.morningstar.martgateway.util.apiproxy.timeperiod;

import com.morningstar.martgateway.domains.apiproxy.entity.FrequencyType;
import com.morningstar.martgateway.domains.apiproxy.entity.IFrequencyType;
import com.morningstar.dataac.martgateway.core.common.util.DateUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class RollingWindow implements IWindowType {

    @Override
    public List<Pair<String, String>> generatePeriods(Date startDate, Date endDate, String frequency, Integer stepSize, Integer windowSize) {
        IFrequencyType frequencyType = FrequencyType.getFrequencyType(frequency).orElseThrow(() -> new IllegalArgumentException("Invalid frequency: " + frequency));
        LocalDate subStartDate = DateUtil.asLocalDate(startDate);
        LocalDate subEndDate = frequencyType.getEndDateByWindowSize(subStartDate, windowSize);
        LocalDate endDateLocalDate = DateUtil.asLocalDate(endDate);
        List<Pair<String, String>> datePairs = new ArrayList<>();
        while (subEndDate.compareTo(endDateLocalDate) <= 0) {
            datePairs.add(Pair.of(DateUtil.formatToGeneralDate(subStartDate), DateUtil.formatToGeneralDate(subEndDate)));
            subStartDate = frequencyType.getRollingWindowNextStartDate(subStartDate, stepSize);
            subEndDate = frequencyType.getEndDateByWindowSize(subStartDate, windowSize);
        }
        return datePairs;
    }

}


