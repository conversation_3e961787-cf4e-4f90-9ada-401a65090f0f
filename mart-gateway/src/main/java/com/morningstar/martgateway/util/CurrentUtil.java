package com.morningstar.martgateway.util;

import com.morningstar.dataac.martgateway.core.common.util.GZipUtils;
import com.morningstar.martgateway.domains.core.entity.MasterHeader;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.Alias;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import java.time.Duration;
import java.util.Random;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONArray;
import org.json.JSONObject;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.morningstar.martgateway.domains.rdb.service.RdbCurrentService.ID;

public final class CurrentUtil {

    private static List<Character> baseChars = new ArrayList<>(62);

    private static final int ENCODE_THRESHOLD = 3000;
    private static final Random random = new Random();

    static {
        IntStream.rangeClosed(48, 57).forEach(value ->
                baseChars.add((char)value)
        );
        IntStream.rangeClosed(65, 90).forEach(value ->
                baseChars.add((char)value)
        );
        IntStream.rangeClosed(97, 122).forEach(value ->
                baseChars.add((char)value)
        );
    }

    private CurrentUtil() {
    }

    public static boolean getReadCache(String readCache) {
        return StringUtils.isEmpty(readCache) ||
                !"false".equalsIgnoreCase(readCache);
    }

    /**
     * extract SubGroup from JSONObject to build a CurrentResult List
     *
     * @param id
     * @param dpArray
     * @param subSet
     * @return CurrentResult List
     */
    public static List<CurrentResult> extractSubGroup(String id, JSONArray dpArray, Collection<DataPoint> subSet, Instant start) {
        List<CurrentResult> currentResults = new ArrayList<>();
        for (int i = 0; i < dpArray.length(); i++) {
            JSONObject jsonObject = extractJsonObject(dpArray, i);
            if (jsonObject.isEmpty()) {
                continue;
            }
            Map<String, String> subMap = new HashMap<>();
            if (subSet.isEmpty()) {
                subSet = jsonObject.keySet().stream()
                        .map(DataPointRepository::getByNid)
                        .collect(Collectors.toSet());
            }
            subSet.forEach(subDp -> {
                if (jsonObject.has(subDp.getNid()) && !jsonObject.isNull(subDp.getNid())) {
                    subMap.put(subDp.getNid(), jsonObject.optString(subDp.getNid()));
                } else if (subDp.getMappingSrc() != null) {
                    String srcNid = subDp.getMappingSrc().getNid();
                    if (jsonObject.has(srcNid) && !jsonObject.isNull(srcNid)) {
                        String rawData = jsonObject.optString(srcNid);
                        subMap.put(subDp.getNid(), subDp.getMappingRef().getAlias(rawData.trim()));
                    }
                }
            });
            if (!subMap.isEmpty()) {
                currentResults.add(new CurrentResult(id, subMap, start));
            }
        }
        return currentResults;
    }

    /**
     * handle situation that the object for the index i is not a JSONObject but a JSONArray,
     * then get the first JSONObject in sub JSONArray
     *
     * @param dpArray
     * @param i
     * @return JSONObject
     */
    private static JSONObject extractJsonObject(JSONArray dpArray, int i) {
        JSONObject jsonObject;
        Object object = dpArray.get(i);
        if (object instanceof JSONArray) {
            jsonObject = ((JSONArray) object).getJSONObject(0);
        } else {
            jsonObject = dpArray.optJSONObject(i);
        }
        return jsonObject == null ? new JSONObject() : jsonObject;
    }

    public static String getObfuscationKey(String portfolioId) {
        long reversed = Long.parseLong(new StringBuilder(portfolioId).reverse().toString());
        int zeros = portfolioId.length() - String.valueOf(reversed).length();
        if (zeros > 1) {
            reversed *= (long)Math.pow(10.0D, (double)zeros - 1.0D);
        }
        StringBuilder key = new StringBuilder();
        encode(key, reversed);
        if (zeros > 1) {
            key.append("*").append(baseChars.get(zeros - 1));
        }
        return key.append("-").append(portfolioId).toString();
    }

    public static String getStorageKey(String idLevel, IdMapper mapper, String groupName) {
        return idLevel + "/" + groupName + "/" + ("PlatformId".equals(idLevel) ? mapper.getInvestmentId() : mapper.getId(idLevel));
    }

    public static String resizeDataObject(JSONObject object) {
        if (object.toString().length() < ENCODE_THRESHOLD) {
            return object.toString();
        } else {
            JSONObject contentBody = new JSONObject();
            contentBody.put("gz", GZipUtils.zipBody(object.toString()));
            return contentBody.toString();
        }
    }

    public static Duration getRandomExpireTime() {
        //add a random duration to avoid Cache Avalanche
        return Duration.ofDays(1).plusSeconds(random.nextInt(1000));
    }

    private static void encode(StringBuilder sb, long number) {
        char c = baseChars.get((int)(number % (long)baseChars.size()));
        sb.append(c);
        long quotient = number / (long)baseChars.size();
        if (quotient >= (long)baseChars.size()) {
            encode(sb, quotient);
        } else if (quotient != 0L) {
            sb.append(baseChars.get((int)quotient));
        }
    }

    public static MasterHeader suppressLegacyAttributes(MasterHeader masterHeader, List<DataPoint> dataPointList) {
        JSONObject dataObject = masterHeader.getValues();
        JSONObject outputObject = new JSONObject();
        dataPointList.forEach(dp -> {
            if (dataObject.has(dp.getNid())) {
                outputObject.put(dp.getNid(), dataObject.get(dp.getNid()));
            }
            if (dp.isMappingDataPoint() && StringUtils.isNotEmpty(dp.getMappingSrc().getNid()) && dataObject.has(dp.getMappingSrc().getNid())) {
                outputObject.put(dp.getMappingSrc().getNid(), dataObject.get(dp.getMappingSrc().getNid()));
            }
        });
        masterHeader.setValues(outputObject);
        return masterHeader;
    }

    public static Map<String, Pair<Alias, String>> getMappingRefDps(List<DataPoint> dps) {
        return dps.stream().filter(d -> d.isMappingDataPoint() || d.getMappingRef() != null)
                .collect(Collectors.toMap(a -> a.getNid(), b -> Pair.of(b.getMappingRef(), b.getMappingSrc() != null ? b.getMappingSrc().getNid() : null)));
    }

    public static Map<String, Pair<Alias, String>> getMappingRefSubDps(List<DataPoint> dps) {
        Map<String, Pair<Alias, String>> mappingRefSubDps = new HashMap<>();
        List<DataPoint> subDataPoints = DataPointUtils.getRdbSubDataPoints(dps);
        if(CollectionUtils.isNotEmpty(subDataPoints)) {
            mappingRefSubDps = getMappingRefDps(subDataPoints);
        }
        return mappingRefSubDps;
    }

    public static <K> Map<String, K> handleMappingDataPoint(Map<String, Pair<Alias, String>> mappingRefDps, Map<String, Object> refRow) {
        Map<String, K> row = new HashMap<>();
        // Map dp with mapSrc first, before mapSrc dp is mapped
        mappingRefDps.entrySet().stream().filter(e ->  StringUtils.isNotEmpty(e.getValue().getRight())).forEach(e -> mapToValue(e, refRow, row));
        mappingRefDps.entrySet().stream().filter(e -> !StringUtils.isNotEmpty(e.getValue().getRight())).forEach(e -> mapToValue(e, refRow, row));
        return row;
    }

    public static void handleMappingSubDataPoint(Map<String, Pair<Alias, String>> mappingRefDps, Map<String, Object> row) {

        if(MapUtils.isEmpty(mappingRefDps)) {
            return;
        }

        row.entrySet().forEach(e -> {
            if(e.getValue() != null && (e.getValue() instanceof List)) {
                ((List<?>) e.getValue()).forEach(l -> {
                    if(l != null && (l instanceof HashMap)) {
                        Map<String, Object> mappedDps = handleMappingDataPoint(mappingRefDps, (Map<String, Object>) l);
                        ((HashMap) l).putAll(mappedDps);
                    }
                });
            }
        });
    }

    public static List<Map<String, Object>> handleMultiValueDataPoint(Set<String> multiValueDps, Map<String, Object> row, String id) {

        List<Map<String, Object>> dataRows = new ArrayList<>();
        row.entrySet().forEach(e -> {
            if(e.getValue() != null && (e.getValue() instanceof List) && multiValueDps.contains(e.getKey())) {
                ((List)e.getValue()).forEach(map -> {
                    Map<String, Object> rowData = new HashMap<>();
                    multiValueDps.forEach(dp -> {
                        if (map instanceof Map && ((Map)map).containsKey(dp)) {
                            rowData.put(dp, ((Map<String, String>)map).get(dp));
                            rowData.put(ID, id);
                        }
                    });
                    dataRows.add(rowData);
               });
            }
        });

        return dataRows;
    }
    private static <K> void mapToValue(Map.Entry<String, Pair<Alias, String>> e, Map<String, Object> refRow, Map<String, K> row) {
        String mappingId = StringUtils.isEmpty(e.getValue().getRight()) ? e.getKey() : e.getValue().getRight();
        Object value = refRow.get(mappingId);
        if (value != null) {
            row.put(e.getKey(), (K)(e.getValue().getLeft() != null ? e.getValue().getLeft().getAlias(String.valueOf(value).trim()): String.valueOf(value)));
        }
    }

    public static Set<String> getSubDataPointsSet(List<DataPoint> dataPoints) {
        return dataPoints.stream().filter(dp -> dp.getCurrentRdb() != null && CollectionUtils.isNotEmpty(dp.getCurrentRdb().getSubDataPoints())).map(DataPoint::getNid).collect(Collectors.toSet());
    }

    public static Set<String> getMultiValueDataPointsSet(List<DataPoint> dataPoints) {
        return dataPoints.stream().filter(dp -> dp.getCurrentRdb() != null && dp.isMultipleValueDataPoint(DataPoint::getCurrentRdb)).map(DataPoint::getNid).collect(Collectors.toSet());
    }
}
