package com.morningstar.martgateway.util.apiproxy;

import com.morningstar.martgateway.domains.apiproxy.entity.enums.DataPointSource;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.gridview.GridviewDataPoint;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODMDB;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.EODPG;
import static  com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.ICEFI;

public class DataPointSourceUtil {

    public static final String MDS_PRODUCT_ID = "mds";
    private final InvestmentHandledDataPointUtil investmentHandledDataPointUtil;
    private final WhiteListCache whiteListCache;

    public DataPointSourceUtil(InvestmentHandledDataPointUtil investmentHandledDataPointUtil, WhiteListCache whiteListCache) {
        this.investmentHandledDataPointUtil = investmentHandledDataPointUtil;
        this.whiteListCache = whiteListCache;
    }

    public Map<DataPointSource, List<GridviewDataPoint>> splitDataPointsPerSource(List<GridviewDataPoint> dpsList, String productId, boolean skipProxy) {
        if (CollectionUtils.isEmpty(dpsList)) {
            return new EnumMap<>(DataPointSource.class);
        }
        if (MDS_PRODUCT_ID.equalsIgnoreCase(productId) ) {
            return getDataPointSource(dpsList, false, skipProxy);
        }
        return getDataPointSource(dpsList, true, skipProxy);
    }

    private Map<DataPointSource, List<GridviewDataPoint>> getDataPointSource(List<GridviewDataPoint> requestedDataPoints, boolean checkWhiteList, boolean skipProxy) {
        Map<DataPointSource, List<GridviewDataPoint>> result = new EnumMap<>(DataPointSource.class);
        if (requestedDataPoints == null || requestedDataPoints.isEmpty()) {
            return result;
        }

        List<GridviewDataPoint> dataPointsList = new ArrayList<>(requestedDataPoints);
        List<GridviewDataPoint> investmentApiDataPoints = getInvestmentApiHandledDataPoints(dataPointsList, checkWhiteList, skipProxy);

        dataPointsList.removeAll(investmentApiDataPoints);
        //TODO refactor this line after direct proxy is completely removed
        result.put(DataPointSource.INVESTMENT_API, investmentApiDataPoints.stream()
                .filter(gridviewDataPoint -> !isDeprecated(gridviewDataPoint.getDataPointId())).collect(Collectors.toList()));
        result.put(DataPointSource.DIRECT_API, dataPointsList);

        return result;
    }

    private List<GridviewDataPoint> getInvestmentApiHandledDataPoints(List<GridviewDataPoint> requestedDataPoints, boolean checkWhiteList, boolean skipProxy) {

        List<GridviewDataPoint> investmentApiList = requestedDataPoints.stream().filter(dp -> investmentHandledDataPointUtil.isInvestmentApiHandledDataPoint(dp) || skipProxy).collect(Collectors.toList());

        if (checkWhiteList) {
            investmentApiList.removeIf(dp ->
                    !isEquityData(dp.getDataPointId()) &&
                    !isEODData(dp.getDataPointId()) &&
                    !isFixedIncomeDatapoint(dp.getDataPointId()) &&
                    !isCustomDatapoint(dp.getDataPointId()) &&
                    !whiteListCache.contains(dp.getDataPointId()) &&
                    !skipProxy);
        }

        return investmentApiList;
    }

    private boolean isEquityData(String dpId) {
        return DataPointRepository.getByNid(dpId).getEquityDatapoint() != null;
    }

    private boolean isEODData(String dpId) {
        String src = DataPointRepository.getByNid(dpId).getSrc();
        return !StringUtils.isEmpty(src) && (src.equals(EODPG) || src.equals(EODMDB));
    }

    private boolean isFixedIncomeDatapoint(String dpId) {
        return StringUtils.contains(DataPointRepository.getByNid(dpId).getSrc(), ICEFI);
    }

    public static boolean isCustomDatapoint(String dpId) {
        return dpId.startsWith("UP");
    }

    private boolean isDeprecated(String dpId) {
        Boolean isDeprecated = DataPointRepository.getByNid(dpId).getIsDeprecated();
        return isDeprecated != null && isDeprecated;
    }
}
