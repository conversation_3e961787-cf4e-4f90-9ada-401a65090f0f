package com.morningstar.martgateway.infrastructures.repo.investmentapiproxy;

import com.google.common.collect.Lists;
import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesRequestEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.DatapointValue;

import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.morningstar.martgateway.domains.apiproxy.exceptions.DOAPIException;
import com.morningstar.martgateway.infrastructures.repo.investmentapiproxy.xml.DynamicDataHandler;
import com.morningstar.martgateway.util.LogUtils;
import com.morningstar.martgateway.util.apiproxy.IOUtil;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;

import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.UUID;
import reactor.netty.http.client.PrematureCloseException;
import reactor.util.retry.Retry;

@Slf4j
public class DynamicDataRepository {

    private static final String REQUEST_TEMPLATE = "<req action='get' subtype='list' type='61' mv='1'></req>";
    private final WebClient webClient;
    private final ProxyUriBuilder proxyUriBuilder;
    private final int dataPointsPerCall;
    private final int backOffSeconds;
    private final int retries;

    public DynamicDataRepository(WebClient webClient, ProxyUriBuilder proxyUriBuilder, int dataPointsPerCall, int backOffSeconds, int retries) {
        this.webClient = webClient;
        this.proxyUriBuilder = proxyUriBuilder;
        this.dataPointsPerCall = dataPointsPerCall;
        this.backOffSeconds = backOffSeconds;
        this.retries = retries;
    }

    public Mono<Map<String, Map<String, DatapointValue>>> getDynamicData(SecuritiesRequestEntity entity) {
        List<List<Datapoint>> datapointPartitions = Lists.partition(entity.getDatapoints(), entity.getDirectDpsBatchSize());
        List<SecuritiesRequestEntity> partitionedEntities = partitionEntities(entity, datapointPartitions);
        Flux<Map<String, Map<String, DatapointValue>>> partitionValues = Flux.fromIterable(partitionedEntities).flatMap(this::getPartitionedValues);
        return partitionValues.collectList().map(this::mergeResponses);
    }

    private Mono<Map<String, Map<String, DatapointValue>>> getPartitionedValues(SecuritiesRequestEntity entityPartition) {
        String xmlBody = entityPartition.generateRequestXml(REQUEST_TEMPLATE, false);
        return getRawData(xmlBody, entityPartition)
                .flatMap(dataBuffer -> {
                    try {
                        try (InputStream inputStream = dataBuffer.asInputStream()) {
                            Map<String, Map<String, DatapointValue>> values =
                                    DynamicDataHandler.handle(IOUtil.wrapXmlRoot(inputStream));
                            return Mono.just(values);
                        }
                    } catch (IOException | DOAPIException e) {
                        log.error("Error converting DataBuffer to InputStream", e);
                        return Mono.error(new MartException("Failed to process data buffer", e));
                    } finally {
                        DataBufferUtils.release(dataBuffer);
                    }
                });
    }

    private List<SecuritiesRequestEntity> partitionEntities(SecuritiesRequestEntity entity, List<List<Datapoint>> datapointPartitions) {
        List<SecuritiesRequestEntity> entityPartitions = new ArrayList<>();
        for (List<Datapoint> partition : datapointPartitions) {
            SecuritiesRequestEntity partialRequestEntity = new SecuritiesRequestEntity();
            partialRequestEntity.setDatapoints(partition);
            partialRequestEntity.setDoApiInvestments(entity.getDoApiInvestments());
            partialRequestEntity.setUserId(entity.getUserIdOrDefault());
            partialRequestEntity.setJobId(entity.getJobId());
            partialRequestEntity.setProductId(entity.getProductId());
            entityPartitions.add(partialRequestEntity);
        }
        return entityPartitions;
    }

    private Mono<DataBuffer> getRawData(String xmlBody, SecuritiesRequestEntity entity) {
        String userId = entity.getUserIdOrDefault();
        String jobId = entity.getJobId();
        return Mono.defer(() -> {
            long startTime = System.currentTimeMillis();
            return webClient
                    .post()
                    .uri(proxyUriBuilder.getUriBuilderURIFunction(userId, jobId))
                    .contentType(MediaType.APPLICATION_XML)
                    .accept(MediaType.APPLICATION_XML)
                    .bodyValue(xmlBody)
                    .header("ETag", UUID.randomUUID().toString())
                    .retrieve()
                    .bodyToMono(DataBuffer.class)
                    .doOnSubscribe(sub -> LogUtils.logDirectAccess(entity))
                    .doOnSuccess(body -> LogUtils.logDirectExecutionTime(entity, startTime))
                    .retryWhen(Retry.backoff(retries, Duration.ofSeconds(backOffSeconds))
                            .maxBackoff(Duration.ofSeconds((long) backOffSeconds * (retries + 1)))
                            .filter(throwable -> throwable instanceof TimeoutException || throwable instanceof PrematureCloseException)
                            .doBeforeRetry(retrySignal ->
                                    log.warn("event_description=\"Retrying Dynamic Direct Proxy request\", retry={}, error=\"{}\", job_id=\"{}\" user_id=\"{}\"",
                                            retrySignal.totalRetries(), retrySignal.failure().getMessage(), jobId, userId))
                            .onRetryExhaustedThrow(((retryBackoffSpec, retrySignal) -> retrySignal.failure())))
                    .doOnError(Exception.class, e -> log.error("event_description=\"Failed Dynamic Direct Proxy request after max retries\", error=\"{}\", job_id=\"{}\" user_id=\"{}\"",
                            e.getMessage(),jobId, userId, e));
        });
    }

    /**
     * Merges multiple partial responses from dynamic data API into one response.
     *
     * @param partialResponses List of partial responses, each containing a map of holdingId to DatapointValue map.
     * @return a merged response, containing a map of holdingId to DatapointValue map.
     */
    private Map<String, Map<String, DatapointValue>> mergeResponses(List<Map<String, Map<String, DatapointValue>>> partialResponses) {
        Map<String, Map<String, DatapointValue>> mergedResponse  = new HashMap<>();

        // Iterate over partial responses
        for (Map<String, Map<String, DatapointValue>> partialResponse : partialResponses) {
            // Add datapoint values to their corresponding investment id
            for (Entry<String, Map<String, DatapointValue>> entry : partialResponse.entrySet()) {
                mergedResponse
                        .computeIfAbsent(entry.getKey(), k -> new HashMap<>())
                        .putAll(entry.getValue());
            }
        }
        return mergedResponse;
    }
}
