package com.morningstar.martgateway.infrastructures.config;

import com.morningstar.dataac.martgateway.core.calculationlib.model.CalcRequest;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntity;
import com.morningstar.dataac.martgateway.core.common.entity.log.LogEntry;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import java.util.function.Function;


@Slf4j
@Aspect
public class ServiceLoggingAspect {


    @Around("execution(* com.morningstar.martgateway.domains.timeseries.service.TScacheService.retrieveTimeSeriesData(..))")
    public Object logRetrieveTimeSeriesData(ProceedingJoinPoint joinPoint) throws Throwable {

        return logDataRetrieval(joinPoint, TSRequest.class, "data retrieval from tscache",
                request ->((TSRequest)request).getDataId());
    }


    @Around("execution(* com.morningstar.martgateway.domains.calc.CalculationService.retrieveCalc(..))")
    public Object logRetrieveCalc(ProceedingJoinPoint joinPoint) throws Throwable {

        return logDataRetrieval(joinPoint, CalcRequest.class, "data retrieval from calc api",
                request -> {
                    CalcRequest calcRequest = (CalcRequest) request;
                    if (calcRequest.getCalcDps() != null && !calcRequest.getCalcDps().isEmpty()) {
                        return String.join(",", calcRequest.getCalcDps());
                    }
                    return "";
                });
    }

    private Object logDataRetrieval(ProceedingJoinPoint joinPoint, Class<?> requestClass, String eventDescription, Function<Object, String> datapointExtractor) throws Throwable {

        Object[] args = joinPoint.getArgs();
        Object request = null;

        for(Object arg : args) {
            if(requestClass.isInstance(arg)) {
                request = arg;
                break;
            }
        }

        Object result = joinPoint.proceed();

        if (result !=null){
            String datapointList = datapointExtractor.apply(request);
            String productId = MDC.get(LogAttribute.PRODUCT_ID.getDisplayName());
            if (StringUtils.hasText(datapointList)) {
                LogEntry.info(
                        new LogEntity(LogAttribute.PRODUCT_ID, productId),
                        new LogEntity(LogAttribute.EVENT_TYPE, "read"),
                        new LogEntity(LogAttribute.EVENT_DESCRIPTION, eventDescription),
                        new LogEntity(LogAttribute.DATAPOINT_LIST, datapointList)
                );
            }
        }

        return result;
    }
}
