package com.morningstar.martgateway.infrastructures.repo.investmentapiproxy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.morningstar.martgateway.domains.apiproxy.entity.DataPointForColumnsResponse;
import com.morningstar.martgateway.domains.apiproxy.entity.Datapoint;
import com.morningstar.martgateway.domains.apiproxy.entity.DatapointValue;
import com.morningstar.martgateway.domains.apiproxy.entity.DoApiInvestment;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesRequestEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.TimeSerialsValue;
import com.morningstar.martgateway.domains.apiproxy.exceptions.DOAPIException;
import com.morningstar.dataac.martgateway.core.common.entity.MartException;
import com.morningstar.martgateway.infrastructures.repo.investmentapiproxy.xml.RegularDataHandler;
import com.morningstar.martgateway.util.LogUtils;
import com.morningstar.martgateway.util.apiproxy.IOUtil;
import java.time.Duration;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import reactor.netty.http.client.PrematureCloseException;
import reactor.util.retry.Retry;

@Slf4j
public class RegularDataRepository {

    private static final int BATCH_SIZE = 100;

    private final WebClient webClient;
    private final ProxyUriBuilder proxyUriBuilder;
    private final int backOffSeconds;
    private final int retries;


    public RegularDataRepository(WebClient webClient, ProxyUriBuilder proxyUriBuilder, int backOffSeconds, int retries) {
        this.webClient = webClient;
        this.proxyUriBuilder = proxyUriBuilder;
        this.backOffSeconds = backOffSeconds;
        this.retries = retries;
    }

    public Flux<DoApiInvestment> getRegularInvestmentData(SecuritiesRequestEntity entity, List<Datapoint> regularDataPoints, List<DataPointForColumnsResponse> dpCols) {
        List<List<DoApiInvestment>> splitSecurities = Lists.partition(entity.getDoApiInvestments(), BATCH_SIZE);

        return Flux.fromIterable(splitSecurities)
                .flatMap(securities -> getFromDirect(entity.copyWithSubInvestmentListAndDatapoints(securities, regularDataPoints)))
                .mapNotNull(dataBuffer -> {
                    try {
                        return convertXmlToJsonData(dataBuffer, entity.getDatapoints(), dpCols);
                    } finally {
                        DataBufferUtils.release(dataBuffer); // prevent memory leak
                    }
                })
                .flatMap(Flux::fromIterable);
    }

    private Mono<DataBuffer> getFromDirect(SecuritiesRequestEntity entity) {
        String requestTemplate = "<req action='get' runuf='0' status='1' subtype='list' type='61' version='AWD3.20' sr='1' er=\"" + BATCH_SIZE + "\" mv='1'></req>";

        String xmlBody = entity.generateRequestXml(requestTemplate, false);
        String userId = entity.getUserId();
        String jobId = entity.getJobId();

        return webClient
                .post()
                .uri(proxyUriBuilder.getUriBuilderURIFunction(entity.getUserIdOrDefault(), jobId))
                .contentType(MediaType.APPLICATION_XML)
                .accept(MediaType.APPLICATION_XML)
                .bodyValue(xmlBody)
                .retrieve()
                .bodyToMono(DataBuffer.class)
                .doOnSubscribe(sub -> LogUtils.logDirectAccess(entity))
                .retryWhen(Retry.backoff(retries, Duration.ofSeconds(backOffSeconds))
                        .maxBackoff(Duration.ofSeconds((long) backOffSeconds * (retries + 1)))
                        .filter(throwable -> throwable instanceof TimeoutException || throwable instanceof PrematureCloseException)
                        .doBeforeRetry(retrySignal ->
                                log.warn("event_description=\"Retrying Regular Direct Proxy request\", retry={}, error=\"{}\", job_id=\"{}\" user_id=\"{}\"",
                                        retrySignal.totalRetries(), retrySignal.failure().getMessage(), jobId, userId))
                        .onRetryExhaustedThrow(((retryBackoffSpec, retrySignal) -> retrySignal.failure())))
                .doOnError(Exception.class, e -> log.error("event_description=\"Failed Regular Direct Proxy request after max retries\", error=\"{}\", job_id=\"{}\" user_id=\"{}\"",
                        e.getMessage(), jobId, userId, e));
    }


    private List<DoApiInvestment> convertXmlToJsonData(DataBuffer dataBuffer, List<Datapoint> flds,
            List<DataPointForColumnsResponse> dpCols) {
        List<DoApiInvestment> doApiInvestments = Lists.newArrayList();

        try (InputStream inputStream = dataBuffer.asInputStream()) {
            LinkedHashMap<String, Datapoint> dpMap = Maps.newLinkedHashMap();
            flds.forEach(e -> dpMap.put(e.getAlias(), e));
            Map<String, DataPointForColumnsResponse> dpColMap = dpCols.stream()
                    .collect(Collectors.toMap(c -> c.getDatapointId() + "_" + c.getAlias(), c -> c));


            Map<String, Map<String, DatapointValue>> datas = RegularDataHandler.handle(IOUtil.wrapXmlRoot(inputStream));
            datas.forEach((id, dpvMap) -> {
                DoApiInvestment doApiInvestment = new DoApiInvestment();
                doApiInvestment.setId(id);
                doApiInvestment.setValues(collectDataPointValues(dpvMap, dpMap, dpColMap));
                doApiInvestments.add(doApiInvestment);
            });
        } catch (IOException | DOAPIException e) {
            log.error("Error in convertXmlToJsonData", e);
            throw new MartException("Failed to process Regular Data buffer", e);
        }
        return doApiInvestments;
    }

    private List<DatapointValue> collectDataPointValues(Map<String, DatapointValue> dpvMap,
                                                        LinkedHashMap<String, Datapoint> dpMap, Map<String, DataPointForColumnsResponse> dpColMap) {
        List<DatapointValue> dpValues = Lists.newArrayList();
        dpMap.forEach((alias, dp) -> {
            DatapointValue dpv = dpvMap.get(alias);
            if (dpv == null) {
                dpv = new DatapointValue();
                dpv.setId(dp.getId());
                dpv.setAlias(dp.getAlias());
            } else {
                updateDataPointValue(dpv, dp, dpColMap);
            }
            dpValues.add(dpv);
        });
        return dpValues;
    }

    private void updateDataPointValue(DatapointValue dpv, Datapoint dp,
                                      Map<String, DataPointForColumnsResponse> dpColMap) {
        dpv.setId(dp.getId());
        if (dp.getIsTsDp()) {
            dpv.setValue(getTimeSerialsListData((List<Pair<String, String>>) dpv.getValue(), dp, dpColMap));
        } else if (dpv.getValue() instanceof List) {
            dpv.setValue(getListData((List<Pair<String, String>>) dpv.getValue()));
        }
    }

    private List<TimeSerialsValue> getTimeSerialsListData(List<Pair<String, String>> vps, Datapoint dp,
                                                          Map<String, DataPointForColumnsResponse> dpColMap) {
        List<TimeSerialsValue> values = Lists.newArrayList();
        DataPointForColumnsResponse dpCol = dpColMap.get(dp.getId() + "_" + dp.getAlias());
        Optional.ofNullable(dpCol).map(DataPointForColumnsResponse::getDatePairs).ifPresent(dates -> {
            Map<Integer, String> tsvMap = Optional.ofNullable(vps).map(
                            es -> es.stream().collect(Collectors.toMap(e -> Integer.parseInt(e.getLeft()), Pair::getRight)))
                    .orElseGet(Maps::newHashMap);
            for (int i = 0; i < dates.size(); i++) {
                values.add(new TimeSerialsValue(dates.get(i).getEndDate(), tsvMap.get(i), i));
            }
        });
        return values;
    }

    private List<Map<String, Object>> getListData(List<Pair<String, String>> vps) {
        return Optional.ofNullable(vps).map(es -> es.stream().map(e -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("date", e.getLeft());
            map.put("value", e.getRight());
            return map;
        }).collect(Collectors.toList())).orElseGet(Lists::newArrayList);
    }
}
