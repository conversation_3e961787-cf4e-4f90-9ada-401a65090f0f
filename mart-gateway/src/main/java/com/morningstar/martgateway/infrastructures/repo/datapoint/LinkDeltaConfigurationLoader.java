package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.Dataset;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.DeltaConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.RdbDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointLoaderInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.AbstractXmlDataPointLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderDefinition;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@DataPointLoaderDefinition(info = DataPointLoaderInfo.DELTA_CONFIGURATION)
public class LinkDeltaConfigurationLoader extends AbstractXmlDataPointLoader {

    public LinkDeltaConfigurationLoader(DatapointConfigFileService datapointConfigFileService, DocumentLoader documentLoader) {
        super(datapointConfigFileService, documentLoader);
    }

    @Override
    public void processDocument(DataPointLoaderContext context, Document document) {
        Map<String, DeltaConfiguration> deltaConfigByGroupName = getDeltaConfigurationBySrcGroup(context, document);
        linkDeltaConfiguration(context, deltaConfigByGroupName);
    }

    private Map<String, DeltaConfiguration> getDeltaConfigurationBySrcGroup(DataPointLoaderContext context, Document document) {
        List<Element> deltaConfigList = document.getRootElement().elements();
        Map<String, DeltaConfiguration> deltaConfigBySrcGroup = new HashMap<>();
        for (Element deltaConfigElement : deltaConfigList) {
            DeltaConfiguration deltaConfiguration = buildDeltaConfiguration(context, deltaConfigElement);
            deltaConfigBySrcGroup.put(deltaConfiguration.getSrcGroup(), deltaConfiguration);
        }
        return deltaConfigBySrcGroup;
    }

    private DeltaConfiguration buildDeltaConfiguration(DataPointLoaderContext context, Element deltaConfigElement) {
        String srcGroup = deltaConfigElement.attributeValue("src-group");
        List<Dataset> datasetList = extractDatasets(context.getDeltaDatasetMap(), deltaConfigElement);
        return new DeltaConfiguration(srcGroup, datasetList);
    }

    private List<Dataset> extractDatasets(Map<String, Dataset> deltaDatasetMap, Element deltaConfigElement) {
        return deltaConfigElement.element("datasets").elements()
                .stream()
                .map(e -> e.attributeValue("name"))
                .filter(deltaDatasetMap::containsKey)
                .map(deltaDatasetMap::get)
                .toList();
    }

    private void linkDeltaConfiguration(DataPointLoaderContext context, Map<String, DeltaConfiguration> deltaConfigByGroupName) {
        Map<String, DataPoint> dataPointMap = context.getDataPointMap();
        for (DataPoint dataPoint : dataPointMap.values()) {
            handleRdbDataPoints(deltaConfigByGroupName, dataPoint);
        }
    }

    private void handleRdbDataPoints(Map<String, DeltaConfiguration> deltaConfigByGroupName, DataPoint dataPoint) {
        handleRdbDataPoint(dataPoint.getCurrentRdb(), deltaConfigByGroupName);
        handleRdbDataPoint(dataPoint.getTsRdb(), deltaConfigByGroupName);
        handleRdbDataPoint(dataPoint.getPostTaxCurrentRdb(), deltaConfigByGroupName);
        handleRdbDataPoint(dataPoint.getPostTaxTsRdb(), deltaConfigByGroupName);
    }

    private void handleRdbDataPoint(RdbDataPoint rdbDataPoint, Map<String, DeltaConfiguration> deltaDataPointGroupMap) {
        if (rdbDataPoint == null || StringUtils.isEmpty(rdbDataPoint.getGroupName())) {
            return;
        }
        Optional.ofNullable(deltaDataPointGroupMap.get(rdbDataPoint.getGroupName()))
                .ifPresent(rdbDataPoint::setDeltaConfiguration);
    }

}
