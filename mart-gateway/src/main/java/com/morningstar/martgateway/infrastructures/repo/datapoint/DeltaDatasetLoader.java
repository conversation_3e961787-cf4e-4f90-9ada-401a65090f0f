package com.morningstar.martgateway.infrastructures.repo.datapoint;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.SearchMapping;
import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.delta.Dataset;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointLoaderInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.AbstractXmlDataPointLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderDefinition;
import org.dom4j.Document;
import org.dom4j.Element;

import java.util.List;

@DataPointLoaderDefinition(info = DataPointLoaderInfo.DELTA_DATASET)
public class DeltaDatasetLoader extends AbstractXmlDataPointLoader {

    private static final String SEARCH_MAPPINGS = "search-mappings";
    private static final String SEARCH_MAPPING = "search-mapping";
    private static final String NAME = "name";
    private static final String ID_LEVEL = "idLevel";
    private static final String TS_TYPE = "tsType";
    private static final String DATA_GROUP = "dataGroup";

    public DeltaDatasetLoader(DatapointConfigFileService datapointConfigFileService, DocumentLoader documentLoader) {
        super(datapointConfigFileService, documentLoader);
    }

    @Override
    public void processDocument(DataPointLoaderContext context, Document document) {
        List<Element> datasets = document.getRootElement().elements();
        for (Element datasetElement : datasets) {
            String name = datasetElement.attributeValue(NAME);
            String idLevel = datasetElement.attributeValue(ID_LEVEL);
            Dataset dataset = Dataset.builder()
                    .name(name)
                    .idLevel(idLevel)
                    .build();
            processSearchMappings(datasetElement, dataset);
            context.getDeltaDatasetMap().put(name, dataset);
        }
    }

    private void processSearchMappings(Element datasetElement, Dataset dataset) {
        Element searchMappingsElement = datasetElement.element(SEARCH_MAPPINGS);
        if (searchMappingsElement == null) {
            return;
        }

        List<SearchMapping> searchMappings = searchMappingsElement.elements(SEARCH_MAPPING).stream()
                .map(this::buildSearchMapping)
                .toList();

        dataset.setSearchMappings(searchMappings);
    }

    private SearchMapping buildSearchMapping(Element searchMappingElement) {
        String tsType = searchMappingElement.attributeValue(TS_TYPE);
        String dataGroup = searchMappingElement.attributeValue(DATA_GROUP);
        return SearchMapping.builder()
                .tsType(tsType)
                .dataGroup(dataGroup)
                .build();
    }

}
