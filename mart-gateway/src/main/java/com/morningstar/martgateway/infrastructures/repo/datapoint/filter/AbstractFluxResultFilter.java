package com.morningstar.martgateway.infrastructures.repo.datapoint.filter;

import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Flux;

import java.util.List;

@Getter
@Setter
public abstract class AbstractFluxResultFilter {

    protected DataPoint dataPoint;

    public abstract Flux<Result> apply(MartRequest martRequest, Flux<Result> resultFlux,
                                       List<? extends DataPoint> dataPoints);
}
