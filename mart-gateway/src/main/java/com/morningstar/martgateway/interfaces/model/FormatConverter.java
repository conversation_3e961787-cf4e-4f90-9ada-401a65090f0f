package com.morningstar.martgateway.interfaces.model;

import com.morningstar.dataac.martgateway.core.common.config.CodeMappings;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.dataac.martgateway.core.common.entity.result.CurrentResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.DataPointError;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupDataTS;
import com.morningstar.dataac.martgateway.core.common.entity.result.GroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TSDatapointValueGroup;
import com.morningstar.dataac.martgateway.core.common.entity.result.TSGroupResultValue;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeseriesGroupData;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeseriesGroupResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.C;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.CurrentPair;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.G;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.GV;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.GroupData;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.MultipleValueDataEntry;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.R;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.SubgroupData;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.TimeseriesData;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.TimeseriesPair;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.util.LanguageUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

@NoArgsConstructor
public final class FormatConverter {


    private static final DateTimeFormatter DATE_FMT_YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * accept Flux of all kinds of result and then return formatted R Flux
     *
     * @return Flux of R
     */
    public Flux<R> format(Flux<Result> resultFlux) {
        return resultFlux
                .filter(r -> r != null && r.getId() != null)
                .groupBy(Result::getId)
                .flatMap(groupedFlux -> groupedFlux.collectList()
                        .flatMapMany(resultList -> Flux.fromIterable(resultList)
                                .map(Result::transform))
                        .groupBy(item -> item.get(0).getClass())
                        .flatMap(subFlux ->
                                //merge lists with same result type
                                subFlux.reduce(new ArrayList(), (origin, append) -> {
                                            origin.addAll(append);
                                            return origin;
                                        })
                                        .doOnNext(Collections::sort)
                        )
                        .reduce(new R(groupedFlux.key()), (R::appendAttribute)));
    }

    public Mono<InvestmentResponse> formatInvestmentResponse(Flux<Result> resultFlux) {
        return formatInvestmentResponse(resultFlux, Collections.emptyMap());
    }

    public Mono<InvestmentResponse> formatInvestmentResponse(Flux<Result> resultFlux, Map<String, String> dpIdAliasMap) {
        return resultFlux.collectList()
                .map(results -> {
                    List<Investment> investments = buildInvestments(results, dpIdAliasMap);
                    Status status = null;
                    if (investments.stream().anyMatch(investment -> !investment.getErrors().isEmpty())) {
                        status = Status.SUCCESS_WITH_DP_ERRORS;
                    }
                    return new InvestmentResponse(status, investments);
                });
    }

    private List<Investment> buildInvestments(List<Result> results, Map<String, String> dpIdAliasMap) {
        return results.stream()
                .filter(result -> result != null && result.getId() != null)
                .collect(groupingBy(Result::getId))
                .entrySet().stream()
                .map(entry -> {
                    String investmentId = entry.getKey();
                    List<Result> resultsForId = entry.getValue();
                    List<GroupData> groupDataList = getGroupDataList(resultsForId, dpIdAliasMap);
                    List<TSDatapointValueGroup> groupTsList= groupTSDataList(resultsForId);
                    return new Investment(
                            investmentId,
                            getCurrentPairList(resultsForId, dpIdAliasMap),
                            groupDataList,
                            getTimeseriesDataMap(resultsForId, groupDataList,dpIdAliasMap).values().stream()
                                    .flatMap(Collection::stream)
                                    .collect(toList()),
                            getDataPointErrors(resultsForId),
                            groupTsList
                    );
                })
                .collect(toList());
    }

    private Set<DataPointError> getDataPointErrors(List<Result> resultsForId) {
        return Stream.concat(
                        getErrorsFromErrorResults(resultsForId).stream(),
                        getErrorsFromOtherResults(resultsForId).stream())
                .collect(Collectors.toSet());
    }

    private static Set<DataPointError> getErrorsFromOtherResults(List<Result> resultsForId) {
        return resultsForId
                .stream()
                .map(Result::getErrors)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }

    private Set<DataPointError> getErrorsFromErrorResults(List<Result> resultsForId) {
        return resultsForId.stream()
                .filter(ErrorResult.class::isInstance)
                .map(ErrorResult.class::cast)
                .map(x -> new DataPointError(x.getDatapointId(), x.getErrorCode()))
                .collect(Collectors.toSet());
    }

    private List<CurrentPair> getCurrentPairList(List<? extends Result> resultList, Map<String, String> dpIdAliasMap) {
        Map<String, String> languageResults = resultList.stream()
                .filter(Objects::nonNull)
                .filter(LanguageUtil::hasLanguageMetaData)
                .map(r -> (Map<String, String>) r.getValues())
                .filter(Objects::nonNull)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        Set<CurrentPair> uniquePairs = new HashSet<>();
        for (Result result : resultList) {
            if (result instanceof CurrentResult currentResult) {
                Map<String, String> paramMap = new HashMap<>();
                Map<String, Map<String, String>> primaryKeys = currentResult.getPrimaryKeys();
                if (MapUtils.isNotEmpty(primaryKeys) && primaryKeys.containsKey("primaryKeys")) {
                    paramMap = primaryKeys.get("primaryKeys");
                }
                if (MapUtils.isNotEmpty(currentResult.getValues())) {
                    Map<String, String> map = currentResult.getValues();
                    for (Map.Entry<String, String> entry : map.entrySet()) {
                        CurrentPair pair = createCurrentPair(
                                entry.getKey(),
                                languageResults.getOrDefault(entry.getKey(), entry.getValue()),
                                getDpsMetaData(currentResult.getPrimaryKeys(), entry.getKey()),
                                dpIdAliasMap,paramMap
                        );
                        uniquePairs.add(pair);
                    }
                }
            }
        }
        return new ArrayList<>(uniquePairs);
    }

    private CurrentPair createCurrentPair(String k, String v, Map<String, String> metaData, Map<String, String> dpIdAliasMap,Map<String, String> paramMap) {
        CurrentPair currentPair;
        String trimmedValue = trimValue(v);
        Optional<String> codeMappedValueOpt = CodeMappings.getCodeMapping(dpIdAliasMap.get(k), trimmedValue);
        if (!dpIdAliasMap.isEmpty() && codeMappedValueOpt.isPresent()) {
            currentPair = new CurrentPair(k, codeMappedValueOpt.get(), trimmedValue, metaData);
        } else if(!paramMap.isEmpty()){
            currentPair = new CurrentPair(k, trimmedValue, null,paramMap);
        }else {
            currentPair = new CurrentPair(k, trimmedValue, null, metaData);
        }
        return currentPair;
    }

    private CurrentPair createSubCurrentPair(String k, String v, Map<String, String> metaData, Map<String, String> dpIdAliasMap) {
        String trimmedValue = trimValue(v);
        Optional<String> codeMappedValueOpt = CodeMappings.getCodeMapping(dpIdAliasMap.getOrDefault(k, k), trimmedValue);
        return codeMappedValueOpt.map(s -> new CurrentPair(k, s, trimmedValue, metaData))
                .orElseGet(() -> new CurrentPair(k, trimmedValue, null, metaData));
    }

    private List<GroupData> getGroupDataList(List<Result> resultList, Map<String, String> dpIdAliasMap) {
        Map<String, List<CurrentResult>> languageResults = new HashMap<>();
        resultList.stream().filter(r -> LanguageUtil.hasLanguageMetaData(r)).forEach(r -> languageResults.putAll(r.getValues()));
        return resultList.stream().filter(result -> result instanceof GroupResult)
                .map(result -> (GroupResult) result)
                .flatMap(result -> {
                    if (MapUtils.isNotEmpty(result.getValues())) {
                        Map<String, List<CurrentResult>> map = result.getValues();
                        if (result.isMultipleValues()) {
                            return map.entrySet().stream().map(data -> createMultipleValueGroupData(data.getKey(),
                                    languageResults.getOrDefault(data.getKey(), data.getValue()), dpIdAliasMap));
                        } else {
                            return map.entrySet().stream().map(entry -> createGroupData(entry.getKey(), entry.getValue(),
                                    getDpsMetaData(result.getPrimaryKeys(), entry.getKey()), dpIdAliasMap));
                        }
                    } else {
                        return Stream.empty();
                    }
                }).distinct().collect(Collectors.toList());
    }

    private GroupData createGroupData(String datapointId, List<CurrentResult> value, Map<String, String> metaData, Map<String, String> dpIdAliasMap) {
        List<SubgroupData> subgroupDataList = getSubgroupDataList(value, dpIdAliasMap);
        return new GroupData(datapointId, subgroupDataList, null, metaData);
    }

    private List<SubgroupData> getSubgroupDataList(List<? extends Result> resultList, Map<String, String> dpIdAliasMap) {
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        return resultList.stream()
                .filter(CurrentResult.class::isInstance)
                .map(CurrentResult.class::cast)
                .map(result -> {
                    List<CurrentPair> currentPairs = result.getValues().entrySet().stream()
                            .map(entry -> createSubCurrentPair(entry.getKey(), entry.getValue(),
                                    getDpsMetaData(result.getPrimaryKeys(), entry.getKey()), dpIdAliasMap))
                            .collect(Collectors.toList());
                    return new SubgroupData(currentPairs);
                }).collect(Collectors.toList());
    }

    private GroupData createMultipleValueGroupData(String key, List<CurrentResult> values, Map<String, String> dpIdAliasMap) {
        return createMultipleValueGroupData(key, values, null, dpIdAliasMap);
    }

    private GroupData createMultipleValueGroupData(String datapointId, List<CurrentResult> value,
                                                   Map<String, String> metaData, Map<String, String> dpIdAliasMap) {
        List<MultipleValueDataEntry> dataEntries = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(value)) {
            dataEntries = value.stream().map(result -> {
                String dataPointId = (dpIdAliasMap != null && dpIdAliasMap.containsKey(datapointId)) ? dpIdAliasMap.get(datapointId) : datapointId;
                String dataPointValue = result.getValues().get(datapointId);
                Optional<String> codeMappedValueOpt = Optional.empty();
                if (StringUtils.isNotEmpty(dataPointValue)) {
                    dataPointValue = dataPointValue.trim();
                    codeMappedValueOpt = CodeMappings.getCodeMapping(dataPointId, dataPointValue);
                }
                if (codeMappedValueOpt.isPresent()) {
                    return new MultipleValueDataEntry(dataPointValue, codeMappedValueOpt.get());
                }
                return new MultipleValueDataEntry(null, dataPointValue);
            }).collect(Collectors.toList());
        }
        return new GroupData(datapointId, null, dataEntries, metaData);
    }

    private Map<String, List<TimeseriesData>> getTimeseriesDataMap(List<Result> resultList,
                                                                   List<GroupData> groupDataList,
                                                                   Map<String, String> dpIdAliasMap) {
        List<TimeSeriesResult> tsResultList = resultList.stream().filter(result -> result instanceof TimeSeriesResult).map(result -> (TimeSeriesResult) result).collect(toList());
        Map<String, List<TimeseriesData>> timeseriesDataMap = new HashMap<>();
        for (TimeSeriesResult result : tsResultList) {
            handleTimeSeriesData(result, timeseriesDataMap, groupDataList, dpIdAliasMap);
        }

        List<TsResult> tsProtoResults = resultList.stream()
                .filter(result -> result instanceof TsResult)
                .map(result -> (TsResult) result)
                .toList();

        for (TsResult tsProto : tsProtoResults) {
            handleTsProtoData(tsProto, timeseriesDataMap);
        }

        return timeseriesDataMap;
    }

    private void handleTimeSeriesData(TimeSeriesResult result, Map<String, List<TimeseriesData>> timeseriesDataMap,
                                      List<GroupData> groupDataList, Map<String, String> dpIdAliasMap) {
        if (MapUtils.isNotEmpty(result.getValues())) {
            for (Map.Entry<String, List<V>> entry : result.getValues().entrySet()) {
                if (CollectionUtils.isNotEmpty(entry.getValue()) && entry.getValue().get(0) instanceof GV) {
                    entry.getValue().forEach(gv -> transformInvestmentResponseGroupValue(entry.getKey(), (GV) gv, groupDataList,
                            getDpsMetaData(result.getPrimaryKeys(), entry.getKey()), dpIdAliasMap));
                } else {
                    TimeseriesData tsData = result.getMultipleValues() != null && result.getMultipleValues()
                            ? transformMultipleValueResponseValues(entry.getKey(), entry.getValue(), getDpsMetaData(result.getPrimaryKeys(), entry.getKey()), dpIdAliasMap)
                            : transformInvestmentResponseValues(entry.getKey(), entry.getValue(), getDpsMetaData(result.getPrimaryKeys(), entry.getKey()), dpIdAliasMap);
                    if (timeseriesDataMap.containsKey(entry.getKey()) && MapUtils.isEmpty(tsData.getPrimaryKeys())) {
                        timeseriesDataMap.get(entry.getKey()).forEach(d -> d.merge(tsData.getTimeseriesPairList()));
                    } else {
                        List<TimeseriesData> tsDataList = timeseriesDataMap.computeIfAbsent(entry.getKey(), i -> new ArrayList<>());
                        tsDataList.add(tsData);
                    }
                }
            }
        }
    }

    private void handleTsProtoData(TsResult result, Map<String, List<TimeseriesData>> timeseriesDataMap) {
        if (MapUtils.isEmpty(result.getValues())) {
            return;
        }

        for (Map.Entry<String, TsDataProtoBuf.TSDataDouble> entry : result.getValues().entrySet()) {
            String dpId = entry.getKey();
            TsDataProtoBuf.TSDataDouble ts = entry.getValue();
            if (ts.getDatesCount() != ts.getValuesCount()) {
                throw new RuntimeException("Dates and values count mismatch for datapoint " + dpId);
            }

            TimeseriesData tsData = new TimeseriesData();
            tsData.setDatapointId(dpId);
            List<TimeseriesPair> pairs = new ArrayList<>();
            int count = ts.getDatesCount();
            for (int i = 0; i < count; i++) {
                TimeseriesPair pair = new TimeseriesPair();
                String date = LocalDate.of(1900, 1, 1).plusDays(ts.getDates(i)).format(DATE_FMT_YYYY_MM_DD);
                pair.setDate(date);
                String raw = String.valueOf(ts.getValues(i));
                pair.setValue(raw);
                pairs.add(pair);
            }
            tsData.setTimeseriesPairList(pairs);
            List<TimeseriesData> list = timeseriesDataMap.computeIfAbsent(dpId, k -> new ArrayList<>());
            list.add(tsData);
        }

    }

    private void transformInvestmentResponseGroupValue(String id, GV gv, List<GroupData> groupDataList,
                                                       Map<String, String> metaData, Map<String, String> dpIdAliasMap) {
        if (gv == null) {
            return;
        }

        for (G g : gv.getG()) {
            for (C c : g.getS()) {
                GroupData groupData = new GroupData();
                groupData.setDatapointId(id);
                SubgroupData subgroupData = new SubgroupData();
                List<SubgroupData> subgroupDataList = new ArrayList<>();
                List<CurrentPair> currentPairList = new ArrayList<>();
                for (V v : c.getC()) {
                    String trimmedValue = trimValue(v.getV());
                    CurrentPair currentPair = new CurrentPair();
                    Optional<String> mappedCodeValueOpt = CodeMappings.getCodeMapping(dpIdAliasMap.get(v.getI()), trimmedValue);
                    if (!dpIdAliasMap.isEmpty() && mappedCodeValueOpt.isPresent()) {
                        currentPair.setDatapointId(v.getI());
                        currentPair.setCode(trimmedValue);
                        currentPair.setValue(mappedCodeValueOpt.get());
                        currentPairList.add(currentPair);
                    } else {
                        currentPair.setDatapointId(v.getI());
                        currentPair.setValue(trimmedValue);
                        currentPairList.add(currentPair);
                    }
                }
                subgroupData.setValues(currentPairList);
                subgroupDataList.add(subgroupData);
                groupData.setSubGroupData(subgroupDataList);
                groupData.setPrimaryKeys(metaData);
                groupDataList.add(groupData);
            }
        }
    }

    private TimeseriesData transformInvestmentResponseValues(String id, List<V> values, Map<String, String> metaData,
                                                             Map<String, String> dpIdAliasMap) {
        TimeseriesData timeseriesData = new TimeseriesData();
        timeseriesData.setDatapointId(id);
        timeseriesData.setPrimaryKeys(metaData);
        if (CollectionUtils.isNotEmpty(values)) {
            List<TimeseriesPair> timeseriesPairList = new ArrayList<>();
            for (V v : values) {
                TimeseriesPair timeseriesPair = new TimeseriesPair();
                handleTimeSeriesPairDates(timeseriesPair, v);
                String trimmedValue = trimValue(v.getV());
                Optional<String> value = CodeMappings.getCodeMapping(dpIdAliasMap.get(id), trimmedValue);
                if (!dpIdAliasMap.isEmpty() && value.isPresent()) {
                    timeseriesPair.setValue(value.get());
                    timeseriesPair.setCode(trimmedValue);
                } else {
                    timeseriesPair.setValue(trimmedValue);
                }
                timeseriesPairList.add(timeseriesPair);
            }
            timeseriesData.setTimeseriesPairList(timeseriesPairList);
        }
        return timeseriesData;
    }

    private TimeseriesData transformMultipleValueResponseValues(String id, List<V> values, Map<String, String> metaData,
                                                                Map<String, String> dpIdAliasMap) {
        TimeseriesData timeseriesData = new TimeseriesData();
        timeseriesData.setDatapointId(id);
        timeseriesData.setPrimaryKeys(metaData);
        if (CollectionUtils.isNotEmpty(values)) {
            Map<String, TimeseriesPair> timeseriesPairMap = new HashMap<>();
            for (V v : values) {
                String date = v.getI();
                TimeseriesPair timeseriesPair = timeseriesPairMap.getOrDefault(date, new TimeseriesPair());
                handleTimeSeriesPairDates(timeseriesPair, v);
                String trimmedValue = trimValue(v.getV());
                Optional<String> value = CodeMappings.getCodeMapping(dpIdAliasMap.get(id), trimmedValue);
                MultipleValueDataEntry entry;
                if (!dpIdAliasMap.isEmpty() && value.isPresent()) {
                    entry = new MultipleValueDataEntry(trimmedValue, value.get());
                } else {
                    entry = new MultipleValueDataEntry(null, trimmedValue);
                }
                if (timeseriesPair.getMultipleValueDataEntries() == null) {
                    timeseriesPair.setMultipleValueDataEntries(new ArrayList<>());
                }
                timeseriesPair.getMultipleValueDataEntries().add(entry);
                timeseriesPairMap.put(date, timeseriesPair);
            }
            List<TimeseriesPair> timeseriesPairList = new ArrayList<>(timeseriesPairMap.values());
            timeseriesData.setTimeseriesPairList(timeseriesPairList);
        }
        return timeseriesData;
    }

    private Map<String, String> getDpsMetaData(Map<String, Map<String, String>> metaMap, String dpsId) {
        return MapUtils.isNotEmpty(metaMap) ? metaMap.get(dpsId) : null;
    }

    private void handleTimeSeriesPairDates(TimeseriesPair timeseriesPair, V v) {
        String[] timePeriodString = v.getI().split(";");
        if (timePeriodString.length == 2) {
            // if a time period has start and end date, then output start and end date
            timeseriesPair.setStartDate(timePeriodString[0]);
            timeseriesPair.setEndDate(timePeriodString[1]);
        } else {
            // else if a time period has only one date, then output date.
            timeseriesPair.setDate(v.getI());
        }
    }

    private String trimValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }
        return value.trim();
    }

    /**
     * Groups the Time Series Data List by the datapointId and returns a list of TSDatapointValueGroup.
     */
    private List<TSDatapointValueGroup> groupTSDataList(List<Result> resultsForId) {
        if (resultsForId == null) {
            return Collections.emptyList();
        }

        return resultsForId.stream()
                .filter(TimeseriesGroupResult.class::isInstance)
                .map(TimeseriesGroupResult.class::cast)
                .map(this::mapGroupResultToDatapointValueGroup)
                .collect(Collectors.toList());
    }

    private TSDatapointValueGroup mapGroupResultToDatapointValueGroup(TimeseriesGroupResult groupResult) {
        Map<String, List<TSGroupResultValue>> dateToValuesMap = flattenGroupResultToDateValuesMap(groupResult);

        List<TimeseriesGroupData> timeSeriesDataList = dateToValuesMap.entrySet().stream()
                .map(this::createTimeseriesGroupData)
                .collect(Collectors.toList());

        String aliasKey = Optional.ofNullable(groupResult.getValues())
                .filter(map -> !map.isEmpty())
                .map(map -> map.keySet().iterator().next())
                .orElse(groupResult.getDatapointId());

        return new TSDatapointValueGroup(aliasKey, timeSeriesDataList);
    }

    private Map<String, List<TSGroupResultValue>> flattenGroupResultToDateValuesMap(TimeseriesGroupResult groupResult) {
        return groupResult.getValues().values().stream()
                .flatMap(List::stream)
                .flatMap(dpGroup -> dpGroup.getTimeSeriesData().stream())
                .flatMap(tsData -> tsData.getGroupData().stream()
                        .flatMap(groupData -> groupData.getValues().stream()
                                .map(val -> new AbstractMap.SimpleEntry<>(tsData.getDate(), val))))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        LinkedHashMap::new,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
    }

    private TimeseriesGroupData createTimeseriesGroupData(Map.Entry<String, List<TSGroupResultValue>> entry) {
        String date = entry.getKey();
        List<TSGroupResultValue> allValues = entry.getValue();

        Map<String, List<String>> dpIdToValues = allValues.stream()
                .collect(Collectors.groupingBy(
                        TSGroupResultValue::getDatapointId,
                        LinkedHashMap::new,
                        Collectors.mapping(TSGroupResultValue::getValue, Collectors.toList())
                ));

        int maxSize = dpIdToValues.values().stream()
                .mapToInt(List::size)
                .max()
                .orElse(0);

        List<GroupDataTS> zippedGroupData = IntStream.range(0, maxSize)
                .mapToObj(i -> dpIdToValues.entrySet().stream()
                        .filter(e -> i < e.getValue().size())
                        .map(e -> new TSGroupResultValue(e.getKey(), e.getValue().get(i)))
                        .collect(Collectors.collectingAndThen(
                                Collectors.toList(),
                                values -> values.isEmpty() ? null : new GroupDataTS(values)
                        )))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return new TimeseriesGroupData(date, zippedGroupData);
    }
}
