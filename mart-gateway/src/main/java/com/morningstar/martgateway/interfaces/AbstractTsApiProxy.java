package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.DataPointAggregate;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.datapoint.IDataPoint;
import com.morningstar.martgateway.applications.tscacheproxy.TsCacheProxyApplication;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.martgateway.interfaces.model.tscacheproxy.FilteredDataPoints;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.morningstar.martgateway.util.TsCacheProxyIdUtil;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public abstract class AbstractTsApiProxy {
    private static final String SEC_ID = "SecId";
    protected final MartDataPipeline martDataPipeLine;

    protected final TsCacheProxyApplication tsCacheProxyApplication;

    protected final IdMapUtil idMapUtil;

    protected static final long START_EPOCH_DAY = LocalDate.of(1900, 1, 1).toEpochDay();
    protected static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    protected AbstractTsApiProxy(MartDataPipeline martDataPipeLine, TsCacheProxyApplication tsCacheProxyApplication, IdMapUtil idMapUtil) {
        this.martDataPipeLine = martDataPipeLine;
        this.tsCacheProxyApplication = tsCacheProxyApplication;
        this.idMapUtil = idMapUtil;
    }

    protected FilteredDataPoints filterDps(List<String> dpsList) {
        Set<String> martDataPoints = new HashSet<>();
        Set<String> tsCacheDataPoints = new HashSet<>();
        for (String dp : dpsList) {
            if (DataPointRepository.dataPointAggregateMapContain(dp)) {
                IDataPoint dataPoint = DataPointRepository.getDpByNid(dp, DataPointAggregate.SrcType.TS);
                if (dataPoint != null) {
                    martDataPoints.add(dp);
                }
            } else if (DataPointRepository.contain(dp)) {
                DataPoint dataPoint = DataPointRepository.getByNid(dp);

                if (dataPoint.isRdbDataPoint()) {
                    martDataPoints.add(dp);
                } else if(dataPoint.isTsApiDataPoint()){ //if it's a defined TSAPI datapoint, use TSGateway thru MartDataPipeline
                    martDataPoints.add(dp);
                }
                else{
                    addTsDatapoint(tsCacheDataPoints,dp);
                }
            } else {
                addTsDatapoint(tsCacheDataPoints,dp);
            }
        }
        return new FilteredDataPoints(new ArrayList<>(martDataPoints), new ArrayList<>(tsCacheDataPoints));
    }

    private void addTsDatapoint(Set<String> tsCacheDataPoints, String dp) {
        if(TsCacheProxyIdUtil.hasId(dp)) {
            tsCacheDataPoints.add(dp);
        }
    }

    protected Map<String, String> getTsSecIdMapAndPrepareRequests(MartRequest martRequest, TSRequest tsRequest) {
        FilteredDataPoints filteredDataPoints = filterDps(martRequest.getDps());
        tsRequest.setDataId(String.join(",", filteredDataPoints.getTsCacheDataPoints()));

        martRequest.setDps(filteredDataPoints.getMartDataPoints());

        Map<String, String> secIdRequestedIdMap = getSecIdForTsRequest(martRequest);
        tsRequest.setSecIds(String.join(",", secIdRequestedIdMap.keySet()));

        return secIdRequestedIdMap;
    }

    protected String buildSecId(String secId, Map<String, IdMapper> idMapperById) {
        if (idMapperById.containsKey(secId)) {
            secId = secId + ";" + idMapperById.get(secId).getSecurityType();
        }
        return secId;
    }

    protected Map<String, String> getSecIdForTsRequest(MartRequest martRequest) {
        Map<String, String> secIdRequestedIdMap = new HashMap<>();
        if (StringUtils.isNotEmpty(martRequest.getCategoryCode())) {
            String id = martRequest.getCategoryCode();
            secIdRequestedIdMap.put(id, id);
        } else {
            secIdRequestedIdMap = martRequest.getIdMappers()
                    .stream().filter(idMapper -> StringUtils.isNotEmpty(idMapper.getId(SEC_ID)))
                    .collect(Collectors.toMap(idMapper -> idMapper.getId(SEC_ID), IdMapper::getInvestmentId, (existing, replacement) -> String.join(",", existing, replacement)));
        }

        return secIdRequestedIdMap;
    }

    protected TSRequest getTsCacheRequest(MartRequest martRequest) {
        return TSRequest.builder()
                .adjustment(StringUtils.isEmpty(martRequest.getAdjustment()) ? "1" : martRequest.getAdjustment())
                .endDate(martRequest.getEndDate())
                .format(0)
                .startDate(martRequest.getStartDate())
                .categoryCode(martRequest.getCategoryCode())
                .universe(martRequest.getUniverse())
                .build();
    }

    protected Flux<Result> getInputTsIdResult(Flux<Result> tsResult, Map<String, String> secIdRequestedIdMap) {
        return tsResult.map(result -> new TimeSeriesResult(secIdRequestedIdMap.get(result.getId()), result.getValues()))
                .flatMap(timeSeriesResult -> {
                    if (timeSeriesResult.getId() != null && timeSeriesResult.getId().contains(",")) {
                        //for two ids(PerformanceId/ShareClassId) share the same SecId
                        String[] arr = timeSeriesResult.getId().split(",");
                        return Flux.fromArray(arr).map(id -> new TimeSeriesResult(id, timeSeriesResult.getValues()));
                    } else {
                        return Flux.just(timeSeriesResult);
                    }
                });
    }
    protected Flux<Result> getTsData(TSRequest tsRequest) {
        return getTsCacheData(tsRequest).filter(data -> data != null && data.getContent() != null && data.getContent().getItems() != null)
                .flatMap(data -> {
                    List<TimeSeriesResult> tsList = new ArrayList<>();
                    data.getContent().getItems().forEach(tsItem -> {
                        Map<String, List<V>> map = new HashMap<>();
                        map.put(tsItem.getDataid(), convertTsDataToV(tsItem.getData()));
                        tsList.add(new TimeSeriesResult(tsItem.getSecid().split(";")[0], map));
                    }) ;
                    return Mono.just(tsList).flatMapMany(Flux::fromIterable);
                });
    }

    private Flux<TSResponse> getTsCacheData(TSRequest tsRequest) {
        return tsCacheProxyApplication.retrieve(tsRequest);
    }
    private List<V> convertTsDataToV(List<TSData> tsDataList) {
        List<V> v = new ArrayList<>();
        tsDataList.forEach(tsData -> {
            String date = getDateFromEpochDay(tsData.getDate());
            String value = String.valueOf(tsData.getValue());
            v.add(new V(date, value));
        });

        return v;
    }

    private String getDateFromEpochDay(long days) {
        return LocalDate.of(1900, 1, 1).plusDays(days).toString();
    }
}
