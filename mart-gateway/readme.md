It's a common gateway component to access different data source. It's not a runnable project but a spring-boot starter depndency. 
To use this program:

1.Please place file Calculation_CPP.dll to windows/system32 in order to run this application locally. It is a cpp calculation library.
https://mswiki.morningstar.com/display/AS/How+to+resolve+Calculation_CPP.dll+issue+in+Windows

2.Assume DATAAC aws account operator/readonly role since the application access resources in aws. 

3.execute "mvn install" in command line

4.Add below dependency to pom.xml:
		<dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>mart-gateway-spring-boot-starter</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>



For protobuf refresh, download protoc from
https://repo1.maven.org/maven2/com/google/protobuf/protoc/3.25.1/
Tutorial: https://protobuf.dev/getting-started/javatutorial/

Run the Java class generation for the .proto file in src/main/resources/protobuf/TsCacheData.proto
Replace the Java class in com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtobuf
