<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>mart-component-gateway</artifactId>
        <groupId>com.morningstar</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.morningstar</groupId>
    <artifactId>mart-gateway</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>Mart-Gateway</name>
    <description>Mart data access gateway starter component</description>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring.boot.version>2.7.18</spring.boot.version>
        <surefire.version>3.2.5</surefire.version>
        <project.encoding>UTF-8</project.encoding>
        <jest.version>6.3.1</jest.version>
        <jaxen.version>1.1.6</jaxen.version>
        <json.version>20200518</json.version>
        <gson.version>2.8.6</gson.version>
        <commons.pool.version>2.8.0</commons.pool.version>
        <reactor.version>3.4.41</reactor.version>
        <redis.version>5.1.4.RELEASE</redis.version>
        <calculation.version>1.0.0</calculation.version>
        <messaging.version>1.0.49-RELEASE</messaging.version>
        <junit-jupiter.version>5.10.5</junit-jupiter.version>
        <sqljdbc.version>9.1.0.jre8-preview</sqljdbc.version>
        <mybatis-spring-boot.version>2.1.3</mybatis-spring-boot.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.4.Final</mapstruct.version>
        <data-point-rest-client.version>1.10</data-point-rest-client.version>
        <avro.version>1.9.2</avro.version>

        <!-- internal share lib -->
        <entitlement.project.version>4.6-RELEASE</entitlement.project.version>

        <sonar.coverage.exclusions>
            **/entity/**/*,
            **/event/**/*,
            **/*Configuration.java,
            **/*Properties.java
        </sonar.coverage.exclusions>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${spring.boot.version}</version>
            <optional>true</optional>
        </dependency>

        <!-- observability -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring.boot.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
            <version>5.3.18</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.18</version>
        </dependency>

        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
            <version>${reactor.version}</version>
        </dependency>

        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <version>${reactor.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.4</version>
        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <!-- ElasticSearch -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons.pool.version}</version>
        </dependency>

        <!-- Dom4j & XPath -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
        </dependency>

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${json.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-protobuf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>

        <!-- Java Bean -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty</artifactId>
            <version>0.9.23.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.morningstar.calculation</groupId>
            <artifactId>calculation</artifactId>
            <version>${calculation.version}</version>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>com.morningstar.calculation</groupId>
            <artifactId>calculationlibrary</artifactId>
            <version>${calculation.version}</version>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>com.morningstar.messaging</groupId>
            <artifactId>domain-objects</artifactId>
            <version>${messaging.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.21</version>
        </dependency>

        <dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>redshift-connection-lib</artifactId>
            <version>2.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
            <version>5.2.8.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>${sqljdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.1</version>
        </dependency>

        <dependency>
            <groupId>io.projectreactor.tools</groupId>
            <artifactId>blockhound</artifactId>
            <version>1.0.8.RELEASE</version>
        </dependency>

        <!-- tests -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <version>${avro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro-maven-plugin</artifactId>
            <version>${avro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro-compiler</artifactId>
            <version>${avro.version}</version>
        </dependency>

        <!-- jaxb -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>4.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>4.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.2.0-jre</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>data-point-loader</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>data-point-loader-test</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>entitlement-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>uim-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>gateway-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>fixed-income-ice-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>rdb-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>redisson-lock</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>mart-equity-package</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar</groupId>
            <artifactId>entitlement-eod-data</artifactId>
            <version>${entitlement.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>custom-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>eod-data</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>calculation-lib-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.morningstar.dataac.martgateway</groupId>
            <artifactId>time-series-data</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>false</fork>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire.version}</version>
                <configuration>
                    <excludes>
                        <exclude>**/*IT.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-maven-plugin</artifactId>
                <version>${avro.version}</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>schema</goal>
                        </goals>
                        <configuration>
                            <sourceDirectory>${project.basedir}/src/main/resources/avro/</sourceDirectory>
                            <outputDirectory>${project.basedir}/src/main/java/</outputDirectory>
                            <includes>
                                <include>**/*.avsc</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>mart-gateway</finalName>
    </build>

    <pluginRepositories>
        <pluginRepository>
            <id>maven-dev-group</id>
            <url>https://artifacts.morningstar.com/repository/maven-dev-group/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
