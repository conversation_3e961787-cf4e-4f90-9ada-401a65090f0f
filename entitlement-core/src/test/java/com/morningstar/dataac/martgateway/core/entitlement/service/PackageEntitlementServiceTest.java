package com.morningstar.dataac.martgateway.core.entitlement.service;

import com.google.common.collect.Lists;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CellMatch;
import com.morningstar.dataac.martgateway.core.entitlement.entity.Criteria;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageRefInfoFactory;
import com.morningstar.dataac.martgateway.core.entitlement.entity.RowFilter;
import com.morningstar.dataac.martgateway.core.entitlement.entity.Universe;
import com.morningstar.dataac.martgateway.core.entitlement.entity.DataPackageItem;
import com.morningstar.dataac.martgateway.core.entitlement.entity.EntitlementMetaData;
import com.morningstar.dataac.martgateway.core.entitlement.entity.IdEntitleStatus;
import com.morningstar.dataac.martgateway.core.entitlement.entity.LicenseCellResponse;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail.EntitlementResponse;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail.EntitlementResponse.IdValue;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail.EntitlementResponse.IndexSeries;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageEntitlementResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.Set;
import java.util.stream.Collectors;

import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageRefInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.INDEX_IDS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.NON_INDEX_IDS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.SERIES_DP;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.VIEW;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.FEED;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.EXPORT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PackageEntitlementServiceTest {
    PackageEntitlementService packageEntitlementService;
    @Mock
    EntitlementBypassService entitlementBypassService;

    IdMapperEntitlementService idMapperEntitlementService;
    @Mock
    EntitlementCacheService entitlementCacheService;
    DataPointPackageConfigLoader dataPointPackageConfigLoader;

    @BeforeEach
    void before() {
        idMapperEntitlementService = new IdMapperEntitlementService();
        dataPointPackageConfigLoader = new DataPointPackageConfigLoader(entitlementCacheService);
        packageEntitlementService = new PackageEntitlementService(
                entitlementCacheService,
                idMapperEntitlementService,
                new RowFilterService(idMapperEntitlementService),
                dataPointPackageConfigLoader,
                entitlementBypassService);

        Map<String, PackageRefInfo> pckgRefMap = new HashMap<>();
        pckgRefMap.put("1", PackageRefInfoFactory.create("Morningstar Category", false, Collections.emptyList()));
        pckgRefMap.put("2", PackageRefInfoFactory.create("Third Party Package", true, Collections.emptyList()));
        pckgRefMap.put("3", PackageRefInfoFactory.create("Third Party Package (Historical)", true, Arrays.asList("isAllowHistoricalData")));
        pckgRefMap.put("4", PackageRefInfoFactory.create("Internal Package", false, Collections.emptyList()));
        pckgRefMap.put("6", PackageRefInfoFactory.create("Some Equity Stuff", false, Collections.emptyList()));
        pckgRefMap.put("7", PackageRefInfoFactory.create("Some Equity Stuff", false, Arrays.asList("isAllowHistoricalData")));
        pckgRefMap.put("157", PackageRefInfoFactory.create("Index Package 1", true, Arrays.asList("isIndex")));
        pckgRefMap.put("158", PackageRefInfoFactory.create("Index Package 2", true, Arrays.asList("isIndex")));
        pckgRefMap.put("159", PackageRefInfoFactory.create("Index Package 3", true, Arrays.asList("isIndex")));
        pckgRefMap.put("200", PackageRefInfoFactory.create("Reference Index Package", false, Arrays.asList("isIndex")));
        pckgRefMap.put("955", PackageRefInfoFactory.create("Fixed Income package", false, Arrays.asList("isNotAllowFeedGeneration")));

        pckgRefMap.put("951", PackageRefInfoFactory.create("EOD Price Data", true, Arrays.asList("isByPassLicenseCheckForView")));
        pckgRefMap.put("902", PackageRefInfoFactory.create("Bypass Universe Package", true, Arrays.asList("isByPassUniverseCheck")));
        pckgRefMap.put("300", PackageRefInfoFactory.create("Bypass Universe Package", true, Arrays.asList("isByPassUniverseCheck")));
        pckgRefMap.put("301", PackageRefInfoFactory.create("Bypass Universe Package", false, Arrays.asList("isByPassUniverseCheck")));
        pckgRefMap.put("302", PackageRefInfoFactory.create("Bypass Universe Historical Package", true, Arrays.asList("isByPassUniverseCheck", "isAllowHistoricalData")));
        pckgRefMap.put("303", PackageRefInfoFactory.create("Bypass Universe Index Package", true, Arrays.asList("isByPassUniverseCheck", "isIndex")));
        pckgRefMap.put("304", PackageRefInfoFactory.create("Bypass Universe Index Historical Package", true, Arrays.asList("isByPassUniverseCheck", "isIndex", "isAllowHistoricalData")));
        when(entitlementCacheService.getPckgRefMap()).thenReturn(pckgRefMap);

        Map<String, IndexSeries> indexMisc = new HashMap<>();
        indexMisc.put("**********", IndexSeries.builder().seriesId("**********").seriesName("JPM Global Diversified Factor").vendorId("4").vendorName("FTSE").isEntitled(false).startDate(null).endDate(null).build());
        when(entitlementCacheService.getIndexMisc()).thenReturn(indexMisc);
    }

    @Test
    void testIndexReferencePackages() {
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(buildMetaData("user1"), buildCacheEntitlement(), getIndexReferencePackage(), getIdMappers(), false, false);
        assertEquals(1, response.getPckDetail().size());
        EntitlementResponse view = response.getPckDetail().get("200").getView();
        assertTrue(view.isEntitled());
        assertEquals(1, view.getIndexSeriesIds().size());
        assertEquals(2, view.getInvestments().size());
        assertEquals(1, view.getIndexSeriesIds().size());
        assertEquals("**********", view.getIndexSeriesIds().get(0).getSeriesId());
        assertTrue(view.getIndexSeriesIds().get(0).isEntitled());
        assertTrue(CollectionUtils.isEmpty(view.getTsInvestments()));
    }

    @Test
    void testAudit() {
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(buildMetaData("user1"), buildCacheEntitlement(), getDataPackages(), getIdMappers(), false, false);
        assertEquals(8, response.getPckDetail().size());
        assertTrue(response.getUnidentifiedDps().contains("dp5"));
        EntitlementResponse view = response.getPckDetail().get("157").getView();
        assertTrue(view.isEntitled());
        assertTrue(view.getIndexSeriesIds().get(0).isEntitled());
    }

    @Test
    void testSummary() {
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(buildMetaData("user1"), buildCacheEntitlement(), getDataPackages(), getIdMappers(), true, false);
        assertEquals(8, response.getPckDetail().size());
        assertTrue(response.getUnidentifiedDps().contains("dp5"));
    }

    @Test
    void testGridView() {
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(buildMetaData("user1"), buildCacheEntitlement(), getDataPackages(), getIdMappers(), false, true);
        assertEquals(8, response.getPckDetail().size());
        assertNotNull(response.getPckDetail().get("1").getExport());
        assertEquals(3, response.getRowFilterExcludeIds().size());
        assertEquals(1, response.getRowFilterExcludeIds().get("export").size());
        assertEquals(0, response.getRowFilterExcludeIds().get("view").size());
        assertEquals(0, response.getRowFilterExcludeIds().get("feed").size());
        assertTrue(response.getRowFilterExcludeIds().get("export").contains("inv7"));
        assertTrue(response.getUnidentifiedDps().contains("dp5"));
    }

    @Test
    void testLicenseNonIndexIds() {
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(buildMetaData("user1"), buildCacheEntitlement(), getDataPackages(), Map.of(NON_INDEX_IDS, getNonIndexIdMappers()), false, false);
        assertEquals(5, response.getPckDetail().size());
        assertFalse(response.getPckDetail().keySet().stream().anyMatch(packageId -> entitlementCacheService.getPckgRefMap().get(packageId).isIndexPackage()));

        PackageEntitlementResponse responseSummary = packageEntitlementService.getPackageEntitlement(buildMetaData("user1"), buildCacheEntitlement(), getDataPackages(), Map.of(NON_INDEX_IDS, getNonIndexIdMappers()), true, false);
        assertEquals(5, responseSummary.getPckDetail().size());
        assertFalse(responseSummary.getPckDetail().keySet().stream().anyMatch(packageId -> entitlementCacheService.getPckgRefMap().get(packageId).isIndexPackage()));
    }

    @Test
    void testLicenseIndexIds() {
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(buildMetaData("user1"), buildCacheEntitlement(), getDataPackages(), Map.of(INDEX_IDS, getIndexIdMappers()), false, false);
        assertEquals(8, response.getPckDetail().size());
        EntitlementResponse view = response.getPckDetail().get("157").getView();
        assertEquals(2, view.getInvestments().get(0).getIndexSeriesIds().size());
        assertEquals(3, view.getIndexSeriesIds().size());
        Set<String> seriesId = view.getIndexSeriesIds().stream().map(IndexSeries::getSeriesId).collect(Collectors.toSet());
        assertTrue(seriesId.containsAll(Set.of("**********", "**********", "**********")));
        IndexSeries indexSeries = view.getIndexSeriesIds().stream().filter(x -> "**********".equals(x.getSeriesId()))
                .findFirst().orElse(null);
        assertNotNull(indexSeries);
        assertTrue(indexSeries.isEntitled());

    }

    @Test
    void testGetIndexDates() {
        CellMatch cell1 = new CellMatch();
        cell1.setPackageId("157");
        Pair<String, String> indexDates1 = ReflectionTestUtils.invokeMethod(packageEntitlementService, "getIndexDates", cell1);
        assertEquals("1900-01-01", indexDates1.getLeft());
        cell1.setPerformanceYears("4");
        Pair<String, String> indexDates2 = ReflectionTestUtils.invokeMethod(packageEntitlementService, "getIndexDates", cell1);
        assertFalse(indexDates2.getLeft().equalsIgnoreCase("1900-01-01"));

        CellMatch cell2 = new CellMatch();
        cell2.setPackageId("158");
        Pair<String, String> indexDates3 = ReflectionTestUtils.invokeMethod(packageEntitlementService, "getIndexDates", cell2);
        assertEquals("1900-01-01", indexDates3.getLeft());
        cell2.setNumberOfYears("4");
        Pair<String, String> indexDates4 = ReflectionTestUtils.invokeMethod(packageEntitlementService, "getIndexDates", cell2);
        assertFalse(indexDates4.getLeft().equalsIgnoreCase("1900-01-01"));
        cell2.setHoldingStartDate("2024-01-01");
        Pair<String, String> indexDates5 = ReflectionTestUtils.invokeMethod(packageEntitlementService, "getIndexDates", cell2);
        assertEquals("2024-01-01", indexDates5.getLeft());

        CellMatch cell3 = new CellMatch();
        cell3.setPackageId("1");  // non-index package
        cell3.setPerformanceYears("4");
        cell3.setNumberOfYears("3");
        cell3.setHoldingStartDate("2024-01-01");
        Pair<String, String> indexDates6 = ReflectionTestUtils.invokeMethod(packageEntitlementService, "getIndexDates", cell3);
        assertEquals("1900-01-01", indexDates6.getLeft());
    }

    @Test
    void testEquityStatus() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch cell1 = new CellMatch();
        cell1.setUseCase("feed");
        cell1.setThirdParty(false);
        cell1.setPackageId("1");
        Criteria shareClassStatusCriteria = new Criteria();
        shareClassStatusCriteria.setDataPointId("EQLNM");
        shareClassStatusCriteria.setOperator("eq");
        shareClassStatusCriteria.setValues(new HashSet<>(List.of("A")));
        Criteria securityTypeCriteria = new Criteria();
        securityTypeCriteria.setDataPointId("3051");
        securityTypeCriteria.setOperator("eq");
        securityTypeCriteria.setValues(new HashSet<>(List.of("ST")));
        List<Criteria> criteria = List.of(shareClassStatusCriteria, securityTypeCriteria);
        Universe universe = new Universe();
        universe.setCriterias(criteria);
        cell1.setUniverses(List.of(universe));
        cachedEntitlement.setCellMatchList(List.of(cell1));

        IdMapper entitledSt = new NonEmptyIdMapper("entitledSt", "{\"SecurityType\":\"ST\", \"SecId\":\"entitledSt\", \"ShareClassStatus\":\"A\"}");
        IdMapper rejectFo = new NonEmptyIdMapper("rejectFo", "{\"SecurityType\":\"FO\", \"SecId\":\"rejectFo\", \"Status\":\"1\"}");
        IdMapper rejectWrongShareClassStatus = new NonEmptyIdMapper("rwscs", "{\"SecurityType\":\"ST\", \"SecId\":\"rwscs\", \"ShareClassStatus\":\"D\"}");
        IdMapper rejectNoShareClassStatus = new NonEmptyIdMapper("rnscs", "{\"SecurityType\":\"ST\", \"SecId\":\"rnscs\", \"Status\":\"A\"}");
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                Map.of(NON_INDEX_IDS, List.of(entitledSt, rejectFo, rejectWrongShareClassStatus, rejectNoShareClassStatus)),
                false,
                false);
        EntitlementResponse feedEntitlement = response.getPckDetail().get("1").getFeed();
        assertEquals(1, feedEntitlement.getInvestments().size());
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("entitledSt"));

        PackageEntitlementResponse summaryResponse = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                Map.of(NON_INDEX_IDS, List.of(entitledSt, rejectFo, rejectWrongShareClassStatus, rejectNoShareClassStatus)),
                true,
                false);
        assertFalse(summaryResponse.getPckDetail().get("1").getFeed().isSupportAllInvestmentIds());

        PackageEntitlementResponse gridViewResponse = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                new HashMap<>() {{
                    put(NON_INDEX_IDS, Lists.newArrayList(entitledSt, rejectFo, rejectWrongShareClassStatus, rejectNoShareClassStatus));
                }},
                false,
                true);
        feedEntitlement = gridViewResponse.getPckDetail().get("1").getFeed();
        assertEquals(1, feedEntitlement.getInvestments().size());
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("entitledSt"));
    }

    @Test
    void testOtherStatus() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();

        CellMatch cell1 = new CellMatch();
        cell1.setUseCase("feed");
        cell1.setThirdParty(false);
        cell1.setPackageId("1");

        // FO criteria
        Criteria foSecurityTypeCriteria = new Criteria();
        foSecurityTypeCriteria.setDataPointId("3051");
        foSecurityTypeCriteria.setOperator("eq");
        foSecurityTypeCriteria.setValues(new HashSet<>(List.of("FO")));
        Criteria foShareClassStatusCriteria = new Criteria();
        foShareClassStatusCriteria.setDataPointId("EQLNM");
        foShareClassStatusCriteria.setOperator("eq");
        foShareClassStatusCriteria.setValues(new HashSet<>(List.of("A")));
        Criteria foDomicileCountryCriteria = new Criteria();
        foDomicileCountryCriteria.setDataPointId("51621");
        foDomicileCountryCriteria.setOperator("eq");
        foDomicileCountryCriteria.setValues(new HashSet<>(List.of("CAN")));

        // FC criteria
        Criteria fcSecurityTypeCriteria = new Criteria();
        fcSecurityTypeCriteria.setDataPointId("3051");
        fcSecurityTypeCriteria.setOperator("eq");
        fcSecurityTypeCriteria.setValues(new HashSet<>(List.of("FC")));
        Criteria fcShareClassStatusCriteria = new Criteria();
        fcShareClassStatusCriteria.setDataPointId("EQLNM");
        fcShareClassStatusCriteria.setOperator("eq");
        fcShareClassStatusCriteria.setValues(new HashSet<>(List.of("A")));
        Criteria fcDomicileCountryCriteria = new Criteria();
        fcDomicileCountryCriteria.setDataPointId("51621");
        fcDomicileCountryCriteria.setOperator("eq");
        fcDomicileCountryCriteria.setValues(new HashSet<>(List.of("USA")));

        List<Criteria> foCriteria = List.of(foSecurityTypeCriteria, foShareClassStatusCriteria, foDomicileCountryCriteria);
        List<Criteria> fcCriteria = List.of(fcSecurityTypeCriteria, fcShareClassStatusCriteria, fcDomicileCountryCriteria);
        Universe foUniverse = new Universe();
        foUniverse.setCriterias(foCriteria);
        Universe fcUniverse = new Universe();
        fcUniverse.setCriterias(fcCriteria);
        cell1.setUniverses(List.of(fcUniverse, foUniverse));
        cachedEntitlement.setCellMatchList(List.of(cell1));

        IdMapper rejectSt = new NonEmptyIdMapper("rejectSt", "{\"SecurityType\":\"ST\", \"SecId\":\"rejectSt\", \"ShareClassStatus\":\"A\"}");
        IdMapper entitledFo = new NonEmptyIdMapper("entitledFo", "{\"SecurityType\":\"FO\", \"SecId\":\"entitledFo\", \"Status\":\"1\", \"DomicileCountry\":\"CAN\"}");
        IdMapper rejectValidFo = new NonEmptyIdMapper("rejectValidFo", "{\"SecurityType\":\"FO\", \"SecId\":\"rejectValidFo\", \"Status\":\"0\", \"DomicileCountry\":\"USA\"}");
        IdMapper rejectInvalidFo = new NonEmptyIdMapper("rejectInvalidFo", "{\"SecurityType\":\"FC\", \"SecId\":\"rejectInvalidFo\", \"Status\":\"666\"}");
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                Map.of(NON_INDEX_IDS, List.of(rejectSt, entitledFo, rejectValidFo, rejectInvalidFo)),
                false,
                false);
        EntitlementResponse feedEntitlement = response.getPckDetail().get("1").getFeed();
        assertEquals(1, feedEntitlement.getInvestments().size());
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("entitledFo"));
    }

    @Test
    void testDomicileCountry() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch cell1 = new CellMatch();
        cell1.setUseCase("feed");
        cell1.setThirdParty(false);
        cell1.setPackageId("1");
        Criteria securityTypeCriteria = new Criteria();
        securityTypeCriteria.setDataPointId("3051");
        securityTypeCriteria.setOperator("eq");
        securityTypeCriteria.setValues(new HashSet<>(List.of("FO")));
        Criteria testedCriteria = new Criteria();
        testedCriteria.setDataPointId("51621");
        testedCriteria.setOperator("eq");
        testedCriteria.setValues(new HashSet<>(List.of("CAN")));
        Universe universe = new Universe();
        universe.setCriterias(List.of(securityTypeCriteria, testedCriteria));
        cell1.setUniverses(List.of(universe));
        cachedEntitlement.setCellMatchList(List.of(cell1));

        IdMapper accept = new NonEmptyIdMapper("accept", "{\"SecurityType\":\"FO\", \"SecId\":\"accept\", \"DomicileCountry\":\"CAN\"}");
        IdMapper reject = new NonEmptyIdMapper("reject", "{\"SecurityType\":\"FO\", \"SecId\":\"reject\", \"DomicileCountry\":\"USA\"}");
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                Map.of(NON_INDEX_IDS, List.of(accept, reject)),
                false,
                false);
        EntitlementResponse feedEntitlement = response.getPckDetail().get("1").getFeed();
        assertEquals(1, feedEntitlement.getInvestments().size());
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("accept"));
    }

    @Test
    void testCountryForSale() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch cell1 = new CellMatch();
        cell1.setUseCase("feed");
        cell1.setThirdParty(false);
        cell1.setPackageId("1");
        Criteria securityTypeCriteria = new Criteria();
        securityTypeCriteria.setDataPointId("3051");
        securityTypeCriteria.setOperator("eq");
        securityTypeCriteria.setValues(new HashSet<>(List.of("FO")));
        Criteria testedCriteria = new Criteria();
        testedCriteria.setDataPointId("49213");
        testedCriteria.setOperator("eq");
        testedCriteria.setValues(new HashSet<>(List.of("CU$$$$$CHE","CU$$$$$DEU")));
        Universe universe = new Universe();
        universe.setCriterias(List.of(securityTypeCriteria, testedCriteria));
        cell1.setUniverses(List.of(universe));
        cachedEntitlement.setCellMatchList(List.of(cell1));

        IdMapper accept = new NonEmptyIdMapper("accept", "{\"SecurityType\":\"FO\", \"SecId\":\"accept\", \"CountryForSale\":\"CU$$$$$CHE\"}");
        IdMapper reject = new NonEmptyIdMapper("reject", "{\"SecurityType\":\"FO\", \"SecId\":\"reject\", \"CountryForSale\":\"CU$$$$$USA\"}");
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                Map.of(NON_INDEX_IDS, List.of(accept, reject)),
                false,
                false);
        EntitlementResponse feedEntitlement = response.getPckDetail().get("1").getFeed();
        assertEquals(1, feedEntitlement.getInvestments().size());
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("accept"));
    }

    @Test
    void testExchangeId() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch cell1 = new CellMatch();
        cell1.setUseCase("feed");
        cell1.setThirdParty(false);
        cell1.setPackageId("1");
        Criteria securityTypeCriteria = new Criteria();
        securityTypeCriteria.setDataPointId("3051");
        securityTypeCriteria.setOperator("eq");
        securityTypeCriteria.setValues(new HashSet<>(List.of("FO")));
        Criteria testedCriteria = new Criteria();
        testedCriteria.setDataPointId("49318");
        testedCriteria.setOperator("eq");
        testedCriteria.setValues(new HashSet<>(List.of("EX$$$$XNAS")));
        Universe universe = new Universe();
        universe.setCriterias(List.of(securityTypeCriteria, testedCriteria));
        cell1.setUniverses(List.of(universe));
        cachedEntitlement.setCellMatchList(List.of(cell1));

        IdMapper accept = new NonEmptyIdMapper("accept", "{\"SecurityType\":\"FO\", \"SecId\":\"accept\", \"ExchangeId\":\"EX$$$$XNAS\"}");
        IdMapper reject = new NonEmptyIdMapper("reject", "{\"SecurityType\":\"FO\", \"SecId\":\"reject\", \"ExchangeId\":\"EX$$$$ABCD\"}");
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                Map.of(NON_INDEX_IDS, List.of(accept, reject)),
                false,
                false);
        EntitlementResponse feedEntitlement = response.getPckDetail().get("1").getFeed();
        assertEquals(1, feedEntitlement.getInvestments().size());
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("accept"));
    }

    @Test
    void testListOfListCellMatch() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();

        // ST cell
        CellMatch package1Cell = new CellMatch();
        package1Cell.setUseCase("feed");
        package1Cell.setThirdParty(false);
        package1Cell.setPackageId("1");

        // ST Criteria
        Criteria stSecurityType = new Criteria();
        stSecurityType.setDataPointId("3051");
        stSecurityType.setOperator("eq");
        stSecurityType.setValues(new HashSet<>(List.of("ST")));
        Criteria stCellShareClassStatus = new Criteria();
        stCellShareClassStatus.setDataPointId("EQLNM");
        stCellShareClassStatus.setOperator("eq");
        stCellShareClassStatus.setValues(new HashSet<>(List.of("A")));
        List<Criteria> stCriteriaList = List.of(stSecurityType, stCellShareClassStatus);

        // FO Criteria
        Criteria foSecurityType = new Criteria();
        foSecurityType.setDataPointId("3051");
        foSecurityType.setOperator("eq");
        foSecurityType.setValues(new HashSet<>(List.of("FO")));
        Criteria foCellShareClassStatus = new Criteria();
        foCellShareClassStatus.setDataPointId("EQLNM");
        foCellShareClassStatus.setOperator("eq");
        foCellShareClassStatus.setValues(new HashSet<>(List.of("A")));
        List<Criteria> foCriteriaList = List.of(foSecurityType, foCellShareClassStatus);
        Universe stUniverse = new Universe();
        stUniverse.setCriterias(stCriteriaList);
        Universe foUniverse = new Universe();
        foUniverse.setCriterias(foCriteriaList);
        package1Cell.setUniverses(List.of(stUniverse, foUniverse));

        cachedEntitlement.setCellMatchList(List.of(package1Cell));

        IdMapper entitledSt = new NonEmptyIdMapper("entitledSt", "{\"SecurityType\":\"ST\", \"SecId\":\"entitledSt\", \"ShareClassStatus\":\"A\"}");
        IdMapper rejectSt = new NonEmptyIdMapper("rejectSt", "{\"SecurityType\":\"ST\", \"SecId\":\"rejectSt\", \"ShareClassStatus\":\"D\"}");
        IdMapper entitledFo = new NonEmptyIdMapper("entitledFo", "{\"SecurityType\":\"FO\", \"SecId\":\"entitledFo\", \"Status\":\"1\"}");
        IdMapper rejectFo = new NonEmptyIdMapper("rejectFo", "{\"SecurityType\":\"FO\", \"SecId\":\"rejectFo\", \"Status\":\"0\"}");

        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1",getPackage("dp1", "1")),
                Map.of(NON_INDEX_IDS, List.of(entitledSt, rejectSt, entitledFo, rejectFo)),
                false,
                false);
        EntitlementResponse feedEntitlement = response.getPckDetail().get("1").getFeed();
        assertEquals(2, feedEntitlement.getInvestments().size());
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("entitledSt"));
        assertTrue(feedEntitlement.getInvestments().stream().map(IdValue::getId).collect(Collectors.toSet()).contains("entitledFo"));
    }

    @Test
    void getIndustryCodeEntitlement() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch package1feed = new CellMatch();
        package1feed.setUseCase("feed");
        package1feed.setPackageId("1");
        CellMatch package2feed = new CellMatch();
        package2feed.setUseCase("feed");
        package2feed.setPackageId("2");
        CellMatch package3feed = new CellMatch();
        package3feed.setUseCase("view");
        package3feed.setPackageId("300");
        CellMatch package2export = new CellMatch();
        package2export.setUseCase("export");
        package2export.setPackageId("2");
        cachedEntitlement.setCellMatchList(List.of(package1feed, package2feed, package3feed, package2export));

        PackageEntitlementResponse industryCodeEntitlement = packageEntitlementService.getPackageEntitlement(
                buildMetaData("someUser"), cachedEntitlement, Map.of("dp1", getPackage("dp1", "300"), "dp2", getPackage("dp2", "301")), Map.of(NON_INDEX_IDS, getNonIndexIdMappers()), false, true);
        assertEquals(2, industryCodeEntitlement.getPckDetail().size());
        assertEquals(300, (int) industryCodeEntitlement.getPckDetail().get("300").getDataPackage());
        assertEquals(301, (int) industryCodeEntitlement.getPckDetail().get("301").getDataPackage());
    }

    @Test
    void getIndustryCodeTSEntitlement() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch packagefeed = new CellMatch();
        packagefeed.setUseCase("feed");
        packagefeed.setPackageId("302");
        cachedEntitlement.setCellMatchList(List.of(packagefeed));

        PackageEntitlementResponse industryCodeEntitlement = packageEntitlementService.getPackageEntitlement(
                buildMetaData("someUser"), cachedEntitlement, Map.of("dp1", getPackage("dp1", "302")), Map.of(NON_INDEX_IDS, getNonIndexIdMappers()), false, true);
        assertEquals(1, industryCodeEntitlement.getPckDetail().size());
        assertEquals(302, (int) industryCodeEntitlement.getPckDetail().get("302").getDataPackage());
        assertTrue(industryCodeEntitlement.getPckDetail().get("302").getFeed().isSupportAllTsInvestmentIds());
    }

    @Test
    void getRegionIdEntitlement() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch package1feed = new CellMatch();
        package1feed.setUseCase("feed");
        package1feed.setPackageId("1");
        CellMatch package2feed = new CellMatch();
        package2feed.setUseCase("feed");
        package2feed.setPackageId("2");
        CellMatch package902view = new CellMatch();
        package902view.setUseCase("view");
        package902view.setPackageId("902");
        CellMatch package902feed = new CellMatch();
        package902feed.setUseCase("feed");
        package902feed.setPackageId("902");
        cachedEntitlement.setCellMatchList(List.of(package1feed, package2feed, package902view, package902feed));

        PackageEntitlementResponse regionIdEntitlement = packageEntitlementService.getPackageLevelOnlyEntitlement(
                "someUser", cachedEntitlement, Map.of("dp3",  getPackage("dp3", "902")
                ));
        assertEquals(1, regionIdEntitlement.getPckDetail().size());
        PackageDetail package902Detail = regionIdEntitlement.getPckDetail().get("902");
        assertTrue(package902Detail.getFeed().isEntitled());
        assertTrue(package902Detail.getView().isEntitled());
    }

    @Test
    void getRegionIdEntitlementEmpty() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch package1feed = new CellMatch();
        package1feed.setUseCase("feed");
        package1feed.setPackageId("1");
        CellMatch package2feed = new CellMatch();
        package2feed.setUseCase("feed");
        package2feed.setPackageId("2");
        cachedEntitlement.setCellMatchList(List.of(package1feed, package2feed));

        PackageEntitlementResponse regionIdEntitlement = packageEntitlementService.getPackageLevelOnlyEntitlement(
                "someUser", cachedEntitlement, Map.of(
                        "dp1", getPackage("dp1", "1"),
                        "dp2", getPackage("dp2", "2"),
                        "dp3",  getPackage("dp3", "902")
                ));
        assertEquals(3, regionIdEntitlement.getPckDetail().entrySet().size());
        PackageDetail package902Detail = regionIdEntitlement.getPckDetail().get("902");
        assertFalse(package902Detail.getFeed().isEntitled());
        assertFalse(package902Detail.getView().isEntitled());
    }

    @Test
    void testCreateMStarPackageInspectionCell() {
        IdMapper idMapper = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FE\", \"SecId\":\"inv1\", \"DomicileCountry\": \"CAN\", \"Status\":\"1\"}");
        Map<String, PackageRefInfo> pckDetails1 = Map.of("1", entitlementCacheService.getPckgRefMap().get("1"));
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        cachedEntitlement.setCellMatchList(Collections.emptyList());
        List<LicenseCellResponse.PackageCell> packageCell1 = packageEntitlementService.buildPckgInspectionCells(pckDetails1, cachedEntitlement, idMapper, "view", null, "productId");
        assertTrue(packageCell1.get(0).isIdEntitled());
        assertNull(packageCell1.get(0).getLicensedToHistoricalData());

        List<LicenseCellResponse.PackageCell> packageCell2 = packageEntitlementService.buildPckgInspectionCells(pckDetails1, cachedEntitlement, idMapper, "view", "2025-01-01", "productId");
        assertTrue(packageCell2.get(0).isIdEntitled());
        assertTrue(packageCell2.get(0).getLicensedToHistoricalData());

        IdMapper indexIdMapper = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FE\", \"SecId\":\"inv1\", \"SeriesId\": \"series1\", \"Status\":\"1\"}");
        Map<String, PackageRefInfo> pckDetails2 = Map.of("200", entitlementCacheService.getPckgRefMap().get("200"));
        List<LicenseCellResponse.PackageCell> packageCell3 = packageEntitlementService.buildPckgInspectionCells(pckDetails2, cachedEntitlement, indexIdMapper, "view", "2025-01-01", "productId");
        assertFalse(CollectionUtils.isEmpty(packageCell3));

        List<LicenseCellResponse.PackageCell> packageCell4 = packageEntitlementService.buildPckgInspectionCells(pckDetails2, cachedEntitlement, indexIdMapper, "view", null, "productId");
        assertFalse(CollectionUtils.isEmpty(packageCell4));
        assertTrue(packageCell4.get(0).isIdEntitled());
        assertNull(packageCell4.get(0).getLicensedToHistoricalData());
    }

    @Test
    void testBuildPackageInspectionCell() {
        IdMapperEntitlementService idService = mock(IdMapperEntitlementService.class);
        PackageEntitlementService testMe =  new PackageEntitlementService(entitlementCacheService, idService, new RowFilterService(idService), new DataPointPackageConfigLoader(entitlementCacheService), entitlementBypassService);
        CellMatch cellMatch = new CellMatch();
        cellMatch.setPackageId("2");
        cellMatch.setUseCase("feed");
        cellMatch.setUniverses(Arrays.asList(new Universe()));
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        cachedEntitlement.setCellMatchList(List.of(cellMatch));
        IdMapper idMapper = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FE\", \"SecId\":\"inv1\", \"DomicileCountry\": \"CAN\", \"Status\":\"1\"}");
        Map<String, PackageRefInfo> pckDetails = Map.of("2", entitlementCacheService.getPckgRefMap().get("2"));


        //Test for entitlement = true
        when(idService.isIdEntitled(any(),any(),anyBoolean(),anyBoolean())).thenReturn(new IdEntitleStatus(true));
        List<LicenseCellResponse.PackageCell> packageCell1 = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper, "feed", null, "productId");
        assertTrue(packageCell1.get(0).isIdEntitled());
        assertEquals(4, packageCell1.get(0).getEntitlementCriterias().size());

        //Test for investmentType not supported
        when(idService.isIdEntitled(any(),any(),anyBoolean(),anyBoolean())).thenReturn(new IdEntitleStatus(false));
        List<LicenseCellResponse.PackageCell> packageCell2 = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper, "feed", null, "productId");
        assertFalse(packageCell2.get(0).isIdEntitled());
        assertTrue(packageCell2.get(0).isPackageEntitled());
        assertFalse(packageCell2.get(0).isInvestmentTypeSupported());

        //Test for entitlement = false, when a universe criteria does not match
        when(idService.isIdEntitled(any(),any(),anyBoolean(),anyBoolean())).thenReturn(new IdEntitleStatus(false, false, Collections.emptySet(), Set.of("domicile"), null));
        List<LicenseCellResponse.PackageCell> packageCell3 = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper, "feed", null, "productId");
        assertFalse(packageCell3.get(0).isIdEntitled());
        assertTrue(packageCell3.get(0).isPackageEntitled());
        assertTrue(packageCell3.get(0).isInvestmentTypeSupported());
        assertTrue(packageCell3.get(0).getEntitlementCriterias().get("status").isEntitled());
        assertFalse(packageCell3.get(0).getEntitlementCriterias().get("domicile").isEntitled());
        assertNull(packageCell3.get(0).getLicensedToHistoricalData());

        //Test for TS entitlement = false
        when(idService.isIdEntitled(any(),any(),anyBoolean(),anyBoolean())).thenReturn(new IdEntitleStatus(false, false, Collections.emptySet(), Set.of("domicile"), null));
        List<LicenseCellResponse.PackageCell> packageCell4 = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper, "feed", "2025-01-01", "productId");
        assertFalse(packageCell4.get(0).isIdEntitled());
        assertFalse(packageCell4.get(0).getLicensedToHistoricalData());

        //Test for Feed Generation not allowed for Fixed Income datapoint
        List<LicenseCellResponse.PackageCell> packageCell5 = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper, "feed", "2025-01-01", "productId");
        assertFalse(packageCell5.get(0).isIdEntitled());
    }


    @Test
    void testBuildIndexPackageCell() {
        IdMapperEntitlementService idService = mock(IdMapperEntitlementService.class);
        PackageEntitlementService testMe = new PackageEntitlementService(entitlementCacheService, idService, new RowFilterService(idService), new DataPointPackageConfigLoader(entitlementCacheService), entitlementBypassService);
        CellMatch cellMatch = new CellMatch();
        cellMatch.setPackageId("158");
        cellMatch.setUseCase("feed");
        Universe univ1 = new Universe();
        univ1.setCriterias(List.of(new Criteria(SERIES_DP, "eq", Set.of("series1"))));
        cellMatch.setUniverses(Arrays.asList(univ1));
        cellMatch.setPerformanceYears("1");
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        cachedEntitlement.setCellMatchList(List.of(cellMatch));

        IdMapper idMapper_multiple_series = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"XI\", \"SecId\":\"inv1\", \"SeriesId\": \"series1,series2\"}");
        IdMapper idMapper_single_series = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"XI\", \"SecId\":\"inv1\", \"SeriesId\": \"series1\"}");
        Map<String, PackageRefInfo> pckDetails = Map.of("158", entitlementCacheService.getPckgRefMap().get("158"));


        //Test for entitlement = true
        IdEntitleStatus mockStatus = new IdEntitleStatus(true);
        mockStatus.addSeriesIds(Set.of("series1"));
        when(idService.isIdEntitled(any(),any(),anyBoolean(),anyBoolean())).thenReturn(mockStatus);
        List<LicenseCellResponse.PackageCell> packageCell_entitled = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper_multiple_series, "feed",null, "productId");
        assertEquals(2, packageCell_entitled.size());
        assertFalse(packageCell_entitled.get(0).isIdEntitled());
        assertEquals(1, packageCell_entitled.get(0).getEntitlementCriterias().size());
        assertEquals("series2", packageCell_entitled.get(0).getEntitlementCriterias().get("seriesId").getValue());
        assertFalse(packageCell_entitled.get(0).getEntitlementCriterias().get("seriesId").isEntitled());
        assertTrue(packageCell_entitled.get(1).isIdEntitled());
        assertEquals("series1", packageCell_entitled.get(1).getEntitlementCriterias().get("seriesId").getValue());
        assertTrue(packageCell_entitled.get(1).getEntitlementCriterias().get("seriesId").isEntitled());

        //Test for TS entitlement = true
        mockStatus.setTsEntitled(true);
        when(idService.isIdEntitled(any(),any(),anyBoolean(),anyBoolean())).thenReturn(mockStatus);
        List<LicenseCellResponse.PackageCell> packageCell_ts_entitled = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper_single_series, "feed", "2025-01-01", "productId");
        assertEquals(1, packageCell_ts_entitled.size());
        assertTrue(packageCell_ts_entitled.get(0).isIdEntitled());
        assertTrue(packageCell_ts_entitled.get(0).getLicensedToHistoricalData());
        assertEquals(2, packageCell_ts_entitled.get(0).getEntitlementCriterias().size());
        assertTrue(packageCell_ts_entitled.get(0).getEntitlementCriterias().get("startDate").isEntitled());

        //Test for entitlement = false
        when(idService.isIdEntitled(any(),any(),anyBoolean(),anyBoolean())).thenReturn(new IdEntitleStatus(false, false));
        List<LicenseCellResponse.PackageCell> packageCell_unentitled = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper_single_series, "feed","2025-01-01", "productId");
        assertEquals(1, packageCell_unentitled.size());
        assertFalse(packageCell_unentitled.get(0).isIdEntitled());
        assertFalse(packageCell_unentitled.get(0).getLicensedToHistoricalData());
        assertEquals(1, packageCell_unentitled.get(0).getEntitlementCriterias().size());

        //Test for entitlement = false when user does not have cell response for index package
        List<LicenseCellResponse.PackageCell> packageCell_missing = testMe.buildPckgInspectionCells(pckDetails, cachedEntitlement, idMapper_multiple_series, "feed","2025-01-01", "productId");
        assertEquals(2, packageCell_missing.size());
        assertFalse(packageCell_missing.get(0).isIdEntitled());
        assertFalse(packageCell_missing.get(0).getLicensedToHistoricalData());
        assertEquals(1, packageCell_missing.get(0).getEntitlementCriterias().size());
        assertFalse(packageCell_missing.get(1).isIdEntitled());
        assertFalse(packageCell_missing.get(1).getLicensedToHistoricalData());
        assertEquals(1, packageCell_missing.get(1).getEntitlementCriterias().size());
    }

    private Map<String, DataPackageItem> getDataPackages() {
        return Map.of("dp1",getPackage("dp1", "1,157,158,159"),
                "dp2",getPackage("dp2", "1"),
                "dp3", getPackage("dp3", "2"),
                "dp4", getPackage("dp4", "4"),
                "dp5", getPackage("dp5", null),
                "dp6", getPackage("dp6", "6"),
        "EO001", getPackage("EO001", "951"));
    }

    private Map<String, DataPackageItem> getIndexReferencePackage() {
        return Map.of("dp1", getPackage("dp1", "200"));
    }

    private Map<String,List<IdMapper>> getIdMappers() {
        IdMapper id1 = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FO\", \"SecId\":\"inv1\"}");
        IdMapper id2 = new NonEmptyIdMapper("inv2", "{\"SecurityType\":\"FC\", \"SecId\":\"inv2\"}");
        IdMapper id3 = new NonEmptyIdMapper("inv3", "{\"SecurityType\":\"XI\", \"SeriesId\":\"**********\", \"SecId\":\"inv3\"}");
        IdMapper id4 = new NonEmptyIdMapper("inv4", "{\"SecurityType\":\"XI\", \"SeriesId\":\"**********\", \"SecId\":\"Sec4\"}");
        IdMapper id5 = new NonEmptyIdMapper("inv5", "{\"SecurityType\":\"MO\", \"SeriesId\":\"PS00000222\", \"SecId\":\"inv5\", \"Status\":\"254\"}");
        IdMapper id6 = new NonEmptyIdMapper("inv6", "{\"SecurityType\":\"ST\", \"SecId\":\"inv6\", \"ShareClassStatus\":\"A\"}");
        IdMapper id7 = new NonEmptyIdMapper("inv7", "{\"SecurityType\":\"MO\", \"SeriesId\":\"PS00000333\", \"SecId\":\"inv7\", \"Status\":\"254\"}");

        return new HashMap<>() {{
            put(NON_INDEX_IDS, Lists.newArrayList(id1,id2,id5,id6,id7));
            put(INDEX_IDS, Lists.newArrayList(id3,id4));
        }};
    }

    private List<IdMapper> getNonIndexIdMappers() {
        IdMapper id1 = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FO\", \"SecId\":\"inv1\"}");
        IdMapper id2 = new NonEmptyIdMapper("inv2", "{\"SecurityType\":\"FC\", \"SecId\":\"inv2\"}");
        return Lists.newArrayList(id1,id2);
    }

    private List<IdMapper> getIndexIdMappers() {
        IdMapper id3 = new NonEmptyIdMapper("inv3", "{\"SecurityType\":\"XI\", \"SeriesId\":\"**********,**********\", \"SecId\":\"inv3\"}");
        IdMapper id4 = new NonEmptyIdMapper("inv4", "{\"SecurityType\":\"XI\", \"SeriesId\":\"**********\", \"SecId\":\"inv4\"}");
        return Lists.newArrayList(id3,id4);
    }

    private DataPackageItem getPackage(String dp, String pck) {
        DataPackageItem packageItem = new DataPackageItem();
        packageItem.setId(dp);
        packageItem.setPkg(pck);
        return packageItem;
    }

    private CachedEntitlement buildCacheEntitlement() {
        CachedEntitlement response = new CachedEntitlement();
        CellMatch cell1 = new CellMatch();
        cell1.setUseCase("feed");
        cell1.setThirdParty(false);
        cell1.setPackageId("1");
        Criteria criteria1 = new Criteria();
        criteria1.setDataPointId("EQNLM");
        criteria1.setOperator("eq");
        criteria1.setValues(new HashSet<>(List.of("A", "I", "D", "O")));
        Criteria criteria2 = new Criteria();
        criteria2.setDataPointId("3051");
        criteria2.setOperator("eq");
        criteria2.setValues(new HashSet<>(Arrays.asList("FO","FC","XI")));
        Universe universe1 = new Universe();
        universe1.setCriterias(List.of(criteria1, criteria2));
        cell1.setUniverses(List.of(universe1));

        CellMatch cell2 = new CellMatch();
        cell2.setUseCase("export");
        cell2.setThirdParty(false);
        cell2.setPackageId("1");
        Criteria criteria21 = new Criteria();
        criteria21.setDataPointId("EQNLM");
        criteria21.setOperator("eq");
        criteria21.setValues(new HashSet<>(List.of("A", "I", "D", "O")));
        Criteria criteria22 = new Criteria();
        criteria22.setDataPointId("3051");
        criteria22.setOperator("eq");
        criteria22.setValues(new HashSet<>(Arrays.asList("FO","FC","XI")));
        Universe universe2 = new Universe();
        universe2.setCriterias(List.of(criteria21, criteria22));
        cell2.setUniverses(List.of(universe2));

        CellMatch cell3 = new CellMatch();
        cell3.setUseCase("view");
        cell3.setPackageId("2");
        cell3.setThirdParty(true);
        Criteria criteria3 = new Criteria();
        criteria3.setDataPointId("3051");
        criteria3.setOperator("eq");
        criteria3.setValues(new HashSet<>(List.of("FO")));
        Universe universe3 = new Universe();
        universe3.setCriterias(List.of(criteria3));
        cell3.setUniverses(List.of(universe3));

        CellMatch cell4 = new CellMatch();
        cell4.setUseCase("view");
        cell4.setThirdParty(true);
        cell4.setPackageId("157");
        Criteria criteria4 = new Criteria();
        criteria4.setDataPointId("3051");
        criteria4.setOperator("eq");
        criteria4.setValues(new HashSet<>(Arrays.asList("XI")));
        Criteria criteria5 = new Criteria();
        criteria5.setDataPointId("OS645");
        criteria5.setOperator("eq");
        criteria5.setValues(new HashSet<>(Arrays.asList("**********")));
        Universe universe4 = new Universe();
        universe4.setCriterias(List.of(criteria4, criteria5));
        cell4.setUniverses(List.of(universe4));

        CellMatch cell5 = new CellMatch();
        cell5.setUseCase("view");
        cell5.setThirdParty(true);
        cell5.setPackageId("157");
        Criteria criteria6 = new Criteria();
        criteria6.setDataPointId("3051");
        criteria6.setOperator("eq");
        criteria6.setValues(new HashSet<>(Arrays.asList("XI")));
        Criteria criteria7 = new Criteria();
        criteria7.setDataPointId("OS645");
        criteria7.setOperator("eq");
        criteria7.setValues(new HashSet<>(Arrays.asList("**********")));
        Universe universe5 = new Universe();
        universe5.setCriterias(List.of(criteria6, criteria7));
        cell5.setUniverses(List.of(universe5));

        CellMatch cell6 = new CellMatch();
        cell1.setUseCase("feed");
        cell1.setThirdParty(false);
        cell1.setPackageId("6");
        Criteria shareClassStatusCriteria = new Criteria();
        shareClassStatusCriteria.setDataPointId("EQLNM");
        shareClassStatusCriteria.setOperator("eq");
        shareClassStatusCriteria.setValues(new HashSet<>(List.of("A")));
        Criteria securityTypeCriteria = new Criteria();
        securityTypeCriteria.setDataPointId("3051");
        securityTypeCriteria.setOperator("eq");
        securityTypeCriteria.setValues(new HashSet<>(List.of("ST")));
        Universe universe6 = new Universe();
        universe6.setCriterias(List.of(shareClassStatusCriteria, securityTypeCriteria));
        cell6.setUniverses((List.of(universe6)));

        List<CellMatch> cells = Arrays.asList(cell1,cell2,cell3,cell4,cell5, cell6);
        response.setCellMatchList(cells);

        RowFilter rowFilter = new RowFilter();
        Criteria criteria_mc1 = new Criteria();
        criteria_mc1.setDataPointId("3051");
        criteria_mc1.setOperator("neq");
        criteria_mc1.setValues(new HashSet<>(Arrays.asList("XI")));

        Criteria criteria_mc2 = new Criteria();
        criteria_mc2.setDataPointId("3044");
        criteria_mc2.setOperator("eq");
        criteria_mc2.setValues(new HashSet<>(List.of("254")));

        Criteria criteria_uc = new Criteria();
        criteria_uc.setDataPointId("OS645");
        criteria_uc.setOperator("eq");
        criteria_uc.setValues(new HashSet<>(Arrays.asList("PS00000222")));

        rowFilter.setMatchCriteriaList(Arrays.asList(criteria_mc1, criteria_mc2));
        rowFilter.setUniverseCriteriaList(Arrays.asList(criteria_uc));
        rowFilter.setUseCase("export");

        response.setRowFilterList(List.of(rowFilter));
        response.setMorningstarFullAccessUseCase(Set.of(VIEW));

        return response;
    }

    @Test
    void testisIndexPackage() {
        Map<String, PackageRefInfo> pckgRefMap = entitlementCacheService.getPckgRefMap();
        assertTrue(pckgRefMap.get("157").isIndexPackage());

        assertFalse(pckgRefMap.get("2").isIndexPackage());

        assertFalse(pckgRefMap.get("1").isIndexPackage());

        PackageRefInfo packageRefInfo = pckgRefMap.get("999");
        assertFalse(packageRefInfo != null && packageRefInfo.isIndexPackage());
    }

    @Test
    void testPackageFilteringBasedOnIndexStatus() {
        // Test when there are no index IDs
        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                buildCacheEntitlement(),
                getDataPackages(),
                Map.of(NON_INDEX_IDS, getNonIndexIdMappers()),
                false,
                false
        );

        // Verify third party index packages are removed
        assertFalse(response.getPckDetail().containsKey("157"));
        assertFalse(response.getPckDetail().containsKey("158"));
        assertFalse(response.getPckDetail().containsKey("159"));

        // Verify non-index packages have correct flag
        assertFalse(response.getPckDetail().get("1").isIndexPackage());
        assertFalse(response.getPckDetail().get("2").isIndexPackage());

        // Test when there are only index IDs in summary mode
        response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                buildCacheEntitlement(),
                getDataPackages(),
                Map.of(INDEX_IDS, getIndexIdMappers()),
                true,
                false
        );

        // Verify only third party index packages remain and have correct flag
        assertTrue(response.getPckDetail().containsKey("157"));
        assertTrue(response.getPckDetail().containsKey("158"));
        assertTrue(response.getPckDetail().containsKey("159"));
        assertTrue(response.getPckDetail().get("157").isIndexPackage());
        assertTrue(response.getPckDetail().get("158").isIndexPackage());
        assertTrue(response.getPckDetail().get("159").isIndexPackage());
        assertFalse(response.getPckDetail().containsKey("1"));
        assertFalse(response.getPckDetail().containsKey("2"));
    }

    @Test
    void testFullAccessUseCaseGrantsEntitlement() {
        // Arrange
        String testProductId = "testProduct";
        String testPackageId = "1";
        String testUseCase = "view";
        when(entitlementBypassService.shouldBypass(testProductId, testPackageId, VIEW)).thenReturn(true);
        when(entitlementBypassService.shouldBypass(testProductId, testPackageId, FEED)).thenReturn(true);
        when(entitlementBypassService.shouldBypass(testProductId, testPackageId, EXPORT)).thenReturn(true);

        // Set up metaData with productId and useCase
        EntitlementMetaData metaData = EntitlementMetaData.builder()
                .userId("user1")
                .productId(testProductId)
                .checkProductIdEntitlement(false)
                .build();

        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        cachedEntitlement.setMorningstarFullAccessUseCase(Set.of(testUseCase));
        CellMatch cell = new CellMatch();
        cell.setPackageId(testPackageId);
        cell.setUseCase(testUseCase);
        cachedEntitlement.setCellMatchList(List.of(cell));

        Map<String, DataPackageItem> dataPackageItemMap = Map.of("dp1", getPackage("dp1", testPackageId));
        Map<String, List<IdMapper>> splitMappers = Map.of(NON_INDEX_IDS, List.of(new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FO\", \"SecId\":\"inv1\"}")));

        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                metaData,
                cachedEntitlement,
                dataPackageItemMap,
                splitMappers,
                false,
                false
        );

        PackageDetail detail = response.getPckDetail().get(testPackageId);
        assertNotNull(detail);
        EntitlementResponse view = detail.getView();
        assertNotNull(view);
        assertTrue(view.isEntitled());
        assertTrue(view.isSupportAllInvestmentIds());
        // If historical, should also support all TS investment ids
        if (view.isHistorical()) {
            assertTrue(view.isSupportAllTsInvestmentIds());
        }
    }

    @Test
    void testBuildPckgInspectionCells_BypassForThirdPartyFeed() {
        // Arrange
        String testProductId = "testProduct";
        String thirdPartyPackageId = "2";
        String useCase = "feed";
        when(entitlementBypassService.shouldBypass(testProductId, thirdPartyPackageId, useCase)).thenReturn(true);

        Map<String, PackageRefInfo> pckDetails = Map.of(thirdPartyPackageId, entitlementCacheService.getPckgRefMap().get(thirdPartyPackageId));
        IdMapper idMapper = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FO\", \"SecId\":\"inv1\"}");

        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        cachedEntitlement.setCellMatchList(Collections.emptyList());
        List<LicenseCellResponse.PackageCell> cells = packageEntitlementService.buildPckgInspectionCells(
                pckDetails,
                cachedEntitlement,
                idMapper,
                useCase,
                null,
                testProductId
        );

        assertFalse(cells.isEmpty());
        LicenseCellResponse.PackageCell cell = cells.get(0);
        assertTrue(cell.isIdEntitled(), "Bypass should grant entitlement for third-party/feed");
        assertTrue(cell.isPackageEntitled());
        assertTrue(cell.isInvestmentTypeSupported());

        String historicalThirdPartyPackageId = "3";
        Map<String, PackageRefInfo> tsPckDetails = Map.of(historicalThirdPartyPackageId, entitlementCacheService.getPckgRefMap().get(historicalThirdPartyPackageId));
        when(entitlementBypassService.shouldBypass(testProductId, historicalThirdPartyPackageId, useCase)).thenReturn(true);

        List<LicenseCellResponse.PackageCell> tsCells = packageEntitlementService.buildPckgInspectionCells(
                tsPckDetails,
                cachedEntitlement,
                idMapper,
                useCase,
                "2025-01-01",
                testProductId
        );

        assertFalse(tsCells.isEmpty());
        LicenseCellResponse.PackageCell tsCell = tsCells.get(0);
        assertTrue(tsCell.isIdEntitled(), "Bypass should grant entitlement for third-party/feed");
        assertTrue(tsCell.isPackageEntitled());
        assertTrue(tsCell.isInvestmentTypeSupported());
        assertTrue(tsCell.getLicensedToHistoricalData());
    }

    @Test
    void testBypassUniverseCheckForViewUseCase() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch cellBypass = new CellMatch();
        cellBypass.setUseCase("view");
        cellBypass.setPackageId("300");
        cachedEntitlement.setCellMatchList(List.of(cellBypass));

        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1", getPackage("dp1", "300")),
                Map.of(NON_INDEX_IDS, getNonIndexIdMappers()),
                false,
                false
        );

        // Assert - All ids should be entitled despite not matching criteria
        EntitlementResponse view = response.getPckDetail().get("300").getView();
        assertTrue(view.isEntitled());
        assertTrue(view.isSupportAllInvestmentIds());
    }

    @Test
    void testBypassUniverseCheckWithHistoricalPackage() {
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        CellMatch cellBypass = new CellMatch();
        cellBypass.setUseCase("view");
        cellBypass.setPackageId("302"); // Historical bypass package
        cachedEntitlement.setCellMatchList(List.of(cellBypass));

        IdMapper foIdMapper = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FO\", \"SecId\":\"inv1\"}");

        PackageEntitlementResponse response = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                Map.of("dp1", getPackage("dp1", "302")),
                Map.of(NON_INDEX_IDS, List.of(foIdMapper)),
                false,
                false
        );

        EntitlementResponse view = response.getPckDetail().get("302").getView();
        assertTrue(view.isEntitled());
        assertTrue(view.isSupportAllInvestmentIds());
        assertTrue(view.isSupportAllTsInvestmentIds());
    }

    @Test
    void testBypassUniverseCheckInCellInspection() {
        Map<String, PackageRefInfo> pckDetails = Map.of("301", entitlementCacheService.getPckgRefMap().get("301"));
        CachedEntitlement cachedEntitlement = new CachedEntitlement();

        CellMatch cellBypass = new CellMatch();
        cellBypass.setUseCase("view");
        cellBypass.setPackageId("301");

        Criteria domicileCriteria = new Criteria();
        domicileCriteria.setDataPointId("51621");
        domicileCriteria.setOperator("eq");
        domicileCriteria.setValues(new HashSet<>(List.of("USA")));

        Universe universe = new Universe();
        universe.setCriterias(List.of(domicileCriteria));
        cellBypass.setUniverses(List.of(universe));
        cachedEntitlement.setCellMatchList(List.of(cellBypass));

        IdMapper idMapper = new NonEmptyIdMapper("inv1", "{\"SecurityType\":\"FO\", \"SecId\":\"inv1\", \"DomicileCountry\":\"CAN\"}");

        List<LicenseCellResponse.PackageCell> cells = packageEntitlementService.buildPckgInspectionCells(
                pckDetails,
                cachedEntitlement,
                idMapper,
                "view",
                null,
                "productId"
        );

        assertEquals(1, cells.size());
        assertTrue(cells.get(0).isIdEntitled());
        assertTrue(cells.get(0).isPackageEntitled());
        assertTrue(cells.get(0).isInvestmentTypeSupported());
        // All criteria should show as entitled despite mismatch
        assertTrue(cells.get(0).getEntitlementCriterias().get("domicile").isEntitled());
    }

    private EntitlementMetaData buildMetaData(String userId) {
        return EntitlementMetaData.builder()
                .userId(userId)
                .productId("productId")
                .checkProductIdEntitlement(false)
                .build();
    }

    @Test
    @DisplayName("when get package entitlement with exception should get null response")
    void shouldGetNull_whenGetPackageEntitlementWithException() {
        //given
        CachedEntitlement cachedEntitlement = new CachedEntitlement();
        Map<String, DataPackageItem> dataPackageItemMap = getDataPackages();
        when(dataPointPackageConfigLoader.loadBaseResponse(dataPackageItemMap, false, false))
                .thenThrow(new RuntimeException());

        //when
        PackageEntitlementResponse entitlementResponse = packageEntitlementService.getPackageEntitlement(
                buildMetaData("user1"),
                cachedEntitlement,
                dataPackageItemMap,
                getIdMappers(),
                false,
                false
        );
        //then
        assertNull(entitlementResponse);
    }
}