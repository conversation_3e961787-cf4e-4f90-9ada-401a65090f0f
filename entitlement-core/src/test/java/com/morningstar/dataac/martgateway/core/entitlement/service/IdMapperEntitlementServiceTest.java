package com.morningstar.dataac.martgateway.core.entitlement.service;

import static org.junit.jupiter.api.Assertions.*;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.NonEmptyIdMapper;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CellMatch;
import com.morningstar.dataac.martgateway.core.entitlement.entity.Criteria;
import com.morningstar.dataac.martgateway.core.entitlement.entity.Universe;
import java.util.List;
import java.util.Set;

import com.morningstar.dataac.martgateway.core.entitlement.entity.IdEntitleStatus;
import org.junit.Test;

public class IdMapperEntitlementServiceTest {

    private static final String SECURITY_TYPE = "3051";
    private static final String SECURITY_STATUS = "3044";
    private static final String SERIES_DP = "OS645";
    private static final String DOMICILE_COUNTRY = "51621";
    private static final String COUNTRY_FOR_SALE = "49213";
    private static final String EXCHANGE_ID = "49318";
    private static final String SHARE_CLASS_STATUS = "EQLNM";
    private static final String FUND_FAMILY_CODES = "6212";

    private static final IdMapperEntitlementService service = new IdMapperEntitlementService();

    @Test
    public void isIdEntitledMultipleUniverses() {
        String rawIdMapper = "{"
                + "\"Status\":1,"
                + "\"SecurityType\":\"ST\","
                + "\"CompanyId\":\"0C000009AL\","
                + "\"PerformanceId\":\"0P0000001V\","
                + "\"PerformanceReady\":1,"
                + "\"PriceReady\":1,"
                + "\"ShareClassStatus\":\"A\","
                + "\"ShareClassId\":\"0P0000001V\","
                + "\"SecId\":\"0P0000001V\","
                + "\"MorningstarIndustryCode\":\"31050010\","
                + "\"ExchangeId\":\"EX$$$$XNYS\","
                + "\"DomicileCountry\":\"USA\","
                + "\"CountryForSale\":\"USA\""
                + "}";
        IdMapper idMapper = new NonEmptyIdMapper("0P0000001V", rawIdMapper);

        // ENTITLED
        Criteria entitledDomicile = new Criteria();
        entitledDomicile.setDataPointId(DOMICILE_COUNTRY);
        entitledDomicile.setValues(Set.of("USA"));

        Criteria entitledCountryForSale = new Criteria();
        entitledCountryForSale.setDataPointId(COUNTRY_FOR_SALE);
        entitledCountryForSale.setValues(Set.of("USA"));

        Criteria entitledShareClassStatus = new Criteria();
        entitledShareClassStatus.setDataPointId(SHARE_CLASS_STATUS);
        entitledShareClassStatus.setValues(Set.of("A"));

        Criteria entitledExchangeId = new Criteria();
        entitledExchangeId.setDataPointId(EXCHANGE_ID);
        entitledExchangeId.setValues(Set.of("EX$$$$XNYS"));

        Criteria entitledSecurityType = new Criteria();
        entitledSecurityType.setDataPointId(SECURITY_TYPE);
        entitledSecurityType.setOperator("eq");
        entitledSecurityType.setValues(Set.of("ST"));

        Universe entitledUniverse = new Universe();
        entitledUniverse.setAllowHistorical(true);
        entitledUniverse.setCriterias(List.of(entitledDomicile, entitledCountryForSale, entitledShareClassStatus,
                entitledExchangeId, entitledSecurityType));

        // UNENTITLED
        Criteria unentitledSecurityType = new Criteria();
        unentitledSecurityType.setDataPointId(SECURITY_TYPE);
        unentitledSecurityType.setOperator("eq");
        unentitledSecurityType.setValues(Set.of("FO"));
        Universe unentitledUniverse = new Universe();
        unentitledUniverse.setCriterias(List.of(unentitledSecurityType));

        CellMatch cellMatch = new CellMatch();
        cellMatch.setUniverses(List.of(entitledUniverse, unentitledUniverse));

        // Expect isEntitled because IdMapper matches at least one universes' criteria
        boolean isEntitled = service.isIdEntitled(idMapper, cellMatch, false, false).isEntitled();
        assertTrue(isEntitled);
        boolean isTsEntitled = service.isIdEntitled(idMapper, cellMatch, false, false).isTsEntitled();
        assertTrue(isTsEntitled);
    }

    @Test
    public void isNotIdEntitled() {
        String rawIdMapper = "{"
                + "\"Status\":1,"
                + "\"SecurityType\":\"ST\","
                + "\"CompanyId\":\"0C000009AL\","
                + "\"PerformanceId\":\"0P0000001V\","
                + "\"PerformanceReady\":1,"
                + "\"PriceReady\":1,"
                + "\"ShareClassStatus\":\"A\","
                + "\"ShareClassId\":\"0P0000001V\","
                + "\"SecId\":\"0P0000001V\","
                + "\"MorningstarIndustryCode\":\"31050010\","
                + "\"ExchangeId\":\"EX$$$$XNYS\","
                + "\"DomicileCountry\":\"USA\","
                + "\"CountryForSale\":\"USA\""
                + "}";
        IdMapper idMapper = new NonEmptyIdMapper("0P0000001V", rawIdMapper);

        // ENTITLED
        Criteria entitledSecurityType = new Criteria();
        entitledSecurityType.setDataPointId(SECURITY_TYPE);
        entitledSecurityType.setOperator("eq");
        entitledSecurityType.setValues(Set.of("ST"));

        Criteria entitledDomicile = new Criteria();
        entitledDomicile.setDataPointId(DOMICILE_COUNTRY);
        entitledDomicile.setValues(Set.of("CAN")); // DOES NOT MATCH

        Universe entitledUniverse = new Universe();
        entitledUniverse.setCriterias(List.of(entitledDomicile, entitledSecurityType));

        CellMatch cellMatch = new CellMatch();
        cellMatch.setUniverses(List.of(entitledUniverse));

        boolean isEntitled = service.isIdEntitled(idMapper, cellMatch, false, false).isEntitled();
        assertFalse(isEntitled);
    }

    @Test
    public void isIdEntitledUniverseRowFilter() {
        String rawIdMapper = "{"
                + "\"Status\":1,"
                + "\"SecurityType\":\"ST\","
                + "\"CompanyId\":\"0C000009AL\","
                + "\"PerformanceId\":\"0P0000001V\","
                + "\"PerformanceReady\":1,"
                + "\"PriceReady\":1,"
                + "\"ShareClassStatus\":\"A\","
                + "\"ShareClassId\":\"0P0000001V\","
                + "\"SecId\":\"0P0000001V\","
                + "\"MorningstarIndustryCode\":\"31050010\","
                + "\"ExchangeId\":\"EX$$$$XNYS\","
                + "\"DomicileCountry\":\"USA\","
                + "\"CountryForSale\":\"USA\""
                + "}";
        IdMapper idMapper = new NonEmptyIdMapper("0P0000001V", rawIdMapper);

        Criteria entitledSecurityType = new Criteria();
        entitledSecurityType.setDataPointId(SECURITY_TYPE);
        entitledSecurityType.setOperator("eq");
        entitledSecurityType.setValues(Set.of("ST"));

        Criteria securityStatus = new Criteria();
        securityStatus.setDataPointId(SECURITY_STATUS);
        securityStatus.setOperator("eq");
        securityStatus.setValues(Set.of("1"));

        Universe entitledUniverse = new Universe();
        entitledUniverse.setCriterias(List.of(entitledSecurityType, securityStatus));

        IdEntitleStatus idEntitleStatus = service.isIdEntitledInUniverse(idMapper, entitledUniverse, false, true, false);
        assertTrue(idEntitleStatus.isEntitled());

        // Modify Security Status criteria to trigger a mismatch
        securityStatus = new Criteria();
        securityStatus.setDataPointId(SECURITY_STATUS);
        securityStatus.setOperator("eq");
        securityStatus.setValues(Set.of("2"));
        entitledUniverse.setCriterias(List.of(entitledSecurityType, securityStatus));

        idEntitleStatus = service.isIdEntitledInUniverse(idMapper, entitledUniverse, false, true, false);
        assertFalse(idEntitleStatus.isEntitled());
    }

    @Test
    public void isSeriesIdEntitled() {
        String rawIdMapper = "{"
                + "\"Status\":1,"
                + "\"SecurityType\":\"XI\","
                + "\"ExchangeTradedShare\":\"0\","
                + "\"CompanyId\":\"0C000023F9\","
                + "\"SeriesId\":\"IS00000660,\","
                + "\"PriceReady\":1,"
                + "\"ShareClassId\":\"XIUSA04G92\","
                + "\"SecId\":\"XIUSA04G92\","
                + "\"MorningstarCategoryId\":\"$FOCA$LB$$\","
                + "\"CountryForSale\":\"CU$$$$$AUS,CU$$$$$NZL\","
                + "\"PerformanceId\":\"0P00001MK8\","
                + "\"MasterPortfolioId\":\"24753\","
                + "\"PerformanceReady\":1,"
                + "\"FundId\":\"FSUSA00L2D\","
                + "\"PostTaxForMixSetting\":\"0\","
                + "\"DomicileCountry\":\"USA\""
                + "}";
        IdMapper idMapper = new NonEmptyIdMapper("XIUSA04G92", rawIdMapper);

        Criteria entitledSecurityType = new Criteria();
        entitledSecurityType.setDataPointId(SECURITY_TYPE);
        entitledSecurityType.setOperator("eq");
        entitledSecurityType.setValues(Set.of("XI"));

        Criteria entitledDomicile = new Criteria();
        entitledDomicile.setDataPointId(DOMICILE_COUNTRY);
        entitledDomicile.setValues(Set.of("USA"));

        Criteria entitledCountryForSale = new Criteria();
        entitledCountryForSale.setDataPointId(COUNTRY_FOR_SALE);
        entitledCountryForSale.setValues(Set.of("CU$$$$$AUS","CU$$$$$NZL"));

        Criteria entitledSeriesId = new Criteria();
        entitledSeriesId.setDataPointId(SERIES_DP);
        entitledSeriesId.setValues(Set.of("IS00000660"));

        Universe entitledUniverse = new Universe();
        entitledUniverse.setCriterias(List.of(entitledSeriesId, entitledSecurityType));

        IdEntitleStatus idEntitleStatus = service.isIdEntitledInUniverse(idMapper, entitledUniverse, true, false, false);
        assertTrue(idEntitleStatus.isEntitled());

        // With rowFilter = true and no SecurityType criteria
        entitledUniverse.setCriterias(List.of(entitledSeriesId));
        idEntitleStatus = service.checkSeriesIdCriteria(idMapper, entitledUniverse, true, false);
        assertTrue(idEntitleStatus.isEntitled());
    }

    @Test
    public void isFundFamilyCodeEntitled() {
        String rawIdMapper = "{"
                + "\"Status\":1,"
                + "\"SecurityType\":\"FO\","
                + "\"ExchangeTradedShare\":\"0\","
                + "\"CompanyId\":\"0C000023F9\","
                + "\"SeriesId\":\"IS00000660,\","
                + "\"PriceReady\":1,"
                + "\"ShareClassId\":\"XIUSA04G92\","
                + "\"SecId\":\"F00000Q877\","
                + "\"MorningstarCategoryId\":\"$FOCA$LB$$\","
                + "\"CountryForSale\":\"CU$$$$$AUS,CU$$$$$NZL\","
                + "\"PerformanceId\":\"0P00001MK8\","
                + "\"MasterPortfolioId\":\"24753\","
                + "\"PerformanceReady\":1,"
                + "\"FundId\":\"FSUSA00L2D\","
                + "\"PostTaxForMixSetting\":\"0\","
                + "\"DomicileCountry\":\"USA\","
                + "\"FundFamilyCode\":\"0C0001Z5A\""
                + "}";
        IdMapper idMapper = new NonEmptyIdMapper("F00000Q877", rawIdMapper);

        Criteria entitledSecurityType = new Criteria();
        entitledSecurityType.setDataPointId(SECURITY_TYPE);
        entitledSecurityType.setOperator("eq");
        entitledSecurityType.setValues(Set.of("FO", "FE"));

        Criteria entitledDomicile = new Criteria();
        entitledDomicile.setDataPointId(DOMICILE_COUNTRY);
        entitledDomicile.setValues(Set.of("USA"));

        Criteria entitledFundFamilyCodes = new Criteria();
        entitledFundFamilyCodes.setDataPointId(FUND_FAMILY_CODES);
        entitledFundFamilyCodes.setValues(Set.of("0C0001Z5A"));


        Universe entitledUniverse = new Universe();
        entitledUniverse.setCriterias(List.of(entitledDomicile, entitledSecurityType, entitledFundFamilyCodes));

        IdEntitleStatus idEntitleStatus = service.isIdEntitledInUniverse(idMapper, entitledUniverse, false, false, false);
        assertTrue(idEntitleStatus.isEntitled());

        // With rowFilter = true and no SecurityType match
        Criteria mismatchFundFamilyCodeType = new Criteria();
        mismatchFundFamilyCodeType.setDataPointId(FUND_FAMILY_CODES);
        mismatchFundFamilyCodeType.setOperator("eq");
        mismatchFundFamilyCodeType.setValues(Set.of("0C0001Z5B"));

        entitledUniverse.setCriterias(List.of(entitledDomicile, entitledSecurityType, mismatchFundFamilyCodeType));
        idEntitleStatus = service.isIdEntitledInUniverse(idMapper, entitledUniverse, true, true, false);
        assertFalse(idEntitleStatus.isEntitled());
    }
}

