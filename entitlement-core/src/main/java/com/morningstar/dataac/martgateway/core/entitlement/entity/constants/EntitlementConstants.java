package com.morningstar.dataac.martgateway.core.entitlement.entity.constants;

import java.util.List;
import java.util.Map;
import java.util.Set;

public final class EntitlementConstants {
    public static final String EQUALS = "eq";
    public static final String CONTAINS = "contains";
    public static final String NOT_EQUALS = "neq";
    public static final String FEED = "feed";
    public static final String VIEW = "view";
    public static final String EXPORT = "export";

    public static final String NEITHER_VIEW_NOR_FEED = "Neither view nor feed";
    public static final String VIEW_ONLY = "View only";

    public static final String NON_INDEX_IDS = "non-index-ids";
    public static final String INDEX_IDS = "index-ids";
    public static final String AUTHORIZATION_CONTEXT_KEY = "authorization";
    public static final String CONTEXT_MAP = "context-map";
    public static final String REQUEST_ID_HEADER = "x-api-requestid";
    public static final String STATUS = "Status";
    public static final String SECURITY_TYPE = "3051";
    public static final String SECURITY_STATUS = "3044";
    public static final String SERIES_DP = "OS645";
    public static final String DOMICILE_COUNTRY = "51621";
    public static final String COUNTRY_FOR_SALE = "49213";
    public static final String EXCHANGE_ID = "49318";
    public static final String FUND_FAMILY_CODES = "6212";
    public static final String SHARE_CLASS_STATUS = "EQLNM";
    public static final String SERIES_ID = "SeriesId";

    public static final Set EXCHANGE_TRADED_FUNDS = Set.of("FE", "FC", "FM", "FH", "FG");

    public static final List<String> DEFAULT_CRITERIA = List.of("domicile", "countryForSales", "status");
    public static final List<String> EQUITY_CRITERIA = List.of("exchangeCode", "shareClassStatus");
    public static final Map<String, String> CRITERIA_DPS = Map.of(DOMICILE_COUNTRY, "domicile",
            COUNTRY_FOR_SALE, "countryForSales",
            EXCHANGE_ID, "exchangeCode",
            SECURITY_STATUS, "status",
            SHARE_CLASS_STATUS, "shareClassStatus",
            FUND_FAMILY_CODES, "fundFamilyCode",
            SERIES_DP, "seriesId");

    public static final Map<String, String> CRITERIA_FIELD_MAPPINGS = Map.of("domicile", "DomicileCountry",
                                                                                            "countryForSales","CountryForSale",
                                                                                            "exchangeCode","ExchangeId",
                                                                                                "status", STATUS,
                                                                                                    "shareClassStatus","ShareClassStatus",
                                                                                                    "fundFamilyCode", "FundFamilyCode",
                                                                                                    "seriesId",SERIES_ID);
    public static final Map<String, String> STATUSES_MAP = Map.of(
            "0", "O",
            "1", "A",
            "254", "A");

    public static final String MOST_RECENT = "mostRecent";
    public static final String TIME_SERIES = "timeSeries";

    //a list of datapoint to bypass the universe check in entitlement
    public static final List<String> BYPASS_UNIVERSE_DATAPOINT_LST = List.of("OS06Y");
}
