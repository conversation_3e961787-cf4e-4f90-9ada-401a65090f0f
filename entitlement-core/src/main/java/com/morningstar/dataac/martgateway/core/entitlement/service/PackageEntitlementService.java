package com.morningstar.dataac.martgateway.core.entitlement.service;

import com.morningstar.dataac.martgateway.core.entitlement.entity.RowFilter;
import com.morningstar.dataac.martgateway.core.entitlement.entity.EntitlementMetaData;
import com.morningstar.dataac.martgateway.core.entitlement.entity.IdEntitleStatus;
import com.morningstar.dataac.martgateway.core.entitlement.entity.LicenseCellResponse;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageRefInfo;
import com.morningstar.dataac.martgateway.core.entitlement.utils.EntitlementUtil;
import org.apache.commons.lang3.StringUtils;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CellMatch;
import com.morningstar.dataac.martgateway.core.entitlement.entity.DataPackageItem;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail.EntitlementResponse;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail.EntitlementResponse.IndexSeries;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail.EntitlementResponse.IdValue;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageDetail;
import com.morningstar.dataac.martgateway.core.entitlement.entity.PackageEntitlementResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.CRITERIA_FIELD_MAPPINGS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.DEFAULT_CRITERIA;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.EQUITY_CRITERIA;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.EXCHANGE_TRADED_FUNDS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.INDEX_IDS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.NON_INDEX_IDS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.STATUS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.STATUSES_MAP;
import static com.morningstar.dataac.martgateway.core.entitlement.utils.EntitlementUtil.isExport;
import static com.morningstar.dataac.martgateway.core.entitlement.utils.EntitlementUtil.isFeed;
import static com.morningstar.dataac.martgateway.core.entitlement.utils.EntitlementUtil.isView;

public class PackageEntitlementService {

    private static final Logger log = LoggerFactory.getLogger(PackageEntitlementService.class);
    private static final String DEFAULT_DATE = "1900-01-01";
    private static final String VIEW = "view";
    private static final String FEED = "feed";
    private static final String EXPORT = "export";
    private static final String SERIES_ID = "SeriesId";

    private final IdMapperEntitlementService idMapperEntitlementService;
    private final EntitlementCacheService entitlementCacheService;
    private final RowFilterService rowFilterService;
    private final DataPointPackageConfigLoader dataPointPackageConfigLoader;
    private final EntitlementBypassService entitlementBypassService;

    public PackageEntitlementService(
            EntitlementCacheService entitlementCacheService,
            IdMapperEntitlementService idMapperEntitlementService,
            RowFilterService rowFilterService,
            DataPointPackageConfigLoader dataPointPackageConfigLoader,
            EntitlementBypassService entitlementBypassService
    ) {
        this.entitlementCacheService = entitlementCacheService;
        this.idMapperEntitlementService = idMapperEntitlementService;
        this.rowFilterService = rowFilterService;
        this.dataPointPackageConfigLoader = dataPointPackageConfigLoader;
        this.entitlementBypassService = entitlementBypassService;
    }

    public PackageEntitlementResponse getPackageEntitlement(
            EntitlementMetaData metaData,
            CachedEntitlement cachedEntitlement,
            Map<String, DataPackageItem> dataPackageItemMap,
            Map<String, List<IdMapper>> splitMappers,
            boolean isSummary,
            boolean needExport
    ) {
        try {
            PackageEntitlementResponse response = dataPointPackageConfigLoader.loadBaseResponse(dataPackageItemMap, isSummary, needExport);
            Map<String, PackageDetail> packageDetail = response.getPckDetail();
            //Remove Index Packages if there are no index ids
            if (CollectionUtils.isEmpty(splitMappers.get(INDEX_IDS))) {
                packageDetail.entrySet().removeIf(entry -> entitlementCacheService.getPckgRefMap().getOrDefault(entry.getKey(), EntitlementUtil.createDefaultPckgRefInfo()).isIndexPackage());
            }
            //Remove Non Index Packages if there are only index ids : ONLY FOR SUMMARY ENDPOINT
            if (isSummary && CollectionUtils.isEmpty(splitMappers.get(NON_INDEX_IDS))) {
                packageDetail.entrySet().removeIf(entry -> !entitlementCacheService.getPckgRefMap().getOrDefault(entry.getKey(), EntitlementUtil.createDefaultPckgRefInfo()).isIndexPackage());
            }
            //Add private series id to be excluded from final result based on row filters
            if (needExport) {
                Map<String, List<String>> rowFilterExcludedIds = rowFilterService.generateRowFilterExcludedIds(splitMappers, cachedEntitlement.getRowFilterList(), metaData);
                response.setRowFilterExcludeIds(rowFilterExcludedIds);
            }
            // apply morningstar full access use case entitlement for specific package
            updatePackageForFullAccessUseCase(response, metaData);
            Map<String, IndexSeries> indexMisc = entitlementCacheService.getIndexMisc();
            cachedEntitlement.getCellMatchList().forEach(cell ->
                    updatePackageForCell(splitMappers, isSummary, needExport, cell, packageDetail.getOrDefault(cell.getPackageId(), null), indexMisc)
            );
            indexPackageCleanup(packageDetail, splitMappers.getOrDefault(INDEX_IDS, Collections.emptyList()), indexMisc, isSummary);

            return response;
        }  catch (Exception e) {
            log.error("event_type=\"Entitlement Service\", event_description=\"Error in processing Entitlement request\", user_id=\"{}\", error=\"{}\"", metaData.getUserId(), e.getMessage(), e);
            return null;
        }
    }

    private void updatePackageForFullAccessUseCase(PackageEntitlementResponse response, EntitlementMetaData metaData) {
        String productId = metaData.getProductId();
        response.getPckDetail().forEach((packageId, details) -> {
            grantProductIdAccess(response, details, productId, packageId, VIEW);
            grantProductIdAccess(response, details, productId, packageId, FEED);
            grantProductIdAccess(response, details, productId, packageId, EXPORT);
        });
    }

    public List<LicenseCellResponse.PackageCell> buildPckgInspectionCells(Map<String, PackageRefInfo> pckDetails, CachedEntitlement cachedEntitlement, IdMapper idMapper, String useCase, String date, String productId) {
        List<LicenseCellResponse.PackageCell> packageCells = new ArrayList<>();
        String investmentType = idMapper.getSecurityType();
        for(Map.Entry<String, PackageRefInfo> pckgInfo : pckDetails.entrySet()) {
            List<String> criterias = getCriteriaFields(investmentType, pckgInfo.getValue().isEssentialsPackage());
            List<CellMatch> cellMatches = cachedEntitlement.getCellMatchList().stream().filter(c -> c.getPackageId().equalsIgnoreCase(pckgInfo.getKey()) && c.getUseCase().equalsIgnoreCase(useCase)).collect(Collectors.toList());
            if(isPackageEntitled(pckgInfo, useCase, productId, cellMatches)) {
                packageCells.addAll(createDefaultEntitledInspectionCell(Integer.valueOf(pckgInfo.getKey()), pckgInfo.getValue(), idMapper, criterias, date));
            }
            else {
                //Have a separate logic for index packages
                if(pckgInfo.getValue().isIndexPackage() || idMapper.isPrivateId()) {
                    //Index package can have multiple cell matches in user entitlement
                    packageCells.addAll(buildSeriesIdInspectionCell(pckgInfo.getKey(), pckgInfo.getValue().getCpqName(), useCase, cellMatches, idMapper, date));
                } else {
                    packageCells.add(buildRegularInvestmentInspectionCell(pckgInfo.getKey(), pckgInfo.getValue().getCpqName(), useCase, cellMatches, idMapper, criterias, date));
                }
            }
        }
        return packageCells;
    }

    private LicenseCellResponse.PackageCell buildRegularInvestmentInspectionCell(String pckgId, String pckgName, String useCase, List<CellMatch> cellMatches, IdMapper idMapper, List<String> criterias, String date) {
        boolean isTsDp = !StringUtils.isEmpty(date);
        Integer pid = Integer.valueOf(pckgId);
        if (CollectionUtils.isEmpty(cellMatches) || (isFeed(useCase) && isFeedNotAllowed(pckgId))) {
            return createDefaultNonIndexPackageCell(pid, pckgName, idMapper, criterias,  isTsDp, false, false);
        }

        CellMatch cell = cellMatches.get(0);
        IdEntitleStatus idEntitleStatus = idMapperEntitlementService.isIdEntitled(idMapper, cell, false, true);

        boolean isEntitled = idEntitleStatus.isEntitled();
        boolean isTsEntitled = idEntitleStatus.isTsEntitled();
        Set<String> missingCriterias = idEntitleStatus.getMissingCriterias();
        //If id is entitled then we support all criterias
        if(isEntitled) {
            return createDefaultNonIndexPackageCell(pid, pckgName, idMapper, criterias, isTsDp, true, isTsEntitled);
        } else {
            //If id is not entitled and we dont have any missing criterias then we do not support the investment type
            //Build the default package and set packageEntitled to true for this case
            if(CollectionUtils.isEmpty(missingCriterias)) {
                LicenseCellResponse.PackageCell packageCell = createDefaultNonIndexPackageCell(pid, pckgName, idMapper, criterias, isTsDp, false, false);
                packageCell.setPackageEntitled(true);
                return packageCell;
            } else {
                LicenseCellResponse.PackageCell packageCell = new LicenseCellResponse.PackageCell(pid, pckgName);
                Map<String, LicenseCellResponse.PackageCell.CriteriaValue> entitlementCriterias = new HashMap<>();
                criterias.forEach( c -> {
                    boolean isSupported = !missingCriterias.contains(c);
                    entitlementCriterias.put(c, new LicenseCellResponse.PackageCell.CriteriaValue(isSupported, getIdMapperFieldValue(c, idMapper)));
                });
                packageCell.setPackageEntitled(true);
                packageCell.setInvestmentTypeSupported(true);
                if(isTsDp) {
                    packageCell.setLicensedToHistoricalData(isTsEntitled);
                }
                packageCell.setEntitlementCriterias(entitlementCriterias);
                return packageCell;
            }
        }
    }

    //If a sec id is mapped to multiple series ids, then each series id will have its own separate package cell block
    private List<LicenseCellResponse.PackageCell> buildSeriesIdInspectionCell(String pckgId, String pckgName, String useCase, List<CellMatch> cells, IdMapper idMapper, String date) {
        List<IdEntitleStatus> idUniverseStatus = new ArrayList<>();
        List<LicenseCellResponse.PackageCell> responses = new ArrayList<>();
        boolean isTsDp = !StringUtils.isEmpty(date);
        Integer pid = Integer.valueOf(pckgId);
        Set<String> seriesIds = splitSeriesId(idMapper.getId(SERIES_ID));
        //If user does not support index package, set all entitlement flags as false
        if(CollectionUtils.isEmpty(cells) || (isFeed(useCase) && isFeedNotAllowed(pckgId))) {
            seriesIds.forEach(sid -> responses.add(createSeriesIdPackageCell(pid, pckgName, idMapper.getSecurityType(), sid, date, false, new IdEntitleStatus(false, false))));
        } else {
            for(CellMatch cell : cells) {
                IdEntitleStatus idEntitleStatus = idMapperEntitlementService.isIdEntitled(idMapper, cell, true, true);
                String indexStartDate = (isTsDp && "XI".equalsIgnoreCase(idMapper.getSecurityType())) ? getIndexDates(cell).getLeft() : null;
                idEntitleStatus.setIndexStartDate(indexStartDate);
                idUniverseStatus.add(idEntitleStatus);
            }
            seriesIds.forEach(sid -> {
                IdEntitleStatus idEntitleStatus = idUniverseStatus.stream().filter(c -> c.isEntitled() && c.getSeriesIds().contains(sid)).findFirst().orElse(new IdEntitleStatus(false, false));
                responses.add(createSeriesIdPackageCell(pid, pckgName, idMapper.getSecurityType(), sid, date, true, idEntitleStatus));
            });
        }
        return responses;
    }
    /**
     * Default Entitled=true is set for the following cases:
     *  1. view/export usecases for morningstar packages
     *  Note: For index packages, we only have morningstar packages for most recent datapoints. THe timesseries entitlement will be false
     *
     * 2. For package,use case and productId combination mentioned in the entitlement-bypass-config.json file
     */
    private List<LicenseCellResponse.PackageCell> createDefaultEntitledInspectionCell(Integer pckgId, PackageRefInfo pckgRefInfo, IdMapper idMapper, List<String> criterias, String date) {
        boolean isTsDp = !StringUtils.isEmpty(date);
        if(!pckgRefInfo.isIndexPackage()) {
            boolean isTsEntitled = !pckgRefInfo.isThirdPartyPackage() || pckgRefInfo.isHistoricalPackage();
            return new ArrayList<>(List.of(createDefaultNonIndexPackageCell(pckgId, pckgRefInfo.getCpqName(), idMapper, criterias, isTsDp, true, isTsEntitled)));
        } else {
            List<LicenseCellResponse.PackageCell> indexCells = new ArrayList<>();
            Set<String> seriesIds = splitSeriesId(idMapper.getId(SERIES_ID));
            seriesIds.forEach(s -> indexCells.add(createSeriesIdPackageCell(pckgId, pckgRefInfo.getCpqName(), idMapper.getSecurityType(), s, date, true, new IdEntitleStatus(true))));
            return indexCells;
        }
    }

    /**
     * This method is currently for special cases where we will short circuit all boolean flags to true or false based on isEntitled flag
     * case 1: For morningstar packages with view/export usecase, we consider id to be entitled (isEntitled = true)
     * case 2: For packages not available in user entitlement, we consider id not be entitled (isEntitled = false)
     * case 3: For packages that do not support investmentType, all criterias will be considered as not entitled
     */
    private LicenseCellResponse.PackageCell createDefaultNonIndexPackageCell(Integer pckgId, String pckgName, IdMapper idMapper, List<String> criterias, boolean isTsDp, boolean isEntitled, boolean isTsEntitled) {
        LicenseCellResponse.PackageCell packageCell = new LicenseCellResponse.PackageCell(pckgId, pckgName);
        packageCell.setInvestmentTypeSupported(isEntitled);
        packageCell.setPackageEntitled(isEntitled);
        if(isTsDp) {
            packageCell.setLicensedToHistoricalData(isTsEntitled);
        }
        Map<String, LicenseCellResponse.PackageCell.CriteriaValue> entitlementCriterias = new HashMap<>();
        criterias.forEach(c -> entitlementCriterias.put(c, new LicenseCellResponse.PackageCell.CriteriaValue(isEntitled, getIdMapperFieldValue(c, idMapper))));
        packageCell.setEntitlementCriterias(entitlementCriterias);
        packageCell.setIdEntitled(isEntitled && (!isTsDp || isTsEntitled));
        return packageCell;
    }

    //For index series, the only criterias we consider are seriesId and startDate
    private LicenseCellResponse.PackageCell createSeriesIdPackageCell(Integer pckgId, String pckgName, String securityType, String seriesId, String date, boolean ispckgEntitled, IdEntitleStatus idEntitleStatus) {
        IndexSeries index = entitlementCacheService.getIndexMisc().getOrDefault(seriesId, new IndexSeries(seriesId));
        boolean isTsDp = !StringUtils.isEmpty(date);
        LicenseCellResponse.PackageCell packageCell = new LicenseCellResponse.PackageCell(pckgId, pckgName);
        packageCell.setInvestmentTypeSupported(ispckgEntitled);
        packageCell.setPackageEntitled(ispckgEntitled);
        Boolean isTsDpEntitled = StringUtils.isEmpty(date) ? null : idEntitleStatus.isTsEntitled();
        packageCell.setLicensedToHistoricalData(isTsDpEntitled);
        Map<String, LicenseCellResponse.PackageCell.CriteriaValue> entitlementCriterias = new HashMap<>();
        entitlementCriterias.put("seriesId", new LicenseCellResponse.PackageCell.CriteriaValue(idEntitleStatus.isEntitled(), seriesId, index.getSeriesName(), index.getVendorName()));
        //Add startDate only for entitled seriesIds
        Boolean isDateEntitled = null;
        //Add startDate criteria only for TS datapoint and only for XI security type
        if(isTsDp && idEntitleStatus.isTsEntitled() && "XI".equalsIgnoreCase(securityType)) {
            isDateEntitled = date.compareTo(idEntitleStatus.getIndexStartDate()) >= 0;
            entitlementCriterias.put("startDate",  new LicenseCellResponse.PackageCell.CriteriaValue(isDateEntitled, idEntitleStatus.getIndexStartDate()));
        }
        packageCell.setEntitlementCriterias(entitlementCriterias);
        //For index TS datapoint : you need to check if universe supports historicalData and if indexStartDate is before the cell date
        packageCell.setIdEntitled(idEntitleStatus.isEntitled() && (!isTsDp || (idEntitleStatus.isTsEntitled() && isTrueOrNull(isDateEntitled))));
        return packageCell;
    }

    public List<String> getRowFilterExcludeIds(String useCase, Map<String, List<IdMapper>> splitMappers,
                                               List<RowFilter> rowFilters,
                                               EntitlementMetaData metaData) {
        Map<String, List<String>> rowFilterExcludeList = rowFilterService.generateRowFilterExcludedIds(splitMappers, rowFilters, metaData);
        return rowFilterExcludeList.getOrDefault(useCase, Collections.emptyList());
    }

    public static boolean isTrueOrNull(Boolean value) {
        return value == null || value;
    }

    private void updatePackageForCell(Map<String, List<IdMapper>> splitMappers, boolean isSummary, boolean needExport,
            CellMatch cell, PackageDetail packageDetail, Map<String, IndexSeries> indexMisc) {
        if (packageDetail == null) {
            return;
        }

        String packageId = cell.getPackageId();
        String useCase = cell.getUseCase();
        EntitlementResponse responseByUseCase = null;

        if (isView(useCase)) {
            responseByUseCase = packageDetail.getView();
        } else if (isFeed(useCase) && !isFeedNotAllowed(packageId)) {
            responseByUseCase = packageDetail.getFeed();
        } else if (isExport(useCase) && needExport) { //EXPORT cases only for Investment API
            responseByUseCase = packageDetail.getExport();
        }

        if (responseByUseCase != null) {
            updatePackage(packageId, responseByUseCase, cell, splitMappers, isSummary, indexMisc);
        }
    }
    //Fetch criterias required for cell level inspection
    //Exception: only for seriesId we will ignore this criteria list
    private List<String> getCriteriaFields(String investmentType, boolean isEssentialsPackage) {
        if(investmentType.equalsIgnoreCase("ST")) {
            return EQUITY_CRITERIA;
        }
        List<String> defaultList = new ArrayList<>(DEFAULT_CRITERIA);
        if(EXCHANGE_TRADED_FUNDS.contains(investmentType)) {
            defaultList.add("exchangeCode");
        }
        if(isEssentialsPackage) {
            defaultList.add("fundFamilyCode");
        }
        return defaultList;
    }

    // Generate entitlements for idType = Industry Code and Region ID. Only the packages are considered for entitlement purposes.
    public PackageEntitlementResponse getPackageLevelOnlyEntitlement(String userId, CachedEntitlement cachedEntitlement, Map<String, DataPackageItem> dataPackageItemMap) {
        try {
            PackageEntitlementResponse response = dataPointPackageConfigLoader.loadBaseResponse(dataPackageItemMap, false, true);
            applyPackageLevelOnlyEntitlement(cachedEntitlement, response);
            return response;
        } catch (Exception e) {
            log.error("event_type=\"Entitlement Service\", event_description=\"Error in processing Industry Code entitlement request\", user_id=\"{}\", error=\"{}\"", userId, e.getMessage(), e);
            return null;
        }
    }

    private static void applyPackageLevelOnlyEntitlement(CachedEntitlement cachedEntitlement, PackageEntitlementResponse response) {
        cachedEntitlement.getCellMatchList().forEach(cell ->
                response.getResponseByUseCase(cell.getPackageId(), cell.getUseCase()).ifPresent(er -> {
                    er.setEntitled(true);
                    er.setSupportAllInvestmentIds(true);
                    er.setSupportAllTsInvestmentIds(er.isHistorical());
                }));
    }

    private void updatePackage(String packageId, EntitlementResponse response, CellMatch cell,
                               Map<String, List<IdMapper>> splitMappers, boolean isSummary, Map<String, IndexSeries> indexMisc) {
        if (entitlementCacheService.getPckgRefMap().getOrDefault(packageId, EntitlementUtil.createDefaultPckgRefInfo()).isIndexPackage()) {
            updateEntitledIndexPackage(response, cell, splitMappers.getOrDefault(INDEX_IDS, Collections.emptyList()),
                    isSummary, indexMisc);
        } else {
            updateEntitledPackage(response, cell, splitMappers.getOrDefault(NON_INDEX_IDS, Collections.emptyList()),
                    isSummary);
        }
    }

    private boolean isFeedNotAllowed(String packageId) {
        return entitlementCacheService.getPckgRefMap().getOrDefault(packageId, EntitlementUtil.createDefaultPckgRefInfo()).isNotAllowFeedGeneration();
    }

    private void updateEntitledPackage(EntitlementResponse response, CellMatch cell, List<IdMapper> idMappers, boolean isSummary) {
        response.setEntitled(true);
        //For internal morningstar packages : view use case : set supportAllInvestmentIds as true
        if (response.isBypassUniverseCheck()) {
            response.setSupportAllInvestmentIds(true);
            response.setSupportAllTsInvestmentIds(response.isHistorical());
        } else if (isSummary) {
            updatePackageSummary(response, cell, idMappers);
        } else if (!response.isSupportAllInvestmentIds()) {
            Map<String, IdEntitleStatus> idEntitlementStatusMap = idMappers.stream().collect(Collectors.toMap(
                    IdMapper::getInvestmentId,
                    imap -> idMapperEntitlementService.isIdEntitled(imap, cell, false, false)));

            updateEntitledPackageForEntitledIds(idEntitlementStatusMap, response, idMappers);
            updateEntitledPackageForTSEntitledIds(idEntitlementStatusMap, response, idMappers);
        }
    }

    private void updateEntitledPackageForEntitledIds(Map<String, IdEntitleStatus> idEntitlementStatusMap, EntitlementResponse response, List<IdMapper> idMappers) {
        List<String> entitledIds = idEntitlementStatusMap.entrySet().stream().filter(e -> e.getValue().isEntitled()).map(Map.Entry::getKey).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(idMappers) && entitledIds.size() == idMappers.size()) {
            response.setSupportAllInvestmentIds(true);
        } else {
            entitledIds.stream()
                    .map(IdValue::new)
                    .toList()
                    .forEach(response::addInvestment);
        }
    }

    private void updateEntitledPackageForTSEntitledIds(Map<String, IdEntitleStatus> idEntitlementStatusMap, EntitlementResponse response, List<IdMapper> idMappers) {
        List<String> tsEntitledIds = idEntitlementStatusMap.entrySet().stream().filter(e -> e.getValue().isTsEntitled()).map(Map.Entry::getKey).collect(Collectors.toList());
        if (response.isHistorical() && !CollectionUtils.isEmpty(idMappers) && tsEntitledIds.size() == idMappers.size()) {
            response.setSupportAllTsInvestmentIds(true);
        } else {
            tsEntitledIds.stream()
                    .map(IdValue::new)
                    .toList()
                    .forEach(response::addTsInvestment);
        }
    }

    private void updatePackageSummary(EntitlementResponse response, CellMatch cell, List<IdMapper> idMappers) {
        //Check entitlements if supportAllInvestmentId flags are not set to true by default
        if(!response.isSupportAllInvestmentIds()) {
            Pair<Boolean, Boolean> entitleForAllIdsDps = isEntitledForAllIds(idMappers, cell);
            boolean allIdsEntitled = entitleForAllIdsDps.getLeft();
            boolean allTsIdsEntitled = response.isHistorical() && entitleForAllIdsDps.getRight();
            response.setSupportAllInvestmentIds(allIdsEntitled);
            response.setSupportAllTsInvestmentIds(allTsIdsEntitled);
        }
    }

    private void updateEntitledIndexPackage(EntitlementResponse response, CellMatch cell, List<IdMapper> idMappers, boolean isSummary, Map<String, IndexSeries> indexMisc) {
        response.setEntitled(true);
        Map<String, IdEntitleStatus> entitledIds = getEntitledSeriesIdPerInvestmentId(cell, idMappers);

        if (!isSummary) {
            List<IdValue> idValues = entitledIds.entrySet()
                    .stream()
                    .map(e -> CollectionUtils.isEmpty(e.getValue().getSeriesIds()) ? new IdValue(e.getKey())
                            : new IdValue(e.getKey(), e.getValue().getSeriesIds())).collect(Collectors.toList()
                    );
            response.addInvestments(idValues);

            List<IdValue> tsIdValues = entitledIds.entrySet()
                    .stream()
                    .filter(e -> e.getValue().isTsEntitled())
                    .map(e -> CollectionUtils.isEmpty(e.getValue().getSeriesIds()) ? new IdValue(e.getKey())
                            : new IdValue(e.getKey(), e.getValue().getSeriesIds())).collect(Collectors.toList()
                    );
            response.addTsInvestments(tsIdValues);
        }

        //Investment ids entitled to an index package cell will have the same TS entitlement
        //Checking the tsEntitlement flag for the first id is enough to decide to calculate the index dates
        boolean areIdsTsEntitled = !entitledIds.isEmpty() && entitledIds.values().iterator().next().isTsEntitled();
        Pair<String,String> indexDates = areIdsTsEntitled ? getIndexDates(cell) : Pair.of(null, null);
        Set<String> seriesIds = new HashSet<>();
        entitledIds.values().forEach(s -> seriesIds.addAll(s.getSeriesIds()));
        List<IndexSeries> indexSeries = seriesIds.stream().map(v -> duplicateIndexSeries(indexMisc.getOrDefault(v, new IndexSeries(v)), indexDates)).toList();
        response.getIndexSeriesIds().addAll(indexSeries);
    }

    private Map<String, IdEntitleStatus> getEntitledSeriesIdPerInvestmentId(CellMatch cell, List<IdMapper> idMappers) {
        Map<String,IdEntitleStatus> entitledIds = new HashMap<>();
        idMappers.forEach(imap -> {
            IdEntitleStatus idEntitlement = idMapperEntitlementService.isIdEntitled(imap, cell, true, false);
            if (idEntitlement.isEntitled()) {
                entitledIds.put(imap.getInvestmentId(), idEntitlement);
            }
        });
        return entitledIds;
    }

    /**
     * Step 1: Add all investment ids and series ids for morningstar index packages for view and export use case, as they are considered entitled
     * Step 2: Add Index series information for non-entitled index ids only for view and feed use cases (this is used only for license audit endpoint)
     */
    private void indexPackageCleanup(Map<String, PackageDetail> pckDetail, List<IdMapper> idMappers, Map<String, IndexSeries> indexMisc, boolean isSummary) {
        pckDetail.entrySet().stream()
                .filter(entry -> entry.getValue().isIndexPackage())
                .forEach(entry -> {
                    addMorningstarEntitledIndexIds(entry.getValue().getView(), idMappers, indexMisc, isSummary);
                    addMorningstarEntitledIndexIds(entry.getValue().getExport(), idMappers, indexMisc, isSummary);

                    addNonEntitledIndexIds(entry.getValue().getView(), idMappers, indexMisc);
                    addNonEntitledIndexIds(entry.getValue().getFeed(), idMappers, indexMisc);
                });
    }

    //Add Index series information for non-entitled index ids
    private void addNonEntitledIndexIds(EntitlementResponse response, List<IdMapper> idMappers, Map<String, IndexSeries> indexMisc) {
        if (!response.isEntitled()) {
            response.setIndexSeriesIds(new ArrayList<>());
        }
        Set<String> ids = new HashSet<>();
        idMappers.forEach(imap -> ids.addAll(splitSeriesId(imap.getId(SERIES_ID))));
        Set<String> responseIds = CollectionUtils.isEmpty(response.getIndexSeriesIds()) ?
                Collections.emptySet() :
                response.getIndexSeriesIds().stream().map(IndexSeries::getSeriesId).collect(Collectors.toSet());
        ids.removeAll(responseIds);
        List<IndexSeries> indexSeries = ids.stream().map(id -> indexMisc.getOrDefault(id, new IndexSeries(id))).toList();
        response.getIndexSeriesIds().addAll(indexSeries);
    }

    //Add all investment ids and index ids to entitlement response for view/export for morningstar index packages
    private void addMorningstarEntitledIndexIds(EntitlementResponse response, List<IdMapper> idMappers, Map<String, IndexSeries> indexMisc, boolean isSummary) {
        if(response != null && response.isEntitled() && response.isSupportAllInvestmentIds()) {
            if(!isSummary) {
                List<IdValue> idValues = idMappers
                        .stream()
                        .map(e -> StringUtils.isEmpty(e.getId(SERIES_ID)) ? new IdValue(e.getInvestmentId())
                                : new IdValue(e.getInvestmentId(), splitSeriesId(e.getId(SERIES_ID)))).collect(Collectors.toList()
                        );
                response.addInvestments(new ArrayList<>(idValues));
            }
            response.setIndexSeriesIds(new ArrayList<>());
            Set<String> ids = new HashSet<>();
            idMappers.forEach(imap -> ids.addAll(splitSeriesId(imap.getId(SERIES_ID))));
            List<IndexSeries> indexSeries = ids.stream().map(id -> duplicateIndexSeries(indexMisc.getOrDefault(id, new IndexSeries(id)), Pair.of(null, null))).toList();
            response.setIndexSeriesIds(new ArrayList<>(indexSeries));
        }
    }

    private Pair<Boolean, Boolean> isEntitledForAllIds(List<IdMapper> idMappers, CellMatch cell) {
        //For edge case where only index ids are passed
        if(CollectionUtils.isEmpty(idMappers)) {
            return Pair.of(false, false);
        }
        List<IdEntitleStatus> idEntitleChecks = idMappers.stream().map(imap -> idMapperEntitlementService.isIdEntitled(imap, cell, false, false)).collect(Collectors.toList());
        boolean supportAllInvestmentIds =  idEntitleChecks.stream().allMatch(IdEntitleStatus::isEntitled);
        boolean supportAllTsInvestmentIds = idEntitleChecks.stream().allMatch(IdEntitleStatus::isTsEntitled);
        return Pair.of(supportAllInvestmentIds, supportAllTsInvestmentIds);
    }

    private Pair<String, String> getIndexDates(CellMatch cell) {
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE.withZone(ZoneOffset.UTC);
        String endDate = formatter.format(Instant.now());
        String startDate = entitlementCacheService.getPckgRefMap().getOrDefault(cell.getPackageId(), EntitlementUtil.createDefaultPckgRefInfo()).isIndexPackage()
                ? Optional.ofNullable(cell.getHoldingStartDate())
                .filter(StringUtils::isNotEmpty)
                .or(() -> Optional.ofNullable(cell.getNumberOfYears())
                        .filter(StringUtils::isNotEmpty)
                        .map(this::parseYearsToDate))
                .or(() -> Optional.ofNullable(cell.getPerformanceYears())
                        .filter(StringUtils::isNotEmpty)
                        .map(this::parseYearsToDate))
                .orElse(DEFAULT_DATE)
                : DEFAULT_DATE;

        return Pair.of(startDate, endDate);
    }

    private String parseYearsToDate(String yearsStr) {
        return DateTimeFormatter.ISO_LOCAL_DATE.withZone(ZoneOffset.UTC).format(
                Instant.now()
                        .atOffset(ZoneOffset.UTC)
                        .minusYears(Integer.parseInt(yearsStr))
                        .plusDays(1)
                        .toInstant()
        );
    }

    //Note: Investment id have a one-to-many relationship with series id
    private Set<String> splitSeriesId(String seriesIds) {
        if (StringUtils.isEmpty(seriesIds)) {
            return Collections.emptySet();
        }
        return Stream.of(StringUtils.split(seriesIds, ",")).collect(Collectors.toSet());
    }

    //Only for status field, map it to the alphabet code based on STATUSES_MAP
    private String getIdMapperFieldValue(String field, IdMapper idMapper) {
        String idMapperKey = CRITERIA_FIELD_MAPPINGS.get(field);
        String value = idMapper.getId(idMapperKey);
        return idMapperKey.equalsIgnoreCase(STATUS) ? STATUSES_MAP.getOrDefault(value, "absent") : value;
    }

    private IndexSeries duplicateIndexSeries(IndexSeries original, Pair<String, String> indexDates) {
        IndexSeries copy = new IndexSeries(original);
        copy.setEntitled(true);
        copy.setStartDate(indexDates.getLeft());
        copy.setEndDate(indexDates.getRight());
        return copy;
    }

    private void grantProductIdAccess(PackageEntitlementResponse response, PackageDetail details, String productId, String packageId, String useCase) {
        if (entitlementBypassService.shouldBypass(productId, packageId, useCase)) {
            log.info("Granting full {} access for packageId: {}, productId: {}", useCase, details.getDataPackage(), productId);
            response.getResponseByUseCase(packageId, useCase)
                    .ifPresent( entitlement -> {
                        entitlement.setEntitled(true);
                        entitlement.setSupportAllInvestmentIds(true);
                        if (entitlement.isHistorical()) {
                            entitlement.setSupportAllTsInvestmentIds(true);
                        }
                    });
        }
    }

    private boolean isPackageEntitled(Map.Entry<String, PackageRefInfo> pckgInfo, String useCase, String productId,
                                      List<CellMatch> cellMatches) {
        PackageRefInfo packageRefInfo = pckgInfo.getValue();
        return (isView(useCase) && packageRefInfo.isByPassLicenseCheckForView())
                || (!isFeed(useCase) && !packageRefInfo.isThirdPartyPackage())
                || (!CollectionUtils.isEmpty(cellMatches) && packageRefInfo.isBypassUniverseCheck())
                || entitlementBypassService.shouldBypass(productId, pckgInfo.getKey(), useCase);
    }
}