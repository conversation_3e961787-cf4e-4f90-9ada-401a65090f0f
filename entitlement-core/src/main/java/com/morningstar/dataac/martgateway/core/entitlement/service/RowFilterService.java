package com.morningstar.dataac.martgateway.core.entitlement.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.core.entitlement.entity.RowFilter;
import com.morningstar.dataac.martgateway.core.entitlement.entity.Universe;
import com.morningstar.dataac.martgateway.core.entitlement.entity.EntitlementMetaData;
import com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.EXPORT;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.VIEW;

public class RowFilterService {

    private final IdMapperEntitlementService idMapperEntitlementService;

    public RowFilterService(IdMapperEntitlementService idMapperEntitlementService) {
        this.idMapperEntitlementService = idMapperEntitlementService;
    }

    public Map<String, List<String>> generateRowFilterExcludedIds(
            Map<String, List<IdMapper>> splitMappers,
            List<RowFilter> rowFilters,
            EntitlementMetaData metaData
    ) {
        Map<String, List<String>> rowFilterExcludeIds = new HashMap<>();
        rowFilterExcludeIds.put(VIEW, new ArrayList<>());
        rowFilterExcludeIds.put(EntitlementConstants.FEED, new ArrayList<>());
        rowFilterExcludeIds.put(EXPORT, new ArrayList<>());

        if (rowFilters.isEmpty()) {
            addPrivateIdsToExcludeLists(splitMappers, rowFilterExcludeIds);
        } else {
            processRowFilters(rowFilters, splitMappers, metaData, rowFilterExcludeIds);
        }
        return rowFilterExcludeIds;
    }

    private void addPrivateIdsToExcludeLists(
            Map<String, List<IdMapper>> splitMappers,
            Map<String, List<String>> rowFilterExcludeIds
    ) {
        for (List<IdMapper> idMappers : splitMappers.values()) {
            for (IdMapper idMapper : idMappers) {
                if (idMapper.isPrivateId()) {
                    String investmentId = idMapper.getInvestmentId();
                    rowFilterExcludeIds.get(VIEW).add(investmentId);
                    rowFilterExcludeIds.get(EXPORT).add(investmentId);
                }
            }
        }
    }

    private void processRowFilters(
            List<RowFilter> rowFilters,
            Map<String, List<IdMapper>> splitMappers,
            EntitlementMetaData metaData,
            Map<String, List<String>> rowFilterExcludeIds
    ) {
        for (RowFilter rowFilter : rowFilters) {
            Universe matchCriteria = new Universe();
            matchCriteria.setCriterias(rowFilter.getMatchCriteriaList());

            Universe universeCriteria = new Universe();
            universeCriteria.setCriterias(rowFilter.getUniverseCriteriaList());

            for (List<IdMapper> idMappers : splitMappers.values()) {
                for (IdMapper idMapper : idMappers) {
                    if (idMapperEntitlementService.checkInvestmentIdCriteria(idMapper, matchCriteria, true, false).isEntitled()) {
                        boolean notEntitledToUniverse = !idMapperEntitlementService
                                .checkSeriesIdCriteria(idMapper, universeCriteria, true, false)
                                .isEntitled();
                        boolean notEntitledToProduct = !isEntitledToProductId(rowFilter, metaData);

                        if (notEntitledToUniverse || notEntitledToProduct) {
                            rowFilterExcludeIds.get(rowFilter.getUseCase()).add(idMapper.getInvestmentId());
                        }
                    }
                }
            }
        }
    }

    public void removeFilteredOutIdMappers(
            Map<String, List<IdMapper>> splitMappers,
            Map<String, List<String>> rowFilterExcludedIds
    ) {
        Set<String> distinctExcludedIds = rowFilterExcludedIds
                .entrySet()
                .stream()
                .flatMap(entry -> entry.getValue().stream())
                .collect(Collectors.toSet());
        for (List<IdMapper> idMappers : splitMappers.values()) {
            idMappers.removeIf(idMapper -> distinctExcludedIds.contains(idMapper.getInvestmentId()));
        }
    }

    private boolean isEntitledToProductId(RowFilter rowFilter, EntitlementMetaData metaData) {
        if (bypassProductIdEntitlement(rowFilter, metaData)) {
            return true;
        }
        if (StringUtils.isEmpty(metaData.getEntitlementProductId())) {
            return false;
        }
        return rowFilter.getProductCodes().stream().anyMatch(productCode -> metaData.getEntitlementProductId().equalsIgnoreCase(productCode));
    }

    private boolean bypassProductIdEntitlement(RowFilter rowFilter, EntitlementMetaData metaData) {
        return !metaData.isCheckProductIdEntitlement() || CollectionUtils.isEmpty(rowFilter.getProductCodes());
    }
}
