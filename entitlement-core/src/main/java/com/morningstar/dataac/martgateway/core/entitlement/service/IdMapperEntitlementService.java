package com.morningstar.dataac.martgateway.core.entitlement.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CellMatch;
import com.morningstar.dataac.martgateway.core.entitlement.entity.Criteria;
import com.morningstar.dataac.martgateway.core.entitlement.entity.Universe;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import java.util.Optional;
import java.util.Set;

import com.morningstar.dataac.martgateway.core.entitlement.entity.IdEntitleStatus;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.COUNTRY_FOR_SALE;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.CRITERIA_DPS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.DOMICILE_COUNTRY;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.EXCHANGE_ID;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.FUND_FAMILY_CODES;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.SECURITY_STATUS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.SECURITY_TYPE;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.SERIES_DP;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.SERIES_ID;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.SHARE_CLASS_STATUS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.STATUS;
import static com.morningstar.dataac.martgateway.core.entitlement.entity.constants.EntitlementConstants.STATUSES_MAP;

public class IdMapperEntitlementService {
    public IdEntitleStatus isIdEntitled(IdMapper idMapper, CellMatch cellMatch, boolean isSeriesId, boolean isCellInspection) {
        Set<String> missingCriterias = new HashSet<>();
        for(Universe universe : cellMatch.getUniverses()) {
            IdEntitleStatus idEntitleStatus = idEntitleCheck(idMapper, universe, isSeriesId, isCellInspection);
            if(idEntitleStatus.isEntitled()) {
                return idEntitleStatus;
            }
            //Only for cell level inspection
            if(isCellInspection) {
                missingCriterias.addAll(idEntitleStatus.getMissingCriterias());
            }
        }
        return new IdEntitleStatus(false, false, missingCriterias);
    }

    //Check if investment id is entitled for both regular and TS datapoints
    private IdEntitleStatus idEntitleCheck(IdMapper idMapper, Universe universe, boolean isSeriesId, boolean isCellInspection) {
        IdEntitleStatus idEntitleStatus = isIdEntitledInUniverse(idMapper, universe, isSeriesId, false, isCellInspection);
        boolean isTsEntitled = idEntitleStatus.isEntitled() ? Optional.ofNullable(universe.getAllowHistorical()).orElse(false) : false;
        idEntitleStatus.setTsEntitled(isTsEntitled);
        return idEntitleStatus;
    }

    //Only for cell level inspection we will check for all criterias, for remaining usecases we will return false immediately for any unmatched criteria
    public IdEntitleStatus isIdEntitledInUniverse(IdMapper idMapper, Universe universe, boolean isSeriesId, boolean isRowFilter, boolean isCellInspection) {
       IdEntitleStatus idEntitleStatus = idMapperContainsSeriesId(idMapper, isSeriesId) ?
                checkSeriesIdCriteria(idMapper, universe, isRowFilter, isCellInspection) :
                checkInvestmentIdCriteria(idMapper, universe, isRowFilter, isCellInspection);
        return idEntitleStatus;
    }

    private boolean idMapperContainsSeriesId(IdMapper idMapper, boolean isSeriesId) {
        return isSeriesId || idMapper.isPrivateId();
    }

    public IdEntitleStatus checkInvestmentIdCriteria(IdMapper idMapper, Universe universe, boolean isRowFilter, boolean isCellInspection) {
        Set<String> missingCriteriaSet = new HashSet<>();
        for (Criteria c : universe.getCriterias()) {
            if (!CollectionUtils.isEmpty(c.getValues())) {
                boolean isEntitled = true;
                switch (c.getDataPointId()) {
                    //If security type does not match, we do not have to check remaining criterias
                    case SECURITY_TYPE -> {
                        if (!securityTypeMatches(idMapper, c)) {
                            return new IdEntitleStatus(false);
                        }
                    }
                    //For row filter, if security status does not match, we do not have to check remaining criterias
                    case SECURITY_STATUS -> {
                        if (isRowFilter && !isSecurityStatusMatch(idMapper, c)) {
                            return new IdEntitleStatus(false);
                        }
                    }
                    case DOMICILE_COUNTRY -> isEntitled = entitledIfCriteriaContainsId(idMapper, c, "DomicileCountry", DOMICILE_COUNTRY, missingCriteriaSet);
                    case COUNTRY_FOR_SALE -> isEntitled = entitledIfCriteriaContainsAnyId(idMapper, c, "CountryForSale", COUNTRY_FOR_SALE, missingCriteriaSet);

                    case EXCHANGE_ID -> isEntitled = entitledIfCriteriaContainsId(idMapper, c, "ExchangeId", EXCHANGE_ID, missingCriteriaSet);

                    case SHARE_CLASS_STATUS -> isEntitled = entitledIfShareClassStatusMatch(idMapper, c, missingCriteriaSet);

                    case FUND_FAMILY_CODES -> isEntitled = entitledIfCriteriaContainsId(idMapper, c, "FundFamilyCode", FUND_FAMILY_CODES, missingCriteriaSet);

                    default -> {}
                }
                //For all cases except cell level inspection, exit the entitlement check if not entitled for a criteria
                if(!isCellInspection && !isEntitled) {
                    return new IdEntitleStatus(false);
                }
            }
        }
        boolean isEntitled = CollectionUtils.isEmpty(missingCriteriaSet);
        IdEntitleStatus idEntitleStatus = new IdEntitleStatus(isEntitled);
        idEntitleStatus.addMissingCriterias(missingCriteriaSet);
        return idEntitleStatus;
    }

    public IdEntitleStatus checkSeriesIdCriteria(IdMapper idMapper, Universe universe, boolean isRowFilter, boolean isCellInspection) {
        Set<String> missingCriterias = new HashSet<>();
        Set<String> entitledSeriesId = new HashSet<>();
        //For Row filter scenarios we dont need to check for security type match, as it is covered by Matched Criteria check
        boolean isSecurityTypeMatched = isRowFilter;
        boolean isSeriesIdMatched = false;
        for (Criteria c : universe.getCriterias()) {
            if (!CollectionUtils.isEmpty(c.getValues())) {
                switch (c.getDataPointId()) {
                    //If security type does not match, we do not have to check remaining criterias
                    case SECURITY_TYPE -> {
                        if (!securityTypeMatches(idMapper, c)) {
                            return new IdEntitleStatus(false);
                        }
                        isSecurityTypeMatched = true;
                    }
                    case SERIES_DP -> {
                        if (!criteriaContainsAnyId(c, splitIds(idMapper.getId(SERIES_ID)))) {
                            missingCriterias.add(CRITERIA_DPS.get(SERIES_DP));
                        } else {
                            isSeriesIdMatched = true;
                            entitledSeriesId.addAll(CollectionUtils.intersection(c.getValues(), splitIds(idMapper.getId(SERIES_ID))));
                        }
                    }
                    default -> {}
                }
            }
        }
        boolean isEntitled = isSecurityTypeMatched && isSeriesIdMatched;
        IdEntitleStatus idEntitleStatus = new IdEntitleStatus(isEntitled);
        if(isCellInspection && isSecurityTypeMatched) {
            idEntitleStatus.addMissingCriterias(missingCriterias);
        }
        idEntitleStatus.addSeriesIds(entitledSeriesId);
        return idEntitleStatus;
    }

    private boolean entitledIfCriteriaContainsId(IdMapper idMapper, Criteria c,
                                                        String idMapperField, String criteriaField,
                                                        Set<String> missingCriteriaSet) {
        if (!criteriaContainsId(c, idMapper.getId(idMapperField))) {
            missingCriteriaSet.add(CRITERIA_DPS.get(criteriaField));
            return false;
        }
        return true;
    }

    private boolean entitledIfCriteriaContainsAnyId(IdMapper idMapper, Criteria c,
                                                    String idMapperField, String criteriaField,
                                                    Set<String> missingCriteriaSet) {
        if (!criteriaContainsAnyId(c, splitIds(idMapper.getId(idMapperField)))) {
            missingCriteriaSet.add(CRITERIA_DPS.get(criteriaField));
            return false;
        }
        return true;
    }

    private boolean entitledIfShareClassStatusMatch(IdMapper idMapper, Criteria c, Set<String> missingCriteriaSet) {
        if (!isShareClassStatusMatch(idMapper, c)) {
            String criteria = isEquitySecurityType(idMapper) ? CRITERIA_DPS.get(SHARE_CLASS_STATUS) : CRITERIA_DPS.get(SECURITY_STATUS);
            missingCriteriaSet.add(criteria);
            return false;
        }
        return true;
    }

    private static boolean isShareClassStatusMatch(IdMapper idMapper, Criteria c) {
        String id;
        if (isEquitySecurityType(idMapper)) {
            id = idMapper.getId("ShareClassStatus");
        } else {
            id = STATUSES_MAP.getOrDefault(idMapper.getId(STATUS), "absent");
        }
        return criteriaContainsId(c, id);
    }

    //Criterias that have both with eq and neq operator
    private static boolean isSecurityStatusMatch(IdMapper idMapper, Criteria c) {
        return (c.getOperator().equalsIgnoreCase("neq") && !criteriaContainsId(c, idMapper.getId(STATUS)))
                || (c.getOperator().equalsIgnoreCase("eq") && criteriaContainsId(c, idMapper.getId(
                STATUS)));
    }

    private static boolean securityTypeMatches(IdMapper idMapper, Criteria c) {
        return (c.getOperator().equalsIgnoreCase("eq") && criteriaContainsId(c, idMapper.getSecurityType()))
                || (c.getOperator().equalsIgnoreCase("neq") && !criteriaContainsId(c, idMapper.getSecurityType()));
    }

    private static boolean criteriaContainsId(Criteria c, String id) {
        return c.getValues().contains(id);
    }
    private static boolean criteriaContainsAnyId(Criteria c, List<String> ids) {
        return !Collections.disjoint(c.getValues(), ids);
    }

    private static boolean isEquitySecurityType(IdMapper idMapper) {
        return idMapper.getSecurityType().equalsIgnoreCase("ST");
    }

    private List<String> splitIds(String seriesIds) {
        if (StringUtils.isEmpty(seriesIds)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(List.of(StringUtils.split(seriesIds, ",")));
    }
}
